<?php
/**
 * Debug script for note ID 12 specifically
 */

// Start session and set up environment
session_start();

// Simulate logged-in user
$_SESSION['user'] = [
    'id' => 2,
    'name' => 'Test User',
    'email' => '<EMAIL>'
];

require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';
require_once 'src/utils/Session.php';
require_once 'src/utils/View.php';

try {
    $noteModel = new Note();
    $noteId = 12; // The specific note from the URL
    
    echo "<h2>Debug Note ID 12</h2>\n";
    
    // Get note using the model
    $note = $noteModel->find($noteId);
    
    if ($note) {
        echo "<h3>Note Data</h3>\n";
        echo "<strong>ID:</strong> " . $note['id'] . "<br>\n";
        echo "<strong>Title:</strong> " . htmlspecialchars($note['title']) . "<br>\n";
        echo "<strong>User ID:</strong> " . $note['user_id'] . "<br>\n";
        echo "<strong>Content exists:</strong> " . (!empty($note['content']) ? 'YES' : 'NO') . "<br>\n";
        echo "<strong>Content length:</strong> " . strlen($note['content'] ?? '') . "<br>\n";
        echo "<strong>Content is empty:</strong> " . (empty($note['content']) ? 'TRUE' : 'FALSE') . "<br>\n";
        echo "<strong>Content trimmed:</strong> '" . htmlspecialchars(trim($note['content'] ?? '')) . "'<br>\n";
        echo "<strong>Content trimmed length:</strong> " . strlen(trim($note['content'] ?? '')) . "<br>\n";
        echo "<strong>Content trimmed is empty:</strong> " . (trim($note['content'] ?? '') === '' ? 'TRUE' : 'FALSE') . "<br>\n";
        
        // Test the exact condition from the view
        $condition1 = !empty($note['content']);
        $condition2 = trim($note['content']) !== '';
        $finalCondition = $condition1 && $condition2;
        
        echo "<h3>View Condition Analysis</h3>\n";
        echo "<strong>!empty(\$note['content']):</strong> " . ($condition1 ? 'TRUE' : 'FALSE') . "<br>\n";
        echo "<strong>trim(\$note['content']) !== '':</strong> " . ($condition2 ? 'TRUE' : 'FALSE') . "<br>\n";
        echo "<strong>Final condition (both):</strong> " . ($finalCondition ? 'TRUE - CONTENT SHOULD SHOW' : 'FALSE - CONTENT WILL NOT SHOW') . "<br>\n";
        
        // Show raw content
        echo "<h3>Raw Content</h3>\n";
        echo "<pre style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
        echo "'" . htmlspecialchars($note['content'] ?? '') . "'";
        echo "</pre>\n";
        
        // Show content as it would appear in view
        if ($finalCondition) {
            echo "<h3>Content as it would appear in view</h3>\n";
            echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>";
            echo nl2br(htmlspecialchars($note['content']));
            echo "</div>\n";
        }
        
        // Check all note fields
        echo "<h3>All Note Fields</h3>\n";
        echo "<pre>" . print_r($note, true) . "</pre>\n";
        
    } else {
        echo "<strong>ERROR:</strong> Note not found!<br>\n";
    }
    
    // Direct database query
    echo "<h3>Direct Database Query</h3>\n";
    $db = Database::getInstance();
    $directNote = $db->fetchOne("SELECT * FROM notes WHERE id = ?", [$noteId]);
    
    if ($directNote) {
        echo "<strong>Direct DB - Content exists:</strong> " . (!empty($directNote['content']) ? 'YES' : 'NO') . "<br>\n";
        echo "<strong>Direct DB - Content length:</strong> " . strlen($directNote['content'] ?? '') . "<br>\n";
        echo "<strong>Direct DB - Content:</strong> <pre>" . htmlspecialchars($directNote['content'] ?? '') . "</pre>\n";
    } else {
        echo "Note not found in database!<br>\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
