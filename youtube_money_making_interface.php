<?php
/**
 * YouTube Money Making Interface
 * 
 * This script provides a user interface for the YouTube Money Making agent.
 */

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentCategory.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$categoryModel = new AIAgentCategory();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Check if the agent exists
$agents = $agentModel->getUserAgents(1);
$youtubeAgentExists = false;
$youtubeAgentId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'YouTube Browser') {
        $youtubeAgentExists = true;
        $youtubeAgentId = $agent['id'];
        break;
    }
}

// Process form submission
$message = '';
$results = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'create_agent' && !$youtubeAgentExists) {
            // Create the agent
            include 'create_youtube_agent.php';
            $message = 'YouTube Browser agent created successfully!';
            
            // Refresh agent data
            $agents = $agentModel->getUserAgents(1);
            foreach ($agents as $agent) {
                if ($agent['name'] === 'YouTube Browser') {
                    $youtubeAgentExists = true;
                    $youtubeAgentId = $agent['id'];
                    break;
                }
            }
        } elseif ($_POST['action'] === 'run_agent' && $youtubeAgentExists) {
            // Run the money making agent
            ob_start();
            include 'youtube_money_making.php';
            $results = ob_get_clean();
            $message = 'YouTube Money Making agent executed successfully!';
        }
    }
}

// Get agent details if it exists
$agentDetails = null;
$agentTasks = [];
$agentInteractions = [];

if ($youtubeAgentExists && $youtubeAgentId) {
    $agentDetails = $agentModel->getAgent($youtubeAgentId, 1);
    
    // Get agent tasks
    $agentTasks = $taskModel->getAgentTasks($youtubeAgentId);
    
    // Get agent interactions
    $agentInteractions = $interactionModel->getAgentInteractions($youtubeAgentId, 10);
}

// Get the most recent money-making analysis if available
$moneyMakingAnalysis = null;
if ($youtubeAgentExists && $youtubeAgentId) {
    foreach ($agentInteractions as $interaction) {
        if ($interaction['interaction_type'] === 'system' && 
            strpos($interaction['content'], 'Completed task: Find YouTube videos about') !== false &&
            strpos($interaction['response'], 'Money-Making Techniques from YouTube') !== false) {
            $moneyMakingAnalysis = $interaction;
            break;
        }
    }
}

// HTML output
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Money Making Agent</title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-2">YouTube Money Making Agent</h1>
        <p class="text-gray-600 dark:text-gray-400 mb-6">Discover profitable money-making techniques and opportunities from YouTube</p>
        
        <?php if ($message): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p><?php echo $message; ?></p>
            </div>
        <?php endif; ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column: Agent Status and Controls -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4">Agent Status</h2>
                    
                    <?php if ($youtubeAgentExists): ?>
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                            <p>YouTube Browser agent is ready to use</p>
                        </div>
                        
                        <div class="mb-4">
                            <h3 class="font-medium mb-2">Agent Details:</h3>
                            <ul class="list-disc list-inside ml-4 text-sm">
                                <li>Name: <?php echo $agentDetails['name']; ?></li>
                                <li>Category: <?php echo $agentDetails['category_name']; ?></li>
                                <li>Status: <?php echo ucfirst($agentDetails['status']); ?></li>
                                <li>Intelligence Level: <?php echo $agentDetails['intelligence_level']; ?>/10</li>
                                <li>Last Active: <?php echo $agentDetails['last_active']; ?></li>
                            </ul>
                        </div>
                        
                        <form method="post" class="mb-6">
                            <input type="hidden" name="action" value="run_agent">
                            
                            <div class="mb-4">
                                <label for="search_topic" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search Topic</label>
                                <input type="text" id="search_topic" name="search_topic" value="passive income OR make money online OR side hustle" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Use OR between terms for broader results</p>
                            </div>
                            
                            <div class="mb-4">
                                <label for="days_ago" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Period (days)</label>
                                <input type="number" id="days_ago" name="days_ago" value="30" min="1" max="365" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label for="max_results" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Maximum Results</label>
                                <input type="number" id="max_results" name="max_results" value="10" min="1" max="50" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label for="min_views" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Minimum Views</label>
                                <input type="number" id="min_views" name="min_views" value="1000" min="100" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label for="api_key" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">YouTube API Key (Optional)</label>
                                <input type="text" id="api_key" name="api_key" placeholder="Enter your YouTube API key" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white">
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">For real YouTube data. If not provided, sample data will be used.</p>
                            </div>
                            
                            <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded">
                                <i class="fas fa-search mr-2"></i> Find Money Making Opportunities
                            </button>
                        </form>
                        
                        <a href="/momentum/ai-agents/view/<?php echo $youtubeAgentId; ?>" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i> View Agent in AI Agents Dashboard
                        </a>
                    <?php else: ?>
                        <div class="flex items-center mb-4">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                            <p>YouTube Browser agent is not set up yet.</p>
                        </div>
                        
                        <form method="post">
                            <input type="hidden" name="action" value="create_agent">
                            <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded">
                                <i class="fas fa-plus mr-2"></i> Create YouTube Browser Agent
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
                
                <?php if ($youtubeAgentExists && !empty($agentTasks)): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Recent Tasks</h2>
                        
                        <div class="space-y-3">
                            <?php 
                            // Get only the most recent 5 tasks
                            $recentTasks = array_slice($agentTasks, 0, 5);
                            foreach ($recentTasks as $task): 
                            ?>
                                <div class="border-l-4 
                                    <?php 
                                        switch ($task['status']) {
                                            case 'completed': echo 'border-green-500'; break;
                                            case 'in_progress': echo 'border-blue-500'; break;
                                            case 'failed': echo 'border-red-500'; break;
                                            default: echo 'border-gray-500';
                                        }
                                    ?> pl-4 py-2">
                                    <div class="flex justify-between items-start">
                                        <span class="text-sm font-medium">
                                            <?php echo htmlspecialchars(substr($task['title'], 0, 50)); ?><?php echo strlen($task['title']) > 50 ? '...' : ''; ?>
                                        </span>
                                        <span class="text-xs px-2 py-1 rounded-full 
                                            <?php 
                                                switch ($task['status']) {
                                                    case 'completed': echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'; break;
                                                    case 'in_progress': echo 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'; break;
                                                    case 'failed': echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'; break;
                                                    default: echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                                                }
                                            ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $task['status'])); ?>
                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <?php echo date('Y-m-d H:i', strtotime($task['created_at'])); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Right Column: Results -->
            <div class="lg:col-span-2">
                <?php if (!empty($results)): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Money Making Opportunities</h2>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded overflow-auto max-h-screen">
                            <?php echo $results; ?>
                        </div>
                    </div>
                <?php elseif ($moneyMakingAnalysis): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">Previous Money Making Analysis</h2>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded overflow-auto max-h-screen">
                            <div class="prose dark:prose-invert max-w-none">
                                <?php
                                // Convert markdown to HTML
                                $analysisHtml = nl2br(htmlspecialchars($moneyMakingAnalysis['response']));
                                
                                // Convert headers
                                $analysisHtml = preg_replace('/^# (.*?)$/m', '<h1 class="text-2xl font-bold mb-4">$1</h1>', $analysisHtml);
                                $analysisHtml = preg_replace('/^## (.*?)$/m', '<h2 class="text-xl font-semibold mt-6 mb-3">$1</h2>', $analysisHtml);
                                $analysisHtml = preg_replace('/^### (.*?)$/m', '<h3 class="text-lg font-medium mt-4 mb-2">$1</h3>', $analysisHtml);
                                
                                // Convert lists
                                $analysisHtml = preg_replace('/^(\d+)\. \*\*(.*?)\*\*$/m', '<div class="mb-2"><strong>$1. $2</strong></div>', $analysisHtml);
                                $analysisHtml = preg_replace('/^   - \*\*(.*?):\*\* (.*)$/m', '<div class="ml-6 mb-1"><span class="font-medium">$1:</span> $2</div>', $analysisHtml);
                                
                                // Convert links
                                $analysisHtml = preg_replace('/\[Watch Video\]\((.*?)\)/', '<a href="$1" target="_blank" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">Watch Video</a>', $analysisHtml);
                                
                                // Convert bold and italic
                                $analysisHtml = preg_replace('/\*\*(.*?)\*\*/m', '<strong>$1</strong>', $analysisHtml);
                                $analysisHtml = preg_replace('/\*(.*?)\*/m', '<em>$1</em>', $analysisHtml);
                                
                                echo $analysisHtml;
                                ?>
                            </div>
                            <div class="mt-4 text-sm text-gray-500 dark:text-gray-400">
                                Analysis generated on: <?php echo date('Y-m-d H:i', strtotime($moneyMakingAnalysis['created_at'])); ?>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                        <h2 class="text-xl font-semibold mb-4">How It Works</h2>
                        <div class="prose dark:prose-invert max-w-none">
                            <p>The YouTube Money Making Agent helps you discover profitable opportunities and techniques by analyzing trending YouTube content. Here's how to use it:</p>
                            
                            <ol class="mt-4 space-y-4">
                                <li class="flex">
                                    <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">1</span>
                                    <div>
                                        <strong>Configure your search</strong>
                                        <p class="text-gray-600 dark:text-gray-400 mt-1">Enter keywords related to money-making opportunities you're interested in. Use "OR" between terms for broader results.</p>
                                    </div>
                                </li>
                                <li class="flex">
                                    <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">2</span>
                                    <div>
                                        <strong>Set your parameters</strong>
                                        <p class="text-gray-600 dark:text-gray-400 mt-1">Choose how far back to search, how many results to return, and the minimum view count to filter for popular content.</p>
                                    </div>
                                </li>
                                <li class="flex">
                                    <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">3</span>
                                    <div>
                                        <strong>Run the analysis</strong>
                                        <p class="text-gray-600 dark:text-gray-400 mt-1">Click "Find Money Making Opportunities" to start the search and analysis process.</p>
                                    </div>
                                </li>
                                <li class="flex">
                                    <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 rounded-full w-6 h-6 flex items-center justify-center mr-3 flex-shrink-0">4</span>
                                    <div>
                                        <strong>Review the results</strong>
                                        <p class="text-gray-600 dark:text-gray-400 mt-1">The agent will categorize opportunities, identify trends, and provide a recommended action plan based on the analysis.</p>
                                    </div>
                                </li>
                            </ol>
                            
                            <div class="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg">
                                <h3 class="text-lg font-medium text-yellow-800 dark:text-yellow-200">Pro Tip</h3>
                                <p class="text-yellow-700 dark:text-yellow-300 mt-1">For the most accurate results, provide your YouTube API key. Without an API key, the agent will use sample data for demonstration purposes.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold mb-4">Popular Money-Making Categories</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center mb-2">
                                    <div class="bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 p-2 rounded-full mr-2">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <h3 class="font-medium">Passive Income</h3>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Income streams that require minimal ongoing effort after initial setup.</p>
                            </div>
                            
                            <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center mb-2">
                                    <div class="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-2 rounded-full mr-2">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <h3 class="font-medium">AI-Based Opportunities</h3>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Leveraging artificial intelligence tools to create value and generate income.</p>
                            </div>
                            
                            <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center mb-2">
                                    <div class="bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 p-2 rounded-full mr-2">
                                        <i class="fas fa-laptop-code"></i>
                                    </div>
                                    <h3 class="font-medium">Freelancing & Services</h3>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Offering skills and services to clients on a project or ongoing basis.</p>
                            </div>
                            
                            <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center mb-2">
                                    <div class="bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300 p-2 rounded-full mr-2">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <h3 class="font-medium">E-Commerce</h3>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Selling physical or digital products through online platforms and marketplaces.</p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="mt-6">
            <a href="/momentum/ai-agents" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300">
                <i class="fas fa-arrow-left mr-1"></i> Back to AI Agents Dashboard
            </a>
        </div>
    </div>
</body>
</html>
