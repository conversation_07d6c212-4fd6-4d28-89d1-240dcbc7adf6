<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include necessary files
require_once BASE_PATH . '/src/utils/Database.php';
require_once BASE_PATH . '/src/models/BaseModel.php';
require_once BASE_PATH . '/src/models/ADHDSymptom.php';
require_once BASE_PATH . '/src/models/ConsistencyTracker.php';

// Get database instance
$db = Database::getInstance();

// Create model instances
$symptomModel = new ADHDSymptom();
$consistencyModel = new ConsistencyTracker();

// Test user ID (assuming user 1 exists)
$userId = 1;

echo "Testing ADHD functions...\n\n";

// Test saving a symptom log
echo "Testing symptom log creation:\n";
$symptomData = [
    'user_id' => $userId,
    'log_date' => date('Y-m-d'),
    'focus_score' => 7,
    'productivity_score' => 6,
    'consistency_score' => 5,
    'organization_score' => 6,
    'impulsivity_score' => 7,
    'emotional_regulation_score' => 8,
    'notes' => 'Test symptom log',
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

$symptomResult = $symptomModel->createLog($symptomData);
echo "- Symptom log creation: " . ($symptomResult ? "SUCCESS (ID: $symptomResult)" : "FAILED") . "\n";

// Test saving a consistency tracker
echo "\nTesting consistency tracker creation:\n";
$trackerData = [
    'user_id' => $userId,
    'habit_name' => 'Test Habit',
    'description' => 'Test habit description',
    'frequency' => 'daily',
    'streak_count' => 0,
    'longest_streak' => 0,
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

$trackerResult = $consistencyModel->createTracker($trackerData);
echo "- Consistency tracker creation: " . ($trackerResult ? "SUCCESS (ID: $trackerResult)" : "FAILED") . "\n";

// If tracker was created, test logging completion
if ($trackerResult) {
    echo "\nTesting consistency log creation:\n";
    $logResult = $consistencyModel->logCompletion(
        $trackerResult,
        date('Y-m-d'),
        true,
        5,
        'Test log'
    );
    echo "- Consistency log creation: " . ($logResult ? "SUCCESS" : "FAILED") . "\n";
}

// Check if the symptom log was saved
if ($symptomResult) {
    $savedLog = $symptomModel->getLogByDate($userId, date('Y-m-d'));
    echo "\nRetrieving saved symptom log:\n";
    echo "- Retrieved log: " . ($savedLog ? "SUCCESS" : "FAILED") . "\n";
    if ($savedLog) {
        echo "  Log data: " . print_r($savedLog, true) . "\n";
    }
}

// Check if the consistency tracker was saved
if ($trackerResult) {
    $savedTracker = $consistencyModel->getTracker($trackerResult);
    echo "\nRetrieving saved consistency tracker:\n";
    echo "- Retrieved tracker: " . ($savedTracker ? "SUCCESS" : "FAILED") . "\n";
    if ($savedTracker) {
        echo "  Tracker data: " . print_r($savedTracker, true) . "\n";
    }
}
