<?php
/**
 * Debug Agent Army Project Creation
 *
 * This script debugs the creation of an AI Agent Army project using the AegisDirectorProjectManager.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/AegisDirectorProjectManager.php';
require_once 'src/models/TaskDependency.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";

    // Check if required tables exist
    $tables = ['projects', 'tasks', 'ai_agents', 'ai_agent_tasks', 'ai_agent_interactions', 'project_agent_assignments', 'task_dependencies'];

    echo "Checking database tables:\n";
    foreach ($tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '{$table}'");
        $exists = $result && $result->rowCount() > 0;
        echo "- {$table}: " . ($exists ? "EXISTS" : "MISSING") . "\n";
    }
} else {
    echo "Database connection failed\n";
    exit;
}

// Create a test project using the AegisDirectorProjectManager
$userId = 1;
$brigadeType = 'content_creation';
$projectName = 'Test Agent Army Project';
$projectDescription = 'This is a test project for the Content Creation Brigade.';
$deadline = date('Y-m-d', strtotime('+7 days'));

echo "Creating Agent Army project with data:\n";
echo "User ID: {$userId}\n";
echo "Brigade Type: {$brigadeType}\n";
echo "Project Name: {$projectName}\n";
echo "Project Description: {$projectDescription}\n";
echo "Deadline: {$deadline}\n";

// Add debugging to the createAgentArmyProject method
class DebugAegisDirectorProjectManager extends AegisDirectorProjectManager {
    // Define models as protected so they can be accessed in this class
    protected $projectModel;
    protected $taskModel;
    protected $agentModel;
    protected $agentTaskModel;
    protected $interactionModel;
    protected $assignmentModel;

    public function __construct() {
        parent::__construct();
        // Initialize models again in this class since they're private in the parent
        $this->projectModel = new Project();
        $this->taskModel = new Task();
        $this->agentModel = new AIAgent();
        $this->agentTaskModel = new AIAgentTask();
        $this->interactionModel = new AIAgentInteraction();
        $this->assignmentModel = new ProjectAgentAssignment();
    }

    public function createAgentArmyProject($userId, $brigadeType, $projectName, $projectDescription, $deadline) {
        // Create the project
        $projectData = [
            'user_id' => $userId,
            'name' => $projectName,
            'description' => $projectDescription,
            'start_date' => date('Y-m-d'),
            'end_date' => $deadline,
            'status' => 'planning',
            'progress' => 0,
            'is_template' => false,
            'brigade_type' => $brigadeType,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        echo "Project data to be created:\n";
        print_r($projectData);

        // Enable error reporting for PDO
        $this->db->getConnection()->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        try {
            // Use direct database insert instead of model
            $projectId = $this->db->insert('projects', $projectData);

            if (!$projectId) {
                echo "Failed to create project. Error info:\n";
                print_r($this->db->getConnection()->errorInfo());
                return false;
            }
        } catch (Exception $e) {
            echo "Exception caught: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
            return false;
        }

        echo "Project created successfully with ID: {$projectId}\n";

        // Get the Aegis Director agent
        $aegisDirector = $this->getAegisDirectorAgent($userId);

        if (!$aegisDirector) {
            echo "Aegis Director agent not found\n";
            return $projectId; // Return project ID even if agent not found
        }

        echo "Found Aegis Director agent with ID: {$aegisDirector['id']}\n";

        // Assign Aegis Director to the project
        $assignmentResult = $this->assignmentModel->assignAgentToProject($projectId, $aegisDirector['id'], 'Brigade Commander');

        if ($assignmentResult) {
            echo "Assigned Aegis Director to the project successfully\n";
        } else {
            echo "Failed to assign Aegis Director to the project\n";
        }

        // Create default tasks based on brigade type
        echo "Creating brigade tasks for {$brigadeType} brigade...\n";
        $tasksCreated = $this->createBrigadeTasks($projectId, $brigadeType, $userId);

        if ($tasksCreated) {
            echo "Successfully created brigade tasks\n";
        } else {
            echo "Failed to create brigade tasks\n";
        }

        return $projectId;
    }

    // Override createBrigadeTasks to add debugging
    public function createBrigadeTasks($projectId, $brigadeType, $userId) {
        echo "Creating tasks for {$brigadeType} brigade...\n";

        // Call parent method to create the tasks
        $result = parent::createBrigadeTasks($projectId, $brigadeType, $userId);

        // Get the tasks that were created
        $tasks = $this->taskModel->getProjectTasks($projectId);
        echo "Created " . count($tasks) . " tasks for the project:\n";

        foreach ($tasks as $index => $task) {
            echo ($index + 1) . ". {$task['title']} (Priority: {$task['priority']})\n";
        }

        return $result;
    }
}

$debugProjectManager = new DebugAegisDirectorProjectManager();
$projectId = $debugProjectManager->createAgentArmyProject($userId, $brigadeType, $projectName, $projectDescription, $deadline);

if ($projectId) {
    echo "Agent Army project created successfully with ID: {$projectId}\n";
} else {
    echo "Failed to create Agent Army project\n";
}
