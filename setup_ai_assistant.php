<?php
/**
 * AI Assistant Setup Script
 *
 * This script sets up the database tables and initial data for the AI Assistant system
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
if (!defined('BASE_PATH')) {
    define('BASE_PATH', __DIR__);
}

// Load database configuration
require_once BASE_PATH . '/src/utils/Database.php';

echo "Setting up AI Assistant System...\n\n";

try {
    $db = Database::getInstance();

    echo "1. Creating AI Prompt System tables...\n";

    // Read and execute the AI prompt system schema
    $schemaFile = BASE_PATH . '/database/ai_prompt_system_schema.sql';
    if (!file_exists($schemaFile)) {
        echo "Schema file not found at: $schemaFile\n";
        echo "Please ensure the database schema file exists.\n";
        exit(1);
    }

    $schema = file_get_contents($schemaFile);

    // Remove comments and split into statements
    $lines = explode("\n", $schema);
    $cleanedLines = [];

    foreach ($lines as $line) {
        $line = trim($line);
        // Skip empty lines and comment lines
        if (empty($line) || strpos($line, '--') === 0) {
            continue;
        }
        $cleanedLines[] = $line;
    }

    $cleanedSchema = implode(' ', $cleanedLines);
    $statements = array_filter(array_map('trim', explode(';', $cleanedSchema)));

    foreach ($statements as $statement) {
        if (empty($statement)) {
            continue;
        }

        try {
            echo "  Executing: " . substr($statement, 0, 50) . "...\n";
            $result = $db->query($statement);
            if ($result !== false) {
                echo "  ✓ Success\n";
            } else {
                echo "  ⚠ Warning: Query returned false\n";
            }
        } catch (Exception $e) {
            echo "  ⚠ Warning: " . $e->getMessage() . "\n";
            echo "  Statement: " . substr($statement, 0, 100) . "...\n";
        }
    }

    echo "\n2. Creating upload directories...\n";

    // Create upload directories
    $uploadDirs = [
        'public/uploads/captures',
        'public/uploads/captures/thumbnails',
        'public/uploads/voice',
        'public/uploads/temp'
    ];

    foreach ($uploadDirs as $dir) {
        $fullPath = BASE_PATH . '/' . $dir;
        if (!is_dir($fullPath)) {
            if (mkdir($fullPath, 0755, true)) {
                echo "  ✓ Created directory: $dir\n";
            } else {
                echo "  ✗ Failed to create directory: $dir\n";
            }
        } else {
            echo "  ✓ Directory already exists: $dir\n";
        }

        // Create .htaccess file for security
        $htaccessPath = $fullPath . '/.htaccess';
        if (!file_exists($htaccessPath)) {
            $htaccessContent = "# Prevent direct access to uploaded files\n";
            $htaccessContent .= "Options -Indexes\n";
            $htaccessContent .= "# Allow only specific file types\n";
            $htaccessContent .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp|mp3|wav|ogg)$\">\n";
            $htaccessContent .= "    Order Allow,Deny\n";
            $htaccessContent .= "    Allow from all\n";
            $htaccessContent .= "</FilesMatch>\n";

            file_put_contents($htaccessPath, $htaccessContent);
            echo "  ✓ Created .htaccess for: $dir\n";
        }
    }

    echo "\n3. Setting up default prompt templates...\n";

    // Insert default prompt templates
    $defaultPrompts = [
        [
            'title' => 'Writing Assistant',
            'description' => 'Help with writing and editing tasks',
            'prompt_text' => 'Please help me with the following writing task: {task_description}

Context: {context}
Tone: {tone}
Target audience: {audience}

Please provide suggestions for improvement, corrections, and enhancements.',
            'category' => 'Writing & Content',
            'variables' => json_encode([
                'task_description' => ['type' => 'text', 'required' => true, 'description' => 'Describe what you need help with'],
                'context' => ['type' => 'text', 'required' => false, 'description' => 'Additional context or background'],
                'tone' => ['type' => 'select', 'options' => ['Professional', 'Casual', 'Academic', 'Creative'], 'default' => 'Professional'],
                'audience' => ['type' => 'text', 'required' => false, 'description' => 'Who is the target audience?']
            ]),
            'tags' => 'writing, editing, content, assistant',
            'is_template' => 1
        ],
        [
            'title' => 'Code Review',
            'description' => 'Get feedback on code quality and suggestions for improvement',
            'prompt_text' => 'Please review the following {language} code and provide feedback:

```{language}
{code}
```

Focus areas:
- Code quality and best practices
- Performance optimization
- Security considerations
- Readability and maintainability
{additional_focus}

Please provide specific suggestions for improvement.',
            'category' => 'Code & Development',
            'variables' => json_encode([
                'language' => ['type' => 'select', 'options' => ['JavaScript', 'Python', 'PHP', 'Java', 'C++', 'Other'], 'required' => true],
                'code' => ['type' => 'textarea', 'required' => true, 'description' => 'Paste your code here'],
                'additional_focus' => ['type' => 'text', 'required' => false, 'description' => 'Any specific areas to focus on?']
            ]),
            'tags' => 'code, review, programming, development',
            'is_template' => 1
        ],
        [
            'title' => 'ADHD Task Breakdown',
            'description' => 'Break down complex tasks into manageable steps for ADHD minds',
            'prompt_text' => 'I have ADHD and need help breaking down this task into smaller, manageable steps:

Task: {task}
Deadline: {deadline}
Estimated time available: {time_available}
Current energy level: {energy_level}

Please provide:
1. A step-by-step breakdown
2. Time estimates for each step
3. Suggestions for maintaining focus
4. Built-in break points
5. Motivation strategies

Make it ADHD-friendly with clear, actionable steps.',
            'category' => 'Personal & ADHD',
            'variables' => json_encode([
                'task' => ['type' => 'text', 'required' => true, 'description' => 'What task do you need to break down?'],
                'deadline' => ['type' => 'text', 'required' => false, 'description' => 'When is this due?'],
                'time_available' => ['type' => 'text', 'required' => false, 'description' => 'How much time do you have?'],
                'energy_level' => ['type' => 'select', 'options' => ['Low', 'Medium', 'High'], 'default' => 'Medium']
            ]),
            'tags' => 'adhd, task, breakdown, productivity, focus',
            'is_template' => 1
        ],
        [
            'title' => 'Business Strategy Analysis',
            'description' => 'Analyze business ideas and strategies',
            'prompt_text' => 'Please analyze the following business idea/strategy:

Business Concept: {business_concept}
Target Market: {target_market}
Budget: {budget}
Timeline: {timeline}

Please provide analysis on:
1. Market viability
2. Potential challenges
3. Revenue opportunities
4. Implementation steps
5. Risk assessment
6. Success metrics

{additional_questions}',
            'category' => 'Business & Strategy',
            'variables' => json_encode([
                'business_concept' => ['type' => 'textarea', 'required' => true, 'description' => 'Describe your business idea'],
                'target_market' => ['type' => 'text', 'required' => false, 'description' => 'Who is your target audience?'],
                'budget' => ['type' => 'text', 'required' => false, 'description' => 'What is your budget?'],
                'timeline' => ['type' => 'text', 'required' => false, 'description' => 'What is your timeline?'],
                'additional_questions' => ['type' => 'text', 'required' => false, 'description' => 'Any specific questions?']
            ]),
            'tags' => 'business, strategy, analysis, planning',
            'is_template' => 1
        ]
    ];

    // Get the first user ID (assuming there's at least one user)
    $users = $db->fetchAll("SELECT id FROM users LIMIT 1");
    if (empty($users)) {
        echo "  ⚠ No users found. Please create a user account first.\n";
    } else {
        $userId = $users[0]['id'];

        foreach ($defaultPrompts as $prompt) {
            // Get category ID
            $categoryResult = $db->fetchOne(
                "SELECT id FROM ai_prompt_categories WHERE name = ? AND (user_id = ? OR is_system = 1)",
                [$prompt['category'], $userId]
            );

            $categoryId = $categoryResult ? $categoryResult['id'] : null;

            // Check if prompt already exists
            $existing = $db->fetchOne(
                "SELECT id FROM ai_prompts WHERE title = ? AND user_id = ?",
                [$prompt['title'], $userId]
            );

            if (!$existing) {
                $result = $db->query(
                    "INSERT INTO ai_prompts (user_id, category_id, title, description, prompt_text, variables, tags, is_template, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
                    [
                        $userId,
                        $categoryId,
                        $prompt['title'],
                        $prompt['description'],
                        $prompt['prompt_text'],
                        $prompt['variables'],
                        $prompt['tags'],
                        $prompt['is_template']
                    ]
                );
                echo "  ✓ Created template: {$prompt['title']}\n";
            } else {
                echo "  ✓ Template already exists: {$prompt['title']}\n";
            }
        }
    }

    echo "\n4. Verifying installation...\n";

    // Verify tables exist
    $tables = [
        'ai_prompt_categories',
        'ai_prompts',
        'ai_prompt_history',
        'ai_prompt_workflows',
        'quick_captures',
        'capture_annotations',
        'ai_api_configurations'
    ];

    foreach ($tables as $table) {
        $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
        if ($result) {
            echo "  ✓ Table exists: $table\n";
        } else {
            echo "  ✗ Table missing: $table\n";
        }
    }

    echo "\n✅ AI Assistant System setup completed successfully!\n\n";
    echo "Next steps:\n";
    echo "1. Visit /momentum/ai-prompts to access the prompt system\n";
    echo "2. Visit /momentum/quick-capture to access the capture system\n";
    echo "3. Check the dashboard for the new AI Assistant widget\n";
    echo "4. Configure AI API keys in the settings (optional)\n\n";

} catch (Exception $e) {
    echo "\n❌ Setup failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
