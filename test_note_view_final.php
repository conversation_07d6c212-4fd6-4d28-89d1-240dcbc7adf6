<?php
/**
 * Final test to verify note view is working
 */

// Start session and set up environment
session_start();

// Simulate logged-in user
$_SESSION['user'] = [
    'id' => 2,
    'name' => 'Test User',
    'email' => '<EMAIL>'
];

require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';
require_once 'src/utils/Session.php';
require_once 'src/utils/View.php';

try {
    $noteModel = new Note();
    $noteId = 12;
    
    // Get note
    $note = $noteModel->find($noteId);
    
    if ($note) {
        echo "<h2>✅ Note View Fix Verification</h2>\n";
        
        // Test the condition
        $condition = !empty($note['content']) && trim($note['content']) !== '';
        echo "<strong>Content condition:</strong> " . ($condition ? '✅ PASS' : '❌ FAIL') . "<br>\n";
        echo "<strong>Content length:</strong> " . strlen($note['content']) . " characters<br>\n";
        
        if ($condition) {
            echo "<div style='background: #d1fae5; border: 2px solid #10b981; padding: 15px; margin: 10px 0; border-radius: 8px;'>\n";
            echo "<h3>✅ SUCCESS: Content will display in view</h3>\n";
            echo "<p><strong>Note ID:</strong> {$note['id']}</p>\n";
            echo "<p><strong>Title:</strong> " . htmlspecialchars($note['title']) . "</p>\n";
            echo "<p><strong>Content preview:</strong></p>\n";
            echo "<div style='background: white; padding: 10px; border: 1px solid #ccc; border-radius: 4px; max-height: 100px; overflow-y: auto;'>\n";
            echo nl2br(htmlspecialchars(substr($note['content'], 0, 200))) . "...\n";
            echo "</div>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #fee2e2; border: 2px solid #ef4444; padding: 15px; margin: 10px 0; border-radius: 8px;'>\n";
            echo "<h3>❌ ISSUE: Content will not display</h3>\n";
            echo "<p>The content condition failed, so the 'No content yet' message would show instead.</p>\n";
            echo "</div>\n";
        }
        
        echo "<h3>🔧 Applied Fixes</h3>\n";
        echo "<ul>\n";
        echo "<li>✅ Removed debug code from view template</li>\n";
        echo "<li>✅ Improved CSS for content visibility</li>\n";
        echo "<li>✅ Added proper styling for content containers</li>\n";
        echo "<li>✅ Increased max-height from 120px to 200px</li>\n";
        echo "<li>✅ Added min-height and padding for better visibility</li>\n";
        echo "<li>✅ Added !important rules to ensure content is visible</li>\n";
        echo "</ul>\n";
        
        echo "<h3>🎯 Next Steps</h3>\n";
        echo "<p>1. Go to <a href='/momentum/notes/view/12' target='_blank'>/momentum/notes/view/12</a></p>\n";
        echo "<p>2. The content should now be visible in a bordered container</p>\n";
        echo "<p>3. You can click the expand button to see more content</p>\n";
        
    } else {
        echo "❌ Note not found!<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
