<?php
/**
 * Project Creation Script for User ID 2
 */

// Database connection
$dbConfig = require_once __DIR__ . '/src/config/database.php';
$pdo = new PDO(
    "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}",
    $dbConfig['username'],
    $dbConfig['password'],
    [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
);

// Read project description from file
$description = file_get_contents(__DIR__ . '/project_description.txt');

// Use User ID 2 (asankapython)
$userId = 2;
echo "Using user ID: $userId (asankapython)\n";

// Insert project
$sql = "INSERT INTO projects (
            user_id, name, description, start_date, end_date, 
            status, is_template, created_at, updated_at
        ) VALUES (
            :user_id, :name, :description, :start_date, :end_date, 
            :status, :is_template, :created_at, :updated_at
        )";

$stmt = $pdo->prepare($sql);
$result = $stmt->execute([
    'user_id' => $userId,
    'name' => 'ADHD-Friendly Project Planning Guide',
    'description' => $description,
    'start_date' => date('Y-m-d'),
    'end_date' => date('Y-m-d', strtotime('+3 months')),
    'status' => 'planning',
    'is_template' => 1,
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
]);

if ($result) {
    $projectId = $pdo->lastInsertId();
    echo "Project created successfully with ID: $projectId\n";
    
    // Add tasks
    $tasks = [
        [
            'title' => '1. Project Structure and Organization',
            'description' => 'Set up the initial project structure and organization framework.',
            'priority' => 'high',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+1 week'))
        ],
        [
            'title' => '2. Task Breakdown and Organization',
            'description' => 'Implement hierarchical task structure, prioritization system, and dependency management.',
            'priority' => 'high',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+2 weeks'))
        ],
        [
            'title' => '3. Time and Resource Planning',
            'description' => 'Develop realistic timelines, allocate resources, and create milestones.',
            'priority' => 'medium',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+3 weeks'))
        ],
        [
            'title' => '4. ADHD-Friendly Execution Strategies',
            'description' => 'Implement Current Focus integration, progress tracking, regular review, and distraction management.',
            'priority' => 'medium',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+4 weeks'))
        ],
        [
            'title' => '5. Documentation and Communication',
            'description' => 'Set up project documentation, team communication, and knowledge capture systems.',
            'priority' => 'low',
            'status' => 'todo',
            'due_date' => date('Y-m-d', strtotime('+5 weeks'))
        ]
    ];
    
    $taskSql = "INSERT INTO tasks (
                    project_id, user_id, title, description, priority, 
                    status, due_date, created_at, updated_at
                ) VALUES (
                    :project_id, :user_id, :title, :description, :priority, 
                    :status, :due_date, :created_at, :updated_at
                )";
    
    $taskStmt = $pdo->prepare($taskSql);
    $taskCount = 0;
    
    foreach ($tasks as $task) {
        $taskResult = $taskStmt->execute([
            'project_id' => $projectId,
            'user_id' => $userId,
            'title' => $task['title'],
            'description' => $task['description'],
            'priority' => $task['priority'],
            'status' => $task['status'],
            'due_date' => $task['due_date'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($taskResult) {
            $taskCount++;
            echo "Created task: {$task['title']}\n";
        }
    }
    
    echo "\nProject creation completed successfully!\n";
    echo "Project ID: $projectId\n";
    echo "Number of tasks created: $taskCount\n";
    echo "\nYou can view your project at: http://localhost/momentum/projects/view/$projectId\n";
} else {
    echo "Failed to create project\n";
}
