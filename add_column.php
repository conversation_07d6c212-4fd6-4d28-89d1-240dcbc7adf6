<?php
/**
 * <PERSON><PERSON><PERSON> to add the current_focus_task_id column to the users table
 */

// Include the Database class
require_once __DIR__ . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Check if the column already exists
$checkColumnSql = "SELECT COUNT(*) as column_exists 
                  FROM information_schema.COLUMNS 
                  WHERE TABLE_SCHEMA = DATABASE() 
                  AND TABLE_NAME = 'users' 
                  AND COLUMN_NAME = 'current_focus_task_id'";

$columnExists = $db->fetchOne($checkColumnSql);

if ($columnExists && $columnExists['column_exists'] > 0) {
    echo "Column 'current_focus_task_id' already exists in the users table.\n";
} else {
    // Add the column
    $alterTableSql = "ALTER TABLE users ADD COLUMN current_focus_task_id INT DEFAULT NULL";
    $result = $db->query($alterTableSql);
    
    if ($result) {
        echo "Column 'current_focus_task_id' added successfully to the users table.\n";
    } else {
        echo "Failed to add column 'current_focus_task_id' to the users table.\n";
    }
}

// Try to add the foreign key constraint
$checkConstraintSql = "SELECT COUNT(*) as constraint_exists 
                      FROM information_schema.TABLE_CONSTRAINTS 
                      WHERE CONSTRAINT_SCHEMA = DATABASE() 
                      AND CONSTRAINT_NAME = 'fk_current_focus_task'";

$constraintExists = $db->fetchOne($checkConstraintSql);

if ($constraintExists && $constraintExists['constraint_exists'] > 0) {
    echo "Foreign key constraint 'fk_current_focus_task' already exists.\n";
} else {
    // Add the foreign key constraint
    $addConstraintSql = "ALTER TABLE users 
                        ADD CONSTRAINT fk_current_focus_task 
                        FOREIGN KEY (current_focus_task_id) REFERENCES tasks(id) ON DELETE SET NULL";
    
    $result = $db->query($addConstraintSql);
    
    if ($result) {
        echo "Foreign key constraint 'fk_current_focus_task' added successfully.\n";
    } else {
        echo "Failed to add foreign key constraint 'fk_current_focus_task'.\n";
        echo "This is not critical - the application will still work without it.\n";
    }
}

echo "Done!\n";
