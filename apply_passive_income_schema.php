<?php
/**
 * Apply Passive Income Schema
 *
 * This script applies the passive income schema to the database.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include database connection
require_once BASE_PATH . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Check if passive_income_streams table already exists
$result = $db->query("SHOW TABLES LIKE 'passive_income_streams'");
$tableExists = $result && $result->rowCount() > 0;

if ($tableExists) {
    echo "passive_income_streams table already exists.\n";
} else {
    echo "Creating passive income tables...\n";

    // Read the schema file
    $schema = file_get_contents(BASE_PATH . '/database/passive_income_schema.sql');

    // Split the schema into individual statements
    $statements = explode(';', $schema);

    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $result = $db->query($statement);
            if ($result) {
                echo ".";
            } else {
                echo "x";
            }
        }
    }

    echo "\nSchema applied successfully!\n";
}

// Check if tables exist
$tables = [
    'passive_income_streams',
    'passive_income_earnings',
    'passive_income_maintenance'
];

echo "\nChecking database tables:\n";
foreach ($tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '{$table}'");
    $exists = $result && $result->rowCount() > 0;
    echo "- {$table}: " . ($exists ? "EXISTS" : "MISSING") . "\n";
}

echo "\nDone!\n";
