<?php
/**
 * Dropdown Test Page
 * 
 * This page is used to test the dropdown fix in isolation.
 */

// Set the base path
$basePath = '/momentum';

// Include the necessary files
require_once '../src/utils/Database.php';
require_once '../src/controllers/DashboardController.php';

// Start the session
session_start();

// Function to check if a path is active
function isActive($path) {
    return false;
}

// Function to generate active class
function activeClass($path) {
    return 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600';
}
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Test</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                    },
                },
            },
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Master Dropdown Fix CSS -->
    <link rel="stylesheet" href="/momentum/public/css/dropdown-master-fix.css">

    <style>
        body {
            padding: 2rem;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #ccc;
            border-radius: 0.5rem;
        }
        .test-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .test-description {
            margin-bottom: 1rem;
        }
        .test-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .test-button {
            padding: 0.5rem 1rem;
            background-color: #0ea5e9;
            color: white;
            border-radius: 0.375rem;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0284c7;
        }
        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f3f4f6;
            border-radius: 0.375rem;
        }
        .dark .test-result {
            background-color: #374151;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Dropdown Test Page</h1>
        
        <div class="test-section">
            <div class="test-title">Navigation Bar Test</div>
            <div class="test-description">
                This test shows the navigation bar with all dropdowns. Hover over the dropdown buttons to test the fix.
            </div>
            
            <!-- Dropdown overlay for capturing clicks outside -->
            <div id="dropdown-overlay" class="dropdown-overlay"></div>
            
            <!-- ADHD Dropdown Menu (fixed position) -->
            <div id="adhd-dropdown-menu" class="dropdown-menu">
                <a href="/momentum/adhd">
                    <i class="fas fa-tachometer-alt mr-2"></i> ADHD Dashboard
                </a>
                <a href="/momentum/adhd/symptom-tracker">
                    <i class="fas fa-chart-line mr-2"></i> Symptom Tracker
                </a>
                <a href="/momentum/adhd/cbt/thought-records">
                    <i class="fas fa-comments mr-2"></i> Thought Records
                </a>
                <a href="/momentum/adhd/productivity/strategies">
                    <i class="fas fa-lightbulb mr-2"></i> Productivity Strategies
                </a>
                <a href="/momentum/adhd/mindfulness">
                    <i class="fas fa-spa mr-2"></i> Mindfulness Exercises
                </a>
                <a href="/momentum/adhd/consistency/trackers">
                    <i class="fas fa-calendar-check mr-2"></i> Consistency Trackers
                </a>
            </div>
            
            <!-- Productivity Dropdown Menu (fixed position) -->
            <div id="productivity-dropdown-menu" class="dropdown-menu">
                <a href="/momentum/productivity/focus-timer">
                    <i class="fas fa-stopwatch mr-2"></i> Focus Timer
                </a>
                <a href="/momentum/productivity/focus-mode">
                    <i class="fas fa-focus mr-2"></i> Focus Mode
                </a>
            </div>
            
            <!-- Tools Dropdown Menu (fixed position) -->
            <div id="tools-dropdown-menu" class="dropdown-menu">
                <a href="/momentum/tools/currency-converter">
                    <i class="fas fa-exchange-alt mr-2"></i> Currency Converter
                </a>
            </div>
            
            <!-- User Dropdown Menu (fixed position) -->
            <div id="user-dropdown-menu" class="dropdown-menu">
                <a href="/momentum/dashboard/toggle-theme">
                    <i class="fas fa-adjust mr-2"></i> Toggle Theme
                </a>
                <a href="/momentum/logout">
                    <i class="fas fa-sign-out-alt mr-2"></i> Sign out
                </a>
            </div>
            
            <div class="bg-white dark:bg-gray-800 shadow" style="position: relative; z-index: 50;">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <!-- Logo and brand -->
                        <div class="flex">
                            <div class="flex-shrink-0 flex items-center">
                                <a href="/momentum/dashboard" class="text-xl font-bold text-primary-600 dark:text-primary-400">
                                    <i class="fas fa-bolt mr-2"></i>Momentum
                                </a>
                            </div>
                        </div>
                        
                        <!-- Desktop navigation -->
                        <div class="hidden md:flex md:items-center md:space-x-4" id="desktop-navigation" style="display: flex !important;">
                            <nav class="flex space-x-4">
                                <a href="/momentum/dashboard" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                                    <i class="fas fa-home mr-1"></i> Dashboard
                                </a>
                                <div class="relative inline-block text-left" id="adhd-dropdown">
                                    <button type="button" id="adhd-dropdown-button" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                                        <i class="fas fa-brain mr-1"></i> ADHD <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                    </button>
                                </div>
                                <div class="relative inline-block text-left" id="productivity-dropdown">
                                    <button type="button" id="productivity-dropdown-button" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                                        <i class="fas fa-clock mr-1"></i> Productivity <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                    </button>
                                </div>
                                <div class="relative inline-block text-left" id="tools-dropdown">
                                    <button type="button" id="tools-dropdown-button" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600">
                                        <i class="fas fa-tools mr-1"></i> Tools <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                    </button>
                                </div>
                            </nav>
                            
                            <!-- User menu -->
                            <div class="ml-4 relative flex-shrink-0">
                                <div>
                                    <button type="button" id="user-menu-button" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        <span class="sr-only">Open user menu</span>
                                        <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Debug Controls</div>
            <div class="test-description">
                Use these controls to help debug the dropdown behavior.
            </div>
            <div class="test-controls">
                <button id="toggle-debug" class="test-button">Toggle Debug Panel</button>
                <button id="clear-log" class="test-button">Clear Debug Log</button>
                <button id="toggle-theme" class="test-button">Toggle Dark Mode</button>
            </div>
        </div>
    </div>
    
    <!-- Dropdown Diagnostics and Fix -->
    <script src="/momentum/public/js/dropdown-diagnostics.js"></script>
    <script src="/momentum/public/js/dropdown-master-fix.js"></script>
    
    <script>
        // Debug controls
        document.getElementById('toggle-debug').addEventListener('click', function() {
            const panel = document.getElementById('dropdown-debug-panel');
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
            }
        });
        
        document.getElementById('clear-log').addEventListener('click', function() {
            const log = document.getElementById('dropdown-debug-log');
            if (log) {
                log.innerHTML = '';
            }
        });
        
        document.getElementById('toggle-theme').addEventListener('click', function() {
            document.documentElement.classList.toggle('dark');
        });
    </script>
</body>
</html>
