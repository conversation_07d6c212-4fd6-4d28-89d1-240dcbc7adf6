<?php
/**
 * Download Pinterest Image API
 *
 * This API endpoint handles downloading Pinterest images using the EnhancedPinterestAPI.
 */

require_once __DIR__ . '/../../src/utils/Environment.php';
require_once __DIR__ . '/../../src/utils/Session.php';
require_once __DIR__ . '/../../src/api/EnhancedPinterestAPI.php';

// Set content type to JSON
header('Content-Type: application/json');

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'You must be logged in to download Pinterest images'
    ]);
    exit;
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
    exit;
}

// Check if the image URL is provided
if (!isset($_POST['image_url']) || empty($_POST['image_url'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Image URL is required'
    ]);
    exit;
}

// Get the image URL and filename
$imageUrl = $_POST['image_url'];
$filename = isset($_POST['filename']) ? $_POST['filename'] : 'pinterest_image_' . time() . '.jpg';

// Sanitize the filename
$filename = preg_replace('/[^a-zA-Z0-9_\-\.]/', '_', $filename);
if (!preg_match('/\.(jpg|jpeg|png|gif)$/i', $filename)) {
    $filename .= '.jpg';
}

// Load environment variables
Environment::load();

// Get credentials from environment variables
$email = Environment::get('PINTEREST_EMAIL');
$password = Environment::get('PINTEREST_PASSWORD');
$username = Environment::get('PINTEREST_USERNAME');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH');
$pythonPath = Environment::get('PYTHON_PATH');

// Initialize the API
$api = EnhancedPinterestAPI::getInstance($email, $password, $username, $pythonPath, $chromeProfile);

// Create the uploads directory if it doesn't exist
$uploadsDir = __DIR__ . '/../uploads/pinterest';
if (!file_exists($uploadsDir)) {
    mkdir($uploadsDir, 0777, true);
}

// Set the output path
$outputPath = $uploadsDir . '/' . $filename;

// Try direct download first using file_get_contents
$imageData = @file_get_contents($imageUrl);

if ($imageData !== false) {
    // Save the image
    if (file_put_contents($outputPath, $imageData)) {
        echo json_encode([
            'success' => true,
            'message' => 'Image downloaded successfully (direct method)',
            'download_url' => '/momentum/uploads/pinterest/' . $filename,
            'file_path' => $outputPath
        ]);
        exit;
    }
}

// If direct download fails, try using the API
$success = $api->downloadImage($imageUrl, $outputPath, false); // Use false for human-like behavior to speed up downloads

if ($success && file_exists($outputPath)) {
    // Return success response with the download URL
    echo json_encode([
        'success' => true,
        'message' => 'Image downloaded successfully (API method)',
        'download_url' => '/momentum/uploads/pinterest/' . $filename,
        'file_path' => $outputPath
    ]);
    exit;
}

// If all methods fail, return error
echo json_encode([
    'success' => false,
    'message' => 'Failed to download image after trying all methods'
]);
