<?php
/**
 * Direct Access to Task Batching and Batch Templates
 * 
 * This script provides a simple interface to directly view the Task Batching and Batch Templates features
 * without requiring the full application infrastructure.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple session handling
session_start();

// Define the feature to display
$feature = $_GET['feature'] ?? 'task-batching';
$validFeatures = ['task-batching', 'batch-templates', 'energy-tracking', 'time-blocking'];

if (!in_array($feature, $validFeatures)) {
    $feature = 'task-batching';
}

// Feature titles and descriptions
$features = [
    'task-batching' => [
        'title' => 'Task Batching',
        'description' => 'Group similar tasks together to reduce context switching and improve focus.',
        'icon' => 'layer-group',
        'color' => 'indigo'
    ],
    'batch-templates' => [
        'title' => 'Batch Templates',
        'description' => 'Create reusable templates for common task groupings and set up recurring batches.',
        'icon' => 'copy',
        'color' => 'purple'
    ],
    'energy-tracking' => [
        'title' => 'Energy Tracking',
        'description' => 'Track your energy levels throughout the day to identify patterns and optimize productivity.',
        'icon' => 'bolt',
        'color' => 'yellow'
    ],
    'time-blocking' => [
        'title' => 'Time Blocking',
        'description' => 'Schedule your tasks in dedicated time blocks to structure your day and maintain focus.',
        'icon' => 'calendar-alt',
        'color' => 'blue'
    ]
];

// HTML content for each feature
$featureContent = [
    'task-batching' => '
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">What is Task Batching?</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Task batching is a productivity technique where you group similar tasks together and complete them in a single session. 
                This reduces context switching and helps you maintain focus, which is especially beneficial for ADHD.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                    <h3 class="font-medium text-green-800 dark:text-green-200 mb-1">High Energy Tasks</h3>
                    <p class="text-sm text-green-700 dark:text-green-300">
                        Tasks that require deep focus, creativity, or complex problem-solving. Schedule these during your peak energy hours.
                    </p>
                </div>
                <div class="bg-yellow-50 dark:bg-yellow-900 p-3 rounded-lg">
                    <h3 class="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Medium Energy Tasks</h3>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300">
                        Routine tasks that require moderate focus. Good for average energy levels throughout the day.
                    </p>
                </div>
                <div class="bg-red-50 dark:bg-red-900 p-3 rounded-lg">
                    <h3 class="font-medium text-red-800 dark:text-red-200 mb-1">Low Energy Tasks</h3>
                    <p class="text-sm text-red-700 dark:text-red-300">
                        Simple, repetitive tasks that don\'t require much mental effort. Perfect for low energy periods.
                    </p>
                </div>
            </div>
            <div class="bg-indigo-50 dark:bg-indigo-900 p-4 rounded-lg">
                <h3 class="font-medium text-indigo-800 dark:text-indigo-200 mb-2">How to Use Task Batching</h3>
                <ol class="list-decimal pl-5 text-indigo-700 dark:text-indigo-300 space-y-2">
                    <li>Identify tasks that require similar mental resources or contexts</li>
                    <li>Group these tasks into batches based on their energy requirements</li>
                    <li>Schedule batches during appropriate energy periods in your day</li>
                    <li>Work through each batch without switching to unrelated tasks</li>
                    <li>Take short breaks between batches to reset your focus</li>
                </ol>
            </div>
        </div>
    ',
    'batch-templates' => '
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">What are Batch Templates?</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Batch templates allow you to create reusable task groupings for common workflows. 
                You can quickly generate new batches from templates and even set up recurring batches that are automatically created on a schedule.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                    <h3 class="font-medium text-blue-800 dark:text-blue-200 mb-1">Regular Templates</h3>
                    <p class="text-sm text-blue-700 dark:text-blue-300">
                        Create templates for common workflows that you can manually generate batches from whenever needed.
                    </p>
                </div>
                <div class="bg-purple-50 dark:bg-purple-900 p-3 rounded-lg">
                    <h3 class="font-medium text-purple-800 dark:text-purple-200 mb-1">Recurring Templates</h3>
                    <p class="text-sm text-purple-700 dark:text-purple-300">
                        Set up templates that automatically generate new batches on a daily, weekday, weekly, or monthly schedule.
                    </p>
                </div>
            </div>
            <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
                <h3 class="font-medium text-purple-800 dark:text-purple-200 mb-2">Benefits of Using Templates</h3>
                <ul class="list-disc pl-5 text-purple-700 dark:text-purple-300 space-y-2">
                    <li>Save time by not having to recreate common task batches</li>
                    <li>Ensure consistency in your workflows</li>
                    <li>Automate the creation of routine batches</li>
                    <li>Reduce decision fatigue by having predefined task groupings</li>
                    <li>Easily adapt templates as your workflows evolve</li>
                </ul>
            </div>
        </div>
    ',
    'energy-tracking' => '
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">What is Energy Tracking?</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Energy tracking helps you monitor your energy levels throughout the day to identify patterns and optimize your productivity.
                By understanding when you typically have high, medium, or low energy, you can schedule tasks accordingly.
            </p>
            <div class="bg-yellow-50 dark:bg-yellow-900 p-4 rounded-lg mb-6">
                <h3 class="font-medium text-yellow-800 dark:text-yellow-200 mb-2">How Energy Tracking Works</h3>
                <ol class="list-decimal pl-5 text-yellow-700 dark:text-yellow-300 space-y-2">
                    <li>Record your energy level on a scale from 1-10 at different times of the day</li>
                    <li>Add notes about factors that might be affecting your energy</li>
                    <li>Review patterns to identify your peak energy periods</li>
                    <li>Get recommendations for task batches that match your current energy level</li>
                </ol>
            </div>
            <div class="flex items-center justify-center mb-6">
                <div class="w-full max-w-md">
                    <div class="flex justify-between mb-1 text-sm text-gray-600 dark:text-gray-400">
                        <span>Low Energy</span>
                        <span>Medium Energy</span>
                        <span>High Energy</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-4">
                        <div class="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-4 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    ',
    'time-blocking' => '
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">What is Time Blocking?</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Time blocking is a productivity technique where you schedule specific blocks of time for different tasks or activities.
                This helps create structure in your day and reduces the mental load of deciding what to work on next.
            </p>
            <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
                <h3 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Benefits of Time Blocking</h3>
                <ul class="list-disc pl-5 text-blue-700 dark:text-blue-300 space-y-2">
                    <li>Creates a clear structure for your day</li>
                    <li>Reduces decision fatigue by pre-planning your activities</li>
                    <li>Helps maintain focus by dedicating specific time to tasks</li>
                    <li>Makes it easier to balance different types of work</li>
                    <li>Provides a visual representation of how you spend your time</li>
                </ul>
            </div>
            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                <h3 class="font-medium text-gray-800 dark:text-gray-200 mb-2">Sample Time Block Schedule</h3>
                <div class="space-y-2">
                    <div class="flex">
                        <div class="w-24 text-gray-600 dark:text-gray-400">8:00 - 9:30</div>
                        <div class="flex-1 bg-green-100 dark:bg-green-900 p-2 rounded">
                            <span class="text-green-800 dark:text-green-200">Deep Work: Project Planning</span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="w-24 text-gray-600 dark:text-gray-400">9:30 - 10:00</div>
                        <div class="flex-1 bg-yellow-100 dark:bg-yellow-900 p-2 rounded">
                            <span class="text-yellow-800 dark:text-yellow-200">Email & Communication</span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="w-24 text-gray-600 dark:text-gray-400">10:00 - 12:00</div>
                        <div class="flex-1 bg-green-100 dark:bg-green-900 p-2 rounded">
                            <span class="text-green-800 dark:text-green-200">Deep Work: Implementation</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    '
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $features[$feature]['title'] ?> - Momentum</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="flex items-center justify-between mb-8">
                <h1 class="text-3xl font-bold">Momentum</h1>
                <div>
                    <button id="darkModeToggle" class="p-2 rounded-full bg-gray-200 dark:bg-gray-700">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:block"></i>
                    </button>
                </div>
            </div>
            
            <!-- Feature Navigation -->
            <div class="flex overflow-x-auto space-x-2 mb-6 pb-2">
                <?php foreach ($validFeatures as $f): ?>
                    <a href="?feature=<?= $f ?>" class="flex-shrink-0 px-4 py-2 rounded-full <?= $f === $feature ? 'bg-' . $features[$f]['color'] . '-500 text-white' : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300' ?> shadow">
                        <i class="fas fa-<?= $features[$f]['icon'] ?> mr-2"></i>
                        <?= $features[$f]['title'] ?>
                    </a>
                <?php endforeach; ?>
            </div>
            
            <!-- Feature Header -->
            <div class="bg-<?= $features[$feature]['color'] ?>-500 text-white rounded-t-lg p-6">
                <div class="flex items-center">
                    <div class="bg-white bg-opacity-20 p-3 rounded-full mr-4">
                        <i class="fas fa-<?= $features[$feature]['icon'] ?> text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold"><?= $features[$feature]['title'] ?></h1>
                        <p class="text-<?= $features[$feature]['color'] ?>-100"><?= $features[$feature]['description'] ?></p>
                    </div>
                </div>
            </div>
            
            <!-- Feature Content -->
            <?= $featureContent[$feature] ?>
            
            <!-- Links -->
            <div class="mt-6 flex flex-wrap gap-4 justify-center">
                <a href="/momentum/productivity/<?= $feature ?>" class="inline-flex items-center px-4 py-2 bg-<?= $features[$feature]['color'] ?>-500 hover:bg-<?= $features[$feature]['color'] ?>-600 text-white rounded shadow">
                    <i class="fas fa-external-link-alt mr-2"></i> Go to <?= $features[$feature]['title'] ?>
                </a>
                <a href="/momentum/productivity_features.php" class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded shadow">
                    <i class="fas fa-th-large mr-2"></i> All Features
                </a>
                <a href="/momentum/dashboard" class="inline-flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded shadow">
                    <i class="fas fa-home mr-2"></i> Dashboard
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        
        // Check for dark mode preference
        if (localStorage.getItem('darkMode') === 'enabled' || 
            (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches && 
             localStorage.getItem('darkMode') !== 'disabled')) {
            document.documentElement.classList.add('dark');
        }
        
        // Toggle dark mode
        darkModeToggle.addEventListener('click', function() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('darkMode', 'disabled');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('darkMode', 'enabled');
            }
        });
    </script>
</body>
</html>
