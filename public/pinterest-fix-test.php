<?php
/**
 * Pinterest Fix Test
 *
 * This script tests the fixes for the Pinterest Clone Research Tool.
 */

// Include necessary files
require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/utils/Session.php';
require_once __DIR__ . '/../src/api/PinterestAPIFactory.php';

// Start output buffering
ob_start();

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    // Set flash message
    Session::setFlash('error', 'Please log in to access the Pinterest Fix Test');

    // Redirect to login page
    header('Location: /momentum/login');
    exit;
}

// Load environment variables
Environment::load();

// Get environment variables
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');
$chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');
$apiType = Environment::get('PINTEREST_API_TYPE', 'unofficial');
$disableApi = Environment::get('DISABLE_PINTEREST_API', 'false');
$pythonPath = Environment::get('PYTHON_PATH', 'python');

// Test results
$results = [];

// Test 1: Check if the consolidated JavaScript file exists
$results[] = [
    'name' => 'Consolidated JavaScript File',
    'success' => file_exists(__DIR__ . '/js/pinterest-consolidated-fix.js'),
    'message' => file_exists(__DIR__ . '/js/pinterest-consolidated-fix.js') ? 
        'The consolidated JavaScript file exists.' : 
        'The consolidated JavaScript file does not exist.'
];

// Test 2: Check if the custom Pinterest script exists
$customScriptPath = __DIR__ . '/../scripts/pinterest/custom_pinterest.py';
$results[] = [
    'name' => 'Custom Pinterest Script',
    'success' => file_exists($customScriptPath),
    'message' => file_exists($customScriptPath) ? 
        'The custom Pinterest script exists.' : 
        'The custom Pinterest script does not exist.'
];

// Test 3: Check if the PinterestAPIFactory can create an API instance
try {
    $api = PinterestAPIFactory::getAPI();
    $results[] = [
        'name' => 'PinterestAPIFactory',
        'success' => $api !== null,
        'message' => $api !== null ? 
            'The PinterestAPIFactory successfully created an API instance.' : 
            'The PinterestAPIFactory failed to create an API instance.'
    ];
} catch (Exception $e) {
    $results[] = [
        'name' => 'PinterestAPIFactory',
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ];
}

// Test 4: Check if Python is available
try {
    $pythonVersion = shell_exec($pythonPath . ' --version 2>&1');
    $results[] = [
        'name' => 'Python Availability',
        'success' => !empty($pythonVersion),
        'message' => !empty($pythonVersion) ? 
            'Python is available: ' . trim($pythonVersion) : 
            'Python is not available.'
    ];
} catch (Exception $e) {
    $results[] = [
        'name' => 'Python Availability',
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ];
}

// Test 5: Check if required Python packages are installed
try {
    $packagesOutput = shell_exec($pythonPath . ' -c "import sys; print(\',\'.join(sys.modules.keys()))" 2>&1');
    $packages = explode(',', $packagesOutput);
    $requiredPackages = ['selenium', 'requests', 'bs4', 'json', 'time', 'random'];
    $missingPackages = [];
    
    foreach ($requiredPackages as $package) {
        if (!in_array($package, $packages)) {
            $missingPackages[] = $package;
        }
    }
    
    $results[] = [
        'name' => 'Python Packages',
        'success' => empty($missingPackages),
        'message' => empty($missingPackages) ? 
            'All required Python packages are installed.' : 
            'Missing Python packages: ' . implode(', ', $missingPackages)
    ];
} catch (Exception $e) {
    $results[] = [
        'name' => 'Python Packages',
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ];
}

// Page title
$pageTitle = 'Pinterest Fix Test';

// End output buffering and get content
$content = ob_get_clean();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="/momentum/css/fontawesome.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold mb-2">Pinterest Fix Test</h1>
            <p class="text-gray-600 dark:text-gray-400">
                This page tests the fixes for the Pinterest Clone Research Tool.
            </p>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            
            <div class="space-y-4">
                <?php foreach ($results as $result): ?>
                    <div class="p-4 rounded-md <?= $result['success'] ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900' ?>">
                        <h3 class="font-semibold <?= $result['success'] ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200' ?>">
                            <?= htmlspecialchars($result['name']) ?>: 
                            <span class="<?= $result['success'] ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                <?= $result['success'] ? 'Success' : 'Failed' ?>
                            </span>
                        </h3>
                        <p class="mt-1 <?= $result['success'] ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300' ?>">
                            <?= htmlspecialchars($result['message']) ?>
                        </p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Environment Variables</h2>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Variable</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Value</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        <tr>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">PINTEREST_API_TYPE</td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($apiType) ?></td>
                        </tr>
                        <tr>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">DISABLE_PINTEREST_API</td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($disableApi) ?></td>
                        </tr>
                        <tr>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">PYTHON_PATH</td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($pythonPath) ?></td>
                        </tr>
                        <tr>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">CHROME_PROFILE_PATH</td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($chromeProfile) ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="flex justify-between">
            <a href="/momentum/clone/pinterest" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Dashboard
            </a>
            <a href="/momentum/clone/pinterest/scraper" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-search mr-2"></i> Try Pinterest Scraper
            </a>
        </div>
    </div>

    <script src="/momentum/js/pinterest-consolidated-fix.js"></script>
</body>
</html>
