<?php
/**
 * Productivity Tools Dashboard
 * 
 * This page provides a comprehensive view of all productivity tools
 * with their implementation status and direct links.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple session handling
session_start();

// Check if user is logged in
$isLoggedIn = isset($_SESSION['user']);
$user = $_SESSION['user'] ?? null;

// Define productivity tools with their details
$productivityTools = [
    [
        'id' => 'focus-timer',
        'title' => 'Focus Timer',
        'description' => 'Pomodoro and other focus techniques to maintain concentration and prevent burnout.',
        'icon' => 'stopwatch',
        'color' => 'red',
        'status' => 'implemented',
        'link' => '/momentum/productivity/focus-timer',
        'features' => [
            'Pomodoro technique (25/5 minute intervals)',
            'Customizable work and break durations',
            'Session tracking and statistics',
            'Audio notifications for transitions'
        ]
    ],
    [
        'id' => 'focus-mode',
        'title' => 'Focus Mode',
        'description' => 'Distraction-free work environment to help maintain attention on the current task.',
        'icon' => 'expand',
        'color' => 'blue',
        'status' => 'implemented',
        'link' => '/momentum/productivity/focus-mode',
        'features' => [
            'Minimalist interface with reduced visual noise',
            'Block notifications during focus sessions',
            'Focus on one task at a time',
            'Ambient background options'
        ]
    ],
    [
        'id' => 'time-blocking',
        'title' => 'Time Blocking',
        'description' => 'Visual scheduling system to structure your day and allocate time for specific activities.',
        'icon' => 'calendar-alt',
        'color' => 'green',
        'status' => 'in-progress',
        'link' => '/momentum/productivity/time-blocking',
        'features' => [
            'Visual day planner with drag-and-drop blocks',
            'Color-coded activities by category',
            'Recurring time blocks for routine activities',
            'Integration with tasks and projects'
        ]
    ],
    [
        'id' => 'energy-tracking',
        'title' => 'Energy Level Tracking',
        'description' => 'Monitor energy levels throughout the day for optimal task scheduling based on your natural rhythms.',
        'icon' => 'bolt',
        'color' => 'yellow',
        'status' => 'planned',
        'link' => '/momentum/productivity/energy-tracking',
        'features' => [
            'Track energy levels on a 1-10 scale',
            'Identify patterns in daily energy fluctuations',
            'Match tasks to appropriate energy levels',
            'Recommendations based on current energy'
        ]
    ],
    [
        'id' => 'task-batching',
        'title' => 'Task Batching',
        'description' => 'Group similar tasks together to reduce context switching and improve focus.',
        'icon' => 'layer-group',
        'color' => 'indigo',
        'status' => 'concept',
        'link' => '/momentum/productivity/task-batching',
        'features' => [
            'Group tasks by context or mental mode',
            'Organize batches by energy requirement',
            'Schedule batches during appropriate energy periods',
            'Templates for common task groupings'
        ]
    ],
    [
        'id' => 'distraction-journal',
        'title' => 'Distraction Journal',
        'description' => 'Log and analyze focus breakers to identify patterns and develop strategies to minimize interruptions.',
        'icon' => 'book',
        'color' => 'purple',
        'status' => 'concept',
        'link' => '#',
        'features' => [
            'Quick logging of distractions as they occur',
            'Categorize distractions by type and trigger',
            'Analyze patterns to identify common disruptors',
            'Develop personalized strategies to minimize interruptions'
        ]
    ],
    [
        'id' => 'batch-templates',
        'title' => 'Batch Templates',
        'description' => 'Create reusable templates for common task groupings and set up recurring batches.',
        'icon' => 'copy',
        'color' => 'pink',
        'status' => 'implemented',
        'link' => '/momentum/productivity/batch-templates',
        'features' => [
            'Save common task groupings as templates',
            'Generate new batches from templates with one click',
            'Set up recurring batches on schedules',
            'Organize templates by energy level and context'
        ]
    ]
];

// Status labels and colors
$statusLabels = [
    'implemented' => [
        'label' => 'Implemented',
        'bg' => 'bg-green-100',
        'text' => 'text-green-800',
        'dark_bg' => 'dark:bg-green-900',
        'dark_text' => 'dark:text-green-200'
    ],
    'in-progress' => [
        'label' => 'In Progress',
        'bg' => 'bg-blue-100',
        'text' => 'text-blue-800',
        'dark_bg' => 'dark:bg-blue-900',
        'dark_text' => 'dark:text-blue-200'
    ],
    'planned' => [
        'label' => 'Planned',
        'bg' => 'bg-yellow-100',
        'text' => 'text-yellow-800',
        'dark_bg' => 'dark:bg-yellow-900',
        'dark_text' => 'dark:text-yellow-200'
    ],
    'concept' => [
        'label' => 'Concept',
        'bg' => 'bg-gray-100',
        'text' => 'text-gray-800',
        'dark_bg' => 'dark:bg-gray-700',
        'dark_text' => 'dark:text-gray-300'
    ]
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Productivity Tools - Momentum</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .tool-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .dark .tool-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }
        .feature-list li {
            position: relative;
            padding-left: 1.5rem;
        }
        .feature-list li:before {
            content: "•";
            position: absolute;
            left: 0.5rem;
            color: currentColor;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-7xl mx-auto">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
                <div>
                    <h1 class="text-3xl font-bold">Productivity Tools</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">ADHD-friendly tools to enhance focus and productivity</p>
                </div>
                <div class="mt-4 md:mt-0 flex items-center space-x-4">
                    <a href="/momentum/dashboard" class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-lg text-gray-700 dark:text-gray-300">
                        <i class="fas fa-home mr-2"></i> Dashboard
                    </a>
                    <button id="darkModeToggle" class="p-2 rounded-full bg-gray-200 dark:bg-gray-700">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:block"></i>
                    </button>
                </div>
            </div>
            
            <!-- Status Filter -->
            <div class="mb-6">
                <div class="flex flex-wrap gap-2">
                    <button class="filter-btn all-btn px-3 py-1.5 rounded-full bg-primary-500 text-white" data-filter="all">
                        All Tools
                    </button>
                    <button class="filter-btn px-3 py-1.5 rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" data-filter="implemented">
                        Implemented
                    </button>
                    <button class="filter-btn px-3 py-1.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" data-filter="in-progress">
                        In Progress
                    </button>
                    <button class="filter-btn px-3 py-1.5 rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" data-filter="planned">
                        Planned
                    </button>
                    <button class="filter-btn px-3 py-1.5 rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300" data-filter="concept">
                        Concept
                    </button>
                </div>
            </div>
            
            <!-- Tools Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php foreach ($productivityTools as $tool): ?>
                    <div class="tool-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-status="<?= $tool['status'] ?>">
                        <div class="h-2 bg-<?= $tool['color'] ?>-500"></div>
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="bg-<?= $tool['color'] ?>-100 dark:bg-<?= $tool['color'] ?>-900 p-3 rounded-full mr-4">
                                    <i class="fas fa-<?= $tool['icon'] ?> text-<?= $tool['color'] ?>-500 text-xl"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-semibold"><?= $tool['title'] ?></h2>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusLabels[$tool['status']]['bg'] ?> <?= $statusLabels[$tool['status']]['text'] ?> <?= $statusLabels[$tool['status']]['dark_bg'] ?> <?= $statusLabels[$tool['status']]['dark_text'] ?>">
                                        <?= $statusLabels[$tool['status']]['label'] ?>
                                    </span>
                                </div>
                            </div>
                            
                            <p class="text-gray-600 dark:text-gray-400 mb-4"><?= $tool['description'] ?></p>
                            
                            <div class="mb-4">
                                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Key Features:</h3>
                                <ul class="text-sm text-gray-600 dark:text-gray-400 feature-list">
                                    <?php foreach ($tool['features'] as $feature): ?>
                                        <li class="mb-1"><?= $feature ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <a href="<?= $tool['link'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-<?= $tool['color'] ?>-500 hover:bg-<?= $tool['color'] ?>-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-<?= $tool['color'] ?>-500">
                                <?php if ($tool['status'] === 'implemented' || $tool['status'] === 'in-progress'): ?>
                                    <i class="fas fa-external-link-alt mr-2"></i> Open Tool
                                <?php elseif ($tool['status'] === 'planned'): ?>
                                    <i class="fas fa-info-circle mr-2"></i> Learn More
                                <?php else: ?>
                                    <i class="fas fa-lightbulb mr-2"></i> View Concept
                                <?php endif; ?>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Productivity Tips -->
            <div class="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">ADHD-Friendly Productivity Tips</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                        <h3 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Body Doubling</h3>
                        <p class="text-blue-700 dark:text-blue-300 text-sm">
                            Work alongside someone else (in person or virtually) to increase accountability and focus.
                            The presence of another person can help maintain motivation and reduce procrastination.
                        </p>
                    </div>
                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                        <h3 class="font-medium text-green-800 dark:text-green-200 mb-2">Two-Minute Rule</h3>
                        <p class="text-green-700 dark:text-green-300 text-sm">
                            If a task takes less than two minutes to complete, do it immediately rather than adding it to your to-do list.
                            This prevents small tasks from piling up and becoming overwhelming.
                        </p>
                    </div>
                    <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
                        <h3 class="font-medium text-purple-800 dark:text-purple-200 mb-2">Task Initiation Strategies</h3>
                        <p class="text-purple-700 dark:text-purple-300 text-sm">
                            Break down tasks into tiny steps and commit to just starting the first step.
                            Often, the hardest part is beginning, and momentum will carry you forward once you start.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        
        // Check for dark mode preference
        if (localStorage.getItem('darkMode') === 'enabled' || 
            (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches && 
             localStorage.getItem('darkMode') !== 'disabled')) {
            document.documentElement.classList.add('dark');
        }
        
        // Toggle dark mode
        darkModeToggle.addEventListener('click', function() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                localStorage.setItem('darkMode', 'disabled');
            } else {
                document.documentElement.classList.add('dark');
                localStorage.setItem('darkMode', 'enabled');
            }
        });
        
        // Filter functionality
        const filterButtons = document.querySelectorAll('.filter-btn');
        const toolCards = document.querySelectorAll('.tool-card');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update active button
                filterButtons.forEach(btn => {
                    btn.classList.remove('bg-primary-500', 'text-white');
                    if (btn.getAttribute('data-filter') !== filter) {
                        btn.classList.add('opacity-75');
                    } else {
                        btn.classList.remove('opacity-75');
                        if (filter === 'all') {
                            btn.classList.add('bg-primary-500', 'text-white');
                        }
                    }
                });
                
                // Filter cards
                toolCards.forEach(card => {
                    if (filter === 'all' || card.getAttribute('data-status') === filter) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
