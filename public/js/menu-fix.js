/**
 * Very Simple Menu Fix
 * 
 * This script fixes dropdown menus with a very simple approach.
 */

// Execute immediately when the page loads
window.onload = function() {
    console.log('Menu fix loaded');
    
    // Add click handlers to all dropdown buttons
    setupDropdown('adhd-dropdown-button', 'adhd-dropdown-menu');
    setupDropdown('productivity-dropdown-button', 'productivity-dropdown-menu');
    setupDropdown('tools-dropdown-button', 'tools-dropdown-menu');
    setupDropdown('user-menu-button', 'user-dropdown-menu');
    
    // Function to set up a dropdown
    function setupDropdown(buttonId, menuId) {
        var button = document.getElementById(buttonId);
        var menu = document.getElementById(menuId);
        
        if (!button || !menu) return;
        
        // Remove any existing onclick attribute to avoid conflicts
        button.removeAttribute('onclick');
        
        // Add new click handler
        button.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log(buttonId + ' clicked');
            
            // Toggle menu visibility
            if (menu.style.display === 'block') {
                menu.style.display = 'none';
            } else {
                // Hide all other menus first
                hideAllMenus();
                
                // Show this menu
                menu.style.display = 'block';
                
                // Position the menu
                var buttonRect = button.getBoundingClientRect();
                menu.style.position = 'fixed';
                menu.style.top = (buttonRect.bottom + 5) + 'px';
                
                // Special positioning for user menu (right-aligned)
                if (buttonId === 'user-menu-button') {
                    menu.style.right = (window.innerWidth - buttonRect.right) + 'px';
                    menu.style.left = 'auto';
                } else {
                    menu.style.left = buttonRect.left + 'px';
                    menu.style.right = 'auto';
                }
                
                menu.style.zIndex = '99999';
            }
            
            return false;
        };
    }
    
    // Function to hide all menus
    function hideAllMenus() {
        var menus = [
            document.getElementById('adhd-dropdown-menu'),
            document.getElementById('productivity-dropdown-menu'),
            document.getElementById('tools-dropdown-menu'),
            document.getElementById('user-dropdown-menu')
        ];
        
        menus.forEach(function(menu) {
            if (menu) menu.style.display = 'none';
        });
    }
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        // If click is not on a dropdown button or menu, hide all menus
        if (!e.target.closest('#adhd-dropdown-button') && 
            !e.target.closest('#adhd-dropdown-menu') && 
            !e.target.closest('#productivity-dropdown-button') && 
            !e.target.closest('#productivity-dropdown-menu') && 
            !e.target.closest('#tools-dropdown-button') && 
            !e.target.closest('#tools-dropdown-menu') && 
            !e.target.closest('#user-menu-button') && 
            !e.target.closest('#user-dropdown-menu')) {
            
            hideAllMenus();
        }
    });
};
