/**
 * ADHD Menu Fix
 * 
 * This is an extremely simple script that handles only the ADHD dropdown menu
 * with no dependencies or complex interactions.
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Get the ADHD button and menu
    const button = document.getElementById('adhd-dropdown-button');
    const menu = document.getElementById('adhd-dropdown-menu');
    
    // If either element doesn't exist, exit
    if (!button || !menu) {
        console.error('ADHD dropdown elements not found');
        return;
    }
    
    // Set initial state
    menu.style.display = 'none';
    menu.style.visibility = 'hidden';
    menu.style.opacity = '0';
    button.setAttribute('aria-expanded', 'false');
    
    // Create a simple overlay for capturing clicks outside
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'transparent';
    overlay.style.zIndex = '99998';
    overlay.style.display = 'none';
    document.body.appendChild(overlay);
    
    // Function to show the menu
    function showMenu() {
        // Position the menu
        const rect = button.getBoundingClientRect();
        menu.style.position = 'fixed';
        menu.style.top = (rect.bottom + 5) + 'px';
        menu.style.left = rect.left + 'px';
        menu.style.zIndex = '99999';
        
        // Make the menu visible
        menu.style.display = 'block';
        menu.style.visibility = 'visible';
        menu.style.opacity = '1';
        
        // Show the overlay
        overlay.style.display = 'block';
        
        // Update ARIA state
        button.setAttribute('aria-expanded', 'true');
    }
    
    // Function to hide the menu
    function hideMenu() {
        menu.style.display = 'none';
        menu.style.visibility = 'hidden';
        menu.style.opacity = '0';
        
        // Hide the overlay
        overlay.style.display = 'none';
        
        // Update ARIA state
        button.setAttribute('aria-expanded', 'false');
    }
    
    // Toggle menu when button is clicked
    button.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        if (menu.style.display === 'block') {
            hideMenu();
        } else {
            hideMenu(); // Hide first to ensure clean state
            showMenu();
        }
    });
    
    // Hide menu when clicking outside
    overlay.addEventListener('click', hideMenu);
    
    // Hide menu when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && menu.style.display === 'block') {
            hideMenu();
        }
    });
});
