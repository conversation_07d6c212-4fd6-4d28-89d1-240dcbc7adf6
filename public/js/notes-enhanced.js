/**
 * Enhanced Notes JavaScript with ADHD-friendly features
 * Includes auto-save, quick actions, templates, and accessibility features
 */

class NotesManager {
    constructor() {
        this.autoSaveInterval = null;
        this.autoSaveDelay = 30000; // 30 seconds
        this.lastSaveTime = null;
        this.currentNoteId = null;
        this.isAutoSaving = false;
        this.templates = [];
        
        this.init();
    }

    init() {
        this.setupAutoSave();
        this.setupEventListeners();
        this.loadTemplates();
        this.setupKeyboardShortcuts();
        this.setupNotifications();
    }

    // Auto-save functionality for ADHD users
    setupAutoSave() {
        const titleInput = document.getElementById('title');
        const contentInput = document.getElementById('content');
        
        if (!titleInput && !contentInput) return;

        // Get current note ID from URL or form
        const urlParts = window.location.pathname.split('/');
        if (urlParts.includes('edit')) {
            this.currentNoteId = urlParts[urlParts.length - 1];
        }

        // Setup auto-save on input changes
        [titleInput, contentInput].forEach(input => {
            if (input) {
                input.addEventListener('input', () => {
                    this.scheduleAutoSave();
                });
            }
        });

        // Show auto-save status
        this.createAutoSaveIndicator();
    }

    scheduleAutoSave() {
        if (this.autoSaveInterval) {
            clearTimeout(this.autoSaveInterval);
        }

        this.autoSaveInterval = setTimeout(() => {
            this.performAutoSave();
        }, this.autoSaveDelay);
    }

    async performAutoSave() {
        if (this.isAutoSaving) return;

        const titleInput = document.getElementById('title');
        const contentInput = document.getElementById('content');
        const categoryInput = document.getElementById('category');
        const tagsInput = document.getElementById('tags');

        if (!titleInput || !contentInput) return;

        const title = titleInput.value.trim();
        const content = contentInput.value.trim();

        // Don't auto-save if both title and content are empty
        if (!title && !content) return;

        this.isAutoSaving = true;
        this.updateAutoSaveStatus('Saving...', 'saving');

        try {
            const data = {
                note_id: this.currentNoteId,
                title: title || 'Untitled Note',
                content: content,
                category: categoryInput ? categoryInput.value : null,
                tags: tagsInput ? tagsInput.value : null
            };

            const response = await fetch('/momentum/notes/auto-save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.lastSaveTime = new Date();
                this.currentNoteId = result.note_id;
                this.updateAutoSaveStatus('Auto-saved', 'saved');
                
                // Update URL if this was a new note
                if (window.location.pathname.includes('/create')) {
                    history.replaceState(null, '', `/momentum/notes/edit/${result.note_id}`);
                }
            } else {
                this.updateAutoSaveStatus('Save failed', 'error');
            }
        } catch (error) {
            console.error('Auto-save error:', error);
            this.updateAutoSaveStatus('Save failed', 'error');
        } finally {
            this.isAutoSaving = false;
        }
    }

    createAutoSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'autoSaveIndicator';
        indicator.className = 'fixed top-4 right-4 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 z-50';
        indicator.style.display = 'none';
        document.body.appendChild(indicator);
    }

    updateAutoSaveStatus(message, status) {
        const indicator = document.getElementById('autoSaveIndicator');
        if (!indicator) return;

        indicator.textContent = message;
        indicator.style.display = 'block';

        // Remove existing status classes
        indicator.classList.remove('bg-blue-500', 'bg-green-500', 'bg-red-500', 'text-white');

        switch (status) {
            case 'saving':
                indicator.classList.add('bg-blue-500', 'text-white');
                break;
            case 'saved':
                indicator.classList.add('bg-green-500', 'text-white');
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 3000);
                break;
            case 'error':
                indicator.classList.add('bg-red-500', 'text-white');
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 5000);
                break;
        }
    }

    // Event listeners for enhanced functionality
    setupEventListeners() {
        // Quick capture form
        const quickCaptureForm = document.getElementById('quickCaptureForm');
        if (quickCaptureForm) {
            quickCaptureForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleQuickCapture();
            });
        }

        // Search input enhancements
        const searchInput = document.querySelector('input[name="q"]');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.handleSearchSuggestions(e.target.value);
            }, 300));
        }
    }

    // Keyboard shortcuts for ADHD productivity
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S for manual save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.performAutoSave();
            }

            // Ctrl/Cmd + N for new note
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                window.location.href = '/momentum/notes/create';
            }

            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[name="q"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Escape to close modals
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    // Notification system for ADHD feedback
    setupNotifications() {
        // Create notification container
        if (!document.getElementById('notificationContainer')) {
            const container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50 space-y-2';
            document.body.appendChild(container);
        }
    }

    showNotification(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notificationContainer');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `px-6 py-3 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-y-0 opacity-100`;
        
        switch (type) {
            case 'success':
                notification.classList.add('bg-green-500');
                break;
            case 'error':
                notification.classList.add('bg-red-500');
                break;
            case 'warning':
                notification.classList.add('bg-yellow-500');
                break;
            default:
                notification.classList.add('bg-blue-500');
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                ${message}
            </div>
        `;

        container.appendChild(notification);

        // Auto-remove notification
        setTimeout(() => {
            notification.classList.add('-translate-y-2', 'opacity-0');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    // Template functionality
    async loadTemplates() {
        try {
            const response = await fetch('/momentum/notes/templates');
            const result = await response.json();
            
            if (result.success) {
                this.templates = result.templates;
            }
        } catch (error) {
            console.error('Failed to load templates:', error);
        }
    }

    // Quick capture functionality
    async handleQuickCapture() {
        const title = document.getElementById('quickTitle').value.trim();
        const content = document.getElementById('quickContent').value.trim();

        if (!title && !content) {
            this.showNotification('Please enter a title or content', 'warning');
            return;
        }

        try {
            const response = await fetch('/momentum/quick-capture/note/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: title || 'Quick Note',
                    content: content,
                    tags: 'quick-capture'
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Note created successfully!', 'success');
                this.hideQuickCapture();
                
                // Optionally redirect to the new note
                setTimeout(() => {
                    window.location.href = `/momentum/notes/view/${result.capture_id}`;
                }, 1000);
            } else {
                this.showNotification('Failed to create note', 'error');
            }
        } catch (error) {
            console.error('Quick capture error:', error);
            this.showNotification('Failed to create note', 'error');
        }
    }

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    closeAllModals() {
        document.querySelectorAll('.fixed.inset-0').forEach(modal => {
            if (modal.classList.contains('hidden')) return;
            modal.classList.add('hidden');
        });
    }

    hideQuickCapture() {
        const modal = document.getElementById('quickCaptureModal');
        if (modal) {
            modal.classList.add('hidden');
            // Clear form
            document.getElementById('quickTitle').value = '';
            document.getElementById('quickContent').value = '';
        }
    }
}

// Global functions for template and modal management
function showTemplateModal() {
    const modal = document.getElementById('templateModal');
    const templateList = document.getElementById('templateList');
    
    if (modal && templateList) {
        // Load templates
        fetch('/momentum/notes/templates')
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    templateList.innerHTML = result.templates.map(template => `
                        <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200"
                             onclick="useTemplate(${template.id})">
                            <h4 class="font-semibold text-gray-900 dark:text-white">${template.name}</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">${template.description}</p>
                            <div class="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-folder mr-1"></i>${template.category || 'General'}
                                <span class="ml-3"><i class="fas fa-chart-bar mr-1"></i>Used ${template.usage_count} times</span>
                            </div>
                        </div>
                    `).join('');
                }
            })
            .catch(error => {
                console.error('Failed to load templates:', error);
                templateList.innerHTML = '<p class="text-gray-500 dark:text-gray-400">Failed to load templates</p>';
            });
        
        modal.classList.remove('hidden');
    }
}

function hideTemplateModal() {
    const modal = document.getElementById('templateModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function useTemplate(templateId) {
    // Redirect to create page with template parameter
    window.location.href = `/momentum/notes/create?template=${templateId}`;
}

function showQuickCapture() {
    const modal = document.getElementById('quickCaptureModal');
    if (modal) {
        modal.classList.remove('hidden');
        // Focus on title input
        setTimeout(() => {
            document.getElementById('quickTitle').focus();
        }, 100);
    }
}

function hideQuickCapture() {
    notesManager.hideQuickCapture();
}

// Quick action functions
async function togglePin(noteId) {
    try {
        const response = await fetch(`/momentum/notes/toggle-pin/${noteId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            notesManager.showNotification(
                result.is_pinned ? 'Note pinned' : 'Note unpinned', 
                'success'
            );
            // Reload page to update display
            setTimeout(() => location.reload(), 1000);
        } else {
            notesManager.showNotification('Failed to update pin status', 'error');
        }
    } catch (error) {
        console.error('Toggle pin error:', error);
        notesManager.showNotification('Failed to update pin status', 'error');
    }
}

async function toggleFavorite(noteId) {
    try {
        const response = await fetch(`/momentum/notes/toggle-favorite/${noteId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            notesManager.showNotification(
                result.is_favorite ? 'Added to favorites' : 'Removed from favorites', 
                'success'
            );
            // Reload page to update display
            setTimeout(() => location.reload(), 1000);
        } else {
            notesManager.showNotification('Failed to update favorite status', 'error');
        }
    } catch (error) {
        console.error('Toggle favorite error:', error);
        notesManager.showNotification('Failed to update favorite status', 'error');
    }
}

function deleteNote(noteId) {
    if (confirm('Are you sure you want to delete this note? This action cannot be undone.')) {
        window.location.href = `/momentum/notes/delete/${noteId}`;
    }
}

function duplicateNote(noteId) {
    // Implementation for note duplication
    notesManager.showNotification('Note duplication feature coming soon!', 'info');
}

function addTagToSearch(tag) {
    const searchInput = document.querySelector('input[name="q"]');
    if (searchInput) {
        const currentValue = searchInput.value.trim();
        const tagQuery = `#${tag}`;
        
        if (!currentValue.includes(tagQuery)) {
            searchInput.value = currentValue ? `${currentValue} ${tagQuery}` : tagQuery;
        }
    }
}

function toggleFavoritesOnly() {
    const currentUrl = new URL(window.location);
    const favoritesOnly = currentUrl.searchParams.get('favorites_only');
    
    if (favoritesOnly) {
        currentUrl.searchParams.delete('favorites_only');
    } else {
        currentUrl.searchParams.set('favorites_only', '1');
    }
    
    window.location.href = currentUrl.toString();
}

// Initialize the notes manager when DOM is loaded
let notesManager;
document.addEventListener('DOMContentLoaded', () => {
    notesManager = new NotesManager();
});

// Prevent data loss on page unload
window.addEventListener('beforeunload', (e) => {
    if (notesManager && notesManager.isAutoSaving) {
        e.preventDefault();
        e.returnValue = 'Your note is being saved. Are you sure you want to leave?';
    }
});
