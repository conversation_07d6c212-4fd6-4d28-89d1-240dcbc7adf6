/**
 * Pinterest Optimized JS
 *
 * This is an optimized version of the Pinterest Clone JavaScript functionality.
 * It combines the essential features from multiple files while improving performance.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Optimized JS loaded');

    // Initialize all functionality at once
    initPinterestFunctionality();
});

/**
 * Initialize all Pinterest functionality
 */
function initPinterestFunctionality() {
    console.time('Pinterest Initialization');

    // Use a small delay to ensure the DOM is fully loaded
    setTimeout(() => {
        // Fix navigation and form submission
        fixNavigation();

        // Fix direct access to Pinterest pins
        fixPinterestLinks();

        // Fix image downloads
        fixImageDownloads();

        // Set up mutation observer to handle dynamically added content
        setupMutationObserver();

        console.timeEnd('Pinterest Initialization');
    }, 10);
}

/**
 * Fix navigation and form submission issues
 */
function fixNavigation() {
    // Fix for the New Scrape button on the dashboard
    const newScrapeButtons = document.querySelectorAll('a[href*="/momentum/clone/pinterest/scraper"]');
    newScrapeButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = '/momentum/clone/pinterest/scraper';
        });
    });

    // Fix for the scraper form submission
    const scraperForm = document.getElementById('scraper-form');
    if (scraperForm) {
        scraperForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(scraperForm);

            // Submit the form using fetch API
            fetch('/momentum/clone/pinterest/process-scrape', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '/momentum/clone/pinterest/view-scrape/' + data.scrape_id;
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error submitting form:', error);
                alert('An error occurred while submitting the form. Please try again.');
            });
        });
    }
}

/**
 * Fix all Pinterest pin links for direct access
 */
function fixPinterestLinks() {
    // Find all Pinterest pin links
    const pinterestLinks = document.querySelectorAll('a[href*="pinterest.com/pin/"], a.pinterest-pin-link');

    pinterestLinks.forEach(function(link) {
        if (link.getAttribute('data-fixed') === 'true') {
            return; // Skip already fixed links
        }

        // Get the original URL
        const originalUrl = link.getAttribute('href');

        // Extract the pin ID
        let pinId = null;

        if (originalUrl && originalUrl.includes('/pin/')) {
            // Extract from URL
            const matches = originalUrl.match(/\/pin\/([0-9]+)/);
            if (matches && matches[1]) {
                pinId = matches[1];
            }
        } else if (link.getAttribute('data-pin-id')) {
            // Extract from data attribute
            pinId = link.getAttribute('data-pin-id');
        }

        if (pinId) {
            // Create a direct Pinterest URL
            const directUrl = `https://www.pinterest.com/pin/${pinId}/`;

            // Update the href attribute
            link.setAttribute('href', directUrl);

            // Mark as fixed
            link.setAttribute('data-fixed', 'true');

            // Add target="_blank" to open in a new tab
            link.setAttribute('target', '_blank');

            // Remove any existing event listeners by cloning the node
            const newLink = link.cloneNode(true);
            link.parentNode.replaceChild(newLink, link);
        }
    });
}

/**
 * Fix image download functionality
 */
function fixImageDownloads() {
    // Find all download buttons
    const downloadButtons = document.querySelectorAll('a[download^="pinterest_"], button.download-pin-image');

    downloadButtons.forEach(function(button) {
        if (button.getAttribute('data-fixed') === 'true') {
            return; // Skip already fixed buttons
        }

        // Get the image URL
        let imageUrl = button.getAttribute('href') || button.getAttribute('data-image-url');

        if (!imageUrl && button.closest('.pin-card')) {
            // Try to find the image URL from the parent card
            const pinCard = button.closest('.pin-card');
            const image = pinCard.querySelector('img');
            if (image) {
                imageUrl = image.getAttribute('src');
            }
        }

        if (imageUrl) {
            // Get the filename
            let filename = 'pinterest_image.jpg';
            if (button.getAttribute('download')) {
                filename = button.getAttribute('download');
            } else if (button.getAttribute('data-filename')) {
                filename = button.getAttribute('data-filename');
            }

            // Mark as fixed
            button.setAttribute('data-fixed', 'true');

            // Add click event listener
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Use the enhanced Pinterest API to download the image
                fetch('/momentum/api/download-pinterest-image.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'image_url=' + encodeURIComponent(imageUrl) + '&filename=' + encodeURIComponent(filename)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.download_url) {
                        // Create a temporary link to download the file
                        const tempLink = document.createElement('a');
                        tempLink.href = data.download_url;
                        tempLink.download = filename;
                        document.body.appendChild(tempLink);
                        tempLink.click();
                        document.body.removeChild(tempLink);
                    } else {
                        console.error('Error downloading image:', data.message);
                        alert('Error downloading image: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error downloading image:', error);
                    alert('Error downloading image. Please try again.');
                });
            });
        }
    });
}

/**
 * Set up a mutation observer to handle dynamically added content
 */
function setupMutationObserver() {
    // Debounce function to limit how often the fix functions are called
    let debounceTimer;
    const debounce = (callback, time) => {
        window.clearTimeout(debounceTimer);
        debounceTimer = window.setTimeout(callback, time);
    };

    // Create a mutation observer with debouncing
    const observer = new MutationObserver(function(mutations) {
        // Use debouncing to avoid excessive processing
        debounce(() => {
            let needsFixing = false;

            // Process only the last few mutations to improve performance
            const recentMutations = mutations.slice(-5);

            for (const mutation of recentMutations) {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // Quick check for Pinterest-related content
                    const html = mutation.target.innerHTML || '';
                    if (html.includes('pinterest.com/pin/') ||
                        html.includes('pinterest-pin-link') ||
                        html.includes('download-pin-image')) {
                        needsFixing = true;
                        break;
                    }

                    // Check only a limited number of nodes
                    const nodesToCheck = Array.from(mutation.addedNodes).slice(0, 3);
                    for (const node of nodesToCheck) {
                        if (node.nodeType === 1) { // Element node
                            if ((node.tagName === 'A' &&
                                (node.href?.includes('pinterest.com/pin/') ||
                                node.classList?.contains('pinterest-pin-link'))) ||
                                (node.tagName === 'A' && node.hasAttribute?.('download') &&
                                node.getAttribute?.('download')?.startsWith('pinterest_')) ||
                                (node.tagName === 'BUTTON' && node.classList?.contains('download-pin-image'))) {
                                needsFixing = true;
                                break;
                            }
                        }
                    }

                    if (needsFixing) break;
                }
            }

            if (needsFixing) {
                console.log('Detected new Pinterest content, fixing...');
                // Fix all links and buttons again
                fixPinterestLinks();
                fixImageDownloads();
            }
        }, 250); // Wait 250ms before processing to batch mutations
    });

    // Start observing the document with optimized configuration
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: false,
        attributeFilter: ['href', 'src', 'class'] // Only observe relevant attribute changes
    });
}
