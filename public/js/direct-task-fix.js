/**
 * Direct Task Fix
 * 
 * This script directly fixes task widgets in the dashboard by:
 * 1. Adding expand/collapse functionality
 * 2. Ensuring proper scrolling behavior
 * 3. Applying ADHD-friendly visual cues
 */

// Execute immediately to ensure it runs as soon as possible
(function() {
    console.log('Direct Task Fix executing immediately');
    
    // Function to add expand buttons to task lists
    function addExpandButtons() {
        console.log('Adding expand buttons to task lists');
        
        // Today's Tasks
        const todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
        const todayTasksList = document.getElementById('today-tasks-list');
        
        if (todayTasksWidget && todayTasksList) {
            // Check if list has enough content to need scrolling
            if (todayTasksList.scrollHeight > todayTasksList.clientHeight) {
                // Create expand button if it doesn't exist
                if (!document.querySelector('.task-list-expand-button[data-target="today-tasks"]')) {
                    const expandButton = document.createElement('button');
                    expandButton.type = 'button';
                    expandButton.className = 'task-list-expand-button';
                    expandButton.setAttribute('data-target', 'today-tasks');
                    expandButton.innerHTML = '<i class="fas fa-chevron-down mr-1"></i> Show more';
                    
                    // Add click handler
                    expandButton.onclick = function() {
                        toggleTaskWidgetExpansion('today-tasks');
                    };
                    
                    // Add button to widget
                    const container = todayTasksList.parentNode;
                    container.appendChild(expandButton);
                }
            }
        }
        
        // Overdue Tasks
        const overdueTasksWidget = document.querySelector('[data-widget="overdue-tasks"]');
        const overdueTasksList = document.getElementById('overdue-tasks-list');
        
        if (overdueTasksWidget && overdueTasksList) {
            // Check if list has enough content to need scrolling
            if (overdueTasksList.scrollHeight > overdueTasksList.clientHeight) {
                // Create expand button if it doesn't exist
                if (!document.querySelector('.task-list-expand-button[data-target="overdue-tasks"]')) {
                    const expandButton = document.createElement('button');
                    expandButton.type = 'button';
                    expandButton.className = 'task-list-expand-button';
                    expandButton.setAttribute('data-target', 'overdue-tasks');
                    expandButton.innerHTML = '<i class="fas fa-chevron-down mr-1"></i> Show more';
                    
                    // Add click handler
                    expandButton.onclick = function() {
                        toggleTaskWidgetExpansion('overdue-tasks');
                    };
                    
                    // Add button to widget
                    const container = overdueTasksList.parentNode;
                    container.appendChild(expandButton);
                }
            }
        }
    }
    
    // Function to toggle task widget expansion
    function toggleTaskWidgetExpansion(widgetType) {
        console.log('Toggling expansion for', widgetType);
        
        const widget = document.querySelector(`[data-widget="${widgetType}"]`);
        const expandButton = document.querySelector(`.task-list-expand-button[data-target="${widgetType}"]`);
        
        if (!widget || !expandButton) return;
        
        // Toggle expanded class
        if (widget.classList.contains('expanded')) {
            // Collapse
            widget.classList.remove('expanded');
            expandButton.innerHTML = '<i class="fas fa-chevron-down mr-1"></i> Show more';
            
            // Scroll to top of widget with smooth animation
            widget.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else {
            // Expand
            widget.classList.add('expanded');
            expandButton.innerHTML = '<i class="fas fa-chevron-up mr-1"></i> Show less';
        }
    }
    
    // Function to apply direct styling to task widgets
    function applyDirectTaskStyling() {
        console.log('Applying direct task styling');
        
        // Today's Tasks Widget
        const todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
        const todayTasksList = document.getElementById('today-tasks-list');
        
        if (todayTasksWidget) {
            todayTasksWidget.style.height = '400px';
            todayTasksWidget.style.maxHeight = '400px';
            todayTasksWidget.style.overflow = 'hidden';
            todayTasksWidget.style.display = 'flex';
            todayTasksWidget.style.flexDirection = 'column';
        }
        
        if (todayTasksList) {
            todayTasksList.style.maxHeight = '300px';
            todayTasksList.style.overflowY = 'auto';
            todayTasksList.style.scrollbarWidth = 'thin';
        }
        
        // Overdue Tasks Widget
        const overdueTasksWidget = document.querySelector('[data-widget="overdue-tasks"]');
        const overdueTasksList = document.getElementById('overdue-tasks-list');
        
        if (overdueTasksWidget) {
            overdueTasksWidget.style.height = '400px';
            overdueTasksWidget.style.maxHeight = '400px';
            overdueTasksWidget.style.overflow = 'hidden';
            overdueTasksWidget.style.display = 'flex';
            overdueTasksWidget.style.flexDirection = 'column';
        }
        
        if (overdueTasksList) {
            overdueTasksList.style.maxHeight = '300px';
            overdueTasksList.style.overflowY = 'auto';
            overdueTasksList.style.scrollbarWidth = 'thin';
        }
        
        // Apply styles to task list containers
        document.querySelectorAll('.task-list-container').forEach(container => {
            container.style.flex = '1';
            container.style.display = 'flex';
            container.style.flexDirection = 'column';
            container.style.overflow = 'hidden';
            container.style.position = 'relative';
        });
    }
    
    // Try to run immediately
    applyDirectTaskStyling();
    setTimeout(addExpandButtons, 500);
    
    // Also run when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        applyDirectTaskStyling();
        addExpandButtons();
    });
    
    // Make function available globally
    window.toggleTaskWidgetExpansion = toggleTaskWidgetExpansion;
})();
