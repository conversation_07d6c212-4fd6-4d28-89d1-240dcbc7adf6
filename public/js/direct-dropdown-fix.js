/**
 * Direct Dropdown Fix
 *
 * This is an extremely simple script that directly manipulates the dropdown menus
 * without any dependencies or complex interactions.
 */

// Execute immediately to ensure it runs as soon as possible
(function() {
    // Function to set up dropdowns once the DOM is ready
    function setupDropdowns() {
        console.log('Setting up dropdowns');

        // Get all dropdown buttons
        var dropdownButtons = {
            adhd: document.getElementById('adhd-dropdown-button'),
            productivity: document.getElementById('productivity-dropdown-button'),
            tools: document.getElementById('tools-dropdown-button'),
            user: document.getElementById('user-menu-button')
        };

        // Get all dropdown menus
        var dropdownMenus = {
            adhd: document.getElementById('adhd-dropdown-menu'),
            productivity: document.getElementById('productivity-dropdown-menu'),
            tools: document.getElementById('tools-dropdown-menu'),
            user: document.getElementById('user-dropdown-menu')
        };

        // Function to hide all menus
        function hideAllMenus() {
            for (var key in dropdownMenus) {
                if (dropdownMenus[key]) {
                    dropdownMenus[key].style.display = 'none';
                }
            }
        }

        // Function to show a specific menu
        function showMenu(buttonKey) {
            // Hide all menus first
            hideAllMenus();

            var button = dropdownButtons[buttonKey];
            var menu = dropdownMenus[buttonKey];

            if (!button || !menu) return;

            // Show this menu
            menu.style.display = 'block';

            // Position the menu
            var buttonRect = button.getBoundingClientRect();
            menu.style.position = 'fixed';
            menu.style.top = (buttonRect.bottom + 5) + 'px';

            // Special positioning for user menu (right-aligned)
            if (buttonKey === 'user') {
                menu.style.right = (window.innerWidth - buttonRect.right) + 'px';
                menu.style.left = 'auto';
            } else {
                menu.style.left = buttonRect.left + 'px';
                menu.style.right = 'auto';
            }

            menu.style.zIndex = '99999';
        }

        // Set up click handlers for all dropdown buttons
        for (var key in dropdownButtons) {
            if (dropdownButtons[key] && dropdownMenus[key]) {
                // Use an IIFE to capture the current key
                (function(currentKey) {
                    var button = dropdownButtons[currentKey];
                    var menu = dropdownMenus[currentKey];

                    // Remove any existing onclick attribute
                    button.removeAttribute('onclick');

                    // Add new click handler
                    button.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log(currentKey + ' button clicked');

                        // Toggle menu visibility
                        if (menu.style.display === 'block') {
                            menu.style.display = 'none';
                        } else {
                            showMenu(currentKey);
                        }

                        return false;
                    };
                })(key);
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            var clickedOnDropdown = false;

            // Check if click was on a dropdown button or menu
            for (var key in dropdownButtons) {
                if ((dropdownButtons[key] && dropdownButtons[key].contains(e.target)) ||
                    (dropdownMenus[key] && dropdownMenus[key].contains(e.target))) {
                    clickedOnDropdown = true;
                    break;
                }
            }

            // If click was outside, hide all menus
            if (!clickedOnDropdown) {
                hideAllMenus();
            }
        });
    }

    // Try to set up dropdowns immediately
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setupDropdowns();
    } else {
        // Otherwise wait for the DOM to be ready
        document.addEventListener('DOMContentLoaded', setupDropdowns);
    }

    // Also set up on window load as a fallback
    window.addEventListener('load', setupDropdowns);
})();
