/**
 * Direct Button Fix
 *
 * This script provides a direct fix for buttons that aren't working on the dashboard.
 * It uses a more direct approach to ensure the buttons work regardless of other scripts.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Direct Button Fix loaded');

    // Fix Layout Button
    fixLayoutButton();

    // Fix Clear Focus Button
    fixClearFocusButton();

    // Fix Focus Mode Toggle
    fixFocusModeToggle();

    // Fix Task Checkboxes
    fixTaskCheckboxes();

    // Fix Mark Complete Button
    fixMarkCompleteButton();

    /**
     * Fix Layout Button
     */
    function fixLayoutButton() {
        const layoutButton = document.getElementById('layout-selector-button');
        const layoutDropdown = document.getElementById('layout-dropdown');

        if (!layoutButton || !layoutDropdown) {
            console.log('Layout button or dropdown not found');
            return;
        }

        console.log('Fixing layout button');

        // Remove any existing event listeners
        const newButton = layoutButton.cloneNode(true);
        layoutButton.parentNode.replaceChild(newButton, layoutButton);

        // Add direct click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Layout button clicked (direct fix)');

            // Toggle dropdown visibility
            const isHidden = layoutDropdown.classList.contains('hidden');

            if (isHidden) {
                // Show dropdown
                layoutDropdown.classList.remove('hidden');
                layoutDropdown.style.display = 'block';

                // Position dropdown - improved positioning
                const buttonRect = newButton.getBoundingClientRect();
                layoutDropdown.style.position = 'fixed';
                layoutDropdown.style.top = (buttonRect.bottom + window.scrollY + 5) + 'px';
                layoutDropdown.style.left = (buttonRect.left + window.scrollX) + 'px';
                layoutDropdown.style.zIndex = '9999';

                // Set active state
                newButton.setAttribute('aria-expanded', 'true');
            } else {
                // Hide dropdown
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (layoutDropdown && !layoutDropdown.classList.contains('hidden') &&
                !newButton.contains(e.target) && !layoutDropdown.contains(e.target)) {
                layoutDropdown.classList.add('hidden');
                layoutDropdown.style.display = 'none';
                newButton.setAttribute('aria-expanded', 'false');
            }
        });

        // Fix view options with more robust approach
        setTimeout(function() {
            const viewOptions = layoutDropdown.querySelectorAll('[data-view]');
            console.log('Found view options:', viewOptions.length);

            viewOptions.forEach(option => {
                // Remove any existing event listeners
                const newOption = option.cloneNode(true);
                option.parentNode.replaceChild(newOption, option);

                // Add multiple event handlers to ensure clicks are captured
                ['click', 'mousedown', 'mouseup'].forEach(eventType => {
                    newOption.addEventListener(eventType, function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const viewMode = this.getAttribute('data-view');
                        console.log(`View option ${eventType} (direct fix):`, viewMode);

                        if (eventType === 'click') {
                            // Apply layout
                            applyLayout(viewMode);

                            // Hide dropdown
                            layoutDropdown.classList.add('hidden');
                            layoutDropdown.style.display = 'none';
                            newButton.setAttribute('aria-expanded', 'false');
                        }
                    });
                });

                // Also set onclick property directly
                newOption.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const viewMode = this.getAttribute('data-view');
                    console.log('View option clicked (direct onclick):', viewMode);

                    // Apply layout
                    applyLayout(viewMode);

                    // Hide dropdown
                    layoutDropdown.classList.add('hidden');
                    layoutDropdown.style.display = 'none';
                    newButton.setAttribute('aria-expanded', 'false');

                    return false;
                };
            });
        }, 100);
    }

    /**
     * Apply Layout
     */
    function applyLayout(layoutName) {
        console.log('Applying layout (direct fix):', layoutName);

        // Get dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }

        // Update view mode text
        const viewModeText = document.getElementById('view-mode-text');
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }

        // Update data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);

        // Save preference
        localStorage.setItem('dashboard_view_mode', layoutName);

        // Apply immediate visual feedback
        const allOptions = document.querySelectorAll('[data-view]');
        allOptions.forEach(option => {
            const optionLayout = option.getAttribute('data-view');
            if (optionLayout === layoutName) {
                option.classList.add('bg-primary-50', 'dark:bg-primary-900', 'border-l-4', 'border-primary-500');
            } else {
                option.classList.remove('bg-primary-50', 'dark:bg-primary-900', 'border-l-4', 'border-primary-500');
            }
        });

        // Apply layout-specific class to body
        document.body.classList.remove('layout-adhd-optimized', 'layout-focus', 'layout-standard', 'layout-custom');
        document.body.classList.add('layout-' + layoutName);

        // Show/hide widget controls
        const widgetControls = document.getElementById('widget-controls');
        if (widgetControls) {
            widgetControls.style.display = layoutName === 'custom' ? 'block' : 'none';
        }

        // Apply immediate visual changes based on layout
        switch(layoutName) {
            case 'adhd-optimized':
                applyADHDOptimizedLayout();
                break;
            case 'focus':
                applyFocusLayout();
                break;
            case 'standard':
                applyStandardLayout();
                break;
            case 'custom':
                applyCustomLayout();
                break;
        }

        // Show success message
        showSuccessMessage(`${layoutName.charAt(0).toUpperCase() + layoutName.slice(1)} layout applied!`);

        // Reload the page after a short delay to apply the layout fully
        setTimeout(() => {
            window.location.reload();
        }, 500);
    }

    /**
     * Apply ADHD Optimized Layout
     */
    function applyADHDOptimizedLayout() {
        console.log('Applying ADHD Optimized Layout');

        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;

        // Show all widgets
        document.querySelectorAll('[data-widget]').forEach(widget => {
            widget.style.display = 'flex';
            widget.style.flexDirection = 'column';
        });

        // Make current focus widget stand out
        const currentFocusWidget = document.getElementById('current-focus-widget');
        if (currentFocusWidget) {
            currentFocusWidget.style.gridColumn = '1 / -1';
            currentFocusWidget.style.borderLeft = '6px solid #0ea5e9';
            currentFocusWidget.style.transform = 'scale(1.02)';
            currentFocusWidget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
        }

        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(14, 165, 233, 0.08)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
    }

    /**
     * Apply Focus Layout
     */
    function applyFocusLayout() {
        console.log('Applying Focus Layout');

        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;

        // Hide all widgets except essential ones
        document.querySelectorAll('[data-widget]').forEach(widget => {
            const widgetType = widget.getAttribute('data-widget');
            if (widgetType === 'current-focus-widget' || widgetType === 'today-tasks' || widgetType === 'keyboard-shortcuts') {
                widget.style.display = 'flex';
                widget.style.flexDirection = 'column';
            } else {
                widget.style.display = 'none';
            }
        });

        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
    }

    /**
     * Apply Standard Layout
     */
    function applyStandardLayout() {
        console.log('Applying Standard Layout');

        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;

        // Show all widgets
        document.querySelectorAll('[data-widget]').forEach(widget => {
            widget.style.display = 'flex';
            widget.style.flexDirection = 'column';
            widget.style.border = '1px solid rgba(0, 0, 0, 0.05)';
            widget.style.borderRadius = '0.5rem';
        });

        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
        dashboardWidgets.style.padding = '1.25rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
    }

    /**
     * Apply Custom Layout
     */
    function applyCustomLayout() {
        console.log('Applying Custom Layout');

        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) return;

        // Show all widgets with custom styling
        document.querySelectorAll('[data-widget]').forEach(widget => {
            widget.style.display = 'flex';
            widget.style.flexDirection = 'column';
            widget.style.border = '2px dotted rgba(139, 92, 246, 0.3)';
            widget.style.position = 'relative';
            widget.style.cursor = 'move';
        });

        // Show widget controls
        const widgetControls = document.getElementById('widget-controls');
        if (widgetControls) {
            widgetControls.style.display = 'block';
        }

        // Apply container styles
        dashboardWidgets.style.backgroundColor = 'rgba(139, 92, 246, 0.05)';
        dashboardWidgets.style.padding = '1.5rem';
        dashboardWidgets.style.borderRadius = '0.75rem';
        dashboardWidgets.style.border = '2px dashed rgba(139, 92, 246, 0.3)';
    }

    /**
     * Fix Clear Focus Button
     */
    function fixClearFocusButton() {
        const clearFocusBtn = document.getElementById('clear-focus-task');

        if (!clearFocusBtn) {
            console.log('Clear focus button not found');
            return;
        }

        console.log('Fixing clear focus button');

        // Remove any existing event listeners
        const newButton = clearFocusBtn.cloneNode(true);
        clearFocusBtn.parentNode.replaceChild(newButton, clearFocusBtn);

        // Add direct click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Clear focus button clicked (direct fix)');

            // Send AJAX request to clear current focus
            fetch('/momentum/dashboard/clear-focus-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Clear focus response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Focus cleared:', data);

                if (data.success) {
                    showSuccessMessage('Focus cleared successfully!');

                    // Reload the page to refresh the widgets
                    window.location.reload();
                } else {
                    console.error('Failed to clear focus:', data.message);
                    alert('Failed to clear focus. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error clearing focus:', error);
                alert('An error occurred. Please try again.');
            });
        });
    }

    /**
     * Fix Focus Mode Toggle
     */
    function fixFocusModeToggle() {
        const focusModeToggle = document.getElementById('focus-mode-toggle');

        if (!focusModeToggle) {
            console.log('Focus mode toggle not found');
            return;
        }

        console.log('Fixing focus mode toggle');

        // Remove any existing event listeners
        const newToggle = focusModeToggle.cloneNode(true);
        focusModeToggle.parentNode.replaceChild(newToggle, focusModeToggle);

        // Add direct click handler
        newToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Focus mode toggle clicked (direct fix)');

            const markCompleteBtn = document.getElementById('mark-complete-btn');
            const taskId = markCompleteBtn ? markCompleteBtn.getAttribute('data-task-id') : null;

            if (taskId) {
                window.location.href = `/momentum/productivity/focus-mode?task_id=${taskId}`;
            } else {
                alert('Please set a focus task first.');
            }
        });
    }

    /**
     * Fix Mark Complete Button
     */
    function fixMarkCompleteButton() {
        const markCompleteBtn = document.getElementById('mark-complete-btn');

        if (!markCompleteBtn) {
            console.log('Mark complete button not found');
            return;
        }

        console.log('Fixing mark complete button');

        // Remove any existing event listeners
        const newButton = markCompleteBtn.cloneNode(true);
        markCompleteBtn.parentNode.replaceChild(newButton, markCompleteBtn);

        // Add direct click handler
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const taskId = this.getAttribute('data-task-id');
            console.log('Mark complete button clicked (direct fix):', taskId);

            // Send AJAX request to mark task as complete
            fetch(`/momentum/tasks/complete/${taskId}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('Mark complete response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Task marked as complete:', data);

                if (data.success) {
                    showSuccessMessage('Task completed successfully!');

                    // Reload the page after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    console.error('Failed to complete task:', data.message);
                    alert('Failed to complete task. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error completing task:', error);
                alert('An error occurred. Please try again.');
            });
        });
    }

    /**
     * Fix Task Checkboxes
     */
    function fixTaskCheckboxes() {
        const taskCheckboxes = document.querySelectorAll('.task-checkbox');

        if (taskCheckboxes.length === 0) {
            console.log('No task checkboxes found');
            return;
        }

        console.log('Fixing task checkboxes:', taskCheckboxes.length);

        taskCheckboxes.forEach((checkbox, index) => {
            // Remove any existing event listeners
            const newCheckbox = checkbox.cloneNode(true);
            checkbox.parentNode.replaceChild(newCheckbox, checkbox);

            // Add direct change handler
            newCheckbox.addEventListener('change', function() {
                const taskId = this.getAttribute('data-task-id');
                const isChecked = this.checked;

                console.log(`Task checkbox ${index + 1} changed (direct fix):`, taskId, isChecked);

                // Send AJAX request to update task status
                fetch(`/momentum/tasks/update-status/${taskId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `status=${isChecked ? 'done' : 'todo'}`
                })
                .then(response => {
                    console.log('Update task status response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Task status updated:', data);

                    if (data.success) {
                        // Update UI
                        const taskItem = document.querySelector(`.task-item[data-task-id="${taskId}"]`);
                        if (taskItem) {
                            const taskTitle = taskItem.querySelector('a');

                            if (isChecked) {
                                taskTitle.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');
                                showSuccessMessage('Task marked as complete!');
                            } else {
                                taskTitle.classList.remove('line-through', 'text-gray-500', 'dark:text-gray-400');
                                showSuccessMessage('Task marked as incomplete');
                            }
                        }
                    } else {
                        console.error('Failed to update task status:', data.message);
                        this.checked = !isChecked; // Revert checkbox state
                        alert('Failed to update task status. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error updating task status:', error);
                    this.checked = !isChecked; // Revert checkbox state
                    alert('An error occurred. Please try again.');
                });
            });
        });
    }

    /**
     * Show Success Message
     */
    function showSuccessMessage(message) {
        console.log('Showing success message:', message);

        // Create message element if it doesn't exist
        let messageElement = document.getElementById('success-message');

        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'success-message';
            messageElement.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 transform transition-all duration-300 opacity-0 translate-y-[-20px]';
            document.body.appendChild(messageElement);
        }

        // Set message text
        messageElement.textContent = message;

        // Show message with animation
        setTimeout(() => {
            messageElement.classList.remove('opacity-0', 'translate-y-[-20px]');
            messageElement.classList.add('opacity-100', 'translate-y-0');
        }, 10);

        // Hide message after delay
        setTimeout(() => {
            messageElement.classList.remove('opacity-100', 'translate-y-0');
            messageElement.classList.add('opacity-0', 'translate-y-[-20px]');
        }, 3000);
    }
});
