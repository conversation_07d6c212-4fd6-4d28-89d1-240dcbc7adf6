/**
 * Layout Button Complete Fix
 * 
 * This script provides a comprehensive fix for the layout button and dropdown functionality.
 * It ensures that the layout button works correctly and that all layout options are clickable.
 * This script should be loaded after all other layout-related scripts to ensure it takes precedence.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Layout Button Complete Fix loaded');
    
    // Wait a short moment to ensure all DOM elements are fully loaded
    setTimeout(function() {
        initializeLayoutButton();
    }, 300);
    
    /**
     * Initialize the layout button and dropdown
     */
    function initializeLayoutButton() {
        // Find the layout button and dropdown
        const layoutButton = document.getElementById('layout-selector-button');
        const layoutDropdown = document.getElementById('layout-dropdown');
        
        if (!layoutButton || !layoutDropdown) {
            console.error('Layout button or dropdown not found');
            console.error('Button found:', !!layoutButton);
            console.error('Dropdown found:', !!layoutDropdown);
            return;
        }
        
        console.log('Found layout button and dropdown');
        
        // Remove any existing event listeners by cloning and replacing the button
        const newButton = layoutButton.cloneNode(true);
        layoutButton.parentNode.replaceChild(newButton, layoutButton);
        
        // Add click event to the new button
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Layout button clicked');
            
            // Toggle dropdown visibility
            toggleDropdown(layoutDropdown);
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!layoutDropdown.classList.contains('hidden') && 
                !newButton.contains(e.target) && 
                !layoutDropdown.contains(e.target)) {
                
                console.log('Clicking outside, hiding dropdown');
                hideDropdown(layoutDropdown, newButton);
            }
        });
        
        // Attach click handlers to all layout options
        const layoutOptions = document.querySelectorAll('[data-view]');
        console.log(`Found ${layoutOptions.length} layout options`);
        
        layoutOptions.forEach(option => {
            // Remove existing click handlers
            const newOption = option.cloneNode(true);
            option.parentNode.replaceChild(newOption, option);
            
            // Add new click handler
            newOption.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const layoutName = this.getAttribute('data-view');
                console.log('Layout option clicked:', layoutName);
                
                // Apply the layout
                applyLayout(layoutName);
                
                // Hide the dropdown
                hideDropdown(layoutDropdown, newButton);
            });
        });
        
        // Apply saved layout on page load
        const savedViewMode = localStorage.getItem('dashboard_view_mode') || 'adhd-optimized';
        console.log('Applying saved layout on page load:', savedViewMode);
        
        // Apply layout with a slight delay to ensure DOM is ready
        setTimeout(() => {
            applyLayout(savedViewMode);
        }, 200);
    }
    
    /**
     * Toggle dropdown visibility
     */
    function toggleDropdown(dropdown) {
        if (dropdown.classList.contains('hidden')) {
            showDropdown(dropdown);
        } else {
            hideDropdown(dropdown);
        }
    }
    
    /**
     * Show dropdown
     */
    function showDropdown(dropdown, button) {
        console.log('Showing dropdown');
        
        // Show dropdown
        dropdown.classList.remove('hidden');
        dropdown.style.display = 'block';
        
        // Position dropdown
        const buttonRect = document.getElementById('layout-selector-button').getBoundingClientRect();
        dropdown.style.position = 'fixed';
        dropdown.style.top = (buttonRect.bottom + window.scrollY) + 'px';
        dropdown.style.left = (buttonRect.left + window.scrollX) + 'px';
        dropdown.style.zIndex = '9999';
        
        // Update ARIA attributes
        if (button) {
            button.setAttribute('aria-expanded', 'true');
        }
    }
    
    /**
     * Hide dropdown
     */
    function hideDropdown(dropdown, button) {
        console.log('Hiding dropdown');
        
        // Hide dropdown
        dropdown.classList.add('hidden');
        dropdown.style.display = 'none';
        
        // Update ARIA attributes
        if (button) {
            button.setAttribute('aria-expanded', 'false');
        }
    }
    
    /**
     * Apply layout
     */
    function applyLayout(layoutName) {
        console.log('Applying layout:', layoutName);
        
        // Get dashboard widgets container
        const dashboardWidgets = document.getElementById('dashboard-widgets');
        if (!dashboardWidgets) {
            console.error('Dashboard widgets container not found');
            return;
        }
        
        // Update view mode text
        const viewModeText = document.getElementById('view-mode-text');
        if (viewModeText) {
            viewModeText.textContent = layoutName.charAt(0).toUpperCase() + layoutName.slice(1) + ' View';
        }
        
        // Save preference
        localStorage.setItem('dashboard_view_mode', layoutName);
        
        // Reset all layout-specific classes
        document.body.classList.remove('layout-adhd-optimized', 'layout-focus', 'layout-standard', 'layout-custom');
        document.body.classList.remove('focus-mode');
        
        // Set data attributes
        dashboardWidgets.setAttribute('data-view-mode', layoutName);
        dashboardWidgets.setAttribute('data-arrangement', layoutName);
        
        // Apply layout-specific class to body
        document.body.classList.add('layout-' + layoutName);
        
        // Apply the appropriate layout
        switch (layoutName) {
            case 'adhd-optimized':
                applyADHDOptimizedLayout();
                break;
            case 'focus':
                applyFocusLayout();
                break;
            case 'standard':
                applyStandardLayout();
                break;
            case 'custom':
                applyCustomLayout();
                break;
        }
        
        // Dispatch a custom event to notify other scripts
        const event = new CustomEvent('layout-changed', {
            detail: {
                layout: layoutName
            }
        });
        document.dispatchEvent(event);
    }
    
    // Layout implementation functions
    function applyADHDOptimizedLayout() {
        console.log('Applying ADHD Optimized Layout');
        // The actual styling is handled by the CSS file
    }
    
    function applyFocusLayout() {
        console.log('Applying Focus Layout');
        
        // Add focus mode class to body
        document.body.classList.add('focus-mode');
        
        // The actual styling is handled by the CSS file
    }
    
    function applyStandardLayout() {
        console.log('Applying Standard Layout');
        // The actual styling is handled by the CSS file
    }
    
    function applyCustomLayout() {
        console.log('Applying Custom Layout');
        
        // Show widget controls if they exist
        const widgetControls = document.getElementById('widget-controls');
        if (widgetControls) {
            widgetControls.classList.remove('hidden');
        }
        
        // The actual styling is handled by the CSS file
    }
});
