/**
 * AI Assistant Widget JavaScript
 * 
 * Handles interactions for the AI Assistant dashboard widget
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeAIAssistantWidget();
});

function initializeAIAssistantWidget() {
    console.log('Initializing AI Assistant Widget');
    
    // Initialize quick capture modal
    initializeQuickCaptureModal();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    // Initialize tooltips
    initializeTooltips();
}

function initializeQuickCaptureModal() {
    const modal = document.getElementById('quickCaptureModal');
    if (!modal) return;
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeQuickCapture();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
            closeQuickCapture();
        }
    });
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Alt + key shortcuts for AI Assistant features
        if (e.altKey && !e.ctrlKey && !e.shiftKey) {
            switch(e.key.toLowerCase()) {
                case 'q':
                    e.preventDefault();
                    openQuickCapture();
                    break;
                case 'p':
                    e.preventDefault();
                    openPromptBuilder();
                    break;
                case 's':
                    e.preventDefault();
                    openScreenshotTool();
                    break;
                case 'n':
                    e.preventDefault();
                    openQuickNote();
                    break;
            }
        }
    });
}

function initializeTooltips() {
    // Add tooltips to action buttons
    const tooltips = [
        { selector: '[onclick="openPromptBuilder()"]', text: 'Create a new AI prompt (Alt+P)' },
        { selector: '[onclick="openScreenshotTool()"]', text: 'Take a screenshot (Alt+S)' },
        { selector: '[onclick="openQuickNote()"]', text: 'Create a quick note (Alt+N)' },
        { selector: '[onclick="openPromptLibrary()"]', text: 'Browse prompt library' }
    ];
    
    tooltips.forEach(tooltip => {
        const elements = document.querySelectorAll(tooltip.selector);
        elements.forEach(element => {
            element.setAttribute('title', tooltip.text);
        });
    });
}

// Quick Capture Modal Functions
function openQuickCapture() {
    const modal = document.getElementById('quickCaptureModal');
    if (modal) {
        modal.classList.remove('hidden');
        // Focus first button for accessibility
        const firstButton = modal.querySelector('button[onclick="startScreenshot()"]');
        if (firstButton) {
            setTimeout(() => firstButton.focus(), 100);
        }
    }
}

function closeQuickCapture() {
    const modal = document.getElementById('quickCaptureModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function startScreenshot() {
    closeQuickCapture();
    
    // Check if we can use the Screen Capture API
    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
        captureScreenWithAPI();
    } else {
        // Fallback to opening screenshot tool in new window
        window.open('/momentum/quick-capture/screenshot', '_blank', 'width=1200,height=800');
    }
}

async function captureScreenWithAPI() {
    try {
        const stream = await navigator.mediaDevices.getDisplayMedia({
            video: { mediaSource: 'screen' }
        });
        
        // Create video element to capture frame
        const video = document.createElement('video');
        video.srcObject = stream;
        video.play();
        
        video.addEventListener('loadedmetadata', () => {
            // Create canvas to capture frame
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            // Convert to blob and upload
            canvas.toBlob(async (blob) => {
                await uploadScreenshot(blob);
                
                // Stop the stream
                stream.getTracks().forEach(track => track.stop());
            }, 'image/png');
        });
        
    } catch (err) {
        console.error('Error capturing screen:', err);
        // Fallback to screenshot tool
        window.open('/momentum/quick-capture/screenshot', '_blank', 'width=1200,height=800');
    }
}

async function uploadScreenshot(blob) {
    try {
        const formData = new FormData();
        formData.append('screenshot', blob, 'screenshot.png');
        formData.append('title', 'Screenshot ' + new Date().toLocaleString());
        formData.append('extract_text', '1'); // Enable OCR
        
        const response = await fetch('/momentum/quick-capture/screenshot/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Screenshot captured successfully!', 'success');
            // Optionally redirect to view the capture
            setTimeout(() => {
                window.location.href = '/momentum/quick-capture/view/' + result.capture_id;
            }, 1000);
        } else {
            showNotification('Failed to save screenshot: ' + (result.error || 'Unknown error'), 'error');
        }
        
    } catch (error) {
        console.error('Error uploading screenshot:', error);
        showNotification('Failed to upload screenshot', 'error');
    }
}

function createQuickNote() {
    closeQuickCapture();
    
    // Create inline note form
    const noteForm = createQuickNoteForm();
    document.body.appendChild(noteForm);
}

function createQuickNoteForm() {
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    
    overlay.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Note</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="quickNoteForm">
                    <div class="mb-4">
                        <input type="text" id="noteTitle" placeholder="Note title (optional)" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div class="mb-4">
                        <textarea id="noteContent" placeholder="Write your note here..." rows="6"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white resize-none"></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <input type="text" id="noteTags" placeholder="Tags (comma-separated)" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="this.closest('.fixed').remove()" 
                                class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                            Save Note
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    // Handle form submission
    const form = overlay.querySelector('#quickNoteForm');
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const title = overlay.querySelector('#noteTitle').value;
        const content = overlay.querySelector('#noteContent').value;
        const tags = overlay.querySelector('#noteTags').value;
        
        if (!content.trim()) {
            showNotification('Note content is required', 'error');
            return;
        }
        
        try {
            const response = await fetch('/momentum/quick-capture/note/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: title || null,
                    content: content,
                    tags: tags || null
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showNotification('Note created successfully!', 'success');
                overlay.remove();
                // Optionally refresh widget data
                refreshAIAssistantWidget();
            } else {
                showNotification('Failed to create note: ' + (result.error || 'Unknown error'), 'error');
            }
            
        } catch (error) {
            console.error('Error creating note:', error);
            showNotification('Failed to create note', 'error');
        }
    });
    
    // Focus the content textarea
    setTimeout(() => {
        const textarea = overlay.querySelector('#noteContent');
        if (textarea) textarea.focus();
    }, 100);
    
    return overlay;
}

function startVoiceNote() {
    closeQuickCapture();
    // For now, redirect to voice capture page
    window.location.href = '/momentum/quick-capture/voice';
}

// Navigation functions
function openPromptBuilder() {
    window.location.href = '/momentum/ai-prompts/create';
}

function openScreenshotTool() {
    window.open('/momentum/quick-capture/screenshot', '_blank', 'width=1200,height=800');
}

function openQuickNote() {
    createQuickNote();
}

function openPromptLibrary() {
    window.location.href = '/momentum/ai-prompts/library';
}

function executePrompt(promptId) {
    window.location.href = '/momentum/ai-prompts/execute/' + promptId;
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                'fa-info-circle'
            } mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function refreshAIAssistantWidget() {
    // Refresh widget data (could be implemented to update stats without page reload)
    console.log('Refreshing AI Assistant widget data...');
    // For now, we'll just reload the page section
    // In a more advanced implementation, this would make an AJAX call to get updated data
}
