/**
 * Hover Fix for Dropdown Menus
 * 
 * This script fixes the flickering issue with dropdown menus
 * by adding a delay before hiding the menu when the mouse leaves.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Hover fix loaded');
    
    // Get the ADHD dropdown elements
    const adhdButton = document.getElementById('adhd-dropdown-button');
    const adhdMenu = document.getElementById('adhd-dropdown-menu');
    const adhdContainer = document.getElementById('adhd-dropdown');
    
    if (!adhdButton || !adhdMenu || !adhdContainer) {
        console.error('ADHD dropdown elements not found');
        return;
    }
    
    // Track hover state
    let isOverButton = false;
    let isOverMenu = false;
    let hideTimeout = null;
    
    // Function to show the menu
    function showMenu() {
        // Clear any pending hide timeout
        if (hideTimeout) {
            clearTimeout(hideTimeout);
            hideTimeout = null;
        }
        
        // Show the menu
        adhdMenu.style.display = 'block';
        adhdMenu.style.opacity = '1';
        adhdMenu.style.visibility = 'visible';
        
        // Position the menu
        const buttonRect = adhdButton.getBoundingClientRect();
        adhdMenu.style.position = 'fixed';
        adhdMenu.style.top = (buttonRect.bottom + window.scrollY) + 'px';
        adhdMenu.style.left = (buttonRect.left + window.scrollX) + 'px';
        adhdMenu.style.zIndex = '9999';
    }
    
    // Function to hide the menu with a delay
    function hideMenuWithDelay() {
        // Only hide if not hovering over button or menu
        if (!isOverButton && !isOverMenu) {
            hideTimeout = setTimeout(function() {
                adhdMenu.style.opacity = '0';
                
                // Hide after transition completes
                setTimeout(function() {
                    if (!isOverButton && !isOverMenu) {
                        adhdMenu.style.display = 'none';
                        adhdMenu.style.visibility = 'hidden';
                    }
                }, 150); // Match this with CSS transition duration
            }, 200); // Delay before starting to hide
        }
    }
    
    // Handle mouse enter/leave for the button
    adhdButton.addEventListener('mouseenter', function() {
        isOverButton = true;
        showMenu();
    });
    
    adhdButton.addEventListener('mouseleave', function() {
        isOverButton = false;
        hideMenuWithDelay();
    });
    
    // Handle mouse enter/leave for the menu
    adhdMenu.addEventListener('mouseenter', function() {
        isOverMenu = true;
        showMenu();
    });
    
    adhdMenu.addEventListener('mouseleave', function() {
        isOverMenu = false;
        hideMenuWithDelay();
    });
    
    // Handle mouse enter/leave for the container
    adhdContainer.addEventListener('mouseenter', function() {
        isOverButton = true;
        showMenu();
    });
    
    adhdContainer.addEventListener('mouseleave', function() {
        isOverButton = false;
        hideMenuWithDelay();
    });
    
    // Handle click on the button
    adhdButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        if (adhdMenu.style.display === 'block') {
            adhdMenu.style.opacity = '0';
            
            setTimeout(function() {
                adhdMenu.style.display = 'none';
                adhdMenu.style.visibility = 'hidden';
            }, 150);
        } else {
            showMenu();
        }
    });
    
    // Handle click outside
    document.addEventListener('click', function(e) {
        if (!adhdButton.contains(e.target) && !adhdMenu.contains(e.target)) {
            isOverButton = false;
            isOverMenu = false;
            
            adhdMenu.style.opacity = '0';
            
            setTimeout(function() {
                adhdMenu.style.display = 'none';
                adhdMenu.style.visibility = 'hidden';
            }, 150);
        }
    });
    
    // Handle window resize and scroll
    window.addEventListener('resize', function() {
        if (adhdMenu.style.display === 'block') {
            const buttonRect = adhdButton.getBoundingClientRect();
            adhdMenu.style.top = (buttonRect.bottom + window.scrollY) + 'px';
            adhdMenu.style.left = (buttonRect.left + window.scrollX) + 'px';
        }
    });
    
    window.addEventListener('scroll', function() {
        if (adhdMenu.style.display === 'block') {
            const buttonRect = adhdButton.getBoundingClientRect();
            adhdMenu.style.top = (buttonRect.bottom + window.scrollY) + 'px';
            adhdMenu.style.left = (buttonRect.left + window.scrollX) + 'px';
        }
    });
});
