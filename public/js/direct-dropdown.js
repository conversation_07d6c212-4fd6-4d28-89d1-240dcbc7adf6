/**
 * Direct Dropdown Menu Fix
 *
 * This is an extremely simple script that directly manipulates the dropdown menus
 * without any dependencies or complex interactions.
 */

// Execute immediately and also when the window loads
(function() {
    function initDropdowns() {
    console.log('Direct dropdown fix loaded');

    // Get all dropdown buttons
    const adhdButton = document.getElementById('adhd-dropdown-button');
    const productivityButton = document.getElementById('productivity-dropdown-button');
    const toolsButton = document.getElementById('tools-dropdown-button');
    const userButton = document.getElementById('user-menu-button');

    // Get all dropdown menus
    const adhdMenu = document.getElementById('adhd-dropdown-menu');
    const productivityMenu = document.getElementById('productivity-dropdown-menu');
    const toolsMenu = document.getElementById('tools-dropdown-menu');
    const userMenu = document.getElementById('user-dropdown-menu');

    // Function to hide all menus
    function hideAllMenus() {
        if (adhdMenu) {
            adhdMenu.style.display = 'none';
            adhdMenu.style.visibility = 'hidden';
        }
        if (productivityMenu) {
            productivityMenu.style.display = 'none';
            productivityMenu.style.visibility = 'hidden';
        }
        if (toolsMenu) {
            toolsMenu.style.display = 'none';
            toolsMenu.style.visibility = 'hidden';
        }
        if (userMenu) {
            userMenu.style.display = 'none';
            userMenu.style.visibility = 'hidden';
        }
    }

    // Function to check if dark mode is active
    function isDarkMode() {
        return document.documentElement.classList.contains('dark') ||
               document.body.classList.contains('dark');
    }

    // Initialize - hide all menus
    hideAllMenus();

    // Add document click handler to close menus when clicking outside
    document.addEventListener('click', function(event) {
        // Check if the click was outside all dropdown buttons and menus
        const isOutsideAdhd = adhdButton && adhdMenu && !adhdButton.contains(event.target) && !adhdMenu.contains(event.target);
        const isOutsideProductivity = productivityButton && productivityMenu && !productivityButton.contains(event.target) && !productivityMenu.contains(event.target);
        const isOutsideTools = toolsButton && toolsMenu && !toolsButton.contains(event.target) && !toolsMenu.contains(event.target);
        const isOutsideUser = userButton && userMenu && !userButton.contains(event.target) && !userMenu.contains(event.target);

        // If click was outside all dropdowns, hide all menus
        if (isOutsideAdhd && isOutsideProductivity && isOutsideTools && isOutsideUser) {
            hideAllMenus();
        }
    });

    // Style dropdown items
    function styleDropdownItems() {
        // Get all dropdown items
        const dropdownItems = document.querySelectorAll('.dropdown-item');

        // Apply styles to each item
        dropdownItems.forEach(item => {
            item.style.display = 'block';
            item.style.padding = '0.75rem 1rem';
            item.style.fontSize = '0.875rem';
            item.style.textDecoration = 'none';
            item.style.whiteSpace = 'nowrap';
            item.style.width = '100%';
            item.style.textOverflow = 'ellipsis';
            item.style.overflow = 'hidden';
            item.style.position = 'relative';
            item.style.cursor = 'pointer';
            item.style.borderBottom = '1px solid rgba(0, 0, 0, 0.05)';

            // Set color based on dark mode
            if (isDarkMode()) {
                item.style.color = '#E5E7EB';
                item.style.borderBottom = '1px solid rgba(255, 255, 255, 0.05)';
            } else {
                item.style.color = '#4B5563';
            }

            // Add hover effect
            item.addEventListener('mouseenter', function() {
                if (isDarkMode()) {
                    this.style.backgroundColor = '#4B5563';
                    this.style.color = '#F9FAFB';
                } else {
                    this.style.backgroundColor = '#F3F4F6';
                    this.style.color = '#111827';
                }
            });

            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
                if (isDarkMode()) {
                    this.style.color = '#E5E7EB';
                } else {
                    this.style.color = '#4B5563';
                }
            });
        });
    }

    // Call the function to style dropdown items
    styleDropdownItems();

    // ADHD dropdown
    if (adhdButton && adhdMenu) {
        // Add click handler
        adhdButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('ADHD button clicked');

            // Toggle menu visibility
            if (adhdMenu.style.display === 'block') {
                adhdMenu.style.display = 'none';
            } else {
                hideAllMenus();

                // Position the menu properly
                const buttonRect = adhdButton.getBoundingClientRect();

                // Get the parent container
                const adhdContainer = document.getElementById('adhd-dropdown');

                // Force the dropdown to appear directly below the button
                adhdMenu.style.position = 'absolute';
                adhdMenu.style.top = '100%'; // Position right below the button
                adhdMenu.style.left = '0';
                adhdMenu.style.right = 'auto';

                // Ensure the parent container has position relative
                if (adhdContainer) {
                    adhdContainer.style.position = 'relative';
                }

                // Log positioning for debugging
                console.log('ADHD button clicked');
                adhdMenu.style.zIndex = '9999';

                // Apply appropriate styles based on dark/light mode
                if (isDarkMode()) {
                    adhdMenu.style.backgroundColor = '#374151';
                    adhdMenu.style.boxShadow = '0 2px 5px rgba(0,0,0,0.4)';
                    adhdMenu.style.color = '#E5E7EB';
                } else {
                    adhdMenu.style.backgroundColor = 'white';
                    adhdMenu.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                    adhdMenu.style.color = '#4B5563';
                }

                adhdMenu.style.borderRadius = '4px';
                adhdMenu.style.display = 'block';
                adhdMenu.style.visibility = 'visible';
                adhdMenu.style.width = 'auto';
                adhdMenu.style.minWidth = '200px';
                adhdMenu.style.padding = '0.5rem 0';

                // Style the dropdown items
                styleDropdownItems();
            }

            return false;
        };
    }

    // Productivity dropdown
    if (productivityButton && productivityMenu) {
        // Add click handler
        productivityButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Productivity button clicked');

            // Toggle menu visibility
            if (productivityMenu.style.display === 'block') {
                productivityMenu.style.display = 'none';
            } else {
                hideAllMenus();

                // Position the menu properly
                const buttonRect = productivityButton.getBoundingClientRect();

                // Get the parent container
                const productivityContainer = document.getElementById('productivity-dropdown');

                // Force the dropdown to appear directly below the button
                productivityMenu.style.position = 'absolute';
                productivityMenu.style.top = '100%'; // Position right below the button
                productivityMenu.style.left = '0';
                productivityMenu.style.right = 'auto';

                // Ensure the parent container has position relative
                if (productivityContainer) {
                    productivityContainer.style.position = 'relative';
                }

                // Log positioning for debugging
                console.log('Productivity button clicked');
                productivityMenu.style.zIndex = '9999';

                // Apply appropriate styles based on dark/light mode
                if (isDarkMode()) {
                    productivityMenu.style.backgroundColor = '#374151';
                    productivityMenu.style.boxShadow = '0 2px 5px rgba(0,0,0,0.4)';
                    productivityMenu.style.color = '#E5E7EB';
                } else {
                    productivityMenu.style.backgroundColor = 'white';
                    productivityMenu.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                    productivityMenu.style.color = '#4B5563';
                }

                productivityMenu.style.borderRadius = '4px';
                productivityMenu.style.display = 'block';
                productivityMenu.style.visibility = 'visible';
                productivityMenu.style.width = 'auto';
                productivityMenu.style.minWidth = '200px';
                productivityMenu.style.padding = '0.5rem 0';

                // Style the dropdown items
                styleDropdownItems();
            }

            return false;
        };
    }

    // Tools dropdown
    if (toolsButton && toolsMenu) {
        // Add click handler
        toolsButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Tools button clicked');

            // Toggle menu visibility
            if (toolsMenu.style.display === 'block') {
                toolsMenu.style.display = 'none';
            } else {
                hideAllMenus();

                // Position the menu properly
                const buttonRect = toolsButton.getBoundingClientRect();

                // Get the parent container
                const toolsContainer = document.getElementById('tools-dropdown');

                // Force the dropdown to appear directly below the button
                toolsMenu.style.position = 'absolute';
                toolsMenu.style.top = '100%'; // Position right below the button
                toolsMenu.style.left = '0';
                toolsMenu.style.right = 'auto';

                // Ensure the parent container has position relative
                if (toolsContainer) {
                    toolsContainer.style.position = 'relative';
                }

                // Log positioning for debugging
                console.log('Tools button clicked');
                toolsMenu.style.zIndex = '9999';

                // Apply appropriate styles based on dark/light mode
                if (isDarkMode()) {
                    toolsMenu.style.backgroundColor = '#374151';
                    toolsMenu.style.boxShadow = '0 2px 5px rgba(0,0,0,0.4)';
                    toolsMenu.style.color = '#E5E7EB';
                } else {
                    toolsMenu.style.backgroundColor = 'white';
                    toolsMenu.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                    toolsMenu.style.color = '#4B5563';
                }

                toolsMenu.style.borderRadius = '4px';
                toolsMenu.style.display = 'block';
                toolsMenu.style.visibility = 'visible';
                toolsMenu.style.width = 'auto';
                toolsMenu.style.minWidth = '200px';
                toolsMenu.style.padding = '0.5rem 0';

                // Style the dropdown items
                styleDropdownItems();
            }

            return false;
        };
    }

    // User dropdown
    if (userButton && userMenu) {
        // Add click handler
        userButton.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('User button clicked');

            // Toggle menu visibility
            if (userMenu.style.display === 'block') {
                userMenu.style.display = 'none';
            } else {
                hideAllMenus();

                // Position the menu properly - user menu is right-aligned
                const buttonRect = userButton.getBoundingClientRect();

                // Get the parent container
                const userContainer = userButton.closest('.relative');

                // Force the dropdown to appear directly below the button
                userMenu.style.position = 'absolute';
                userMenu.style.top = '100%'; // Position right below the button
                userMenu.style.left = 'auto';
                userMenu.style.right = '0'; // Right-align the menu

                // Ensure the parent container has position relative
                if (userContainer) {
                    userContainer.style.position = 'relative';
                }

                // Log positioning for debugging
                console.log('User button clicked');
                userMenu.style.zIndex = '9999';

                // Apply appropriate styles based on dark/light mode
                if (isDarkMode()) {
                    userMenu.style.backgroundColor = '#374151';
                    userMenu.style.boxShadow = '0 2px 5px rgba(0,0,0,0.4)';
                    userMenu.style.color = '#E5E7EB';
                } else {
                    userMenu.style.backgroundColor = 'white';
                    userMenu.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
                    userMenu.style.color = '#4B5563';
                }

                userMenu.style.borderRadius = '4px';
                userMenu.style.display = 'block';
                userMenu.style.visibility = 'visible';
                userMenu.style.width = 'auto';
                userMenu.style.minWidth = '200px';
                userMenu.style.padding = '0.5rem 0';

                // Style the dropdown items
                styleDropdownItems();
            }

            return false;
        };
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        // Check if the click was on a dropdown button or menu
        if (!e.target.closest('#adhd-dropdown-button') &&
            !e.target.closest('#adhd-dropdown-menu') &&
            !e.target.closest('#productivity-dropdown-button') &&
            !e.target.closest('#productivity-dropdown-menu') &&
            !e.target.closest('#tools-dropdown-button') &&
            !e.target.closest('#tools-dropdown-menu') &&
            !e.target.closest('#user-menu-button') &&
            !e.target.closest('#user-dropdown-menu')) {

            hideAllMenus();
        }
    });
    }

    // Run immediately
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(initDropdowns, 0);
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initDropdowns, 0);
        });
    }

    // Also run when the window loads as a fallback
    window.addEventListener('load', function() {
        setTimeout(initDropdowns, 0);
    });
})();
