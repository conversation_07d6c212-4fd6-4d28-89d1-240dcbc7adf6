/**
 * Pinterest Advanced Fix
 *
 * This script specifically fixes the advanced options toggle and real scraping toggle
 * in the Pinterest scraper interface.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Advanced Fix loaded');

    // Fix for advanced options toggle
    const toggleAdvanced = document.getElementById('toggle-advanced');
    const advancedOptions = document.getElementById('advanced-options');
    const advancedShow = document.getElementById('advanced-show');
    const advancedHide = document.getElementById('advanced-hide');

    if (toggleAdvanced && advancedOptions) {
        console.log('Found advanced options elements');
        
        // Make sure the advanced options are hidden by default
        advancedOptions.classList.add('hidden');
        
        if (advancedShow && advancedHide) {
            advancedShow.classList.remove('hidden');
            advancedHide.classList.add('hidden');
        }
        
        // Remove existing event listeners
        const newToggleAdvanced = toggleAdvanced.cloneNode(true);
        toggleAdvanced.parentNode.replaceChild(newToggleAdvanced, toggleAdvanced);
        
        // Add click handler
        newToggleAdvanced.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Toggle advanced clicked');
            
            advancedOptions.classList.toggle('hidden');
            
            if (advancedShow && advancedHide) {
                advancedShow.classList.toggle('hidden');
                advancedHide.classList.toggle('hidden');
            }
            
            return false;
        });
    }

    // Fix for real scraping toggle
    const useRealScraping = document.getElementById('use_real_scraping');
    const pinterestCredentials = document.getElementById('pinterest-credentials');

    if (useRealScraping && pinterestCredentials) {
        console.log('Found Pinterest credentials elements');
        
        // Make sure credentials are hidden by default
        pinterestCredentials.classList.add('hidden');
        
        // Remove existing event listeners
        const newUseRealScraping = useRealScraping.cloneNode(true);
        useRealScraping.parentNode.replaceChild(newUseRealScraping, useRealScraping);
        
        // Add change handler
        newUseRealScraping.addEventListener('change', function() {
            console.log('Use real scraping changed:', this.checked);
            if (this.checked) {
                pinterestCredentials.classList.remove('hidden');
            } else {
                pinterestCredentials.classList.add('hidden');
            }
        });
    }

    // Make sure scraping progress is hidden by default
    const scrapingProgress = document.getElementById('scraping-progress');
    if (scrapingProgress) {
        console.log('Found scraping progress element');
        scrapingProgress.classList.add('hidden');
    }
});
