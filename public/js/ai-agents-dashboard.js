/**
 * AI Agents Dashboard
 *
 * JavaScript functionality for the AI Agents Army dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Agents Dashboard loaded');

    // Initialize tooltips
    initTooltips();

    // Initialize agent status indicators
    initStatusIndicators();

    // Initialize skill badges
    initSkillBadges();

    // Initialize rating sliders
    initRatingSliders();

    // Initialize agent cards
    initAgentCards();

    // Initialize category collapsible sections
    initCategoryCollapsible();

    // Initialize search functionality
    initAgentSearch();

    // Initialize dashboard layout integration
    initDashboardIntegration();
});

/**
 * Initialize tooltips
 */
function initTooltips() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    
    tooltips.forEach(tooltip => {
        tooltip.addEventListener('mouseenter', function() {
            const tooltipText = this.getAttribute('data-tooltip');
            const tooltipEl = document.createElement('div');
            tooltipEl.className = 'absolute z-10 px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm tooltip dark:bg-gray-700';
            tooltipEl.textContent = tooltipText;
            tooltipEl.style.bottom = 'calc(100% + 5px)';
            tooltipEl.style.left = '50%';
            tooltipEl.style.transform = 'translateX(-50%)';
            tooltipEl.style.whiteSpace = 'nowrap';
            
            this.style.position = 'relative';
            this.appendChild(tooltipEl);
        });
        
        tooltip.addEventListener('mouseleave', function() {
            const tooltipEl = this.querySelector('.tooltip');
            if (tooltipEl) {
                tooltipEl.remove();
            }
        });
    });
}

/**
 * Initialize agent status indicators
 */
function initStatusIndicators() {
    const statusIndicators = document.querySelectorAll('.status-indicator');
    
    statusIndicators.forEach(indicator => {
        const status = indicator.getAttribute('data-status');
        indicator.classList.add(`status-${status}`);
    });
}

/**
 * Initialize skill badges
 */
function initSkillBadges() {
    const skillBadges = document.querySelectorAll('.skill-badge');
    
    skillBadges.forEach(badge => {
        const skillType = badge.getAttribute('data-skill-type');
        badge.classList.add(skillType);
    });
}

/**
 * Initialize rating sliders
 */
function initRatingSliders() {
    const ratingSliders = document.querySelectorAll('.agent-rating-slider');
    
    ratingSliders.forEach(slider => {
        const ratingType = slider.getAttribute('data-rating-type');
        slider.classList.add(`${ratingType}-slider`);
        
        const valueDisplay = document.getElementById(`${slider.id}-value`);
        if (valueDisplay) {
            valueDisplay.textContent = slider.value;
            
            slider.addEventListener('input', function() {
                valueDisplay.textContent = this.value;
            });
        }
    });
}

/**
 * Initialize agent cards
 */
function initAgentCards() {
    const agentCards = document.querySelectorAll('.agent-card');
    
    agentCards.forEach(card => {
        const categoryType = card.getAttribute('data-category');
        if (categoryType) {
            card.classList.add(categoryType);
        }
        
        // Add hover animation for ADHD-friendly visual feedback
        card.classList.add('pulse-on-hover');
    });
}

/**
 * Initialize category collapsible sections
 */
function initCategoryCollapsible() {
    const categoryHeaders = document.querySelectorAll('.category-header');
    
    categoryHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-category-id');
            const categoryContent = document.getElementById(`category-content-${categoryId}`);
            
            if (categoryContent) {
                const isExpanded = categoryContent.classList.contains('hidden');
                
                // Toggle content visibility
                if (isExpanded) {
                    categoryContent.classList.remove('hidden');
                    this.querySelector('.toggle-icon').classList.remove('fa-chevron-down');
                    this.querySelector('.toggle-icon').classList.add('fa-chevron-up');
                } else {
                    categoryContent.classList.add('hidden');
                    this.querySelector('.toggle-icon').classList.remove('fa-chevron-up');
                    this.querySelector('.toggle-icon').classList.add('fa-chevron-down');
                }
            }
        });
    });
}

/**
 * Initialize agent search functionality
 */
function initAgentSearch() {
    const searchInput = document.getElementById('agent-search');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const agentCards = document.querySelectorAll('.agent-card');
            
            agentCards.forEach(card => {
                const agentName = card.getAttribute('data-agent-name').toLowerCase();
                const agentDescription = card.getAttribute('data-agent-description')?.toLowerCase() || '';
                
                if (agentName.includes(searchTerm) || agentDescription.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Check if any agents are visible in each category
            const categories = document.querySelectorAll('.agent-category');
            categories.forEach(category => {
                const visibleAgents = category.querySelectorAll('.agent-card[style="display: none;"]');
                const categoryHeader = document.querySelector(`.category-header[data-category-id="${category.getAttribute('data-category-id')}"]`);
                
                if (visibleAgents.length === 0) {
                    categoryHeader.style.display = 'none';
                } else {
                    categoryHeader.style.display = '';
                }
            });
        });
    }
}

/**
 * Initialize dashboard layout integration
 */
function initDashboardIntegration() {
    // Listen for dashboard layout changes
    document.addEventListener('layout-changed', function(event) {
        const layout = event.detail.layout;
        console.log('Dashboard layout changed:', layout);
        
        // Apply layout-specific styles to AI Agents widget
        const aiAgentsWidget = document.querySelector('[data-widget="ai-agents-widget"]');
        
        if (aiAgentsWidget) {
            // Reset styles
            aiAgentsWidget.style.order = '';
            
            // Apply layout-specific styles
            switch (layout) {
                case 'adhd-optimized':
                    // Position the widget in a specific order for ADHD-optimized layout
                    aiAgentsWidget.style.order = '5';
                    break;
                    
                case 'focus':
                    // Hide the widget in focus mode
                    aiAgentsWidget.style.display = 'none';
                    break;
                    
                case 'standard':
                    // Standard layout
                    aiAgentsWidget.style.display = 'block';
                    break;
                    
                case 'custom':
                    // Custom layout - no specific styles
                    aiAgentsWidget.style.display = 'block';
                    break;
            }
        }
    });
}
