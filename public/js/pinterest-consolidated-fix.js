/**
 * Pinterest Consolidated Fix
 * 
 * This script combines all the necessary fixes for the Pinterest Clone Research Tool
 * to ensure proper functionality without conflicts.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Pinterest Consolidated Fix loaded');
    
    // Fix navigation buttons
    fixNavigationButtons();
    
    // Fix Pinterest pin links
    fixPinterestLinks();
    
    // Fix scraper form
    fixScraperForm();
    
    // Set up mutation observer to handle dynamically added content
    setupMutationObserver();
});

/**
 * Fix navigation buttons (New Scrape, View Trends, etc.)
 */
function fixNavigationButtons() {
    // Find all navigation buttons
    const navButtons = document.querySelectorAll('a[href*="/momentum/clone/pinterest/"]');
    
    if (navButtons.length > 0) {
        console.log('Found navigation buttons:', navButtons.length);
        
        navButtons.forEach(button => {
            // Skip if already fixed
            if (button.getAttribute('data-fixed') === 'true') {
                return;
            }
            
            // Remove existing event listeners by cloning
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // Add our direct click handler
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('Navigation button clicked, redirecting to:', this.href);
                window.location.href = this.href;
                
                return false;
            });
            
            // Mark as fixed
            newButton.setAttribute('data-fixed', 'true');
        });
    }
}

/**
 * Fix Pinterest pin links for direct access
 */
function fixPinterestLinks() {
    // Find all Pinterest pin links
    const pinterestLinks = document.querySelectorAll('a[href*="pinterest.com/pin/"], a.pinterest-pin-link');
    
    if (pinterestLinks.length > 0) {
        console.log('Found Pinterest links:', pinterestLinks.length);
        
        pinterestLinks.forEach(link => {
            // Skip if already fixed
            if (link.getAttribute('data-fixed') === 'true') {
                return;
            }
            
            // Get the original URL
            const originalUrl = link.getAttribute('href');
            
            // Extract the pin ID
            let pinId = null;
            
            if (originalUrl && originalUrl.includes('/pin/')) {
                // Extract from URL
                const matches = originalUrl.match(/\/pin\/([0-9]+)/);
                if (matches && matches[1]) {
                    pinId = matches[1];
                }
            } else if (link.getAttribute('data-pin-id')) {
                // Extract from data attribute
                pinId = link.getAttribute('data-pin-id');
            }
            
            if (pinId) {
                // Create a direct Pinterest URL
                const directUrl = `https://www.pinterest.com/pin/${pinId}/`;
                
                // Update the href attribute
                link.setAttribute('href', directUrl);
                
                // Remove existing event listeners by cloning
                const newLink = link.cloneNode(true);
                link.parentNode.replaceChild(newLink, link);
                
                // Mark as fixed
                newLink.setAttribute('data-fixed', 'true');
                
                // Add target="_blank" to open in a new tab
                newLink.setAttribute('target', '_blank');
            }
        });
    }
}

/**
 * Fix scraper form submission
 */
function fixScraperForm() {
    // Find the scraper form
    const scraperForm = document.getElementById('scraper-form');
    
    if (scraperForm && !scraperForm.getAttribute('data-fixed')) {
        console.log('Found scraper form, adding direct handler');
        
        // Remove existing event listeners by cloning
        const newForm = scraperForm.cloneNode(true);
        scraperForm.parentNode.replaceChild(newForm, scraperForm);
        
        // Mark as fixed
        newForm.setAttribute('data-fixed', 'true');
        
        // Add our direct submit handler
        newForm.addEventListener('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Scraper form submitted');
            
            // Get form data
            const formData = new FormData(newForm);
            
            // Show progress indicator
            const progressElement = document.getElementById('scraping-progress');
            if (progressElement) {
                progressElement.classList.remove('hidden');
                
                // Update progress bar to show activity
                const progressBar = document.getElementById('progress-bar');
                const progressStatus = document.getElementById('progress-status');
                const progressPercentage = document.getElementById('progress-percentage');
                
                if (progressBar) progressBar.style.width = '10%';
                if (progressStatus) progressStatus.textContent = 'Processing...';
                if (progressPercentage) progressPercentage.textContent = '10%';
            }
            
            // Disable submit button
            const submitButton = newForm.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
            
            // Submit the form via fetch API
            fetch('/momentum/clone/pinterest/process-scrape', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                
                if (data.success && data.scrape_id) {
                    // Show success message
                    if (progressElement) {
                        const progressStatus = document.getElementById('progress-status');
                        if (progressStatus) {
                            progressStatus.textContent = 'Completed';
                            progressStatus.classList.remove('text-blue-600', 'bg-blue-200');
                            progressStatus.classList.add('text-green-600', 'bg-green-200');
                        }
                        
                        const progressBar = document.getElementById('progress-bar');
                        if (progressBar) progressBar.style.width = '100%';
                        
                        const progressPercentage = document.getElementById('progress-percentage');
                        if (progressPercentage) progressPercentage.textContent = '100%';
                    }
                    
                    // Redirect to the results page
                    console.log('Redirecting to view-scrape/' + data.scrape_id);
                    setTimeout(() => {
                        window.location.href = '/momentum/clone/pinterest/view-scrape/' + data.scrape_id;
                    }, 1000);
                } else {
                    // Show error
                    alert(data.message || 'An error occurred while processing the scrape request.');
                    
                    // Re-enable submit button
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                
                // Show error
                alert('An error occurred while processing the scrape request.');
                
                // Re-enable submit button
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                }
            });
            
            return false;
        });
    }
}

/**
 * Set up mutation observer to handle dynamically added content
 */
function setupMutationObserver() {
    // Create a mutation observer
    const observer = new MutationObserver(function(mutations) {
        let needsFixing = false;
        
        // Check if we need to fix anything
        for (const mutation of mutations) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                needsFixing = true;
                break;
            }
        }
        
        if (needsFixing) {
            // Fix everything again
            setTimeout(() => {
                fixNavigationButtons();
                fixPinterestLinks();
                fixScraperForm();
            }, 100);
        }
    });
    
    // Start observing the document
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('Mutation observer set up');
}
