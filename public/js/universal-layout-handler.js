/**
 * Universal Layout Dropdown Handler
 * 
 * This script provides a consistent way to handle layout dropdown functionality
 * across all dashboard versions.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Universal Layout Dropdown Handler loaded');
    
    // Find all layout buttons on the page
    const layoutButtons = document.querySelectorAll('button[id*="layout"], .layout-button, [data-action="layout"]');
    
    // Process each layout button
    layoutButtons.forEach(button => {
        console.log('Found layout button:', button.id || button.className);
        
        // Find the associated dropdown
        let dropdown;
        
        // Try different methods to find the dropdown
        if (button.getAttribute('aria-controls')) {
            dropdown = document.getElementById(button.getAttribute('aria-controls'));
        } else if (button.nextElementSibling && (
            button.nextElementSibling.classList.contains('dropdown-menu') || 
            button.nextElementSibling.classList.contains('dropdown-content')
        )) {
            dropdown = button.nextElementSibling;
        } else {
            // Look for a dropdown near this button
            const parent = button.closest('.relative, .dropdown');
            if (parent) {
                dropdown = parent.querySelector('.dropdown-menu, .dropdown-content, [id*="dropdown"]');
            }
        }
        
        if (!dropdown) {
            console.log('No dropdown found for button:', button.id || button.className);
            return;
        }
        
        console.log('Found dropdown:', dropdown.id || dropdown.className);
        
        // Remove any existing click listeners by cloning and replacing
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
        
        // Add click event listener to toggle dropdown
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Layout button clicked');
            
            // Toggle dropdown visibility
            const isHidden = dropdown.classList.contains('hidden');
            
            if (isHidden) {
                // Show dropdown
                dropdown.classList.remove('hidden');
                
                // Position the dropdown correctly
                const buttonRect = newButton.getBoundingClientRect();
                
                // Calculate position relative to the viewport
                const dropdownTop = buttonRect.bottom + window.scrollY;
                const dropdownLeft = buttonRect.left + window.scrollX;
                
                // Set position and show
                dropdown.style.position = 'absolute';
                dropdown.style.top = dropdownTop + 'px';
                dropdown.style.left = dropdownLeft + 'px';
                dropdown.style.zIndex = '9999';
                
                // Ensure the dropdown is visible within the viewport
                setTimeout(() => {
                    const dropdownRect = dropdown.getBoundingClientRect();
                    const viewportWidth = window.innerWidth;
                    const viewportHeight = window.innerHeight;
                    
                    // Adjust if dropdown would go off-screen to the right
                    if (dropdownRect.right > viewportWidth) {
                        const rightOffset = dropdownRect.right - viewportWidth + 10;
                        dropdown.style.left = (dropdownLeft - rightOffset) + 'px';
                    }
                    
                    // Adjust if dropdown would go off-screen at the bottom
                    if (dropdownRect.bottom > viewportHeight) {
                        const bottomOffset = dropdownRect.bottom - viewportHeight + 10;
                        dropdown.style.top = (dropdownTop - bottomOffset) + 'px';
                    }
                }, 0);
                
                // Set active state on button
                newButton.setAttribute('aria-expanded', 'true');
                newButton.classList.add('bg-gray-100', 'dark:bg-gray-600');
            } else {
                // Hide dropdown
                dropdown.classList.add('hidden');
                newButton.setAttribute('aria-expanded', 'false');
                newButton.classList.remove('bg-gray-100', 'dark:bg-gray-600');
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!newButton.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.classList.add('hidden');
                newButton.setAttribute('aria-expanded', 'false');
                newButton.classList.remove('bg-gray-100', 'dark:bg-gray-600');
            }
        });
        
        // Handle view option selection
        const viewOptions = dropdown.querySelectorAll('.view-option, [data-view]');
        viewOptions.forEach(option => {
            // Remove any existing click listeners by cloning and replacing
            const newOption = option.cloneNode(true);
            option.parentNode.replaceChild(newOption, option);
            
            // Add event listener to the new option
            newOption.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const viewMode = this.getAttribute('data-view');
                console.log('View option clicked:', viewMode);
                
                // Find the dashboard widgets container
                const dashboardWidgets = document.getElementById('dashboard-widgets');
                if (dashboardWidgets) {
                    // Update data attribute
                    dashboardWidgets.setAttribute('data-view-mode', viewMode);
                    dashboardWidgets.setAttribute('data-arrangement', viewMode);
                    
                    // Update view mode indicator
                    const viewModeText = document.getElementById('view-mode-text');
                    if (viewModeText) {
                        viewModeText.textContent = viewMode.charAt(0).toUpperCase() + viewMode.slice(1) + ' View';
                    }
                    
                    // Save the current view mode preference
                    localStorage.setItem('dashboard_view_mode', viewMode);
                }
                
                // Hide dropdown
                dropdown.classList.add('hidden');
                newButton.setAttribute('aria-expanded', 'false');
                newButton.classList.remove('bg-gray-100', 'dark:bg-gray-600');
            });
        });
    });
});
