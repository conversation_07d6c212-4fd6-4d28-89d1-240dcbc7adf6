/**
 * Layout Button Debug Script
 * 
 * This script helps debug issues with the layout button dropdown functionality.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Layout Debug Script loaded');
    
    // Find all layout buttons on the page
    const layoutButtons = document.querySelectorAll('button[id*="layout"], .layout-button, [data-action="layout"]');
    console.log('Found layout buttons:', layoutButtons.length);
    
    // Log details about each button
    layoutButtons.forEach((button, index) => {
        console.log(`Layout button ${index + 1}:`, {
            id: button.id,
            classes: button.className,
            text: button.innerText.trim(),
            attributes: Array.from(button.attributes).map(attr => `${attr.name}="${attr.value}"`).join(', '),
            hasEventListeners: button._events && button._events.length > 0
        });
        
        // Add a click event listener to log when the button is clicked
        button.addEventListener('click', function(e) {
            console.log(`Layout button ${index + 1} clicked:`, e);
            
            // Check if the event is being stopped from propagating
            if (e.defaultPrevented) {
                console.log('Default action was prevented');
            }
        });
    });
    
    // Check for dropdowns
    const dropdowns = document.querySelectorAll('.dropdown-menu, [id*="dropdown"], .dropdown-content');
    console.log('Found dropdowns:', dropdowns.length);
    
    // Log details about each dropdown
    dropdowns.forEach((dropdown, index) => {
        console.log(`Dropdown ${index + 1}:`, {
            id: dropdown.id,
            classes: dropdown.className,
            isHidden: dropdown.classList.contains('hidden') || 
                     window.getComputedStyle(dropdown).display === 'none' ||
                     window.getComputedStyle(dropdown).visibility === 'hidden',
            parent: dropdown.parentElement ? dropdown.parentElement.tagName : 'none',
            position: window.getComputedStyle(dropdown).position,
            zIndex: window.getComputedStyle(dropdown).zIndex
        });
    });
    
    // Add a global click listener to see what's being clicked
    document.addEventListener('click', function(e) {
        console.log('Document clicked:', {
            target: e.target.tagName,
            targetId: e.target.id,
            targetClasses: e.target.className,
            path: e.composedPath().map(el => el.tagName || el.toString()).join(' > ')
        });
    }, true); // Use capture phase to see the event before it's potentially stopped
});
