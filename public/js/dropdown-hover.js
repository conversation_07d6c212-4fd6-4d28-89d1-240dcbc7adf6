/**
 * Dropdown Menu with Hover Support
 * 
 * This script provides dropdown functionality with both click and hover support.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Dropdown hover script loaded');
    
    // Get all dropdown containers
    const dropdownContainers = [
        document.getElementById('adhd-dropdown'),
        document.getElementById('productivity-dropdown'),
        document.getElementById('tools-dropdown'),
        document.querySelector('.relative:has(#user-menu-button)') // User dropdown container
    ];
    
    // Get all dropdown buttons
    const dropdownButtons = [
        document.getElementById('adhd-dropdown-button'),
        document.getElementById('productivity-dropdown-button'),
        document.getElementById('tools-dropdown-button'),
        document.getElementById('user-menu-button')
    ];
    
    // Get all dropdown menus
    const dropdownMenus = [
        document.getElementById('adhd-dropdown-menu'),
        document.getElementById('productivity-dropdown-menu'),
        document.getElementById('tools-dropdown-menu'),
        document.getElementById('user-dropdown-menu')
    ];
    
    // Function to hide all menus
    function hideAllMenus() {
        dropdownMenus.forEach(menu => {
            if (menu) {
                menu.style.display = 'none';
            }
        });
    }
    
    // Function to show a specific menu
    function showMenu(index) {
        // Hide all menus first
        hideAllMenus();
        
        // Show the selected menu
        if (dropdownMenus[index]) {
            dropdownMenus[index].style.display = 'block';
        }
    }
    
    // Set up click handlers for dropdown buttons
    dropdownButtons.forEach((button, index) => {
        if (button) {
            // Remove any existing onclick attribute
            button.removeAttribute('onclick');
            
            // Add click handler
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Toggle menu visibility
                if (dropdownMenus[index] && dropdownMenus[index].style.display === 'block') {
                    dropdownMenus[index].style.display = 'none';
                } else {
                    showMenu(index);
                }
            });
        }
    });
    
    // Set up hover handlers for dropdown containers
    dropdownContainers.forEach((container, index) => {
        if (container) {
            // Add mouseenter handler
            container.addEventListener('mouseenter', function() {
                showMenu(index);
            });
            
            // Add mouseleave handler
            container.addEventListener('mouseleave', function() {
                if (dropdownMenus[index]) {
                    dropdownMenus[index].style.display = 'none';
                }
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        let clickedOnDropdown = false;
        
        // Check if click was on a dropdown button or menu
        dropdownContainers.forEach((container) => {
            if (container && container.contains(e.target)) {
                clickedOnDropdown = true;
            }
        });
        
        // If click was outside, hide all menus
        if (!clickedOnDropdown) {
            hideAllMenus();
        }
    });
    
    // Initialize dropdown menus (hide them initially)
    hideAllMenus();
});
