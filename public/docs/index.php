<?php
/**
 * Documentation System
 *
 * A more accessible documentation system for the Momentum platform
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once __DIR__ . '/../../src/utils/Session.php';

// Start session
Session::start();

// Define documentation categories and pages
$docCategories = [
    'getting-started' => [
        'title' => 'Getting Started',
        'icon' => 'fa-rocket',
        'color' => 'green',
        'description' => 'New to Momentum? Start here for a quick overview and setup guide.',
        'pages' => [
            'quick-start' => [
                'title' => 'Quick Start Guide',
                'file' => 'content/getting_started/quick_start.md',
                'icon' => 'fa-play',
                'description' => 'Get up and running with Momentum in just 10 minutes.',
                'time' => '10 min'
            ],
            'system-overview' => [
                'title' => 'System Overview',
                'file' => 'content/getting_started/system_overview.md',
                'icon' => 'fa-sitemap',
                'description' => 'Complete overview of all Momentum features and capabilities.',
                'time' => '15 min'
            ],
            'first-steps' => [
                'title' => 'Your First Steps',
                'file' => 'content/getting_started/first_steps.md',
                'icon' => 'fa-footprints',
                'description' => 'Essential setup tasks to get the most out of Momentum.',
                'time' => '20 min'
            ]
        ]
    ],
    'adhd-management' => [
        'title' => 'ADHD Management',
        'icon' => 'fa-brain',
        'color' => 'purple',
        'description' => 'Comprehensive tools designed specifically for ADHD brains.',
        'featured' => true,
        'pages' => [
            'adhd-quick-start' => [
                'title' => 'ADHD Quick Start',
                'file' => 'content/adhd/quick_start.md',
                'icon' => 'fa-bolt',
                'description' => 'Get started with ADHD management tools in 20 minutes.',
                'time' => '20 min',
                'featured' => true
            ],
            'executive-function' => [
                'title' => 'Executive Function Exercises',
                'file' => 'content/adhd/executive_function.md',
                'icon' => 'fa-dumbbell',
                'description' => 'Interactive exercises to strengthen cognitive skills.',
                'time' => '5-15 min'
            ],
            'medication-tracker' => [
                'title' => 'Medication Tracker',
                'file' => 'content/adhd/medication_tracker.md',
                'icon' => 'fa-pills',
                'description' => 'Advanced medication management with effectiveness analysis.',
                'time' => '10 min'
            ],
            'trigger-analysis' => [
                'title' => 'Trigger Identification',
                'file' => 'content/adhd/trigger_analysis.md',
                'icon' => 'fa-search',
                'description' => 'AI-powered pattern recognition for trigger management.',
                'time' => '15 min'
            ],
            'symptom-tracking' => [
                'title' => 'Symptom Tracking',
                'file' => 'content/adhd/symptom_tracking.md',
                'icon' => 'fa-chart-line',
                'description' => 'Comprehensive ADHD symptom monitoring and analysis.',
                'time' => '5 min'
            ],
            'cbt-tools' => [
                'title' => 'CBT Tools',
                'file' => 'content/adhd/cbt_tools.md',
                'icon' => 'fa-lightbulb',
                'description' => 'Cognitive Behavioral Therapy tools for thought management.',
                'time' => '10 min'
            ]
        ]
    ],
    'productivity' => [
        'title' => 'Productivity Tools',
        'icon' => 'fa-tasks',
        'color' => 'blue',
        'description' => 'Time management, focus enhancement, and productivity systems.',
        'pages' => [
            'task-management' => [
                'title' => 'Task Management',
                'file' => 'content/productivity/task_management.md',
                'icon' => 'fa-check-square',
                'description' => 'Create, organize, and track tasks effectively.',
                'time' => '10 min'
            ],
            'time-blocking' => [
                'title' => 'Time Blocking',
                'file' => 'content/productivity/time_blocking.md',
                'icon' => 'fa-calendar-alt',
                'description' => 'Schedule your day with time blocking techniques.',
                'time' => '15 min'
            ],
            'focus-mode' => [
                'title' => 'Focus Mode',
                'file' => 'content/productivity/focus_mode.md',
                'icon' => 'fa-eye',
                'description' => 'Eliminate distractions and maintain focus.',
                'time' => '5 min'
            ],
            'energy-tracking' => [
                'title' => 'Energy Tracking',
                'file' => 'content/productivity/energy_tracking.md',
                'icon' => 'fa-battery-three-quarters',
                'description' => 'Track and optimize your energy levels.',
                'time' => '10 min'
            ]
        ]
    ],
    'financial-management' => [
        'title' => 'Financial Management',
        'icon' => 'fa-dollar-sign',
        'color' => 'emerald',
        'description' => 'Budgeting, expense tracking, and financial goal management.',
        'pages' => [
            'budgeting' => [
                'title' => 'Budgeting System',
                'file' => 'content/financial/budgeting.md',
                'icon' => 'fa-calculator',
                'description' => 'Create and manage budgets effectively.',
                'time' => '15 min'
            ],
            'expense-tracking' => [
                'title' => 'Expense Tracking',
                'file' => 'content/financial/expense_tracking.md',
                'icon' => 'fa-receipt',
                'description' => 'Track expenses and analyze spending patterns.',
                'time' => '10 min'
            ],
            'income-streams' => [
                'title' => 'Income Stream Management',
                'file' => 'content/financial/income_streams.md',
                'icon' => 'fa-stream',
                'description' => 'Manage multiple income sources and opportunities.',
                'time' => '20 min'
            ]
        ]
    ],
    'ai-agents' => [
        'title' => 'AI Agents',
        'icon' => 'fa-robot',
        'color' => 'indigo',
        'description' => 'Intelligent agents to automate tasks and boost productivity.',
        'pages' => [
            'aegis-director' => [
                'title' => 'Aegis Director Agent',
                'file' => 'content/ai_agents/aegis_director.md',
                'icon' => 'fa-shield-alt',
                'description' => 'Executive functioning support and project management.',
                'time' => '15 min'
            ],
            'youtube-agent' => [
                'title' => 'YouTube Browser Agent',
                'file' => 'content/ai_agents/youtube_agent.md',
                'icon' => 'fa-youtube',
                'description' => 'Analyze YouTube content for money-making opportunities.',
                'time' => '10 min'
            ],
            'agent-army' => [
                'title' => 'AI Agent Army',
                'file' => 'content/ai_agents/agent_army.md',
                'icon' => 'fa-users',
                'description' => 'Build and deploy specialized agent brigades.',
                'time' => '30 min'
            ]
        ]
    ],
    'business-tools' => [
        'title' => 'Business Tools',
        'icon' => 'fa-briefcase',
        'color' => 'orange',
        'description' => 'Freelance management, online business, and income evaluation.',
        'pages' => [
            'freelance-management' => [
                'title' => 'Freelance Management',
                'file' => 'content/business/freelance_management.md',
                'icon' => 'fa-handshake',
                'description' => 'Manage clients, projects, and invoices.',
                'time' => '20 min'
            ],
            'online-business' => [
                'title' => 'Online Business Dashboard',
                'file' => 'content/business/online_business.md',
                'icon' => 'fa-chart-bar',
                'description' => 'Monitor and grow your online business.',
                'time' => '15 min'
            ],
            'income-evaluator' => [
                'title' => 'Income Stream Evaluator',
                'file' => 'content/business/income_evaluator.md',
                'icon' => 'fa-balance-scale',
                'description' => 'Evaluate and compare income opportunities.',
                'time' => '25 min'
            ]
        ]
    ],
    'health-wellness' => [
        'title' => 'Health & Wellness',
        'icon' => 'fa-heart',
        'color' => 'red',
        'description' => 'Medical tracking, wellness monitoring, and health management.',
        'pages' => [
            'medical-reports' => [
                'title' => 'Medical Reports',
                'file' => 'content/health/medical_reports.md',
                'icon' => 'fa-file-medical',
                'description' => 'Track and manage medical test results.',
                'time' => '10 min'
            ],
            'wellness-tracking' => [
                'title' => 'Wellness Tracking',
                'file' => 'content/health/wellness_tracking.md',
                'icon' => 'fa-heartbeat',
                'description' => 'Monitor mood, sleep, and overall wellness.',
                'time' => '15 min'
            ]
        ]
    ],
    'advanced-features' => [
        'title' => 'Advanced Features',
        'icon' => 'fa-cogs',
        'color' => 'gray',
        'description' => 'Advanced tools and customization options.',
        'pages' => [
            'api-integration' => [
                'title' => 'API Integration',
                'file' => 'content/advanced/api_integration.md',
                'icon' => 'fa-plug',
                'description' => 'Integrate with external services and APIs.',
                'time' => '30 min'
            ],
            'customization' => [
                'title' => 'Customization Options',
                'file' => 'content/advanced/customization.md',
                'icon' => 'fa-palette',
                'description' => 'Customize Momentum to fit your workflow.',
                'time' => '20 min'
            ],
            'troubleshooting' => [
                'title' => 'Troubleshooting',
                'file' => 'content/advanced/troubleshooting.md',
                'icon' => 'fa-wrench',
                'description' => 'Common issues and solutions.',
                'time' => '10 min'
            ]
        ]
    ]
];

// Handle page request
$currentCategory = isset($_GET['category']) ? $_GET['category'] : null;
$currentPage = isset($_GET['page']) ? $_GET['page'] : null;

// Validate category and page
$validCategory = isset($docCategories[$currentCategory]);
$validPage = $validCategory && isset($docCategories[$currentCategory]['pages'][$currentPage]);

// Get page content if valid
$pageContent = '';
$pageTitle = 'Documentation';

if ($validCategory && $validPage) {
    $pageInfo = $docCategories[$currentCategory]['pages'][$currentPage];
    $pageTitle = $pageInfo['title'];
    $filePath = __DIR__ . '/' . $pageInfo['file'];

    if (file_exists($filePath)) {
        $pageContent = file_get_contents($filePath);
    } else {
        $pageContent = "# {$pageInfo['title']}\n\nThis documentation page is under construction.";
    }
}

// Function to convert Markdown to HTML
function markdownToHtml($markdown) {
    // Simple Markdown parser (for a production environment, use a proper Markdown library)
    $html = $markdown;

    // Headers
    $html = preg_replace('/^# (.*?)$/m', '<h1>$1</h1>', $html);
    $html = preg_replace('/^## (.*?)$/m', '<h2>$1</h2>', $html);
    $html = preg_replace('/^### (.*?)$/m', '<h3>$1</h3>', $html);
    $html = preg_replace('/^#### (.*?)$/m', '<h4>$1</h4>', $html);
    $html = preg_replace('/^##### (.*?)$/m', '<h5>$1</h5>', $html);

    // Bold and Italic
    $html = preg_replace('/\*\*(.*?)\*\*/s', '<strong>$1</strong>', $html);
    $html = preg_replace('/\*(.*?)\*/s', '<em>$1</em>', $html);

    // Lists
    $html = preg_replace('/^- (.*?)$/m', '<li>$1</li>', $html);
    $html = preg_replace('/^[0-9]+\. (.*?)$/m', '<li>$1</li>', $html);

    // Wrap lists in ul/ol tags
    $html = preg_replace('/<li>(.*?)<\/li>(?!\s*<li>)/s', '<ul><li>$1</li></ul>', $html);
    $html = preg_replace('/<\/ul>\s*<ul>/', '', $html);

    // Links
    $html = preg_replace('/\[(.*?)\]\((.*?)\)/s', '<a href="$2">$1</a>', $html);

    // Code blocks
    $html = preg_replace('/```(.*?)```/s', '<pre><code>$1</code></pre>', $html);
    $html = preg_replace('/`(.*?)`/s', '<code>$1</code>', $html);

    // Paragraphs
    $html = preg_replace('/^(?!<h|<ul|<li|<\/|<pre)(.*?)$/m', '<p>$1</p>', $html);

    return $html;
}

// Convert page content to HTML
if ($pageContent) {
    $pageContent = markdownToHtml($pageContent);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - Momentum Documentation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* ADHD-Friendly Design System */
        :root {
            --primary-purple: #8b5cf6;
            --primary-blue: #3b82f6;
            --primary-green: #10b981;
            --primary-orange: #f59e0b;
            --primary-red: #ef4444;
            --primary-indigo: #6366f1;
            --primary-emerald: #059669;
            --primary-gray: #6b7280;

            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-accent: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-light: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #1e293b;
                --bg-secondary: #334155;
                --bg-accent: #475569;
                --text-primary: #f1f5f9;
                --text-secondary: #cbd5e1;
                --border-light: #475569;
            }
        }

        /* Layout */
        .sidebar {
            height: calc(100vh - 80px);
            overflow-y: auto;
            scrollbar-width: thin;
        }

        .content {
            height: calc(100vh - 80px);
            overflow-y: auto;
            scrollbar-width: thin;
        }

        /* ADHD-Friendly Scrollbars */
        .sidebar::-webkit-scrollbar,
        .content::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track,
        .content::-webkit-scrollbar-track {
            background: var(--bg-accent);
        }

        .sidebar::-webkit-scrollbar-thumb,
        .content::-webkit-scrollbar-thumb {
            background: var(--primary-purple);
            border-radius: 3px;
        }

        /* Featured sections */
        .featured-section {
            background: linear-gradient(135deg, var(--primary-purple), var(--primary-blue));
            border: none !important;
        }

        .featured-section h3,
        .featured-section p {
            color: white !important;
        }

        /* Category cards with better visual hierarchy */
        .category-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .category-purple { border-left-color: var(--primary-purple); }
        .category-blue { border-left-color: var(--primary-blue); }
        .category-green { border-left-color: var(--primary-green); }
        .category-orange { border-left-color: var(--primary-orange); }
        .category-red { border-left-color: var(--primary-red); }
        .category-indigo { border-left-color: var(--primary-indigo); }
        .category-emerald { border-left-color: var(--primary-emerald); }
        .category-gray { border-left-color: var(--primary-gray); }

        /* Time indicators */
        .time-badge {
            background: var(--bg-accent);
            color: var(--text-secondary);
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-weight: 500;
        }

        /* Search functionality */
        .search-container {
            position: relative;
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: 0.5rem;
            box-shadow: var(--shadow-lg);
            z-index: 50;
            max-height: 300px;
            overflow-y: auto;
        }

        /* Progress indicators */
        .progress-indicator {
            height: 4px;
            background: var(--bg-accent);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-purple), var(--primary-blue));
            transition: width 0.3s ease;
        }

        /* Documentation content styling */
        .doc-content {
            line-height: 1.7;
            font-size: 1rem;
        }

        .doc-content h1 {
            font-size: 2.25rem;
            font-weight: 800;
            margin-top: 2rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--border-light);
            color: var(--text-primary);
        }

        .doc-content h2 {
            font-size: 1.875rem;
            font-weight: 700;
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .doc-content h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .doc-content h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-top: 1.25rem;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        .doc-content p {
            margin-bottom: 1.25rem;
            color: var(--text-secondary);
        }

        .doc-content ul, .doc-content ol {
            margin-left: 1.5rem;
            margin-bottom: 1.25rem;
            color: var(--text-secondary);
        }

        .doc-content li {
            margin-bottom: 0.5rem;
        }

        .doc-content a {
            color: var(--primary-purple);
            text-decoration: underline;
            font-weight: 500;
        }

        .doc-content a:hover {
            color: var(--primary-blue);
        }

        .doc-content code {
            background: var(--bg-accent);
            color: var(--text-primary);
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
        }

        .doc-content pre {
            background: var(--bg-accent);
            padding: 1.5rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-light);
        }

        .doc-content pre code {
            background: transparent;
            padding: 0;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .sidebar {
                height: auto;
                max-height: 50vh;
            }

            .content {
                height: auto;
            }

            .doc-content h1 {
                font-size: 1.875rem;
            }

            .doc-content h2 {
                font-size: 1.5rem;
            }
        }

        /* Focus states for accessibility */
        .focus\:ring-purple:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
        }

        /* Animation for page transitions */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <a href="/momentum/dashboard" class="flex items-center group">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3 group-hover:scale-105 transition-transform">
                            <i class="fas fa-book text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">Momentum Help Center</h1>
                            <p class="text-sm text-gray-500">Complete documentation & guides</p>
                        </div>
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex items-center space-x-4">
                    <div class="search-container">
                        <div class="relative">
                            <input type="text"
                                   id="search-input"
                                   placeholder="Search documentation..."
                                   class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <div id="search-results" class="search-results hidden"></div>
                    </div>

                    <!-- Quick Links -->
                    <a href="/momentum/adhd" class="text-purple-600 hover:text-purple-700 font-medium">
                        <i class="fas fa-brain mr-1"></i> ADHD Tools
                    </a>
                    <a href="/momentum/dashboard" class="text-gray-600 hover:text-gray-700">
                        <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-700 hover:bg-gray-100">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col md:flex-row">
            <!-- Sidebar -->
            <div class="w-full md:w-64 flex-shrink-0 mb-6 md:mb-0 md:mr-6">
                <div class="bg-white rounded-lg shadow sidebar overflow-y-auto">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Documentation</h2>
                    </div>
                    <nav class="p-4 space-y-6">
                        <?php foreach ($docCategories as $categoryId => $category): ?>
                            <div>
                                <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                                    <i class="fas <?= $category['icon'] ?> text-<?= $category['color'] ?>-500 mr-2"></i>
                                    <?= htmlspecialchars($category['title']) ?>
                                </h3>
                                <ul class="space-y-1">
                                    <?php foreach ($category['pages'] as $pageId => $page): ?>
                                        <li>
                                            <a href="?category=<?= $categoryId ?>&page=<?= $pageId ?>"
                                               class="block px-3 py-2 rounded-md text-sm font-medium <?= ($currentCategory === $categoryId && $currentPage === $pageId) ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-500' : 'text-gray-700 hover:bg-gray-50' ?>">
                                                <i class="fas <?= $page['icon'] ?> mr-2 <?= ($currentCategory === $categoryId && $currentPage === $pageId) ? 'text-indigo-500' : 'text-gray-400' ?>"></i>
                                                <?= htmlspecialchars($page['title']) ?>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endforeach; ?>
                    </nav>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow content overflow-y-auto">
                    <?php if ($validCategory && $validPage): ?>
                        <div class="p-6">
                            <div class="border-b border-gray-200 pb-4 mb-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-<?= $docCategories[$currentCategory]['color'] ?>-100 p-2 rounded-md">
                                        <i class="fas <?= $docCategories[$currentCategory]['pages'][$currentPage]['icon'] ?> text-<?= $docCategories[$currentCategory]['color'] ?>-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h2 class="text-2xl font-bold text-gray-900"><?= htmlspecialchars($docCategories[$currentCategory]['pages'][$currentPage]['title']) ?></h2>
                                        <p class="text-sm text-gray-500"><?= htmlspecialchars($docCategories[$currentCategory]['pages'][$currentPage]['description']) ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="doc-content">
                                <?= $pageContent ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Momentum Documentation</h2>
                            <p class="text-gray-600 mb-6">Select a documentation page from the sidebar to get started.</p>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                                <?php foreach ($docCategories as $categoryId => $category): ?>
                                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition category-<?= $category['color'] ?> border-l-4">
                                        <h3 class="text-xl font-semibold text-gray-800 mb-2 flex items-center">
                                            <i class="fas <?= $category['icon'] ?> text-<?= $category['color'] ?>-500 mr-2"></i>
                                            <?= htmlspecialchars($category['title']) ?>
                                        </h3>
                                        <ul class="mt-4 space-y-2">
                                            <?php foreach ($category['pages'] as $pageId => $page): ?>
                                                <li>
                                                    <a href="?category=<?= $categoryId ?>&page=<?= $pageId ?>" class="text-indigo-600 hover:text-indigo-800 flex items-start">
                                                        <i class="fas <?= $page['icon'] ?> text-gray-400 mr-2 mt-1"></i>
                                                        <span><?= htmlspecialchars($page['title']) ?></span>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight current page in sidebar
            const currentCategory = '<?= $currentCategory ?>';
            const currentPage = '<?= $currentPage ?>';

            if (currentCategory && currentPage) {
                const activeLink = document.querySelector(`a[href="?category=${currentCategory}&page=${currentPage}"]`);
                if (activeLink) {
                    activeLink.scrollIntoView({ block: 'center' });
                }
            }
        });
    </script>
</body>
</html>