<?php
/**
 * Setup Selenium Pinterest Scraper
 * 
 * This script installs the required packages and sets up the Selenium-based Pinterest scraper.
 */

// Include necessary files
require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Environment.php';

// Start output buffering
ob_start();

// Load environment variables
Environment::load();

// Get Chrome profile path
$chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');

// Check if Chrome profile is set
if (empty($chromeProfile)) {
    $chromeProfile = 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 39';
}

// Check if Chrome profile exists
$profileExists = file_exists($chromeProfile);

// Define script paths
$scriptDir = __DIR__ . '/../scripts/pinterest';
$setupScriptPath = $scriptDir . '/setup_selenium.py';
$seleniumScriptPath = $scriptDir . '/selenium_pinterest.py';

// Create the script directory if it doesn't exist
if (!file_exists($scriptDir)) {
    mkdir($scriptDir, 0755, true);
}

// Create the setup script if it doesn't exist
if (!file_exists($setupScriptPath)) {
    $setupScriptContent = <<<'PYTHON'
#!/usr/bin/env python
"""
Setup Selenium Pinterest Scraper

This script installs the required packages for the Selenium-based Pinterest scraper.
"""

import sys
import subprocess
import json

def install_package(package):
    """
    Install a Python package using pip
    
    Args:
        package (str): Package name
    
    Returns:
        bool: True if installation was successful, False otherwise
    """
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """
    Main function
    """
    # Print status
    print(json.dumps({
        "status": "Installing required packages...",
        "progress": 0
    }))
    
    # Install required packages
    packages = ["selenium", "webdriver-manager"]
    total_packages = len(packages)
    installed_packages = 0
    
    for package in packages:
        if install_package(package):
            installed_packages += 1
            print(json.dumps({
                "status": f"Installed {package}",
                "progress": int((installed_packages / total_packages) * 100)
            }))
        else:
            print(json.dumps({
                "status": f"Failed to install {package}",
                "progress": int((installed_packages / total_packages) * 100),
                "error": f"Failed to install {package}"
            }))
    
    # Check if all packages were installed
    if installed_packages == total_packages:
        print(json.dumps({
            "status": "All packages installed successfully",
            "progress": 100,
            "success": True
        }))
    else:
        print(json.dumps({
            "status": f"Installed {installed_packages}/{total_packages} packages",
            "progress": int((installed_packages / total_packages) * 100),
            "success": False
        }))

if __name__ == "__main__":
    main()
PYTHON;
    
    file_put_contents($setupScriptPath, $setupScriptContent);
}

// Check if the Selenium script exists
$seleniumScriptExists = file_exists($seleniumScriptPath);

// Run the setup script if requested
$setupOutput = '';
$setupSuccess = false;

if (isset($_POST['setup']) && $_POST['setup'] === 'true') {
    // Get Python path
    $pythonPath = Environment::get('PYTHON_PATH', 'python');
    
    // Run the setup script
    $command = escapeshellcmd($pythonPath) . ' ' . escapeshellarg($setupScriptPath);
    $setupOutput = shell_exec($command . ' 2>&1');
    
    // Check if the setup was successful
    $setupSuccess = strpos($setupOutput, '"success": true') !== false;
    
    // Create the Selenium script if it doesn't exist
    if (!$seleniumScriptExists) {
        $seleniumScriptContent = file_get_contents(__DIR__ . '/../scripts/pinterest/selenium_pinterest.py');
        if ($seleniumScriptContent) {
            file_put_contents($seleniumScriptPath, $seleniumScriptContent);
            $seleniumScriptExists = true;
        }
    }
}

// Test the Selenium script if requested
$testOutput = '';
$testSuccess = false;

if (isset($_POST['test']) && $_POST['test'] === 'true') {
    // Get Python path
    $pythonPath = Environment::get('PYTHON_PATH', 'python');
    
    // Get Pinterest credentials
    $email = Environment::get('PINTEREST_EMAIL', '');
    $password = Environment::get('PINTEREST_PASSWORD', '');
    
    // Run the Selenium script with the search command
    $command = escapeshellcmd($pythonPath) . ' ' . escapeshellarg($seleniumScriptPath) . ' search --query "travel" --limit 5 --chrome_profile ' . escapeshellarg($chromeProfile);
    $testOutput = shell_exec($command . ' 2>&1');
    
    // Check if the test was successful
    $testSuccess = strpos($testOutput, '"pin_id"') !== false;
}

// Update login.py and search.py to use the Selenium script
if (isset($_POST['update']) && $_POST['update'] === 'true') {
    // Create login.py
    $loginScriptPath = $scriptDir . '/login.py';
    $loginScriptContent = <<<'PYTHON'
#!/usr/bin/env python
"""
Pinterest Login Script using Selenium

This script uses Selenium to log in to Pinterest.
"""

import sys
import os
import json
import subprocess

# Get credentials from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
chrome_profile = sys.argv[5] if len(sys.argv) > 5 else None

# Call the Selenium Pinterest script
script_path = os.path.join(os.path.dirname(__file__), "selenium_pinterest.py")
args = ["python", script_path, "login", "--email", email, "--password", password]
if chrome_profile:
    args.extend(["--chrome_profile", chrome_profile])

try:
    result = subprocess.run(args, capture_output=True, text=True)
    print(result.stdout)
except Exception as e:
    print(json.dumps({
        "success": False,
        "message": f"Error: {str(e)}"
    }))
PYTHON;
    
    file_put_contents($loginScriptPath, $loginScriptContent);
    
    // Create search.py
    $searchScriptPath = $scriptDir . '/search.py';
    $searchScriptContent = <<<'PYTHON'
#!/usr/bin/env python
"""
Pinterest Search Script using Selenium

This script uses Selenium to search Pinterest.
"""

import sys
import os
import json
import subprocess

# Get search parameters from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
scope = sys.argv[5]
query = sys.argv[6]
limit = int(sys.argv[7]) if len(sys.argv) > 7 else 20
chrome_profile = sys.argv[8] if len(sys.argv) > 8 else None

# Call the Selenium Pinterest script
script_path = os.path.join(os.path.dirname(__file__), "selenium_pinterest.py")
args = ["python", script_path, "search", "--query", query, "--scope", scope, "--limit", str(limit)]
if chrome_profile:
    args.extend(["--chrome_profile", chrome_profile])

try:
    result = subprocess.run(args, capture_output=True, text=True)
    print(result.stdout)
except Exception as e:
    print(json.dumps({
        "success": False,
        "message": f"Error: {str(e)}"
    }))
PYTHON;
    
    file_put_contents($searchScriptPath, $searchScriptContent);
    
    // Update the .env file to use the Selenium-based Pinterest scraper
    $envFile = __DIR__ . '/../.env';
    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);
        $envContent = preg_replace('/PINTEREST_API_TYPE=.*/', 'PINTEREST_API_TYPE=unofficial', $envContent);
        $envContent = preg_replace('/DISABLE_PINTEREST_API=.*/', 'DISABLE_PINTEREST_API=false', $envContent);
        file_put_contents($envFile, $envContent);
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Selenium Pinterest Scraper</title>
    
    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .step {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        .step-success {
            background-color: #d1fae5;
            border-left: 4px solid #10b981;
        }
        .step-error {
            background-color: #fee2e2;
            border-left: 4px solid #ef4444;
        }
        .step-warning {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
        }
        .step-info {
            background-color: #e0f2fe;
            border-left: 4px solid #0ea5e9;
        }
        .code {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin-top: 0.5rem;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container">
        <h1 class="text-3xl font-bold mb-6">Setup Selenium Pinterest Scraper</h1>
        
        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Status</h2>
            
            <div class="step <?= $profileExists ? 'step-success' : 'step-error' ?>">
                <strong>Chrome Profile Directory:</strong> <?= htmlspecialchars($chromeProfile) ?>
                <?php if ($profileExists): ?>
                    <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Profile directory exists</p>
                <?php else: ?>
                    <p class="text-red-600"><i class="fas fa-times-circle mr-1"></i> Profile directory does not exist</p>
                <?php endif; ?>
            </div>
            
            <div class="step <?= file_exists($setupScriptPath) ? 'step-success' : 'step-warning' ?>">
                <strong>Setup Script:</strong> <?= htmlspecialchars($setupScriptPath) ?>
                <?php if (file_exists($setupScriptPath)): ?>
                    <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Setup script exists</p>
                <?php else: ?>
                    <p class="text-yellow-600"><i class="fas fa-exclamation-triangle mr-1"></i> Setup script will be created</p>
                <?php endif; ?>
            </div>
            
            <div class="step <?= $seleniumScriptExists ? 'step-success' : 'step-warning' ?>">
                <strong>Selenium Script:</strong> <?= htmlspecialchars($seleniumScriptPath) ?>
                <?php if ($seleniumScriptExists): ?>
                    <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Selenium script exists</p>
                <?php else: ?>
                    <p class="text-yellow-600"><i class="fas fa-exclamation-triangle mr-1"></i> Selenium script will be created</p>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if (isset($_POST['setup']) && $_POST['setup'] === 'true'): ?>
            <div class="section">
                <h2 class="text-xl font-semibold mb-4">Setup Results</h2>
                
                <div class="step <?= $setupSuccess ? 'step-success' : 'step-error' ?>">
                    <strong>Setup Status:</strong>
                    <?php if ($setupSuccess): ?>
                        <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Setup completed successfully</p>
                    <?php else: ?>
                        <p class="text-red-600"><i class="fas fa-times-circle mr-1"></i> Setup failed</p>
                    <?php endif; ?>
                    
                    <?php if (!empty($setupOutput)): ?>
                        <div class="code mt-4"><?= nl2br(htmlspecialchars($setupOutput)) ?></div>
                    <?php endif; ?>
                </div>
                
                <?php if ($setupSuccess): ?>
                    <div class="bg-green-100 p-4 rounded-md mt-4">
                        <p class="text-green-800"><i class="fas fa-check-circle mr-2"></i> The Selenium-based Pinterest scraper has been set up successfully. You can now use it to access real Pinterest data.</p>
                    </div>
                <?php else: ?>
                    <div class="bg-red-100 p-4 rounded-md mt-4">
                        <p class="text-red-800"><i class="fas fa-times-circle mr-2"></i> The setup failed. Please check the output above for more information.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_POST['test']) && $_POST['test'] === 'true'): ?>
            <div class="section">
                <h2 class="text-xl font-semibold mb-4">Test Results</h2>
                
                <div class="step <?= $testSuccess ? 'step-success' : 'step-error' ?>">
                    <strong>Test Status:</strong>
                    <?php if ($testSuccess): ?>
                        <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Test completed successfully</p>
                    <?php else: ?>
                        <p class="text-red-600"><i class="fas fa-times-circle mr-1"></i> Test failed</p>
                    <?php endif; ?>
                    
                    <?php if (!empty($testOutput)): ?>
                        <div class="code mt-4"><?= nl2br(htmlspecialchars(substr($testOutput, 0, 1000))) ?><?= strlen($testOutput) > 1000 ? '...' : '' ?></div>
                    <?php endif; ?>
                </div>
                
                <?php if ($testSuccess): ?>
                    <div class="bg-green-100 p-4 rounded-md mt-4">
                        <p class="text-green-800"><i class="fas fa-check-circle mr-2"></i> The Selenium-based Pinterest scraper is working correctly. You can now update the Pinterest API to use it.</p>
                    </div>
                <?php else: ?>
                    <div class="bg-red-100 p-4 rounded-md mt-4">
                        <p class="text-red-800"><i class="fas fa-times-circle mr-2"></i> The test failed. Please check the output above for more information.</p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_POST['update']) && $_POST['update'] === 'true'): ?>
            <div class="section">
                <h2 class="text-xl font-semibold mb-4">Update Results</h2>
                
                <div class="step step-success">
                    <strong>Update Status:</strong>
                    <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Update completed successfully</p>
                    <p class="mt-2">The following files have been updated:</p>
                    <ul class="list-disc list-inside ml-4 mt-2">
                        <li><?= htmlspecialchars($scriptDir . '/login.py') ?></li>
                        <li><?= htmlspecialchars($scriptDir . '/search.py') ?></li>
                        <li><?= htmlspecialchars(__DIR__ . '/../.env') ?></li>
                    </ul>
                </div>
                
                <div class="bg-green-100 p-4 rounded-md mt-4">
                    <p class="text-green-800"><i class="fas fa-check-circle mr-2"></i> The Pinterest API has been updated to use the Selenium-based Pinterest scraper. You can now use real Pinterest data in your application.</p>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Setup Instructions</h2>
            
            <p class="mb-4">This tool will set up a Selenium-based Pinterest scraper that can extract real data from Pinterest. Follow these steps to set it up:</p>
            
            <ol class="list-decimal list-inside space-y-4 mb-6">
                <li>
                    <strong>Install Required Packages</strong>
                    <p class="text-gray-600 ml-6">Click the "Setup Selenium" button to install the required Python packages (selenium and webdriver-manager).</p>
                </li>
                <li>
                    <strong>Test the Scraper</strong>
                    <p class="text-gray-600 ml-6">Click the "Test Selenium" button to test the Selenium-based Pinterest scraper. This will search for "travel" on Pinterest and return the results.</p>
                </li>
                <li>
                    <strong>Update the Pinterest API</strong>
                    <p class="text-gray-600 ml-6">Click the "Update API" button to update the Pinterest API to use the Selenium-based Pinterest scraper. This will modify the login.py and search.py scripts to use Selenium.</p>
                </li>
            </ol>
            
            <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                <form method="post" action="">
                    <input type="hidden" name="setup" value="true">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                        <i class="fas fa-cog mr-2"></i> Setup Selenium
                    </button>
                </form>
                
                <form method="post" action="">
                    <input type="hidden" name="test" value="true">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                        <i class="fas fa-vial mr-2"></i> Test Selenium
                    </button>
                </form>
                
                <form method="post" action="">
                    <input type="hidden" name="update" value="true">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                        <i class="fas fa-sync mr-2"></i> Update API
                    </button>
                </form>
            </div>
        </div>
        
        <div class="mt-8 flex space-x-4">
            <a href="/momentum/pinterest-api-test.php" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                <i class="fas fa-vial mr-2"></i> API Test
            </a>
            <a href="/momentum/clone/pinterest" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Dashboard
            </a>
        </div>
    </div>
</body>
</html>
<?php
// End output buffering and display the page
echo ob_get_clean();
?>
