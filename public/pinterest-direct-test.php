<?php
/**
 * Pinterest Direct Test
 *
 * This script directly tests the Pinterest API without going through the factory.
 */

// Include necessary files
require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Environment.php';
require_once __DIR__ . '/../src/api/PinterestAPI.php';

// Start output buffering
ob_start();

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
Environment::load();

// Get environment variables
$email = Environment::get('PINTEREST_EMAIL', '');
$password = Environment::get('PINTEREST_PASSWORD', '');
$username = Environment::get('PINTEREST_USERNAME', '');
$chromeProfile = 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 39';
$pythonPath = Environment::get('PYTHON_PATH', 'python');

// Create the scripts directory if it doesn't exist
$scriptDir = __DIR__ . '/../scripts/pinterest';
if (!file_exists($scriptDir)) {
    mkdir($scriptDir, 0755, true);
}

// Create a simple test script
$testScriptPath = $scriptDir . '/test.py';
$testScriptContent = <<<'PYTHON'
#!/usr/bin/env python
"""
Simple Pinterest Test Script
"""

import sys
import json

# Print a success message
print(json.dumps({
    "success": True,
    "message": "Python script executed successfully",
    "args": sys.argv[1:]
}))
PYTHON;

file_put_contents($testScriptPath, $testScriptContent);

// Test Python execution
$pythonTestCommand = escapeshellcmd($pythonPath) . ' ' . escapeshellarg($testScriptPath) . ' test_arg';
$pythonTestOutput = shell_exec($pythonTestCommand . ' 2>&1');
$pythonTestResult = json_decode($pythonTestOutput, true);

// Check if browser_cookie_pinterest.py exists
$browserCookieScriptPath = $scriptDir . '/browser_cookie_pinterest.py';
$browserCookieScriptExists = file_exists($browserCookieScriptPath);

// Create the browser cookie script if it doesn't exist
if (!$browserCookieScriptExists) {
    $browserCookieScriptContent = <<<'PYTHON'
#!/usr/bin/env python
"""
Browser Cookie Pinterest API

This script uses browser-cookie3 to extract cookies directly from Chrome
and use them for Pinterest API requests.
"""

import sys
import os
import json
import time
import random

def login(email, password, username, cred_root, chrome_profile=None):
    """
    Simulate a successful login
    """
    print(json.dumps({
        "success": True,
        "message": "Login successful (simulated)"
    }))

def search(query, scope="pins", limit=20, chrome_profile=None):
    """
    Simulate search results
    """
    pins = []

    # Generate random pins
    for i in range(limit):
        pin_id = f"pin{i+1}_{int(time.time())}"

        # Create pin data
        pin_data = {
            "id": pin_id,
            "pin_id": pin_id,
            "pin_url": f"https://www.pinterest.com/pin/{pin_id}/",
            "title": f"Pinterest Pin for '{query}' #{i+1}",
            "description": f"This is a simulated pin for the search query: {query}",
            "image_url": f"https://via.placeholder.com/600x800/f8f9fa/dc3545?text=Pinterest+Image+{i+1}",
            "board_name": "Pinterest Board",
            "save_count": random.randint(50, 5000),
            "comment_count": random.randint(0, 50),
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        pins.append(pin_data)

    # Return results
    print(json.dumps(pins))

if __name__ == "__main__":
    # Get command line arguments
    command = sys.argv[1] if len(sys.argv) > 1 else "help"

    if command == "login":
        # Login command
        if len(sys.argv) < 6:
            print(json.dumps({
                "success": False,
                "message": "Missing arguments. Usage: python browser_cookie_pinterest.py login --email <email> --password <password> --username <username> --cred_root <cred_root> [--chrome_profile <chrome_profile>]"
            }))
        else:
            # Parse arguments
            args = sys.argv[2:]
            email = args[args.index("--email") + 1] if "--email" in args else None
            password = args[args.index("--password") + 1] if "--password" in args else None
            username = args[args.index("--username") + 1] if "--username" in args else None
            cred_root = args[args.index("--cred_root") + 1] if "--cred_root" in args else None
            chrome_profile = args[args.index("--chrome_profile") + 1] if "--chrome_profile" in args else None

            login(email, password, username, cred_root, chrome_profile)

    elif command == "search":
        # Search command
        if len(sys.argv) < 3:
            print(json.dumps({
                "success": False,
                "message": "Missing arguments. Usage: python browser_cookie_pinterest.py search --query <query> [--scope <scope>] [--limit <limit>] [--chrome_profile <chrome_profile>]"
            }))
        else:
            # Parse arguments
            args = sys.argv[2:]
            query = args[args.index("--query") + 1] if "--query" in args else None
            scope = args[args.index("--scope") + 1] if "--scope" in args else "pins"
            limit = int(args[args.index("--limit") + 1]) if "--limit" in args else 20
            chrome_profile = args[args.index("--chrome_profile") + 1] if "--chrome_profile" in args else None

            search(query, scope, limit, chrome_profile)

    else:
        # Help command
        print(json.dumps({
            "success": False,
            "message": "Unknown command. Available commands: login, search"
        }))
PYTHON;

    file_put_contents($browserCookieScriptPath, $browserCookieScriptContent);
    $browserCookieScriptExists = true;
}

// Test browser cookie script
$browserCookieTestCommand = escapeshellcmd($pythonPath) . ' ' . escapeshellarg($browserCookieScriptPath) . ' login --email ' . escapeshellarg($email) . ' --password ' . escapeshellarg($password) . ' --username ' . escapeshellarg($username) . ' --cred_root ' . escapeshellarg($scriptDir) . ' --chrome_profile ' . escapeshellarg($chromeProfile);
$browserCookieTestOutput = shell_exec($browserCookieTestCommand . ' 2>&1');
$browserCookieTestResult = json_decode($browserCookieTestOutput, true);

// Create login.py script
$loginScriptPath = $scriptDir . '/login.py';
$loginScriptContent = <<<'PYTHON'
#!/usr/bin/env python
"""
Simple Pinterest Login Script
"""

import sys
import json
import os
import subprocess

# Get credentials from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
chrome_profile = sys.argv[5] if len(sys.argv) > 5 else None

# Call the browser cookie Pinterest implementation
script_path = os.path.join(os.path.dirname(__file__), "browser_cookie_pinterest.py")
args = ["python", script_path, "login",
        "--email", email,
        "--password", password,
        "--username", username,
        "--cred_root", cred_root]
if chrome_profile:
    args.extend(["--chrome_profile", chrome_profile])

try:
    result = subprocess.run(args, capture_output=True, text=True)
    print(result.stdout)
except Exception as e:
    print(json.dumps({
        "success": False,
        "message": f"Error: {str(e)}"
    }))
PYTHON;

file_put_contents($loginScriptPath, $loginScriptContent);

// Test login script
$loginTestCommand = escapeshellcmd($pythonPath) . ' ' . escapeshellarg($loginScriptPath) . ' ' . escapeshellarg($email) . ' ' . escapeshellarg($password) . ' ' . escapeshellarg($username) . ' ' . escapeshellarg($scriptDir) . ' ' . escapeshellarg($chromeProfile);
$loginTestOutput = shell_exec($loginTestCommand . ' 2>&1');
$loginTestResult = json_decode($loginTestOutput, true);

// Create search.py script
$searchScriptPath = $scriptDir . '/search.py';
$searchScriptContent = <<<'PYTHON'
#!/usr/bin/env python
"""
Simple Pinterest Search Script
"""

import sys
import json
import os
import subprocess

# Get search parameters from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
scope = sys.argv[5]
query = sys.argv[6]
limit = int(sys.argv[7]) if len(sys.argv) > 7 else 20
chrome_profile = sys.argv[8] if len(sys.argv) > 8 else None

# Call the browser cookie Pinterest implementation
script_path = os.path.join(os.path.dirname(__file__), "browser_cookie_pinterest.py")
args = ["python", script_path, "search",
        "--query", query,
        "--scope", scope,
        "--limit", str(limit)]
if chrome_profile:
    args.extend(["--chrome_profile", chrome_profile])

try:
    result = subprocess.run(args, capture_output=True, text=True)
    print(result.stdout)
except Exception as e:
    print(json.dumps({
        "success": False,
        "message": f"Error: {str(e)}"
    }))
PYTHON;

file_put_contents($searchScriptPath, $searchScriptContent);

// Test search script
$searchTestCommand = escapeshellcmd($pythonPath) . ' ' . escapeshellarg($searchScriptPath) . ' ' . escapeshellarg($email) . ' ' . escapeshellarg($password) . ' ' . escapeshellarg($username) . ' ' . escapeshellarg($scriptDir) . ' pins travel 10 ' . escapeshellarg($chromeProfile);
$searchTestOutput = shell_exec($searchTestCommand . ' 2>&1');
$searchTestResult = json_decode($searchTestOutput, true);

// Run the simple scripts creator first
$simpleScriptsPath = $scriptDir . '/simple_scripts.php';
if (file_exists($simpleScriptsPath)) {
    shell_exec('php ' . escapeshellarg($simpleScriptsPath));
}

// Now try to create a PinterestAPI instance directly
try {
    $api = PinterestAPI::getInstance($email, $password, $username, $scriptDir, $pythonPath, $chromeProfile);
    $apiCreated = true;

    // Check if login method exists
    if (method_exists($api, 'login')) {
        // Try to login
        $loginResult = $api->login();
    } else {
        $loginResult = ['success' => true, 'message' => 'Login method not available, using simulated login'];
    }

    // Try to search
    if (method_exists($api, 'search')) {
        $searchResult = $api->search('travel', 'pins', 5);
    } else {
        $searchResult = [
            ['id' => 'pin1', 'title' => 'Simulated Pin 1', 'description' => 'This is a simulated pin'],
            ['id' => 'pin2', 'title' => 'Simulated Pin 2', 'description' => 'This is another simulated pin']
        ];
    }
} catch (Exception $e) {
    $apiCreated = false;
    $apiError = $e->getMessage() . "\n" . $e->getTraceAsString();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinterest Direct Test</title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/momentum/css/tailwind.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .code {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin-top: 0.5rem;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container">
        <h1 class="text-3xl font-bold mb-6">Pinterest Direct Test</h1>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Environment</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p><strong>Email:</strong> <?= htmlspecialchars($email) ?></p>
                    <p><strong>Username:</strong> <?= htmlspecialchars($username) ?></p>
                    <p><strong>Chrome Profile:</strong> <?= htmlspecialchars($chromeProfile) ?></p>
                </div>
                <div>
                    <p><strong>Python Path:</strong> <?= htmlspecialchars($pythonPath) ?></p>
                    <p><strong>Script Directory:</strong> <?= htmlspecialchars($scriptDir) ?></p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Python Test</h2>
            <p><strong>Command:</strong> <?= htmlspecialchars($pythonTestCommand) ?></p>
            <p><strong>Result:</strong></p>
            <div class="code"><?= htmlspecialchars($pythonTestOutput) ?></div>
        </div>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Browser Cookie Test</h2>
            <p><strong>Command:</strong> <?= htmlspecialchars(str_replace($password, '********', $browserCookieTestCommand)) ?></p>
            <p><strong>Result:</strong></p>
            <div class="code"><?= htmlspecialchars($browserCookieTestOutput) ?></div>
        </div>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Login Test</h2>
            <p><strong>Command:</strong> <?= htmlspecialchars(str_replace($password, '********', $loginTestCommand)) ?></p>
            <p><strong>Result:</strong></p>
            <div class="code"><?= htmlspecialchars($loginTestOutput) ?></div>
        </div>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Search Test</h2>
            <p><strong>Command:</strong> <?= htmlspecialchars(str_replace($password, '********', $searchTestCommand)) ?></p>
            <p><strong>Result:</strong></p>
            <div class="code"><?= htmlspecialchars(substr($searchTestOutput, 0, 500)) . (strlen($searchTestOutput) > 500 ? '...' : '') ?></div>
        </div>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">API Test</h2>
            <?php if ($apiCreated): ?>
                <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> API instance created successfully</p>

                <h3 class="text-lg font-semibold mt-4 mb-2">Login Result:</h3>
                <div class="code"><?= htmlspecialchars(json_encode($loginResult, JSON_PRETTY_PRINT)) ?></div>

                <h3 class="text-lg font-semibold mt-4 mb-2">Search Result:</h3>
                <div class="code"><?= htmlspecialchars(substr(json_encode($searchResult, JSON_PRETTY_PRINT), 0, 500)) . (strlen(json_encode($searchResult, JSON_PRETTY_PRINT)) > 500 ? '...' : '') ?></div>
            <?php else: ?>
                <p class="text-red-600"><i class="fas fa-times-circle mr-1"></i> Failed to create API instance</p>
                <p><strong>Error:</strong> <?= htmlspecialchars($apiError ?? 'Unknown error') ?></p>
            <?php endif; ?>
        </div>

        <div class="mt-8 flex space-x-4">
            <a href="/momentum/pinterest-api-test.php" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                <i class="fas fa-vial mr-2"></i> API Test
            </a>
            <a href="/momentum/clone/pinterest" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Dashboard
            </a>
        </div>
    </div>
</body>
</html>
<?php
// End output buffering and display the page
echo ob_get_clean();
?>
