/**
 * Master Dropdown Fix CSS
 *
 * Comprehensive CSS for all dropdown menus in the application.
 * Ensures consistent styling and smooth transitions.
 *
 * Version 2.0 - Enhanced with better hover detection and transitions
 */

/* Dropdown overlay for capturing clicks */
.dropdown-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9998 !important;
    background-color: transparent !important;
    display: none !important;
    pointer-events: none !important;
}

.dropdown-overlay.active {
    display: block !important;
    pointer-events: auto !important;
}

/* Base styles for all dropdown menus */
.dropdown-menu {
    position: fixed !important;
    z-index: 9999 !important;
    background-color: white !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    min-width: 14rem !important;
    padding: 0.5rem 0 !important;
    transition: opacity 0.15s ease-in-out !important;
    opacity: 0 !important;
    visibility: hidden !important;
    display: none !important;
    will-change: opacity !important; /* Optimize for animations */
}

/* Dark mode for dropdown menus */
.dark .dropdown-menu {
    background-color: #374151 !important; /* dark:bg-gray-700 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15) !important;
}

/* Dropdown menu items */
.dropdown-menu a {
    display: block !important;
    padding: 0.5rem 1rem !important;
    color: #4B5563 !important; /* text-gray-700 */
    font-size: 0.875rem !important; /* text-sm */
    white-space: nowrap !important;
    width: 100% !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    transition: background-color 0.15s ease-in-out !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Last menu item (no border) */
.dropdown-menu a:last-child {
    border-bottom: none !important;
}

/* Dark mode for menu items */
.dark .dropdown-menu a {
    color: #E5E7EB !important; /* dark:text-gray-200 */
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* Hover state for menu items */
.dropdown-menu a:hover {
    background-color: #F3F4F6 !important; /* hover:bg-gray-100 */
}

/* Dark mode hover state for menu items */
.dark .dropdown-menu a:hover {
    background-color: #4B5563 !important; /* dark:hover:bg-gray-600 */
}

/* Add a small gap between button and menu for better hover behavior */
.dropdown-menu::before {
    content: '' !important;
    position: absolute !important;
    top: -10px !important;
    left: 0 !important;
    right: 0 !important;
    height: 10px !important;
    background-color: transparent !important;
}

/* Add a visual indicator (arrow) to the dropdown menu */
.dropdown-menu::after {
    content: '' !important;
    position: absolute !important;
    top: -5px !important;
    left: 20px !important;
    width: 10px !important;
    height: 10px !important;
    transform: rotate(45deg) !important;
    background-color: inherit !important;
    box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05) !important;
    z-index: -1 !important;
}

/* Ensure dropdown containers have position relative */
#adhd-dropdown,
#productivity-dropdown,
#tools-dropdown,
.relative {
    position: relative !important;
}

/* Ensure dropdown buttons have proper cursor and hover effect */
#adhd-dropdown-button,
#productivity-dropdown-button,
#tools-dropdown-button,
#user-menu-button {
    cursor: pointer !important;
    transition: opacity 0.15s ease-in-out !important;
}

#adhd-dropdown-button:hover,
#productivity-dropdown-button:hover,
#tools-dropdown-button:hover,
#user-menu-button:hover {
    opacity: 0.9 !important;
}

/* Fix for z-index stacking context */
.bg-white.dark\:bg-gray-800.shadow {
    z-index: 50 !important;
    position: relative !important;
}

/* Specific styles for each dropdown menu */
#adhd-dropdown-menu,
#productivity-dropdown-menu,
#tools-dropdown-menu,
#user-dropdown-menu {
    max-height: 400px !important;
    overflow-y: auto !important;
}

/* User dropdown menu positioning */
#user-dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

#user-dropdown-menu::after {
    left: auto !important;
    right: 20px !important;
}


