<?php
/**
 * Help Center Routes
 *
 * This file contains routes for the help center pages.
 */

// Include the necessary files
require_once '../src/utils/Database.php';
require_once '../src/controllers/BaseController.php';
require_once '../src/controllers/HelpController.php';

// Start the session
session_start();

// Create a new instance of the HelpController
$controller = new HelpController();

// Get the requested path
$path = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '/';

// Route the request
switch ($path) {
    case '/':
    case '/index':
        $controller->index();
        break;
    case '/user-guide':
        $controller->userGuide();
        break;
    case '/feature-overview':
        $controller->featureOverview();
        break;
    case '/tutorials':
        $controller->tutorials();
        break;
    case '/faq':
        $controller->faq();
        break;
    case '/troubleshooting':
        $controller->troubleshooting();
        break;
    case '/contact-support':
        $controller->contactSupport();
        break;
    case '/adhd-project-planning-guide':
        $controller->adhdProjectPlanningGuide();
        break;
    case '/adhd-project-planning-guide-md':
        $controller->adhdProjectPlanningGuideMd();
        break;
    case '/dashboard-redesign-plan':
        $controller->dashboardRedesignPlan();
        break;
    case '/online-income-features':
        $controller->onlineIncomeFeatures();
        break;
    case '/online-income-strategies-guide':
        $controller->onlineIncomeStrategiesGuide();
        break;
    case '/online-income-strategies-guide-md':
        $controller->onlineIncomeStrategiesGuideMd();
        break;
    case '/online-business-features':
        $controller->onlineBusinessFeatures();
        break;
    default:
        // Redirect to the help center home page by default
        header('Location: /momentum/public/help-routes.php');
        exit;
}
