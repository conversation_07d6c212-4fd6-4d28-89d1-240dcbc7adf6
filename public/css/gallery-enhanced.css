/**
 * Enhanced Gallery Styles
 *
 * Improved UI/UX for gallery views with better alignment and layout
 */

/* ===== Container Improvements ===== */

.tools-container,
.quick-capture-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
}

/* ===== Header Improvements ===== */

.gallery-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dark .gallery-header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: #334155;
}

.gallery-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.dark .gallery-title {
    color: #f1f5f9;
}

.gallery-subtitle {
    color: #64748b;
    font-size: 1rem;
}

.dark .gallery-subtitle {
    color: #94a3b8;
}

/* ===== Filter Bar Improvements ===== */

.filter-container {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.dark .filter-container {
    background: #1e293b;
    border-color: #334155;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1.5rem;
    justify-content: center;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 200px;
}

.filter-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    white-space: nowrap;
}

.dark .filter-label {
    color: #d1d5db;
}

.filter-select,
.filter-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.dark .filter-select,
.dark .filter-input {
    background: #374151;
    border-color: #4b5563;
    color: white;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-button {
    padding: 0.75rem 1.5rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-button:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

/* ===== Gallery Grid Improvements ===== */

.capture-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    padding: 1rem;
}

/* ===== Card Improvements ===== */

.capture-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    min-height: 280px;
    display: flex;
    flex-direction: column;
}

.dark .capture-card {
    background: #1e293b;
    border-color: #334155;
}

.capture-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #3b82f6;
}

.capture-card.pinned {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.dark .capture-card.pinned {
    background: linear-gradient(135deg, #451a03 0%, #1e293b 100%);
    border-color: #f59e0b;
}

.capture-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    position: relative;
}

.dark .capture-card-header {
    border-bottom-color: #334155;
}

.capture-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.dark .capture-card-title {
    color: #f1f5f9;
}

.capture-card-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.capture-type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.capture-type-badge.screenshot {
    background: #dbeafe;
    color: #1e40af;
}

.capture-type-badge.note {
    background: #dcfce7;
    color: #166534;
}

.capture-type-badge.voice {
    background: #fed7aa;
    color: #9a3412;
}

.dark .capture-type-badge.screenshot {
    background: #1e3a8a;
    color: #93c5fd;
}

.dark .capture-type-badge.note {
    background: #14532d;
    color: #86efac;
}

.dark .capture-type-badge.voice {
    background: #9a3412;
    color: #fdba74;
}

.pin-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    color: #f59e0b;
    font-size: 1.25rem;
}

.capture-card-body {
    padding: 0 1.5rem 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.capture-preview {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.capture-content {
    color: #4b5563;
    line-height: 1.6;
    margin-bottom: 1rem;
    flex: 1;
}

.dark .capture-content {
    color: #9ca3af;
}

.capture-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.capture-tag {
    padding: 0.25rem 0.75rem;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.dark .capture-tag {
    background: #374151;
    color: #9ca3af;
}

.capture-card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8fafc;
}

.dark .capture-card-footer {
    border-top-color: #334155;
    background: #0f172a;
}

.capture-actions {
    display: flex;
    gap: 0.5rem;
}

.capture-action-btn {
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.dark .capture-action-btn {
    background: #374151;
    border-color: #4b5563;
    color: #9ca3af;
}

.capture-action-btn:hover {
    background: #f3f4f6;
    color: #374151;
    transform: translateY(-1px);
}

.dark .capture-action-btn:hover {
    background: #4b5563;
    color: #d1d5db;
}

.capture-action-btn.pin {
    color: #f59e0b;
    border-color: #f59e0b;
}

.capture-action-btn.pin:hover {
    background: #fffbeb;
}

.capture-action-btn.delete {
    color: #ef4444;
    border-color: #ef4444;
}

.capture-action-btn.delete:hover {
    background: #fef2f2;
}

/* ===== Empty State Improvements ===== */

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1rem;
    border: 2px dashed #d1d5db;
    margin: 2rem 0;
}

.dark .empty-state {
    background: #1e293b;
    border-color: #4b5563;
}

.empty-state-icon {
    font-size: 4rem;
    color: #9ca3af;
    margin-bottom: 1.5rem;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.75rem;
}

.dark .empty-state-title {
    color: #f1f5f9;
}

.empty-state-description {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 1.125rem;
}

.dark .empty-state-description {
    color: #9ca3af;
}

/* ===== Responsive Design ===== */

@media (max-width: 1024px) {
    .capture-gallery {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .filter-row {
        justify-content: flex-start;
    }
}

@media (max-width: 768px) {
    .tools-container,
    .quick-capture-container {
        padding: 1rem;
    }

    .capture-gallery {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .gallery-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .capture-card-header,
    .capture-card-body,
    .capture-card-footer {
        padding: 1rem;
    }

    .capture-actions {
        gap: 0.25rem;
    }

    .capture-action-btn {
        padding: 0.375rem;
        font-size: 0.875rem;
    }
}

/* ===== Modal Improvements ===== */

#quickCaptureModal {
    backdrop-filter: blur(4px);
}

#quickCaptureModal .relative {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid #e5e7eb;
    max-width: 28rem;
    width: 90%;
}

.dark #quickCaptureModal .relative {
    background: #1e293b;
    border-color: #334155;
}

#quickCaptureModal .space-y-3 > * {
    transition: all 0.2s ease;
}

#quickCaptureModal .space-y-3 > *:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.2);
}

/* ===== Load More Button Improvements ===== */

.load-more-container {
    text-align: center;
    margin-top: 3rem;
    padding: 2rem;
}

.load-more-btn {
    display: inline-flex;
    align-items: center;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    font-weight: 600;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.load-more-btn:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== Accessibility Improvements ===== */

.capture-card:focus,
.filter-select:focus,
.filter-input:focus,
.filter-button:focus,
.capture-action-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.capture-card:focus:not(:focus-visible),
.filter-select:focus:not(:focus-visible),
.filter-input:focus:not(:focus-visible),
.filter-button:focus:not(:focus-visible),
.capture-action-btn:focus:not(:focus-visible) {
    outline: none;
}

/* ===== Animation Improvements ===== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.capture-card {
    animation: fadeInUp 0.3s ease-out;
}

.capture-card:nth-child(1) { animation-delay: 0.1s; }
.capture-card:nth-child(2) { animation-delay: 0.2s; }
.capture-card:nth-child(3) { animation-delay: 0.3s; }
.capture-card:nth-child(4) { animation-delay: 0.4s; }
.capture-card:nth-child(5) { animation-delay: 0.5s; }
.capture-card:nth-child(6) { animation-delay: 0.6s; }

/* ===== Loading States ===== */

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.dark .loading-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== Utility Classes ===== */

.text-balance {
    text-wrap: balance;
}

.transition-all-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.dark .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
}
