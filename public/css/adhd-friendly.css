/**
 * ADHD-Friendly CSS
 * 
 * This stylesheet provides ADHD-optimized styles for the Momentum dashboard.
 * It focuses on reducing visual noise, improving focus, enhancing readability,
 * and supporting executive function challenges.
 */

/* ===== Base Styles ===== */

/* Improved focus styles for all interactive elements */
:focus {
    outline: 3px solid rgba(14, 165, 233, 0.5) !important; /* Primary color with transparency */
    outline-offset: 2px !important;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ===== Typography Enhancements ===== */

/* Improved line height for better readability */
body {
    line-height: 1.6;
}

/* Stronger headings for better visual hierarchy */
h1, h2, h3 {
    font-weight: 700;
    letter-spacing: -0.01em;
}

/* Slightly larger text for better readability */
.text-sm {
    font-size: 0.9rem !important;
}

/* ===== Layout Improvements ===== */

/* Add subtle container shadows for depth perception */
.shadow {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.shadow-lg {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Improved spacing between elements */
.gap-6 {
    gap: 1.75rem !important;
}

/* Slightly more padding in containers */
.px-4 {
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
}

.py-4 {
    padding-top: 1.25rem !important;
    padding-bottom: 1.25rem !important;
}

/* ===== Widget Enhancements ===== */

/* Current Focus Widget - Make it stand out more */
[data-widget="current-focus-widget"] {
    border-left-width: 6px !important;
    transition: transform 0.2s ease-in-out;
}

/* Subtle hover effect for widgets */
[data-widget]:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* Task items hover effect */
.task-item {
    transition: background-color 0.15s ease-in-out;
}

.task-item:hover {
    background-color: rgba(14, 165, 233, 0.05);
}

/* Make the current focus task stand out more */
.task-item.current-focus {
    border-left-width: 4px !important;
    padding-left: 0.75rem !important;
}

/* ===== Button Enhancements ===== */

/* Larger touch targets for buttons */
button, 
.btn,
[type="button"] {
    min-height: 2.5rem;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

/* More prominent primary buttons */
.bg-primary-600 {
    background-color: rgb(2, 132, 199) !important; /* sky-700 */
}

.bg-primary-700 {
    background-color: rgb(3, 105, 161) !important; /* sky-800 */
}

/* More visible hover states */
.hover\:bg-primary-700:hover {
    background-color: rgb(3, 105, 161) !important; /* sky-800 */
}

/* ===== Form Controls ===== */

/* Larger checkboxes for easier interaction */
input[type="checkbox"] {
    width: 1.25rem !important;
    height: 1.25rem !important;
}

/* ===== Color Enhancements ===== */

/* Softer background colors in light mode */
.bg-white {
    background-color: #fcfcfc !important;
}

.bg-gray-50 {
    background-color: #f8f9fa !important;
}

/* Warmer dark mode colors to reduce eye strain */
.dark .bg-gray-800 {
    background-color: #1e2734 !important;
}

.dark .bg-gray-900 {
    background-color: #111827 !important;
}

/* ===== Status Indicators ===== */

/* More prominent status badges */
.inline-flex.items-center.px-2\.5.py-0\.5.rounded-full {
    font-weight: 600 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

/* ===== Focus Mode Enhancements ===== */

/* Focus mode - reduce opacity of non-essential elements */
body.focus-mode [data-widget]:not([data-widget="current-focus-widget"]):not([data-widget="today-tasks"]):not([data-widget="keyboard-shortcuts"]) {
    opacity: 0.5;
    filter: grayscale(30%);
}

/* ===== Animations ===== */

/* Subtle pulse animation for the current focus widget when empty */
@keyframes subtle-pulse {
    0% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4); }
    70% { box-shadow: 0 0 0 6px rgba(14, 165, 233, 0); }
    100% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0); }
}

#current-focus-empty {
    animation: subtle-pulse 2s infinite;
}

/* Task completion animation */
@keyframes task-complete {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.task-complete-animation {
    animation: task-complete 0.5s ease-in-out;
}

/* ===== Accessibility Enhancements ===== */

/* Improved color contrast for text */
.text-gray-500 {
    color: #64748b !important; /* slate-500 - darker than default gray-500 */
}

.dark .text-gray-400 {
    color: #94a3b8 !important; /* slate-400 - lighter than default gray-400 in dark mode */
}

/* ===== Responsive Improvements ===== */

/* Better small screen layout */
@media (max-width: 640px) {
    .py-6 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
    
    .gap-6 {
        gap: 1rem !important;
    }
    
    .mb-6 {
        margin-bottom: 1rem !important;
    }
}

/* ===== Print Styles ===== */

@media print {
    [data-widget]:not([data-widget="current-focus-widget"]):not([data-widget="today-tasks"]):not([data-widget="overdue-tasks"]) {
        display: none !important;
    }
    
    body {
        background-color: white !important;
        color: black !important;
    }
    
    .shadow, .shadow-lg {
        box-shadow: none !important;
    }
}
