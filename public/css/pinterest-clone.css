/**
 * Pinterest Clone Research Tool Styles
 */

/* Pinterest-inspired color scheme */
:root {
    --pinterest-red: #e60023;
    --pinterest-red-hover: #ad081b;
    --pinterest-dark-red: #8e0000;
    --pinterest-light-red: #ffccd3;
    --pinterest-gray: #767676;
    --pinterest-light-gray: #efefef;
    --pinterest-dark-gray: #333333;
}

/* Pinterest-style card grid */
.pinterest-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    grid-auto-rows: 10px;
    grid-gap: 16px;
}

.pinterest-grid .pin-card {
    grid-row-end: span 40;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 16px;
    overflow: hidden;
}

.pinterest-grid .pin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.pinterest-grid .pin-card img {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 16px 16px 0 0;
}

/* Pinterest-style buttons */
.pinterest-btn {
    background-color: var(--pinterest-red);
    color: white;
    border-radius: 24px;
    padding: 12px 16px;
    font-weight: 600;
    transition: background-color 0.2s ease-in-out;
}

.pinterest-btn:hover {
    background-color: var(--pinterest-red-hover);
}

.pinterest-btn-outline {
    background-color: white;
    color: var(--pinterest-red);
    border: 2px solid var(--pinterest-red);
    border-radius: 24px;
    padding: 10px 16px;
    font-weight: 600;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.pinterest-btn-outline:hover {
    background-color: var(--pinterest-light-red);
}

/* Pinterest-style search input */
.pinterest-search {
    border-radius: 24px;
    padding: 12px 16px;
    border: 2px solid var(--pinterest-light-gray);
    width: 100%;
    transition: border-color 0.2s ease-in-out;
}

.pinterest-search:focus {
    border-color: var(--pinterest-red);
    outline: none;
}

/* Pinterest-style badges */
.pinterest-badge {
    background-color: var(--pinterest-light-gray);
    color: var(--pinterest-dark-gray);
    border-radius: 24px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 600;
}

.pinterest-badge-red {
    background-color: var(--pinterest-light-red);
    color: var(--pinterest-dark-red);
}

/* Pinterest-style modal */
.pinterest-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
}

.pinterest-modal-content {
    background-color: white;
    border-radius: 16px;
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Pinterest-style loading animation */
.pinterest-loading {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}

.pinterest-loading div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: var(--pinterest-red);
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

.pinterest-loading div:nth-child(1) {
    left: 8px;
    animation: pinterest-loading1 0.6s infinite;
}

.pinterest-loading div:nth-child(2) {
    left: 8px;
    animation: pinterest-loading2 0.6s infinite;
}

.pinterest-loading div:nth-child(3) {
    left: 32px;
    animation: pinterest-loading2 0.6s infinite;
}

.pinterest-loading div:nth-child(4) {
    left: 56px;
    animation: pinterest-loading3 0.6s infinite;
}

@keyframes pinterest-loading1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes pinterest-loading3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}

@keyframes pinterest-loading2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}

/* Dark mode adjustments */
.dark .pinterest-search {
    background-color: #1f2937;
    border-color: #374151;
    color: white;
}

.dark .pinterest-modal-content {
    background-color: #1f2937;
    color: white;
}

.dark .pinterest-btn-outline {
    background-color: #1f2937;
    color: var(--pinterest-light-red);
}

.dark .pinterest-badge {
    background-color: #374151;
    color: #d1d5db;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .pinterest-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* Masonry layout for pins */
.masonry-grid {
    column-count: 3;
    column-gap: 16px;
}

.masonry-grid .pin-card {
    break-inside: avoid;
    margin-bottom: 16px;
}

@media (max-width: 768px) {
    .masonry-grid {
        column-count: 2;
    }
}

@media (max-width: 640px) {
    .masonry-grid {
        column-count: 1;
    }
}

/* Tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
    margin-bottom: 5px;
}

.tooltip:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
    z-index: 10;
}
