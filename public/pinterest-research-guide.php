<?php
/**
 * Pinterest Research Guide
 *
 * This page provides a practical guide for using the Enhanced Pinterest Scraper for real research.
 */

require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Session.php';
require_once __DIR__ . '/../src/utils/Environment.php';

// Start the session
Session::start();

// Check if the user is logged in
if (!Session::isLoggedIn()) {
    // Set flash message
    Session::setFlash('error', 'Please log in to access the Pinterest Research Guide');
    
    // Redirect to login page
    header('Location: /momentum/login');
    exit;
}

// Page title
$pageTitle = 'Pinterest Research Guide';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <link rel="stylesheet" href="/momentum/css/tailwind.css">
    <link rel="stylesheet" href="/momentum/css/fontawesome.css">
    <script src="/momentum/js/alpine.js" defer></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <div class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold mb-2">Pinterest Research Guide</h1>
            <p class="text-gray-600 dark:text-gray-400">
                A practical guide for using the Enhanced Pinterest Scraper for real research.
            </p>
        </div>
        
        <!-- Introduction -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Introduction</h2>
            
            <p class="mb-4">
                The Enhanced Pinterest Scraper is a powerful tool for conducting research on Pinterest content. 
                It allows you to extract real data from Pinterest, including pin titles, descriptions, save counts, 
                board names, and high-resolution images.
            </p>
            
            <p class="mb-4">
                This guide will walk you through the process of using the scraper for real research purposes, 
                from setting up the environment to analyzing the results.
            </p>
        </div>
        
        <!-- Step 1: Setup -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Step 1: Set Up the Environment</h2>
            
            <ol class="list-decimal pl-6 mb-4 space-y-2">
                <li>Go to the <a href="/momentum/test-enhanced-pinterest-scraper.php" class="text-blue-600 dark:text-blue-400 hover:underline">Test Page</a></li>
                <li>Click the "Install Python Packages" button to install all required dependencies</li>
                <li>Verify in the Debug Information section that all packages are installed correctly</li>
            </ol>
            
            <div class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 p-4 rounded">
                <p><i class="fas fa-info-circle mr-2"></i> The scraper requires Python with the following packages: selenium, webdriver-manager, requests, beautifulsoup4, and browser-cookie3.</p>
            </div>
        </div>
        
        <!-- Step 2: Configure -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Step 2: Configure Your Chrome Profile</h2>
            
            <ol class="list-decimal pl-6 mb-4 space-y-2">
                <li>Go to the <a href="/momentum/setup-enhanced-pinterest.php" class="text-blue-600 dark:text-blue-400 hover:underline">Setup Page</a></li>
                <li>Enter your Chrome profile path (e.g., <code class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 1</code>)</li>
                <li>Click "Save Configuration"</li>
            </ol>
            
            <div class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 p-4 rounded">
                <p><i class="fas fa-exclamation-triangle mr-2"></i> Make sure you're logged into Pinterest in the Chrome profile you specify.</p>
            </div>
        </div>
        
        <!-- Step 3: Research Planning -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Step 3: Plan Your Research</h2>
            
            <p class="mb-4">Before scraping, define your research goals:</p>
            
            <ul class="list-disc pl-6 mb-4 space-y-2">
                <li><strong>Trend Analysis</strong>: Identify trending content in specific niches</li>
                <li><strong>Content Strategy</strong>: Analyze successful pins to inform your content creation</li>
                <li><strong>Competitor Research</strong>: Study what works for competitors in your space</li>
                <li><strong>Audience Insights</strong>: Understand what resonates with your target audience</li>
            </ul>
            
            <p class="mb-4">Example research questions:</p>
            
            <ul class="list-disc pl-6 mb-4 space-y-2">
                <li>What types of home decor pins get the most saves?</li>
                <li>What are the common elements in viral recipe pins?</li>
                <li>How do successful fashion pins differ from less popular ones?</li>
                <li>What color schemes are trending in interior design pins?</li>
            </ul>
        </div>
        
        <!-- Step 4: Data Collection -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Step 4: Collect Data</h2>
            
            <p class="mb-4">Use the Enhanced Pinterest Scraper to collect data:</p>
            
            <ol class="list-decimal pl-6 mb-4 space-y-2">
                <li>Go to the <a href="/momentum/test-enhanced-pinterest-scraper.php" class="text-blue-600 dark:text-blue-400 hover:underline">Test Page</a></li>
                <li>In the "New Scrape" section:
                    <ul class="list-disc pl-6 mt-2 space-y-1">
                        <li>Enter a specific research query (e.g., "sustainable home decor", "minimalist kitchen", "bohemian fashion")</li>
                        <li>Set the limit to 5-10 for testing, or higher for more comprehensive research</li>
                        <li>Click "Run New Scrape"</li>
                    </ul>
                </li>
                <li>Review the results and download any relevant images</li>
                <li>Repeat with different queries to build a comprehensive dataset</li>
            </ol>
            
            <div class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 p-4 rounded">
                <p><i class="fas fa-lightbulb mr-2"></i> <strong>Pro Tip:</strong> Use specific, long-tail queries for more targeted results. For example, instead of "home decor", try "scandinavian minimalist living room" or "industrial farmhouse kitchen decor".</p>
            </div>
        </div>
        
        <!-- Step 5: Data Analysis -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Step 5: Analyze the Data</h2>
            
            <p class="mb-4">Once you've collected data, analyze it to extract insights:</p>
            
            <ul class="list-disc pl-6 mb-4 space-y-2">
                <li><strong>Quantitative Analysis</strong>:
                    <ul class="list-disc pl-6 mt-2 space-y-1">
                        <li>Compare save counts across different types of content</li>
                        <li>Identify patterns in popular pins (e.g., image style, color schemes)</li>
                        <li>Track trends over time by repeating searches periodically</li>
                    </ul>
                </li>
                <li><strong>Qualitative Analysis</strong>:
                    <ul class="list-disc pl-6 mt-2 space-y-1">
                        <li>Analyze pin titles and descriptions for common themes or keywords</li>
                        <li>Study visual elements (composition, colors, text overlays)</li>
                        <li>Examine board names to understand content categorization</li>
                    </ul>
                </li>
            </ul>
            
            <div class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 p-4 rounded">
                <p><i class="fas fa-chart-bar mr-2"></i> <strong>Analysis Example:</strong> If researching kitchen decor trends, you might find that pins with white kitchens get 30% more saves than dark kitchens, or that pins with the word "budget" in the title perform better than those with "luxury".</p>
            </div>
        </div>
        
        <!-- Step 6: Apply Insights -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Step 6: Apply Research Insights</h2>
            
            <p class="mb-4">Use the insights from your research to inform your strategy:</p>
            
            <ul class="list-disc pl-6 mb-4 space-y-2">
                <li><strong>Content Creation</strong>: Create content based on successful patterns you've identified</li>
                <li><strong>SEO Optimization</strong>: Use popular keywords in your pin titles and descriptions</li>
                <li><strong>Visual Strategy</strong>: Adopt visual elements that perform well in your niche</li>
                <li><strong>Board Organization</strong>: Structure your boards based on popular categorizations</li>
            </ul>
            
            <div class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 p-4 rounded">
                <p><i class="fas fa-rocket mr-2"></i> <strong>Success Story:</strong> A home decor blogger used this research method to identify that "budget-friendly Scandinavian" was trending. They created a series of pins on this topic, resulting in a 250% increase in traffic to their website.</p>
            </div>
        </div>
        
        <!-- Ethical Considerations -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">Ethical Considerations</h2>
            
            <p class="mb-4">When using the scraper for research, keep these ethical guidelines in mind:</p>
            
            <ul class="list-disc pl-6 mb-4 space-y-2">
                <li>Respect Pinterest's terms of service and rate limits</li>
                <li>Use the human-like behavior option to avoid overloading Pinterest's servers</li>
                <li>Don't republish scraped content without proper attribution</li>
                <li>Use the data for research and inspiration, not direct copying</li>
                <li>Keep scraped data secure and respect user privacy</li>
            </ul>
            
            <div class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 p-4 rounded">
                <p><i class="fas fa-balance-scale mr-2"></i> <strong>Important:</strong> This tool is provided for research purposes only. Always respect copyright and intellectual property rights when using the data you collect.</p>
            </div>
        </div>
        
        <!-- Navigation Links -->
        <div class="mt-8 text-center">
            <a href="/momentum/test-enhanced-pinterest-scraper.php" class="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-4">
                <i class="fas fa-vial mr-2"></i> Go to Test Page
            </a>
            <a href="/momentum/setup-enhanced-pinterest.php" class="inline-block px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mr-4">
                <i class="fas fa-cog mr-2"></i> Go to Setup
            </a>
            <a href="/momentum/clone/pinterest" class="inline-block px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Clone
            </a>
        </div>
    </div>
    
    <script>
        // Simple script to enhance the user experience
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to all links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        });
    </script>
</body>
</html>
