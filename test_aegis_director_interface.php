<?php
/**
 * Test Aegis Director Interface
 * 
 * This script tests the Aegis Director interface.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AegisDirectorProjectManager.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/AgentBrigadeRole.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();
$projectModel = new Project();
$projectTaskModel = new Task();
$projectManager = new AegisDirectorProjectManager();
$assignmentModel = new ProjectAgentAssignment();
$brigadeRoleModel = new AgentBrigadeRole();

// Check database connection
if ($db) {
    echo "Database connection successful<br>";
} else {
    echo "Database connection failed<br>";
    exit;
}

// Get the Aegis Director agent
$agents = $agentModel->getUserAgents(1);
$aegisDirectorId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'Aegis Director') {
        $aegisDirectorId = $agent['id'];
        break;
    }
}

if (!$aegisDirectorId) {
    echo "Aegis Director agent not found. Running create_aegis_director_agent.php...<br>";
    include 'create_aegis_director_agent.php';
    
    // Check again
    $agents = $agentModel->getUserAgents(1);
    foreach ($agents as $agent) {
        if ($agent['name'] === 'Aegis Director') {
            $aegisDirectorId = $agent['id'];
            break;
        }
    }
    
    if (!$aegisDirectorId) {
        echo "Failed to create Aegis Director agent.<br>";
        exit;
    }
}

echo "Found Aegis Director agent with ID: {$aegisDirectorId}<br>";

// Get brigade roles
$contentCreationRoles = $brigadeRoleModel->getBrigadeRoles('content_creation');
$leadGenerationRoles = $brigadeRoleModel->getBrigadeRoles('lead_generation');
$customerSupportRoles = $brigadeRoleModel->getBrigadeRoles('customer_support');
$dataAnalysisRoles = $brigadeRoleModel->getBrigadeRoles('data_analysis');

echo "Found " . count($contentCreationRoles) . " Content Creation roles<br>";
echo "Found " . count($leadGenerationRoles) . " Lead Generation roles<br>";
echo "Found " . count($customerSupportRoles) . " Customer Support roles<br>";
echo "Found " . count($dataAnalysisRoles) . " Data Analysis roles<br>";

// Create a test interaction with Aegis Director
$interactionId = $interactionModel->createInteraction([
    'agent_id' => $aegisDirectorId,
    'user_id' => 1,
    'interaction_type' => 'command',
    'content' => "I want to create a Content Creation Brigade to generate blog posts for my website.",
    'created_at' => date('Y-m-d H:i:s')
]);

if ($interactionId) {
    echo "Created interaction with ID: {$interactionId}<br>";
    
    // Update the interaction with a response
    $response = "I'll help you create a Content Creation Brigade for your blog posts. This brigade will consist of specialized agents working together to produce high-quality content efficiently.\n\n";
    $response .= "The Content Creation Brigade includes these roles:\n";
    
    foreach ($contentCreationRoles as $role) {
        $response .= "- {$role['name']}: {$role['description']}\n";
    }
    
    $response .= "\nTo get started, I'll create a new project for your Content Creation Brigade. What's the name of your website, and what type of blog posts do you want to create?";
    
    $interactionModel->updateInteraction($interactionId, [
        'response' => $response,
        'success' => true,
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    echo "Updated interaction with response<br>";
} else {
    echo "Failed to create interaction<br>";
}

// Create a test project
$projectName = "Test Content Creation Brigade";
$projectDescription = "This is a test project for the Content Creation Brigade to generate blog posts.";
$deadline = date('Y-m-d', strtotime('+7 days'));
$brigadeType = "content_creation";

$projectId = $projectManager->createAgentArmyProject(
    1, // User ID
    $brigadeType,
    $projectName,
    $projectDescription,
    $deadline
);

if ($projectId) {
    echo "Created AI Agent Army project with ID: {$projectId}<br>";
    
    // Create some tasks for the project
    $tasks = [
        [
            'title' => 'Research trending topics in our niche',
            'description' => 'Identify popular topics and keywords that are currently trending in our industry.',
            'priority' => 'high',
            'estimated_time' => 120, // minutes
            'status' => 'todo'
        ],
        [
            'title' => 'Create content calendar for next month',
            'description' => 'Develop a schedule of blog posts with topics, keywords, and publication dates.',
            'priority' => 'high',
            'estimated_time' => 90,
            'status' => 'todo'
        ],
        [
            'title' => 'Write first blog post draft',
            'description' => 'Create the first draft of the highest priority blog post from the content calendar.',
            'priority' => 'medium',
            'estimated_time' => 180,
            'status' => 'todo'
        ],
        [
            'title' => 'Edit and optimize blog post',
            'description' => 'Review the draft, make edits for clarity and flow, and optimize for SEO.',
            'priority' => 'medium',
            'estimated_time' => 120,
            'status' => 'todo'
        ],
        [
            'title' => 'Publish and promote blog post',
            'description' => 'Publish the finalized blog post and create social media promotion strategy.',
            'priority' => 'medium',
            'estimated_time' => 60,
            'status' => 'todo'
        ]
    ];
    
    foreach ($tasks as $taskData) {
        $taskData['user_id'] = 1;
        $taskData['project_id'] = $projectId;
        $taskData['created_at'] = date('Y-m-d H:i:s');
        $taskData['updated_at'] = date('Y-m-d H:i:s');
        
        $taskId = $projectTaskModel->create($taskData);
        if ($taskId) {
            echo "Created task: {$taskData['title']}<br>";
        }
    }
    
    // Generate a progress report
    $report = $projectManager->generateProjectProgressReport($projectId, 1);
    
    echo "<h3>Project Progress Report</h3>";
    echo "<pre>" . htmlspecialchars($report) . "</pre>";
} else {
    echo "Failed to create AI Agent Army project<br>";
}

echo "<br>Test completed successfully!<br>";
echo "You can now view the Aegis Director interface at: <a href='/momentum/aegis_director_interface.php'>Aegis Director Interface</a><br>";
echo "You can view all agents at: <a href='/momentum/ai-agents'>AI Agents Dashboard</a><br>";
echo "You can view projects at: <a href='/momentum/projects'>Projects Dashboard</a><br>";
