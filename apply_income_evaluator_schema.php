<?php
/**
 * Apply Income Evaluator Schema
 *
 * This script applies the income evaluator schema to the database.
 */

// Define base path
define('BASE_PATH', __DIR__);

// Load database configuration
require_once BASE_PATH . '/src/config/database.php';

// Load database class
require_once BASE_PATH . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Check if tables already exist
$result = $db->query("SHOW TABLES LIKE 'evaluation_criteria'");
$tableExists = $result && $result->num_rows > 0;

if ($tableExists) {
    echo "evaluation_criteria table already exists.\n";
} else {
    echo "Creating income evaluator tables...\n";

    // Read the schema file
    $schema = file_get_contents(BASE_PATH . '/database/income_evaluator_schema.sql');

    // Split the schema into individual statements
    $statements = explode(';', $schema);

    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $result = $db->query($statement);
            if ($result) {
                echo ".";
            } else {
                echo "x";
                echo "\nError: " . $db->error() . "\n";
                echo "Statement: " . $statement . "\n";
            }
        }
    }

    echo "\nSchema applied successfully!\n";
}

// Check if tables exist
$tables = [
    'evaluation_criteria',
    'opportunity_comparisons',
    'comparison_opportunities',
    'comparison_criteria_scores',
    'user_skills',
    'opportunity_skill_requirements'
];

echo "\nVerifying tables:\n";
foreach ($tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '$table'");
    $exists = $result && $result->num_rows > 0;
    echo "- $table: " . ($exists ? "exists" : "missing") . "\n";
}

echo "\nDone!\n";
