-- ADHD Symptom Tracking & CBT Tables

-- Symptom tracking table
CREATE TABLE adhd_symptom_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    log_date DATE NOT NULL,
    focus_score INT NOT NULL COMMENT 'Ability to maintain attention (1-10)',
    productivity_score INT NOT NULL COMMENT 'Task completion and efficiency (1-10)',
    consistency_score INT NOT NULL COMMENT 'Ability to maintain routines (1-10)',
    organization_score INT NOT NULL COMMENT 'Planning and organizing ability (1-10)',
    impulsivity_score INT NOT NULL COMMENT 'Control over impulses (1-10)',
    emotional_regulation_score INT NOT NULL COMMENT 'Ability to manage emotions (1-10)',
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Specific symptom events
CREATE TABLE adhd_symptom_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    symptom_type ENUM('focus', 'productivity', 'consistency', 'organization', 'impulsivity', 'emotional') NOT NULL,
    situation TEXT NOT NULL COMMENT 'What was happening when the symptom occurred',
    intensity INT NOT NULL COMMENT '1-10 scale',
    impact TEXT COMMENT 'How it affected you',
    coping_strategy TEXT COMMENT 'What helped or could have helped',
    timestamp DATETIME NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- CBT Thought Records
CREATE TABLE thought_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    situation TEXT NOT NULL COMMENT 'What happened? When? Where? Who with?',
    emotions TEXT NOT NULL COMMENT 'What emotions did you feel?',
    emotion_intensity INT NOT NULL COMMENT '1-100 scale',
    automatic_thoughts TEXT NOT NULL COMMENT 'What thoughts went through your mind?',
    cognitive_distortions TEXT COMMENT 'What thinking patterns were present',
    evidence_for TEXT COMMENT 'What facts support this thought?',
    evidence_against TEXT COMMENT 'What facts don't support this thought?',
    balanced_thought TEXT COMMENT 'More balanced/realistic perspective',
    outcome_emotion TEXT COMMENT 'How do you feel now?',
    outcome_intensity INT COMMENT '1-100 scale',
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Cognitive Distortions Reference Table
CREATE TABLE cognitive_distortions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    adhd_example TEXT NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Productivity Strategies
CREATE TABLE productivity_strategies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    implementation_steps TEXT NOT NULL,
    adhd_challenge VARCHAR(100) NOT NULL COMMENT 'What ADHD challenge this addresses',
    difficulty ENUM('easy', 'medium', 'hard') NOT NULL,
    time_required VARCHAR(50) NOT NULL COMMENT 'How long it takes to implement'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Strategy Tracking
CREATE TABLE user_strategies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    strategy_id INT NOT NULL,
    start_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    effectiveness_rating INT COMMENT '1-10 scale',
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (strategy_id) REFERENCES productivity_strategies(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Consistency Tracker
CREATE TABLE consistency_trackers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    habit_name VARCHAR(100) NOT NULL,
    description TEXT,
    frequency ENUM('daily', 'weekdays', 'weekends', 'weekly', 'custom') NOT NULL,
    custom_days VARCHAR(50) COMMENT 'JSON array of days if frequency is custom',
    time_of_day TIME,
    streak_count INT DEFAULT 0,
    longest_streak INT DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Consistency Log
CREATE TABLE consistency_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracker_id INT NOT NULL,
    log_date DATE NOT NULL,
    completed BOOLEAN DEFAULT FALSE,
    difficulty_rating INT COMMENT '1-10 scale, how hard was it to complete',
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (tracker_id) REFERENCES consistency_trackers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default cognitive distortions
INSERT INTO cognitive_distortions (name, description, adhd_example) VALUES
('All-or-Nothing Thinking', 'Seeing things in black and white categories with no middle ground', 'If I can\'t complete this task perfectly, I\'m a total failure'),
('Catastrophizing', 'Expecting the worst possible outcome', 'I forgot that meeting, now I\'ll definitely lose my job'),
('Disqualifying the Positive', 'Rejecting positive experiences by insisting they "don\'t count"', 'I finished that project on time, but that was just luck'),
('Emotional Reasoning', 'Believing something is true because it "feels" true', 'I feel overwhelmed, so this task must be impossible'),
('Labeling', 'Attaching a negative label to yourself instead of describing the behavior', 'I\'m so scattered and disorganized. I\'m a complete mess'),
('Magnification', 'Exaggerating the importance of problems or shortcomings', 'Making one mistake on this report ruins the whole thing'),
('Mental Filtering', 'Focusing exclusively on negative elements while ignoring positives', 'My boss pointed out one error, so the entire presentation was terrible'),
('Mind Reading', 'Assuming you know what others are thinking without evidence', 'Everyone in this meeting can tell I\'m struggling to pay attention'),
('Overgeneralization', 'Drawing broad conclusions from a single event', 'I got distracted during this task, I always get distracted with everything'),
('Should Statements', 'Having rigid rules about how you and others "should" behave', 'I should be able to focus as easily as everyone else');

-- Mindfulness Exercises Table
CREATE TABLE mindfulness_exercises (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    category ENUM('breathing', 'meditation', 'grounding', 'body_scan', 'impulse_control') NOT NULL,
    description TEXT NOT NULL,
    instructions TEXT NOT NULL,
    duration_minutes INT NOT NULL COMMENT 'Duration in minutes',
    difficulty ENUM('beginner', 'intermediate', 'advanced') NOT NULL,
    benefits TEXT NOT NULL COMMENT 'How this helps with ADHD symptoms',
    audio_file VARCHAR(255) COMMENT 'Path to audio file if available'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Mindfulness Practice Logs
CREATE TABLE user_mindfulness_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    exercise_id INT NOT NULL,
    practice_date DATETIME NOT NULL,
    duration_minutes INT NOT NULL,
    mood_before ENUM('very_poor', 'poor', 'neutral', 'good', 'very_good') NOT NULL,
    mood_after ENUM('very_poor', 'poor', 'neutral', 'good', 'very_good') NOT NULL,
    focus_improvement INT COMMENT 'Rating 1-10 of perceived focus improvement',
    notes TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (exercise_id) REFERENCES mindfulness_exercises(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default mindfulness exercises
INSERT INTO mindfulness_exercises (name, category, description, instructions, duration_minutes, difficulty, benefits) VALUES
('Box Breathing', 'breathing', 'A simple breathing technique to reduce stress and improve focus', '1. Breathe in through your nose for 4 seconds\n2. Hold your breath for 4 seconds\n3. Exhale through your mouth for 4 seconds\n4. Hold your breath for 4 seconds\n5. Repeat for the duration', 5, 'beginner', 'Reduces anxiety, improves concentration, helps with emotional regulation'),
('4-7-8 Breathing', 'breathing', 'A breathing pattern that promotes relaxation', '1. Breathe in quietly through your nose for 4 seconds\n2. Hold your breath for 7 seconds\n3. Exhale completely through your mouth for 8 seconds\n4. Repeat for the duration', 5, 'beginner', 'Reduces anxiety and stress, helps with impulse control, promotes better sleep'),
('5-4-3-2-1 Grounding', 'grounding', 'A sensory awareness exercise to bring attention to the present moment', '1. Acknowledge 5 things you can see\n2. Acknowledge 4 things you can touch\n3. Acknowledge 3 things you can hear\n4. Acknowledge 2 things you can smell\n5. Acknowledge 1 thing you can taste', 5, 'beginner', 'Helps with anxiety, brings focus to the present, interrupts racing thoughts'),
('Body Scan Meditation', 'body_scan', 'A practice of paying attention to parts of the body and bodily sensations', '1. Sit or lie down in a comfortable position\n2. Close your eyes and take a few deep breaths\n3. Bring attention to your feet and notice any sensations\n4. Slowly move attention up through your body\n5. Notice sensations without judgment', 10, 'intermediate', 'Improves body awareness, reduces physical tension, helps with hyperactivity'),
('S.T.O.P. Technique', 'impulse_control', 'A mindfulness-based approach to managing impulsive behaviors', '1. Stop what you\'re doing\n2. Take a breath\n3. Observe what\'s happening internally and externally\n4. Proceed with awareness', 3, 'beginner', 'Improves impulse control, creates space between stimulus and response, reduces reactivity');

-- Insert default productivity strategies
INSERT INTO productivity_strategies (name, description, implementation_steps, adhd_challenge, difficulty, time_required) VALUES
('Pomodoro Technique', 'Work in focused sprints with short breaks in between', '1. Choose a task\n2. Set a timer for 25 minutes\n3. Work on the task until the timer rings\n4. Take a 5-minute break\n5. After 4 pomodoros, take a longer 15-30 minute break', 'Focus and Productivity', 'easy', '25-minute sessions'),
('Task Chunking', 'Break down large tasks into smaller, manageable pieces', '1. Identify a large task or project\n2. Break it down into smaller subtasks\n3. Make each subtask specific and actionable\n4. Tackle one chunk at a time', 'Overwhelm and Procrastination', 'medium', '10-15 minutes for planning'),
('Body Doubling', 'Work alongside someone else to increase accountability and focus', '1. Find a work buddy (in person or virtual)\n2. Set a specific time to work together\n3. Each person works on their own tasks\n4. The presence of another person helps maintain focus', 'Motivation and Focus', 'easy', 'Varies based on task'),
('The 2-Minute Rule', 'If a task takes less than 2 minutes, do it immediately', '1. When you encounter a task, ask if it will take less than 2 minutes\n2. If yes, do it immediately\n3. If no, schedule it or add it to your task list', 'Procrastination and Task Initiation', 'easy', '2 minutes per task'),
('Time Blocking', 'Schedule specific blocks of time for different activities', '1. Identify your most important tasks\n2. Assign specific time blocks in your calendar\n3. Include buffer time between blocks\n4. Treat these appointments with yourself as non-negotiable', 'Time Management and Structure', 'medium', '15-30 minutes for planning');
