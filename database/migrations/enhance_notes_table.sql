-- Enhanced Notes Table Migration
-- Adds ADHD-friendly features and performance optimizations

-- Add new columns for enhanced functionality
ALTER TABLE notes ADD COLUMN IF NOT EXISTS is_favorite TINYINT(1) DEFAULT 0;
ALTER TABLE notes ADD COLUMN IF NOT EXISTS priority_level ENUM('low', 'medium', 'high') DEFAULT 'medium';
ALTER TABLE notes ADD COLUMN IF NOT EXISTS last_accessed DATETIME NULL;
ALTER TABLE notes ADD COLUMN IF NOT EXISTS auto_saved TINYINT(1) DEFAULT 0;
ALTER TABLE notes ADD COLUMN IF NOT EXISTS word_count INT DEFAULT 0;
ALTER TABLE notes ADD COLUMN IF NOT EXISTS reading_time INT DEFAULT 0; -- in minutes
ALTER TABLE notes ADD COLUMN IF NOT EXISTS color_code VARCHAR(7) NULL; -- hex color for visual organization
ALTER TABLE notes ADD COLUMN IF NOT EXISTS template_id INT NULL;
ALTER TABLE notes ADD COLUMN IF NOT EXISTS parent_note_id INT NULL; -- for note linking
ALTER TABLE notes ADD COLUMN IF NOT EXISTS note_type ENUM('note', 'template', 'checklist', 'journal') DEFAULT 'note';

-- Add indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_notes_user_priority ON notes(user_id, priority_level);
CREATE INDEX IF NOT EXISTS idx_notes_user_category ON notes(user_id, category);
CREATE INDEX IF NOT EXISTS idx_notes_user_updated ON notes(user_id, updated_at);
CREATE INDEX IF NOT EXISTS idx_notes_user_pinned ON notes(user_id, is_pinned);
CREATE INDEX IF NOT EXISTS idx_notes_user_favorite ON notes(user_id, is_favorite);
CREATE INDEX IF NOT EXISTS idx_notes_last_accessed ON notes(last_accessed);
CREATE INDEX IF NOT EXISTS idx_notes_auto_saved ON notes(auto_saved);
CREATE INDEX IF NOT EXISTS idx_notes_template ON notes(template_id);
CREATE INDEX IF NOT EXISTS idx_notes_parent ON notes(parent_note_id);

-- Full-text search index for better search performance
ALTER TABLE notes ADD FULLTEXT(title, content, tags) IF NOT EXISTS;

-- Create note templates table
CREATE TABLE IF NOT EXISTS note_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_content TEXT,
    category VARCHAR(100),
    default_tags VARCHAR(500),
    is_public TINYINT(1) DEFAULT 0,
    usage_count INT DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    INDEX idx_templates_user (user_id),
    INDEX idx_templates_public (is_public),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create note links table for connecting related notes
CREATE TABLE IF NOT EXISTS note_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    source_note_id INT NOT NULL,
    target_note_id INT NOT NULL,
    link_type ENUM('reference', 'related', 'parent', 'child') DEFAULT 'related',
    created_at DATETIME NOT NULL,
    INDEX idx_links_source (source_note_id),
    INDEX idx_links_target (target_note_id),
    UNIQUE KEY unique_link (source_note_id, target_note_id, link_type),
    FOREIGN KEY (source_note_id) REFERENCES notes(id) ON DELETE CASCADE,
    FOREIGN KEY (target_note_id) REFERENCES notes(id) ON DELETE CASCADE
);

-- Create note attachments table
CREATE TABLE IF NOT EXISTS note_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    note_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_at DATETIME NOT NULL,
    INDEX idx_attachments_note (note_id),
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE
);

-- Create note version history table
CREATE TABLE IF NOT EXISTS note_versions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    note_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    version_number INT NOT NULL,
    change_summary VARCHAR(500),
    created_at DATETIME NOT NULL,
    INDEX idx_versions_note (note_id),
    INDEX idx_versions_note_version (note_id, version_number),
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE
);

-- Create note analytics table for ADHD insights
CREATE TABLE IF NOT EXISTS note_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    note_id INT NOT NULL,
    action_type ENUM('view', 'edit', 'create', 'delete', 'search') NOT NULL,
    session_duration INT NULL, -- in seconds
    timestamp DATETIME NOT NULL,
    metadata JSON NULL, -- for storing additional context
    INDEX idx_analytics_user (user_id),
    INDEX idx_analytics_note (note_id),
    INDEX idx_analytics_timestamp (timestamp),
    INDEX idx_analytics_action (action_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE
);

-- Insert default note templates for ADHD users
INSERT IGNORE INTO note_templates (user_id, name, description, template_content, category, default_tags, is_public, created_at, updated_at) VALUES
(0, 'Daily Journal', 'ADHD-friendly daily reflection template', '# Daily Journal - {{date}}\n\n## 🎯 Today''s Focus\n- [ ] Priority 1:\n- [ ] Priority 2:\n- [ ] Priority 3:\n\n## 💭 Thoughts & Feelings\n\n\n## ✅ Accomplishments\n\n\n## 🔄 Tomorrow''s Prep\n\n\n## 📝 Random Notes\n', 'Personal', 'journal,daily,reflection', 1, NOW(), NOW()),
(0, 'Meeting Notes', 'Structured meeting notes template', '# Meeting: {{title}}\n\n**Date:** {{date}}\n**Attendees:** \n**Duration:** \n\n## 📋 Agenda\n- \n\n## 🗣️ Discussion Points\n\n\n## ✅ Action Items\n- [ ] \n\n## 📝 Follow-up\n\n', 'Work', 'meeting,work,action-items', 1, NOW(), NOW()),
(0, 'Project Planning', 'ADHD-friendly project breakdown template', '# Project: {{title}}\n\n## 🎯 Goal\n\n\n## 📊 Success Criteria\n- \n\n## 🔄 Phases\n### Phase 1:\n- [ ] \n\n### Phase 2:\n- [ ] \n\n### Phase 3:\n- [ ] \n\n## ⚠️ Potential Obstacles\n- \n\n## 🎉 Rewards\n- \n', 'Work', 'project,planning,goals', 1, NOW(), NOW()),
(0, 'Quick Capture', 'Fast note template for ADHD brain dumps', '# Quick Capture - {{time}}\n\n## 💡 Main Idea\n\n\n## 🔗 Related To\n\n\n## ⏰ Follow-up Needed?\n- [ ] Yes - When: \n- [ ] No\n\n## 🏷️ Tags\n', 'Ideas', 'quick,capture,ideas', 1, NOW(), NOW()),
(0, 'Learning Notes', 'Template for capturing learning and insights', '# Learning: {{topic}}\n\n## 📚 Source\n\n\n## 🔑 Key Points\n- \n\n## 💡 Insights\n\n\n## 🔗 Connections\n\n\n## 📝 Action Items\n- [ ] \n', 'Learning', 'learning,notes,insights', 1, NOW(), NOW());

-- Update existing notes to set default values
UPDATE notes SET 
    priority_level = 'medium',
    is_favorite = 0,
    auto_saved = 0,
    note_type = 'note'
WHERE priority_level IS NULL OR is_favorite IS NULL;

-- Update word count and reading time for existing notes
UPDATE notes SET 
    word_count = (LENGTH(content) - LENGTH(REPLACE(content, ' ', '')) + 1),
    reading_time = GREATEST(1, ROUND((LENGTH(content) - LENGTH(REPLACE(content, ' ', '')) + 1) / 200))
WHERE content IS NOT NULL AND content != '';
