-- AI Agents Army Schema

-- Create ai_agent_categories table
CREATE TABLE IF NOT EXISTS ai_agent_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(20) DEFAULT '#6366F1', -- Default indigo color
    icon VARCHAR(50) DEFAULT 'fa-robot',
    display_order INT DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_agents table
CREATE TABLE IF NOT EXISTS ai_agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    category_id INT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    capabilities TEXT,
    status ENUM('active', 'inactive', 'training', 'error') DEFAULT 'active',
    avatar VARCHAR(255),
    personality_traits TEXT,
    intelligence_level INT DEFAULT 5, -- Scale of 1-10
    efficiency_rating DECIMAL(3,1) DEFAULT 5.0, -- Scale of 0-10
    reliability_score DECIMAL(3,1) DEFAULT 5.0, -- Scale of 0-10
    last_active DATETIME,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES ai_agent_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_agent_tasks table
CREATE TABLE IF NOT EXISTS ai_agent_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id INT NOT NULL,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    due_date DATETIME,
    completion_date DATETIME,
    success_rating INT, -- Scale of 1-10
    feedback TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES ai_agents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_agent_interactions table
CREATE TABLE IF NOT EXISTS ai_agent_interactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id INT NOT NULL,
    user_id INT NOT NULL,
    interaction_type ENUM('command', 'query', 'feedback', 'training', 'system') NOT NULL,
    content TEXT NOT NULL,
    response TEXT,
    success BOOLEAN DEFAULT TRUE,
    created_at DATETIME NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES ai_agents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_agent_skills table
CREATE TABLE IF NOT EXISTS ai_agent_skills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    skill_type ENUM('analytical', 'creative', 'technical', 'communication', 'research', 'automation', 'productivity') NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_agent_skill_mappings table (for many-to-many relationship)
CREATE TABLE IF NOT EXISTS ai_agent_skill_mappings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id INT NOT NULL,
    skill_id INT NOT NULL,
    proficiency_level INT DEFAULT 5, -- Scale of 1-10
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES ai_agents(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES ai_agent_skills(id) ON DELETE CASCADE,
    UNIQUE KEY agent_skill_unique (agent_id, skill_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Default data will be inserted separately
