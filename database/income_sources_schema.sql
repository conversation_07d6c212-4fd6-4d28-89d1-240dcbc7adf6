-- Income Sources Management Schema

-- Create income_sources table
CREATE TABLE income_sources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern VARCHAR(50), -- daily, weekly, monthly, etc.
    expected_amount DECIMAL(10, 2),
    category VARCHAR(50),
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create income_source_transactions table to link transactions to income sources
CREATE TABLE income_source_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    income_source_id INT NOT NULL,
    transaction_id INT NOT NULL,
    created_at DATETIME NOT NULL,
    FOREIG<PERSON> KEY (income_source_id) REFERENCES income_sources(id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES finances(id) ON DELETE CASCADE,
    UNIQUE KEY unique_source_transaction (income_source_id, transaction_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add income_source_id column to finances table for direct linking (optional approach)
-- ALTER TABLE finances 
-- ADD COLUMN income_source_id INT NULL AFTER user_id,
-- ADD FOREIGN KEY (income_source_id) REFERENCES income_sources(id) ON DELETE SET NULL;
