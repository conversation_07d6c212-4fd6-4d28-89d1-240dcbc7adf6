-- AI Prompt Integration System Schema

-- Create ai_prompt_categories table
CREATE TABLE IF NOT EXISTS ai_prompt_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(20) DEFAULT '#6366F1',
    icon VARCHAR(50) DEFAULT 'fa-brain',
    display_order INT DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_prompts table
CREATE TABLE IF NOT EXISTS ai_prompts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    category_id INT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    prompt_text TEXT NOT NULL,
    variables JSON, -- Store variable definitions and defaults
    tags VARCHAR(500),
    is_template BOOLEAN DEFAULT FALSE,
    is_favorite BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    usage_count INT DEFAULT 0,
    effectiveness_rating DECIMAL(3,1) DEFAULT 0.0, -- Average rating from 0-10
    version INT DEFAULT 1,
    parent_prompt_id INT, -- For versioning/forking
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES ai_prompt_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_prompt_id) REFERENCES ai_prompts(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_prompt_history table
CREATE TABLE IF NOT EXISTS ai_prompt_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    prompt_id INT NOT NULL,
    user_id INT NOT NULL,
    executed_prompt TEXT NOT NULL, -- The actual prompt sent (with variables filled)
    ai_provider ENUM('openai', 'anthropic', 'google', 'local', 'other') NOT NULL,
    model_name VARCHAR(100),
    response_text LONGTEXT,
    response_metadata JSON, -- Store tokens, cost, etc.
    execution_time_ms INT,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    user_rating INT, -- 1-10 rating of response quality
    user_feedback TEXT,
    created_at DATETIME NOT NULL,
    FOREIGN KEY (prompt_id) REFERENCES ai_prompts(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_prompt_workflows table (for chaining prompts)
CREATE TABLE IF NOT EXISTS ai_prompt_workflows (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    workflow_steps JSON NOT NULL, -- Array of prompt IDs and conditions
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create quick_captures table
CREATE TABLE IF NOT EXISTS quick_captures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('screenshot', 'note', 'voice', 'mixed') NOT NULL,
    title VARCHAR(255),
    content TEXT,
    file_path VARCHAR(500), -- For screenshots/audio files
    file_type VARCHAR(50),
    file_size INT,
    thumbnail_path VARCHAR(500),
    ocr_text LONGTEXT, -- Extracted text from screenshots
    tags VARCHAR(500),
    category VARCHAR(100),
    is_pinned BOOLEAN DEFAULT FALSE,
    linked_prompt_id INT, -- Link to related AI prompt
    linked_task_id INT, -- Link to related task
    linked_project_id INT, -- Link to related project
    metadata JSON, -- Store additional data like dimensions, device info, etc.
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (linked_prompt_id) REFERENCES ai_prompts(id) ON DELETE SET NULL,
    FOREIGN KEY (linked_task_id) REFERENCES tasks(id) ON DELETE SET NULL,
    FOREIGN KEY (linked_project_id) REFERENCES projects(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create capture_annotations table
CREATE TABLE IF NOT EXISTS capture_annotations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    capture_id INT NOT NULL,
    user_id INT NOT NULL,
    annotation_type ENUM('text', 'arrow', 'rectangle', 'circle', 'highlight', 'blur') NOT NULL,
    position_data JSON NOT NULL, -- Store coordinates, dimensions, etc.
    content TEXT, -- For text annotations
    style_data JSON, -- Store colors, fonts, etc.
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (capture_id) REFERENCES quick_captures(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create ai_api_configurations table
CREATE TABLE IF NOT EXISTS ai_api_configurations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    provider ENUM('openai', 'anthropic', 'google', 'local', 'other') NOT NULL,
    api_key_encrypted TEXT, -- Encrypted API key
    model_preferences JSON, -- Default models for different tasks
    rate_limits JSON, -- User-defined rate limits
    cost_tracking JSON, -- Track API usage costs
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY user_provider_unique (user_id, provider)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default prompt categories
INSERT INTO ai_prompt_categories (user_id, name, description, color, icon, display_order, is_system, created_at, updated_at) VALUES
(1, 'Writing & Content', 'Prompts for writing assistance, content creation, and editing', '#10B981', 'fa-pen-fancy', 1, TRUE, NOW(), NOW()),
(1, 'Analysis & Research', 'Prompts for data analysis, research, and critical thinking', '#3B82F6', 'fa-chart-line', 2, TRUE, NOW(), NOW()),
(1, 'Brainstorming & Ideas', 'Creative prompts for ideation and problem-solving', '#F59E0B', 'fa-lightbulb', 3, TRUE, NOW(), NOW()),
(1, 'Code & Development', 'Programming and technical development prompts', '#8B5CF6', 'fa-code', 4, TRUE, NOW(), NOW()),
(1, 'Learning & Education', 'Prompts for learning new concepts and skills', '#EF4444', 'fa-graduation-cap', 5, TRUE, NOW(), NOW()),
(1, 'Business & Strategy', 'Business planning, strategy, and decision-making prompts', '#059669', 'fa-briefcase', 6, TRUE, NOW(), NOW()),
(1, 'Personal & ADHD', 'Personal productivity and ADHD management prompts', '#EC4899', 'fa-brain', 7, TRUE, NOW(), NOW());
