# Momentum - ADHD-Friendly Personal Management System

A web-based Personal Management System specifically designed to support users with ADHD symptoms like procrastination, difficulty with task completion, focus challenges, and overwhelm. Momentum helps you stay organized and focused with a clean, distraction-free interface.

## Features

- **Dashboard**: Customizable overview showing key information with Current Focus widget
- **Task Management**: Daily, weekly, monthly calendar views with task breakdown and filtering
- **Idea Catcher / Brain Dump**: Quick-entry section for capturing thoughts and ideas
- **Productivity Tools**: Focus timer and focus mode to enhance concentration
- **Financial Management**: Income/expense tracking, budget overview, subscription tracker
- **Notes**: Secure storage for important information
- **Tools**: Utility tools including currency converter
- **Income Opportunity Explorer**: Discover and evaluate online income opportunities
- **Passive Income Portfolio**: Track and manage passive income streams
- **Freelance Project Manager**: Organize freelance clients, projects, and payments
- **Online Business Dashboard**: Monitor key metrics for online business ventures
- **AI Agent Army**: Deploy specialized AI agents organized into brigades for business automation
- **Aegis Director**: Executive functioning partner for ADHD support and project management
- **YouTube Money Making Agent**: Discover profitable opportunities from trending YouTube content

## ADHD-Friendly Design

- Clean, uncluttered, and visually calming design
- Customizable themes (light/dark mode)
- Clear visual hierarchy and consistent layout
- Minimal clicks for common actions
- Encouraging and non-judgmental language
- Mobile-responsive design

## Technical Specifications

- **Backend**: PHP
- **Database**: MySQL/MariaDB
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **CSS Framework**: Tailwind CSS

## Installation

### Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher / MariaDB 10.3 or higher
- Web server (Apache, Nginx)

### Setup Instructions

1. **Clone the repository**

```bash
git clone https://github.com/asankaperera9/momentum.git
cd momentum
```

2. **Create a database**

Create a new MySQL/MariaDB database for the application.

```sql
CREATE DATABASE momentum CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **Import the database schema**

```bash
mysql -u username -p momentum < database/schema.sql
```

4. **Configure the database connection**

Edit the database configuration in `src/config/database.php`:

```php
return [
    'host' => 'localhost',
    'username' => 'your_username',
    'password' => 'your_password',
    'database' => 'momentum',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
];
```

5. **Set up the web server**

#### For Apache:

Make sure the `public` directory is set as the document root.

Example Apache virtual host configuration:

```apache
<VirtualHost *:80>
    ServerName momentum.local
    DocumentRoot /path/to/momentum/public

    <Directory /path/to/momentum/public>
        Options -Indexes +FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/momentum-error.log
    CustomLog ${APACHE_LOG_DIR}/momentum-access.log combined
</VirtualHost>
```

#### For Nginx:

```nginx
server {
    listen 80;
    server_name momentum.local;
    root /path/to/momentum/public;

    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

6. **Set permissions**

Make sure the web server has write permissions to the necessary directories:

```bash
chmod -R 755 .
chmod -R 777 public/uploads  # If you add file uploads
```

7. **Access the application**

Open your web browser and navigate to your configured domain (e.g., http://momentum.local).

## Local Development with Laragon

If you're using Laragon for local development:

1. Place the project in the Laragon `www` directory
2. Right-click the Laragon tray icon and select "Create a new website"
3. Enter the project name (e.g., momentum)
4. Laragon will automatically configure the virtual host and add it to your hosts file
5. Access the site at http://momentum.test

## Usage

1. Register a new account
2. Log in to access the dashboard
3. Start by adding tasks, ideas, or financial information
4. Use the Current Focus feature to stay focused on your most important task
5. Explore productivity tools like the Focus Timer to enhance concentration

## Recent Updates

- Added AI Agent Army implementation with specialized brigades
- Enhanced Aegis Director with project management capabilities
- Added YouTube Money Making Agent for opportunity discovery
- Implemented 24-hour rapid implementation planning
- Added brigade project templates and agent role assignments
- Added Online Business Dashboard feature
- Added Current Focus widget to the dashboard
- Implemented live currency conversion tool
- Fixed navigation menu and dropdown functionality
- Improved task filtering system

## License

This project is licensed under the MIT License - see the LICENSE file for details.
