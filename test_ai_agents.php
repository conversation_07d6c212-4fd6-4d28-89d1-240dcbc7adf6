<?php
/**
 * Test script for AI Agents Army
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Load database
require_once BASE_PATH . '/src/utils/Database.php';

// Load models
require_once BASE_PATH . '/src/models/AIAgent.php';
require_once BASE_PATH . '/src/models/AIAgentCategory.php';
require_once BASE_PATH . '/src/models/AIAgentTask.php';
require_once BASE_PATH . '/src/models/AIAgentInteraction.php';

// Test database connection
$db = Database::getInstance();
if ($db) {
    echo "Database connection successful<br>";
} else {
    echo "Database connection failed<br>";
    exit;
}

// Test AIAgentCategory model
$categoryModel = new AIAgentCategory();
$categories = $categoryModel->getUserCategories(1);
echo "Categories count: " . count($categories) . "<br>";
foreach ($categories as $category) {
    echo "Category: " . $category['name'] . "<br>";
}

// Test AIAgent model
$agentModel = new AIAgent();
$agents = $agentModel->getUserAgents(1);
echo "<br>Agents count: " . count($agents) . "<br>";

// Test agent statistics
$agentStats = $agentModel->getAgentStats(1);
echo "<br>Agent statistics:<br>";
echo "Total agents: " . ($agentStats['total_agents'] ?? 0) . "<br>";
echo "Active agents: " . ($agentStats['active_agents'] ?? 0) . "<br>";
echo "Average intelligence: " . number_format($agentStats['avg_intelligence'] ?? 0, 1) . "<br>";
echo "Average efficiency: " . number_format($agentStats['avg_efficiency'] ?? 0, 1) . "<br>";
echo "Average reliability: " . number_format($agentStats['avg_reliability'] ?? 0, 1) . "<br>";

// Create a test agent
echo "<br>Creating a test agent...<br>";
$agentId = $agentModel->createAgent([
    'user_id' => 1,
    'category_id' => $categories[0]['id'] ?? null,
    'name' => 'Test Agent',
    'description' => 'This is a test agent',
    'capabilities' => 'Testing',
    'personality_traits' => 'Helpful, Friendly',
    'intelligence_level' => 8,
    'efficiency_rating' => 7.5,
    'reliability_score' => 9.0,
    'status' => 'active',
    'last_active' => date('Y-m-d H:i:s')
]);

if ($agentId) {
    echo "Test agent created with ID: $agentId<br>";
    
    // Get the agent
    $agent = $agentModel->getAgent($agentId, 1);
    if ($agent) {
        echo "Agent retrieved: " . $agent['name'] . "<br>";
    } else {
        echo "Failed to retrieve agent<br>";
    }
    
    // Create a test task
    $taskModel = new AIAgentTask();
    $taskId = $taskModel->createTask([
        'agent_id' => $agentId,
        'user_id' => 1,
        'title' => 'Test Task',
        'description' => 'This is a test task',
        'priority' => 'medium',
        'status' => 'pending',
        'due_date' => date('Y-m-d H:i:s', strtotime('+1 day'))
    ]);
    
    if ($taskId) {
        echo "Test task created with ID: $taskId<br>";
    } else {
        echo "Failed to create test task<br>";
    }
    
    // Create a test interaction
    $interactionModel = new AIAgentInteraction();
    $interactionId = $interactionModel->createInteraction([
        'agent_id' => $agentId,
        'user_id' => 1,
        'interaction_type' => 'command',
        'content' => 'Hello, Test Agent!',
        'response' => 'Hello, User! How can I help you today?',
        'success' => true
    ]);
    
    if ($interactionId) {
        echo "Test interaction created with ID: $interactionId<br>";
    } else {
        echo "Failed to create test interaction<br>";
    }
    
    // Get agent tasks
    $tasks = $taskModel->getAgentTasks($agentId);
    echo "<br>Tasks count: " . count($tasks) . "<br>";
    
    // Get agent interactions
    $interactions = $interactionModel->getAgentInteractions($agentId);
    echo "Interactions count: " . count($interactions) . "<br>";
    
    // Get task statistics
    $taskStats = $taskModel->getTaskStats(1);
    echo "<br>Task statistics:<br>";
    echo "Total tasks: " . ($taskStats['total_tasks'] ?? 0) . "<br>";
    echo "Pending tasks: " . ($taskStats['pending_tasks'] ?? 0) . "<br>";
    echo "In progress tasks: " . ($taskStats['in_progress_tasks'] ?? 0) . "<br>";
    echo "Completed tasks: " . ($taskStats['completed_tasks'] ?? 0) . "<br>";
    
    // Get interaction statistics
    $interactionStats = $interactionModel->getInteractionStats(1);
    echo "<br>Interaction statistics:<br>";
    echo "Total interactions: " . ($interactionStats['total_interactions'] ?? 0) . "<br>";
    echo "Command count: " . ($interactionStats['command_count'] ?? 0) . "<br>";
    echo "Query count: " . ($interactionStats['query_count'] ?? 0) . "<br>";
    echo "Successful interactions: " . ($interactionStats['successful_interactions'] ?? 0) . "<br>";
} else {
    echo "Failed to create test agent<br>";
}

echo "<br>Test completed!";
