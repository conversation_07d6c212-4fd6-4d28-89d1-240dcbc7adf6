/**
 * Footer Component
 * A reusable footer component for the WindSurf VibeCoding approach
 */

class Footer {
  constructor(options = {}) {
    this.copyright = options.copyright || `© ${new Date().getFullYear()} WindSurf VibeCoding`;
    this.links = options.links || [];
    this.socialLinks = options.socialLinks || [];
    this.element = this.createFooter();
  }

  createFooter() {
    const footer = document.createElement('footer');
    footer.classList.add('site-footer');
    
    // Create copyright section
    const copyrightSection = document.createElement('div');
    copyrightSection.classList.add('footer-copyright');
    copyrightSection.textContent = this.copyright;
    
    footer.appendChild(copyrightSection);
    
    // Create links section if links are provided
    if (this.links.length > 0) {
      const linksSection = document.createElement('div');
      linksSection.classList.add('footer-links');
      
      const linksList = document.createElement('ul');
      
      this.links.forEach(link => {
        const linkItem = document.createElement('li');
        
        const linkEl = document.createElement('a');
        linkEl.href = link.url;
        linkEl.textContent = link.text;
        
        linkItem.appendChild(linkEl);
        linksList.appendChild(linkItem);
      });
      
      linksSection.appendChild(linksList);
      footer.appendChild(linksSection);
    }
    
    // Create social links section if socialLinks are provided
    if (this.socialLinks.length > 0) {
      const socialSection = document.createElement('div');
      socialSection.classList.add('footer-social');
      
      const socialList = document.createElement('ul');
      
      this.socialLinks.forEach(link => {
        const socialItem = document.createElement('li');
        
        const socialLink = document.createElement('a');
        socialLink.href = link.url;
        socialLink.setAttribute('aria-label', link.name);
        
        // If icon is provided, use it
        if (link.icon) {
          const icon = document.createElement('img');
          icon.src = link.icon;
          icon.alt = link.name;
          socialLink.appendChild(icon);
        } else {
          socialLink.textContent = link.name;
        }
        
        socialItem.appendChild(socialLink);
        socialList.appendChild(socialItem);
      });
      
      socialSection.appendChild(socialList);
      footer.appendChild(socialSection);
    }
    
    return footer;
  }

  render(container) {
    container.appendChild(this.element);
    return this;
  }
}

export default Footer;
