# Weekly Review Checklist

Use this checklist to conduct a comprehensive weekly review of your AI Agent Army brigade. This review should be conducted at the same time each week to establish a consistent rhythm of evaluation and improvement.

## Performance Review

- [ ] Analyze key performance indicators for the week
- [ ] Compare performance to previous weeks
- [ ] Identify any significant changes or trends
- [ ] Review resource utilization patterns
- [ ] Analyze quality metrics and trends
- [ ] Review efficiency metrics and bottlenecks
- [ ] Analyze user/customer feedback received
- [ ] Review any SLA or commitment compliance
- [ ] Analyze cost efficiency metrics
- [ ] Prepare performance summary for stakeholders

## Brigade-Specific Weekly Review

### Content Creation Brigade Weekly Review

- [ ] Review content production volume vs. targets
- [ ] Analyze content quality scores
- [ ] Review SEO performance of published content
- [ ] Analyze engagement metrics across content pieces
- [ ] Review conversion performance from content
- [ ] Analyze content topic performance patterns
- [ ] Review content format effectiveness
- [ ] Analyze distribution channel performance
- [ ] Review content calendar adherence
- [ ] Analyze resource allocation across content types

### Lead Generation Brigade Weekly Review

- [ ] Review prospect identification volume vs. targets
- [ ] Analyze prospect quality metrics
- [ ] Review outreach campaign performance
- [ ] Analyze response rates across segments
- [ ] Review conversion rates through the funnel
- [ ] Analyze cost per lead metrics
- [ ] Review personalization effectiveness
- [ ] Analyze channel performance comparison
- [ ] Review follow-up sequence effectiveness
- [ ] Analyze sales team feedback on leads

### Customer Support Brigade Weekly Review

- [ ] Review query volume and patterns
- [ ] Analyze resolution rates and times
- [ ] Review customer satisfaction trends
- [ ] Analyze escalation patterns
- [ ] Review self-service utilization
- [ ] Analyze knowledge base effectiveness
- [ ] Review channel performance comparison
- [ ] Analyze query categorization accuracy
- [ ] Review response quality metrics
- [ ] Analyze cost per resolution trends

### Data Analysis Brigade Weekly Review

- [ ] Review insights generated vs. targets
- [ ] Analyze recommendation implementation rates
- [ ] Review business impact of insights
- [ ] Analyze data processing efficiency
- [ ] Review analysis accuracy metrics
- [ ] Analyze visualization effectiveness feedback
- [ ] Review stakeholder satisfaction
- [ ] Analyze time from question to insight metrics
- [ ] Review data source reliability
- [ ] Analyze resource allocation across analyses

## Issue Management Review

- [ ] Review all issues identified during the week
- [ ] Analyze issue resolution effectiveness
- [ ] Identify any recurring or systemic issues
- [ ] Review impact of issues on performance
- [ ] Analyze root causes of significant issues
- [ ] Review issue response time metrics
- [ ] Identify preventive measures for common issues
- [ ] Review issue documentation quality
- [ ] Analyze resource allocation for issue resolution
- [ ] Update issue management procedures if needed

## Resource Utilization Review

- [ ] Analyze agent utilization rates
- [ ] Review task allocation efficiency
- [ ] Analyze peak vs. off-peak performance
- [ ] Review resource bottlenecks
- [ ] Analyze cost efficiency metrics
- [ ] Review capacity planning projections
- [ ] Identify resource optimization opportunities
- [ ] Analyze external resource dependencies
- [ ] Review resource scaling needs
- [ ] Update resource allocation plan if needed

## Process Improvement Review

- [ ] Review workflow efficiency metrics
- [ ] Analyze process bottlenecks
- [ ] Review handoff effectiveness between stages
- [ ] Analyze quality control effectiveness
- [ ] Review automation opportunities identified
- [ ] Analyze process variation and standardization
- [ ] Review process documentation accuracy
- [ ] Analyze process adaptation needs
- [ ] Review feedback on process improvements
- [ ] Update process improvement roadmap

## Integration Performance Review

- [ ] Review all integration points performance
- [ ] Analyze any integration failures or issues
- [ ] Review data synchronization effectiveness
- [ ] Analyze API performance and reliability
- [ ] Review external dependency risks
- [ ] Analyze integration cost efficiency
- [ ] Review integration security and compliance
- [ ] Analyze integration scalability
- [ ] Review integration documentation accuracy
- [ ] Update integration improvement roadmap

## Team and Stakeholder Review

- [ ] Review team performance and collaboration
- [ ] Analyze stakeholder feedback and satisfaction
- [ ] Review communication effectiveness
- [ ] Analyze training and skill development needs
- [ ] Review knowledge sharing effectiveness
- [ ] Analyze team capacity and workload balance
- [ ] Review stakeholder engagement levels
- [ ] Analyze alignment with stakeholder expectations
- [ ] Review recognition and celebration of wins
- [ ] Update team development plan if needed

## Planning for Next Week

- [ ] Set key priorities for the coming week
- [ ] Allocate resources based on priorities
- [ ] Schedule key activities and milestones
- [ ] Assign ownership for improvement initiatives
- [ ] Plan for any upcoming changes or events
- [ ] Schedule necessary meetings and reviews
- [ ] Update risk management plan
- [ ] Prepare communication plan for stakeholders
- [ ] Set performance targets for the coming week
- [ ] Update the weekly review agenda if needed

---

## Weekly Performance Summary

| Key Metric | Target | This Week | Last Week | Change | Status |
|------------|--------|-----------|-----------|--------|--------|
|            |        |           |           |        |        |
|            |        |           |           |        |        |
|            |        |           |           |        |        |
|            |        |           |           |        |        |
|            |        |           |           |        |        |

## Key Achievements This Week

1. 
2. 
3. 
4. 
5. 

## Key Challenges This Week

1. 
2. 
3. 
4. 
5. 

## Improvement Initiatives

| Initiative | Status | Progress | Owner | Next Steps |
|------------|--------|----------|-------|------------|
|            |        |          |       |            |
|            |        |          |       |            |
|            |        |          |       |            |
|            |        |          |       |            |

## Next Week's Priorities

1. 
2. 
3. 
4. 
5. 

## Notes & Action Items

Use this section to track specific notes, challenges, and action items from the weekly review:

1. 
2. 
3. 
4. 
5. 

---

**Week:** [Enter Week Number/Date Range]

**Review Lead:** [Enter Name]

**Participants:** [Enter Names]

**Next Review Date:** [Enter Date]
