# Online Income Strategies Implementation Plan

This document outlines the implementation plan for adding online income tracking and management features to the Momentum app. The plan is divided into phases to allow for incremental development and testing.

## Phase 1: Core Income Opportunity Explorer

### Database Schema Updates

1. Create `income_opportunities` table:
   ```sql
   CREATE TABLE income_opportunities (
     id INT AUTO_INCREMENT PRIMARY KEY,
     user_id INT NOT NULL,
     name VARCHAR(255) NOT NULL,
     description TEXT,
     category VARCHAR(100) NOT NULL,
     income_type ENUM('active', 'passive', 'semi-passive') NOT NULL,
     skill_level ENUM('beginner', 'intermediate', 'advanced', 'mixed') NOT NULL,
     startup_cost ENUM('none', 'low', 'medium', 'high') NOT NULL,
     time_commitment ENUM('minimal', 'low', 'medium', 'high') NOT NULL,
     estimated_income_min DECIMAL(10,2),
     estimated_income_max DECIMAL(10,2),
     income_frequency ENUM('hourly', 'daily', 'weekly', 'monthly', 'per-project', 'variable') NOT NULL,
     time_to_first_income VARCHAR(100),
     status ENUM('considering', 'researching', 'implementing', 'active', 'paused', 'abandoned') NOT NULL DEFAULT 'considering',
     priority INT NOT NULL DEFAULT 0,
     notes TEXT,
     resources TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
   );
   ```

2. Create `income_opportunity_logs` table:
   ```sql
   CREATE TABLE income_opportunity_logs (
     id INT AUTO_INCREMENT PRIMARY KEY,
     opportunity_id INT NOT NULL,
     log_date DATE NOT NULL,
     hours_spent DECIMAL(5,2),
     amount_earned DECIMAL(10,2),
     activities TEXT,
     challenges TEXT,
     wins TEXT,
     next_steps TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE CASCADE
   );
   ```

3. Create `income_opportunity_milestones` table:
   ```sql
   CREATE TABLE income_opportunity_milestones (
     id INT AUTO_INCREMENT PRIMARY KEY,
     opportunity_id INT NOT NULL,
     name VARCHAR(255) NOT NULL,
     description TEXT,
     target_date DATE,
     completion_date DATE,
     status ENUM('pending', 'in-progress', 'completed', 'delayed') NOT NULL DEFAULT 'pending',
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE CASCADE
   );
   ```

### Backend Implementation

1. Create `IncomeOpportunityController.php` with the following methods:
   - `index()` - List all income opportunities
   - `create()` - Show create form
   - `store()` - Save new opportunity
   - `view($id)` - View opportunity details
   - `edit($id)` - Show edit form
   - `update($id)` - Update opportunity
   - `delete($id)` - Delete opportunity
   - `logActivity($id)` - Log activity for an opportunity
   - `addMilestone($id)` - Add milestone to opportunity
   - `updateMilestone($id)` - Update milestone status
   - `deleteMilestone($id)` - Delete milestone

2. Create `IncomeOpportunity.php` model with methods for:
   - CRUD operations
   - Filtering opportunities by type, status, priority
   - Calculating total earnings
   - Tracking time invested
   - Calculating ROI

3. Create `IncomeOpportunityLog.php` model for activity logging

4. Create `IncomeOpportunityMilestone.php` model for milestone tracking

### Frontend Implementation

1. Create views for:
   - Opportunity listing page with filtering options
   - Opportunity detail page
   - Create/edit opportunity forms
   - Activity logging form
   - Milestone management interface

2. Implement dashboard widgets:
   - Active opportunities summary
   - Recent earnings
   - Upcoming milestones
   - Time investment tracking

3. Create visualization components:
   - Income growth charts
   - Time vs. earnings comparison
   - Opportunity ROI comparison

## Phase 2: Passive Income Portfolio

### Database Schema Updates

1. Create `passive_income_streams` table:
   ```sql
   CREATE TABLE passive_income_streams (
     id INT AUTO_INCREMENT PRIMARY KEY,
     user_id INT NOT NULL,
     opportunity_id INT,
     name VARCHAR(255) NOT NULL,
     description TEXT,
     platform VARCHAR(255),
     category VARCHAR(100) NOT NULL,
     setup_date DATE,
     initial_investment DECIMAL(10,2),
     maintenance_hours_per_month DECIMAL(5,2),
     status ENUM('setup', 'growing', 'stable', 'declining', 'inactive') NOT NULL DEFAULT 'setup',
     notes TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
     FOREIGN KEY (opportunity_id) REFERENCES income_opportunities(id) ON DELETE SET NULL
   );
   ```

2. Create `passive_income_earnings` table:
   ```sql
   CREATE TABLE passive_income_earnings (
     id INT AUTO_INCREMENT PRIMARY KEY,
     stream_id INT NOT NULL,
     earning_date DATE NOT NULL,
     amount DECIMAL(10,2) NOT NULL,
     notes TEXT,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     FOREIGN KEY (stream_id) REFERENCES passive_income_streams(id) ON DELETE CASCADE
   );
   ```

3. Create `passive_income_maintenance` table:
   ```sql
   CREATE TABLE passive_income_maintenance (
     id INT AUTO_INCREMENT PRIMARY KEY,
     stream_id INT NOT NULL,
     maintenance_date DATE NOT NULL,
     hours_spent DECIMAL(5,2) NOT NULL,
     tasks_performed TEXT,
     next_maintenance_date DATE,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     FOREIGN KEY (stream_id) REFERENCES passive_income_streams(id) ON DELETE CASCADE
   );
   ```

### Backend & Frontend Implementation

Implement controllers, models, and views similar to Phase 1, with focus on:
- Passive income stream management
- Earnings tracking
- Maintenance scheduling
- Performance analytics
- Portfolio diversification recommendations

## Phase 3: Freelance Project Manager

### Database Schema Updates

1. Create tables for:
   - Freelance clients
   - Freelance projects
   - Project deliverables
   - Invoices and payments
   - Portfolio items
   - Testimonials

### Backend & Frontend Implementation

Implement controllers, models, and views for:
- Client management
- Project tracking
- Deliverable management
- Invoice generation
- Payment tracking
- Portfolio management
- Rate calculation tools

## Phase 4: Online Business Dashboard

### Database Schema Updates

1. Create tables for:
   - Business ventures
   - Products/services
   - Sales tracking
   - Expense tracking
   - Customer data
   - Marketing campaigns

### Backend & Frontend Implementation

Implement controllers, models, and views for:
- Business metrics dashboard
- Inventory management
- Sales tracking
- Expense management
- Customer relationship management
- Marketing campaign scheduling

## Phase 5: Income Stream Evaluator

### Database Schema Updates

1. Create tables for:
   - Evaluation criteria
   - Opportunity comparisons
   - Skill assessments
   - Action plans

### Backend & Frontend Implementation

Implement controllers, models, and views for:
- Opportunity comparison tools
- ROI calculators
- Skill-to-opportunity matching
- Prioritization frameworks
- Action plan generation

## Integration with Existing Features

1. Connect with Financial Management:
   - Link income streams to income sources in the financial module
   - Include online income in financial reports
   - Connect expenses related to online income opportunities

2. Connect with Task Management:
   - Create tasks related to income opportunities
   - Link milestones to project tasks
   - Set reminders for maintenance activities

3. Connect with ADHD Management:
   - Provide ADHD-friendly workflows for income activities
   - Track focus and productivity during income-generating work
   - Implement strategies for maintaining consistency

## Implementation Timeline

- Phase 1: 4-6 weeks
- Phase 2: 3-4 weeks
- Phase 3: 4-6 weeks
- Phase 4: 4-6 weeks
- Phase 5: 3-4 weeks
- Integration: 2-3 weeks

Total estimated time: 20-29 weeks
