# Medical Reports Feature Documentation

## Overview

The Medical Reports feature allows users to track and manage their medical test results, including blood tests, urine tests, scans, and other medical reports. This feature provides a comprehensive solution for maintaining health records and monitoring changes in health parameters over time.

## Table of Contents

1. [Feature Components](#feature-components)
2. [Database Structure](#database-structure)
3. [User Interface](#user-interface)
4. [Functionality](#functionality)
5. [Usage Guide](#usage-guide)
6. [Technical Implementation](#technical-implementation)
7. [Future Enhancements](#future-enhancements)

## Feature Components

The Medical Reports feature consists of the following components:

1. **Medical Dashboard** - Overview of medical reports and health status
2. **Reports Management** - Add, view, edit, and delete medical test reports
3. **Parameter Tracking** - Track individual test parameters and their values over time
4. **Report Generation** - Generate reports and analyze trends
5. **Parameter History** - View the history of specific parameters over time

## Database Structure

The feature uses three main tables:

### 1. medical_test_reports

Stores basic information about each medical test report.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key |
| user_id | INT | Foreign key to users table |
| report_type | VARCHAR | Type of report (blood, urine, scan, other) |
| report_title | VARCHAR | Title of the report |
| report_date | DATE | Date of the test |
| lab_name | VARCHAR | Name of the laboratory |
| doctor_name | VARCHAR | Name of the doctor |
| file_path | VARCHAR | Path to uploaded report file |
| notes | TEXT | Additional notes |
| created_at | DATETIME | Creation timestamp |
| updated_at | DATETIME | Last update timestamp |

### 2. medical_test_parameters

Stores individual test parameters and their values.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key |
| report_id | INT | Foreign key to medical_test_reports table |
| parameter_name | VARCHAR | Name of the parameter |
| parameter_value | VARCHAR | Value of the parameter |
| unit | VARCHAR | Unit of measurement |
| reference_range_min | VARCHAR | Minimum reference range |
| reference_range_max | VARCHAR | Maximum reference range |
| is_abnormal | TINYINT | Flag for abnormal values |
| notes | TEXT | Additional notes |
| created_at | DATETIME | Creation timestamp |
| updated_at | DATETIME | Last update timestamp |

### 3. common_test_parameters

Pre-populated with common test parameters and reference ranges.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key |
| test_type | VARCHAR | Type of test (blood, urine, scan, other) |
| parameter_name | VARCHAR | Name of the parameter |
| display_name | VARCHAR | Display name of the parameter |
| unit | VARCHAR | Unit of measurement |
| reference_range_min | VARCHAR | Minimum reference range |
| reference_range_max | VARCHAR | Maximum reference range |
| description | TEXT | Description of the parameter |
| category | VARCHAR | Category of the parameter |
| created_at | DATETIME | Creation timestamp |
| updated_at | DATETIME | Last update timestamp |

## User Interface

The Medical Reports feature includes the following UI components:

### 1. Medical Dashboard

- Overview of reports by type
- Recent reports summary
- Abnormal parameters highlighting
- Quick access to add new reports

### 2. Reports Listing

- Filterable list of reports
- Sorting by date, type, etc.
- Search functionality
- Actions (view, edit, delete)

### 3. Report Creation Form

- Form for adding new reports
- Dynamic parameter fields based on report type
- File upload for report documents
- Custom parameter addition

### 4. Report Detail View

- Detailed view of a report
- Parameter values with reference ranges
- Abnormal values highlighting
- File viewer for uploaded documents

### 5. Parameter History View

- History of a specific parameter over time
- Trend visualization
- Statistics (min, max, average, change)
- Data table with all values

### 6. Report Generation

- Generate reports for specific date ranges
- Filter by report type
- Export options (CSV, PDF, Print)
- Summary statistics

## Functionality

### 1. Report Management

- **Add Report**: Create a new medical test report with detailed information
- **View Report**: View the details of a specific report
- **Edit Report**: Update the information in an existing report
- **Delete Report**: Remove a report from the system
- **Upload Files**: Upload and store report files (PDF, images)

### 2. Parameter Tracking

- **Record Parameters**: Record test parameters with values, units, and reference ranges
- **Identify Abnormal Results**: Automatically identify values outside reference ranges
- **Track History**: Track parameter values over time
- **Compare Values**: Compare parameter values across different reports

### 3. Report Generation and Analysis

- **Filter Reports**: Filter reports by type, date range, etc.
- **Generate Summaries**: Generate summaries of reports
- **Export Data**: Export report data in various formats
- **Analyze Trends**: Analyze trends in parameter values over time

## Usage Guide

### Adding a New Report

1. Navigate to the Medical Reports section
2. Click "Add New Report"
3. Select the report type (blood, urine, scan, other)
4. Fill in the basic report information (title, date, lab, doctor)
5. Enter parameter values for the selected report type
6. Add any custom parameters if needed
7. Upload report files if available
8. Add notes if needed
9. Click "Save Report"

### Viewing Report History

1. Navigate to the Medical Reports section
2. Use filters to narrow down the list of reports
3. Click on a report to view its details
4. View parameter values and abnormal results
5. Access uploaded files if available

### Tracking Parameter History

1. Navigate to a report detail view
2. Click on a parameter name to view its history
3. View the trend chart and statistics
4. See all values of the parameter across different reports

### Generating Reports

1. Navigate to the "Generate Reports" section
2. Select a date range and report type
3. View the summary of reports for the selected criteria
4. Export the data in the desired format

## Technical Implementation

### Controllers

- **MedicalController.php**: Handles all medical report-related actions

### Models

- **MedicalTestReport.php**: Manages report data and operations

### Views

- **medical/index.php**: Medical dashboard
- **medical/reports/index.php**: Reports listing
- **medical/reports/report_form.php**: Report creation/editing form
- **medical/reports/view.php**: Report detail view
- **medical/reports/parameter_history.php**: Parameter history view
- **medical/reports/generate.php**: Report generation view

### JavaScript

- **medical.js**: Handles dynamic form behavior and AJAX requests

## Future Enhancements

1. **Advanced Visualization**: Implement more advanced visualization for parameter trends
2. **AI-Powered Analysis**: Add AI-powered analysis of test results and recommendations
3. **Integration with Health APIs**: Integrate with health APIs for automatic data import
4. **Mobile App Integration**: Develop a mobile app for on-the-go access to health records
5. **Reminder System**: Implement reminders for upcoming tests and follow-ups
6. **Health Goals**: Add functionality to set and track health goals based on test parameters
7. **Doctor Sharing**: Add ability to share reports with healthcare providers
8. **Comparative Analysis**: Enhanced comparative analysis between different time periods
9. **PDF Report Generation**: Generate comprehensive PDF reports for healthcare providers
