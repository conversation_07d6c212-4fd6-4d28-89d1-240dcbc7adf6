-- SQL for AI Agent Army Project Integration

-- Add agent_id field to tasks table if it doesn't exist
ALTER TABLE tasks 
ADD COLUMN agent_id INT NULL,
ADD CONSTRAINT fk_tasks_agent
FOREIGN KEY (agent_id) REFERENCES ai_agents(id)
ON DELETE SET NULL;

-- Add brigade_type field to projects table
ALTER TABLE projects
ADD COLUMN brigade_type VARCHAR(50) NULL,
ADD COLUMN is_brigade_template BOOLEAN DEFAULT FALSE,
ADD COLUMN parent_brigade_id INT NULL,
ADD CONSTRAINT fk_projects_parent_brigade
FOREIGN KEY (parent_brigade_id) REFERENCES projects(id)
ON DELETE SET NULL;

-- Create project_agent_assignments table for multiple agents per project
CREATE TABLE IF NOT EXISTS project_agent_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    agent_id INT NOT NULL,
    role VARCHAR(100) NULL,
    assignment_date DATETIME NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    CONSTRAINT fk_project_assignments_project
        FOREIGN KEY (project_id) REFERENCES projects(id)
        ON DELETE CASCADE,
    CONSTRAINT fk_project_assignments_agent
        FOREIGN KEY (agent_id) REFERENCES ai_agents(id)
        ON DELETE CASCADE,
    CONSTRAINT uq_project_agent_role
        UNIQUE (project_id, agent_id, role)
);

-- Create brigade_coordination table for cross-brigade dependencies
CREATE TABLE IF NOT EXISTS brigade_coordination (
    id INT AUTO_INCREMENT PRIMARY KEY,
    source_project_id INT NOT NULL,
    target_project_id INT NOT NULL,
    coordination_type VARCHAR(50) NOT NULL,
    description TEXT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    CONSTRAINT fk_coordination_source
        FOREIGN KEY (source_project_id) REFERENCES projects(id)
        ON DELETE CASCADE,
    CONSTRAINT fk_coordination_target
        FOREIGN KEY (target_project_id) REFERENCES projects(id)
        ON DELETE CASCADE,
    CONSTRAINT uq_brigade_coordination
        UNIQUE (source_project_id, target_project_id, coordination_type)
);

-- Create agent_brigade_roles table to define specialized roles within brigades
CREATE TABLE IF NOT EXISTS agent_brigade_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    brigade_type VARCHAR(50) NOT NULL,
    description TEXT NULL,
    capabilities TEXT NULL,
    required_skills TEXT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    CONSTRAINT uq_brigade_role_name
        UNIQUE (name, brigade_type)
);

-- Insert default brigade types
INSERT INTO agent_brigade_roles (name, brigade_type, description, capabilities, required_skills, created_at, updated_at)
VALUES 
-- Content Creation Brigade Roles
('Research Agent', 'content_creation', 'Gathers information and identifies trending topics', 'Web research, trend analysis, information gathering', 'Research, Analysis, Information Verification', NOW(), NOW()),
('Content Planning Agent', 'content_creation', 'Creates content outlines and strategies', 'Content planning, outline creation, topic clustering', 'Content Strategy, Organization, Planning', NOW(), NOW()),
('Writing Agent', 'content_creation', 'Generates actual content based on outlines', 'Content writing, storytelling, voice adaptation', 'Writing, Creativity, Adaptation', NOW(), NOW()),
('Editing Agent', 'content_creation', 'Refines and improves content quality', 'Editing, proofreading, quality enhancement', 'Editing, Grammar, Style', NOW(), NOW()),
('SEO Optimization Agent', 'content_creation', 'Ensures content ranks well in search engines', 'SEO analysis, keyword optimization, metadata enhancement', 'SEO, Analytics, Optimization', NOW(), NOW()),

-- Lead Generation Brigade Roles
('Prospect Identification Agent', 'lead_generation', 'Finds potential clients matching criteria', 'Lead identification, market research, targeting', 'Research, Analysis, Targeting', NOW(), NOW()),
('Research Agent', 'lead_generation', 'Gathers detailed information about prospects', 'Prospect research, data collection, profile building', 'Research, Data Collection, Analysis', NOW(), NOW()),
('Personalization Agent', 'lead_generation', 'Creates customized outreach messages', 'Message personalization, communication tailoring', 'Writing, Personalization, Psychology', NOW(), NOW()),
('Engagement Agent', 'lead_generation', 'Manages follow-up sequences', 'Follow-up management, engagement tracking, response handling', 'Communication, Timing, Persistence', NOW(), NOW()),
('Analytics Agent', 'lead_generation', 'Tracks campaign performance and optimizes strategies', 'Performance analysis, strategy optimization, reporting', 'Analytics, Reporting, Optimization', NOW(), NOW()),

-- Customer Support Brigade Roles
('Triage Agent', 'customer_support', 'Categorizes and prioritizes incoming queries', 'Query categorization, priority assessment, routing', 'Analysis, Prioritization, Classification', NOW(), NOW()),
('Knowledge Agent', 'customer_support', 'Retrieves relevant information from knowledge bases', 'Knowledge retrieval, information search, context understanding', 'Research, Knowledge Management, Context', NOW(), NOW()),
('Response Agent', 'customer_support', 'Generates personalized, helpful responses', 'Response generation, personalization, problem solving', 'Communication, Problem Solving, Empathy', NOW(), NOW()),
('Escalation Agent', 'customer_support', 'Identifies when human intervention is needed', 'Escalation assessment, handoff management, issue tracking', 'Analysis, Decision Making, Coordination', NOW(), NOW()),
('Analytics Agent', 'customer_support', 'Tracks performance and identifies improvement opportunities', 'Performance analysis, trend identification, optimization', 'Analytics, Pattern Recognition, Reporting', NOW(), NOW()),

-- Data Analysis Brigade Roles
('Data Collection Agent', 'data_analysis', 'Gathers and organizes data from various sources', 'Data collection, source integration, data organization', 'Data Collection, Integration, Organization', NOW(), NOW()),
('Processing Agent', 'data_analysis', 'Cleans, normalizes, and prepares data for analysis', 'Data cleaning, normalization, preparation', 'Data Processing, Cleaning, Transformation', NOW(), NOW()),
('Analysis Agent', 'data_analysis', 'Identifies patterns, trends, and insights', 'Pattern recognition, trend analysis, insight generation', 'Analysis, Statistics, Pattern Recognition', NOW(), NOW()),
('Visualization Agent', 'data_analysis', 'Creates clear, compelling data visualizations', 'Data visualization, chart creation, visual storytelling', 'Visualization, Design, Communication', NOW(), NOW()),
('Recommendation Agent', 'data_analysis', 'Generates actionable recommendations', 'Recommendation generation, action planning, prioritization', 'Decision Making, Strategy, Communication', NOW(), NOW());
