const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

module.exports = (env, argv) => {
    const isProduction = argv.mode === 'production';
    
    return {
        mode: isProduction ? 'production' : 'development',
        entry: {
            // Main bundles
            'main': './src/js/main.js',
            'dashboard': './src/js/dashboard.js',
            'productivity': './src/js/productivity.js',
            
            // Feature-specific bundles
            'task-manager': './public/js/modules/task-manager.js',
            'dashboard-layout': './public/js/modules/dashboard-layout.js',
            'adhd-dashboard': './public/js/adhd-dashboard.js',
        },
        output: {
            filename: isProduction ? 'js/[name].[contenthash].min.js' : 'js/[name].js',
            path: path.resolve(__dirname, 'public/dist'),
            publicPath: '/momentum/dist/',
            clean: true,
        },
        devtool: isProduction ? false : 'source-map',
        optimization: {
            minimize: isProduction,
            minimizer: [
                new TerserPlugin({
                    terserOptions: {
                        format: {
                            comments: false,
                        },
                        compress: {
                            drop_console: isProduction,
                        },
                    },
                    extractComments: false,
                }),
                new CssMinimizerPlugin(),
            ],
            splitChunks: {
                cacheGroups: {
                    commons: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                    },
                },
            },
        },
        module: {
            rules: [
                {
                    test: /\.css$/,
                    use: [
                        MiniCssExtractPlugin.loader,
                        'css-loader',
                    ],
                },
            ],
        },
        plugins: [
            new MiniCssExtractPlugin({
                filename: isProduction ? 'css/[name].[contenthash].min.css' : 'css/[name].css',
            }),
        ],
    };
};
