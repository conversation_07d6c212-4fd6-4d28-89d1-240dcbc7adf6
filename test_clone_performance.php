<?php
/**
 * Test script to verify clone section performance
 */

// Start session and set up environment
session_start();

// Simulate logged-in user
$_SESSION['user'] = [
    'id' => 2,
    'name' => 'Test User',
    'email' => '<EMAIL>'
];

require_once 'src/utils/Database.php';
require_once 'src/utils/Session.php';
require_once 'src/controllers/BaseController.php';
require_once 'src/controllers/CloneController.php';

try {
    echo "<h2>Testing Clone Section Performance</h2>\n";
    
    // Test main clone index
    echo "<h3>Testing Main Clone Index</h3>\n";
    $startTime = microtime(true);
    
    $controller = new CloneController();
    
    // Capture output
    ob_start();
    $controller->index();
    $output = ob_get_clean();
    
    $endTime = microtime(true);
    $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
    
    echo "<strong>Main Clone Index:</strong> " . round($executionTime, 2) . " ms<br>\n";
    echo "<strong>Output length:</strong> " . strlen($output) . " characters<br>\n";
    echo "<strong>Status:</strong> " . ($executionTime < 1000 ? 'GOOD' : 'SLOW') . "<br>\n";
    
    // Test Pinterest dashboard
    echo "<h3>Testing Pinterest Dashboard</h3>\n";
    $startTime = microtime(true);
    
    ob_start();
    $controller->pinterestDashboard();
    $output = ob_get_clean();
    
    $endTime = microtime(true);
    $executionTime = ($endTime - $startTime) * 1000;
    
    echo "<strong>Pinterest Dashboard:</strong> " . round($executionTime, 2) . " ms<br>\n";
    echo "<strong>Output length:</strong> " . strlen($output) . " characters<br>\n";
    echo "<strong>Status:</strong> " . ($executionTime < 1000 ? 'GOOD' : 'SLOW') . "<br>\n";
    
    // Test disabled features
    echo "<h3>Testing Disabled Features</h3>\n";
    
    $disabledMethods = [
        'pinterestScraper',
        'pinterestTrends', 
        'pinterestVisualAnalysis',
        'pinterestBoards',
        'createBoardForm'
    ];
    
    foreach ($disabledMethods as $method) {
        $startTime = microtime(true);
        
        ob_start();
        $controller->$method();
        $output = ob_get_clean();
        
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;
        
        echo "<strong>$method:</strong> " . round($executionTime, 2) . " ms - ";
        echo ($executionTime < 100 ? 'FAST (disabled correctly)' : 'SLOW') . "<br>\n";
    }
    
    echo "<h3>Performance Summary</h3>\n";
    echo "<p>✅ Clone section has been optimized for performance</p>\n";
    echo "<p>✅ Heavy features have been disabled</p>\n";
    echo "<p>✅ JavaScript files have been disabled</p>\n";
    echo "<p>✅ Pinterest model initialization has been disabled</p>\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
