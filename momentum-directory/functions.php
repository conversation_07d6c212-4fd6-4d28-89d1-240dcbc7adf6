<?php
/**
 * Momentum Directory functions and definitions
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('MOMENTUM_DIR_VERSION', '1.0.0');
define('MOMENTUM_DIR_PATH', get_template_directory());
define('MOMENTUM_DIR_URL', get_template_directory_uri());

// Include theme configuration
require_once MOMENTUM_DIR_PATH . '/inc/config.php';

/**
 * Theme setup
 */
function momentum_directory_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'momentum-directory'),
        'footer' => esc_html__('Footer Menu', 'momentum-directory'),
    ));

    // Switch default core markup to output valid HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));

    // Add theme support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 250,
        'width'       => 250,
        'flex-width'  => true,
        'flex-height' => true,
    ));
}
add_action('after_setup_theme', 'momentum_directory_setup');

/**
 * Register widget area
 */
function momentum_directory_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', 'momentum-directory'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here.', 'momentum-directory'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => esc_html__('Footer Widgets', 'momentum-directory'),
        'id'            => 'footer-widgets',
        'description'   => esc_html__('Add footer widgets here.', 'momentum-directory'),
        'before_widget' => '<div class="col-md-4 col-sm-6"><section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section></div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'momentum_directory_widgets_init');

/**
 * Enqueue scripts and styles
 */
function momentum_directory_scripts() {
    // Enqueue Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500&display=swap', array(), null);
    
    // Enqueue Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0');
    
    // Enqueue main stylesheet
    wp_enqueue_style('momentum-directory-style', get_stylesheet_uri(), array(), MOMENTUM_DIR_VERSION);
    
    // Enqueue custom CSS
    wp_enqueue_style('momentum-directory-custom', MOMENTUM_DIR_URL . '/assets/css/custom.css', array(), MOMENTUM_DIR_VERSION);
    
    // Enqueue jQuery
    wp_enqueue_script('jquery');
    
    // Enqueue custom JavaScript
    wp_enqueue_script('momentum-directory-script', MOMENTUM_DIR_URL . '/assets/js/main.js', array('jquery'), MOMENTUM_DIR_VERSION, true);
    
    // Localize script for AJAX
    wp_localize_script('momentum-directory-script', 'momentum_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('momentum-ajax-nonce'),
    ));
    
    // Comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'momentum_directory_scripts');

/**
 * Register Custom Post Type for Business Listings
 */
function momentum_directory_register_post_types() {
    global $customPostKey;
    
    $labels = array(
        'name'                  => _x('Business Listings', 'Post Type General Name', 'momentum-directory'),
        'singular_name'         => _x('Business Listing', 'Post Type Singular Name', 'momentum-directory'),
        'menu_name'             => __('Business Listings', 'momentum-directory'),
        'name_admin_bar'        => __('Business Listing', 'momentum-directory'),
        'archives'              => __('Listing Archives', 'momentum-directory'),
        'attributes'            => __('Listing Attributes', 'momentum-directory'),
        'parent_item_colon'     => __('Parent Listing:', 'momentum-directory'),
        'all_items'             => __('All Listings', 'momentum-directory'),
        'add_new_item'          => __('Add New Listing', 'momentum-directory'),
        'add_new'               => __('Add New', 'momentum-directory'),
        'new_item'              => __('New Listing', 'momentum-directory'),
        'edit_item'             => __('Edit Listing', 'momentum-directory'),
        'update_item'           => __('Update Listing', 'momentum-directory'),
        'view_item'             => __('View Listing', 'momentum-directory'),
        'view_items'            => __('View Listings', 'momentum-directory'),
        'search_items'          => __('Search Listing', 'momentum-directory'),
        'not_found'             => __('Not found', 'momentum-directory'),
        'not_found_in_trash'    => __('Not found in Trash', 'momentum-directory'),
        'featured_image'        => __('Featured Image', 'momentum-directory'),
        'set_featured_image'    => __('Set featured image', 'momentum-directory'),
        'remove_featured_image' => __('Remove featured image', 'momentum-directory'),
        'use_featured_image'    => __('Use as featured image', 'momentum-directory'),
        'insert_into_item'      => __('Insert into listing', 'momentum-directory'),
        'uploaded_to_this_item' => __('Uploaded to this listing', 'momentum-directory'),
        'items_list'            => __('Listings list', 'momentum-directory'),
        'items_list_navigation' => __('Listings list navigation', 'momentum-directory'),
        'filter_items_list'     => __('Filter listings list', 'momentum-directory'),
    );
    
    $args = array(
        'label'                 => __('Business Listing', 'momentum-directory'),
        'description'           => __('Business listings for the directory', 'momentum-directory'),
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-store',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rewrite'               => array('slug' => 'business'),
    );
    
    register_post_type($customPostKey, $args);
}
add_action('init', 'momentum_directory_register_post_types');

/**
 * Register Custom Taxonomy for Business Locations
 */
function momentum_directory_register_taxonomies() {
    global $customPostKey, $customTaxonomyKey;
    
    $labels = array(
        'name'                       => _x('Business Locations', 'Taxonomy General Name', 'momentum-directory'),
        'singular_name'              => _x('Business Location', 'Taxonomy Singular Name', 'momentum-directory'),
        'menu_name'                  => __('Locations', 'momentum-directory'),
        'all_items'                  => __('All Locations', 'momentum-directory'),
        'parent_item'                => __('Parent Location', 'momentum-directory'),
        'parent_item_colon'          => __('Parent Location:', 'momentum-directory'),
        'new_item_name'              => __('New Location Name', 'momentum-directory'),
        'add_new_item'               => __('Add New Location', 'momentum-directory'),
        'edit_item'                  => __('Edit Location', 'momentum-directory'),
        'update_item'                => __('Update Location', 'momentum-directory'),
        'view_item'                  => __('View Location', 'momentum-directory'),
        'separate_items_with_commas' => __('Separate locations with commas', 'momentum-directory'),
        'add_or_remove_items'        => __('Add or remove locations', 'momentum-directory'),
        'choose_from_most_used'      => __('Choose from the most used', 'momentum-directory'),
        'popular_items'              => __('Popular Locations', 'momentum-directory'),
        'search_items'               => __('Search Locations', 'momentum-directory'),
        'not_found'                  => __('Not Found', 'momentum-directory'),
        'no_terms'                   => __('No locations', 'momentum-directory'),
        'items_list'                 => __('Locations list', 'momentum-directory'),
        'items_list_navigation'      => __('Locations list navigation', 'momentum-directory'),
    );
    
    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rewrite'                    => array('slug' => 'location'),
    );
    
    register_taxonomy($customTaxonomyKey, array($customPostKey), $args);
}
add_action('init', 'momentum_directory_register_taxonomies');

/**
 * AJAX handler for getting child taxonomies
 */
function momentum_get_child_taxonomies() {
    global $customTaxonomyKey;
    
    // Check nonce for security
    check_ajax_referer('momentum-ajax-nonce', 'nonce');
    
    $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
    
    if ($parent_id) {
        $terms = get_terms(array(
            'taxonomy' => $customTaxonomyKey,
            'hide_empty' => false,
            'parent' => $parent_id,
        ));
        
        $response = array();
        
        if (!empty($terms) && !is_wp_error($terms)) {
            foreach ($terms as $term) {
                $response[] = array(
                    'id' => $term->term_id,
                    'name' => $term->name,
                    'link' => get_term_link($term),
                );
            }
        }
        
        wp_send_json($response);
    }
    
    wp_send_json(array());
}
add_action('wp_ajax_get_child_taxonomies', 'momentum_get_child_taxonomies');
add_action('wp_ajax_nopriv_get_child_taxonomies', 'momentum_get_child_taxonomies');

/**
 * Include additional files
 */
require_once MOMENTUM_DIR_PATH . '/inc/template-functions.php';
require_once MOMENTUM_DIR_PATH . '/inc/acf-fields.php';
require_once MOMENTUM_DIR_PATH . '/inc/breadcrumbs.php';
