<?php
/**
 * The template for displaying the footer
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

    <footer id="colophon" class="site-footer">
        <div class="footer-widgets">
            <div class="container">
                <div class="row">
                    <div class="col col-md-4 col-sm-6">
                        <div class="footer-widget footer-about">
                            <?php if (file_exists(get_template_directory() . '/assets/images/logo.png')) : ?>
                                <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/logo.png'); ?>" alt="<?php echo esc_attr(get_bloginfo('name')); ?>" class="footer-logo">
                            <?php else : ?>
                                <h3 class="footer-logo-text"><?php bloginfo('name'); ?></h3>
                            <?php endif; ?>
                            <p><?php echo esc_html(get_bloginfo('description')); ?></p>
                            <div class="social-links">
                                <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                                <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                                <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                                <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col col-md-4 col-sm-6">
                        <div class="footer-widget footer-links">
                            <h3 class="widget-title"><?php esc_html_e('Quick Links', 'momentum-directory'); ?></h3>
                            <?php
                            wp_nav_menu(array(
                                'theme_location' => 'footer',
                                'menu_class'     => 'footer-menu',
                                'container'      => false,
                                'depth'          => 1,
                                'fallback_cb'    => function() {
                                    echo '<ul class="footer-menu">';
                                    echo '<li><a href="' . esc_url(home_url('/')) . '">' . esc_html__('Home', 'momentum-directory') . '</a></li>';
                                    echo '<li><a href="#">' . esc_html__('About Us', 'momentum-directory') . '</a></li>';
                                    echo '<li><a href="#">' . esc_html__('Listings', 'momentum-directory') . '</a></li>';
                                    echo '<li><a href="#">' . esc_html__('Add Listing', 'momentum-directory') . '</a></li>';
                                    echo '<li><a href="#">' . esc_html__('Contact', 'momentum-directory') . '</a></li>';
                                    echo '<li><a href="#">' . esc_html__('Privacy Policy', 'momentum-directory') . '</a></li>';
                                    echo '</ul>';
                                },
                            ));
                            ?>
                        </div>
                    </div>
                    
                    <div class="col col-md-4 col-sm-12">
                        <div class="footer-widget footer-contact">
                            <h3 class="widget-title"><?php esc_html_e('Contact Us', 'momentum-directory'); ?></h3>
                            <ul class="contact-info">
                                <li>
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>123 Business Street, Suite 100<br>New York, NY 10001</span>
                                </li>
                                <li>
                                    <i class="fas fa-phone"></i>
                                    <span>+****************</span>
                                </li>
                                <li>
                                    <i class="fas fa-envelope"></i>
                                    <span>info@<?php echo esc_html(str_replace('https://', '', home_url('/'))); ?></span>
                                </li>
                                <li>
                                    <i class="fas fa-clock"></i>
                                    <span>Monday - Friday: 9:00 AM - 5:00 PM<br>Saturday - Sunday: Closed</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <div class="container">
                <div class="copyright">
                    <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. <?php esc_html_e('All Rights Reserved.', 'momentum-directory'); ?></p>
                </div>
                <div class="footer-bottom-links">
                    <a href="#"><?php esc_html_e('Terms of Service', 'momentum-directory'); ?></a>
                    <a href="#"><?php esc_html_e('Privacy Policy', 'momentum-directory'); ?></a>
                    <a href="#"><?php esc_html_e('Sitemap', 'momentum-directory'); ?></a>
                </div>
            </div>
        </div>
    </footer><!-- #colophon -->
</div><!-- #page -->

<?php wp_footer(); ?>

<script>
    // Mobile menu toggle
    (function($) {
        $('.menu-toggle').on('click', function() {
            $(this).toggleClass('active');
            $('.main-navigation').toggleClass('toggled');
        });
        
        // User dropdown toggle
        $('.user-menu-toggle').on('click', function(e) {
            e.preventDefault();
            $('.user-dropdown').toggleClass('active');
        });
        
        // Close user dropdown when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.user-menu').length) {
                $('.user-dropdown').removeClass('active');
            }
        });
    })(jQuery);
</script>

</body>
</html>
