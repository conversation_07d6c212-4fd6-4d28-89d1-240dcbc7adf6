<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function momentum_directory_pingback_header() {
    if (is_singular() && pings_open()) {
        printf('<link rel="pingback" href="%s">', esc_url(get_bloginfo('pingback_url')));
    }
}
add_action('wp_head', 'momentum_directory_pingback_header');

/**
 * Get the number of posts in a term
 */
function momentum_get_term_post_count($term_id, $taxonomy, $post_type) {
    $args = array(
        'post_type' => $post_type,
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'tax_query' => array(
            array(
                'taxonomy' => $taxonomy,
                'field' => 'term_id',
                'terms' => $term_id,
            ),
        ),
    );
    
    $query = new WP_Query($args);
    return $query->found_posts;
}

/**
 * Format work hours from JSON
 */
function momentum_format_work_hours($work_time_json) {
    if (!$work_time_json) {
        return '';
    }
    
    $work_time = json_decode($work_time_json, true);
    if (!isset($work_time['work_hours']['timetable'])) {
        return '';
    }
    
    $days = array(
        'monday' => __('Monday', 'momentum-directory'),
        'tuesday' => __('Tuesday', 'momentum-directory'),
        'wednesday' => __('Wednesday', 'momentum-directory'),
        'thursday' => __('Thursday', 'momentum-directory'),
        'friday' => __('Friday', 'momentum-directory'),
        'saturday' => __('Saturday', 'momentum-directory'),
        'sunday' => __('Sunday', 'momentum-directory'),
    );
    
    $output = '<div class="business-hours">';
    
    foreach ($days as $day_key => $day_label) {
        $day_hours = $work_time['work_hours']['timetable'][$day_key][0] ?? null;
        
        $output .= '<div class="hours-row">';
        $output .= '<span class="day">' . esc_html($day_label) . '</span>';
        
        if ($day_hours && isset($day_hours['open']['hour']) && isset($day_hours['close']['hour'])) {
            $open_hour = sprintf('%02d:%02d', $day_hours['open']['hour'], $day_hours['open']['minute']);
            $close_hour = sprintf('%02d:%02d', $day_hours['close']['hour'], $day_hours['close']['minute']);
            
            if ($open_hour === '00:00' && $close_hour === '00:00') {
                $output .= '<span class="hours closed">' . esc_html__('Closed', 'momentum-directory') . '</span>';
            } else {
                $output .= '<span class="hours">' . esc_html($open_hour) . ' - ' . esc_html($close_hour) . '</span>';
            }
        } else {
            $output .= '<span class="hours not-available">' . esc_html__('Not available', 'momentum-directory') . '</span>';
        }
        
        $output .= '</div>';
    }
    
    $output .= '</div>';
    
    return $output;
}

/**
 * Format rating from JSON
 */
function momentum_format_rating($rating_json) {
    if (!$rating_json) {
        return '';
    }
    
    $rating_data = json_decode($rating_json, true);
    if (!isset($rating_data['value'])) {
        return '';
    }
    
    $rating = $rating_data['value'];
    $max_rating = $rating_data['rating_max'] ?? 5;
    $votes_count = $rating_data['votes_count'] ?? 0;
    
    $output = '<div class="rating">';
    
    // Stars
    for ($i = 1; $i <= 5; $i++) {
        if ($i <= $rating) {
            $output .= '<i class="fas fa-star"></i>';
        } elseif ($i - 0.5 <= $rating) {
            $output .= '<i class="fas fa-star-half-alt"></i>';
        } else {
            $output .= '<i class="far fa-star"></i>';
        }
    }
    
    // Rating value
    $output .= '<span class="rating-value">' . esc_html(number_format($rating, 1)) . '</span>';
    
    // Votes count
    if ($votes_count > 0) {
        $output .= '<span class="votes-count">(' . esc_html($votes_count) . ' ' . esc_html(_n('vote', 'votes', $votes_count, 'momentum-directory')) . ')</span>';
    }
    
    $output .= '</div>';
    
    return $output;
}

/**
 * Format contact info from JSON
 */
function momentum_format_contact_info($contact_info_json) {
    if (!$contact_info_json) {
        return '';
    }
    
    $contact_info = json_decode($contact_info_json, true);
    if (!is_array($contact_info)) {
        return '';
    }
    
    $output = '<div class="contact-info">';
    
    foreach ($contact_info as $contact) {
        if (isset($contact['type']) && isset($contact['value'])) {
            $icon = 'fa-info-circle';
            $label = ucfirst($contact['type']);
            
            if ($contact['type'] === 'telephone') {
                $icon = 'fa-phone';
                $label = __('Phone', 'momentum-directory');
                $output .= '<div class="contact-item">';
                $output .= '<i class="fas ' . esc_attr($icon) . '"></i>';
                $output .= '<span>' . esc_html($label) . ': <a href="tel:' . esc_attr($contact['value']) . '">' . esc_html($contact['value']) . '</a></span>';
                $output .= '</div>';
            } elseif ($contact['type'] === 'email') {
                $icon = 'fa-envelope';
                $label = __('Email', 'momentum-directory');
                $output .= '<div class="contact-item">';
                $output .= '<i class="fas ' . esc_attr($icon) . '"></i>';
                $output .= '<span>' . esc_html($label) . ': <a href="mailto:' . esc_attr($contact['value']) . '">' . esc_html($contact['value']) . '</a></span>';
                $output .= '</div>';
            } elseif ($contact['type'] === 'website') {
                $icon = 'fa-globe';
                $label = __('Website', 'momentum-directory');
                $output .= '<div class="contact-item">';
                $output .= '<i class="fas ' . esc_attr($icon) . '"></i>';
                $output .= '<span>' . esc_html($label) . ': <a href="' . esc_url($contact['value']) . '" target="_blank" rel="noopener noreferrer">' . esc_html($contact['value']) . '</a></span>';
                $output .= '</div>';
            } else {
                $output .= '<div class="contact-item">';
                $output .= '<i class="fas ' . esc_attr($icon) . '"></i>';
                $output .= '<span>' . esc_html($label) . ': ' . esc_html($contact['value']) . '</span>';
                $output .= '</div>';
            }
        }
    }
    
    $output .= '</div>';
    
    return $output;
}

/**
 * Get human-readable attribute name
 */
function momentum_get_attribute_label($attribute) {
    $attribute = str_replace(array('has_', '_'), array('', ' '), $attribute);
    return ucwords($attribute);
}

/**
 * Add custom body classes
 */
function momentum_directory_body_classes($classes) {
    // Add a class if we're on the directory pages
    if (is_post_type_archive('business_listing') || is_singular('business_listing') || is_tax('business_location')) {
        $classes[] = 'directory-page';
    }
    
    // Add a class for the front page
    if (is_front_page()) {
        $classes[] = 'directory-home';
    }
    
    return $classes;
}
add_filter('body_class', 'momentum_directory_body_classes');
