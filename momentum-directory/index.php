<?php
/**
 * The main template file
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <div class="container">
            <div class="row">
                <div class="col col-md-8">
                    <?php
                    if (have_posts()) :
                        
                        /* Start the Loop */
                        while (have_posts()) :
                            the_post();
                            
                            /*
                             * Include the Post-Type-specific template for the content.
                             * If you want to override this in a child theme, then include a file
                             * called content-___.php (where ___ is the Post Type name) and that will be used instead.
                             */
                            get_template_part('template-parts/content', get_post_type());
                            
                        endwhile;
                        
                        the_posts_navigation(array(
                            'prev_text' => '<i class="fas fa-arrow-left"></i> ' . esc_html__('Older posts', 'momentum-directory'),
                            'next_text' => esc_html__('Newer posts', 'momentum-directory') . ' <i class="fas fa-arrow-right"></i>',
                        ));
                        
                    else :
                        
                        get_template_part('template-parts/content', 'none');
                        
                    endif;
                    ?>
                </div>
                
                <div class="col col-md-4">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>
    </main><!-- #main -->
</div><!-- #primary -->

<?php
get_footer();
