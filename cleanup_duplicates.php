<?php
/**
 * <PERSON><PERSON><PERSON> to clean up duplicate entries in the productivity_strategies table
 */

// Include database configuration
require_once __DIR__ . '/src/config/database.php';
require_once __DIR__ . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

echo "Starting cleanup of duplicate productivity strategies...\n";

// First, identify duplicate strategy names
$sql = "SELECT name, COUNT(*) as count, MIN(id) as keep_id
        FROM productivity_strategies
        GROUP BY name
        HAVING COUNT(*) > 1";

$duplicates = $db->fetchAll($sql);

if (empty($duplicates)) {
    echo "No duplicates found.\n";
    exit;
}

echo "Found " . count($duplicates) . " strategies with duplicates.\n";

// Begin transaction
$db->beginTransaction();

try {
    // For each duplicate set
    foreach ($duplicates as $duplicate) {
        $name = $duplicate['name'];
        $keepId = $duplicate['keep_id'];
        
        echo "Processing duplicate strategy: {$name} (keeping ID: {$keepId})\n";
        
        // Find all IDs except the one to keep
        $sql = "SELECT id FROM productivity_strategies 
                WHERE name = ? AND id != ?";
        $duplicateIds = $db->fetchAll($sql, [$name, $keepId]);
        
        if (empty($duplicateIds)) {
            echo "  No duplicate IDs found for {$name}.\n";
            continue;
        }
        
        $ids = array_map(function($item) {
            return $item['id'];
        }, $duplicateIds);
        
        echo "  Found duplicate IDs: " . implode(', ', $ids) . "\n";
        
        // Update user_strategies to point to the ID we're keeping
        foreach ($ids as $id) {
            $sql = "UPDATE user_strategies 
                    SET strategy_id = ? 
                    WHERE strategy_id = ?";
            $result = $db->query($sql, [$keepId, $id]);
            
            echo "  Updated user_strategies for ID {$id} -> {$keepId}: " . 
                 ($result ? "Success" : "Failed") . "\n";
        }
        
        // Delete the duplicate entries
        foreach ($ids as $id) {
            $sql = "DELETE FROM productivity_strategies WHERE id = ?";
            $result = $db->query($sql, [$id]);
            
            echo "  Deleted duplicate strategy ID {$id}: " . 
                 ($result ? "Success" : "Failed") . "\n";
        }
    }
    
    // Commit the transaction
    $db->commit();
    echo "Cleanup completed successfully.\n";
    
} catch (Exception $e) {
    // Rollback the transaction on error
    $db->rollback();
    echo "Error during cleanup: " . $e->getMessage() . "\n";
    exit(1);
}

echo "Verifying results...\n";

// Verify no more duplicates exist
$sql = "SELECT name, COUNT(*) as count
        FROM productivity_strategies
        GROUP BY name
        HAVING COUNT(*) > 1";

$remainingDuplicates = $db->fetchAll($sql);

if (empty($remainingDuplicates)) {
    echo "Verification successful: No duplicates remain.\n";
} else {
    echo "Warning: " . count($remainingDuplicates) . " strategies still have duplicates.\n";
    foreach ($remainingDuplicates as $duplicate) {
        echo "  {$duplicate['name']}: {$duplicate['count']} entries\n";
    }
}

echo "Done.\n";
