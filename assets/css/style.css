/* AIToolsHub - Custom Styles */

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Tool Card Hover Effects */
.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Toggle Switch */
.toggle-checkbox:checked {
    right: 0;
    border-color: #3b82f6;
}

.toggle-checkbox:checked + .toggle-label {
    background-color: #3b82f6;
}

/* Range Slider */
input[type="range"] {
    -webkit-appearance: none;
    height: 6px;
    background: #e2e8f0;
    border-radius: 5px;
    background-image: linear-gradient(#3b82f6, #3b82f6);
    background-size: 50% 100%;
    background-repeat: no-repeat;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 18px;
    width: 18px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    box-shadow: 0 0 2px 0 #555;
}

input[type="range"]::-webkit-slider-runnable-track {
    -webkit-appearance: none;
    box-shadow: none;
    border: none;
    background: transparent;
}

/* Custom Checkbox */
.custom-checkbox {
    display: none;
}

.custom-checkbox + label {
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    display: inline-block;
}

.custom-checkbox + label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border: 2px solid #cbd5e1;
    background: #fff;
    border-radius: 4px;
}

.custom-checkbox:checked + label:before {
    background: #3b82f6;
    border-color: #3b82f6;
}

.custom-checkbox:checked + label:after {
    content: '';
    position: absolute;
    left: 7px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Star Rating */
.star-rating input {
    display: none;
}

.star-rating label {
    float: right;
    cursor: pointer;
    color: #ccc;
}

.star-rating label:before {
    content: '\e68d';
    font-family: 'remixicon';
}

.star-rating input:checked ~ label {
    color: #ffb700;
}

.star-rating label:hover,
.star-rating label:hover ~ label {
    color: #ffb700;
}

.star-rating input:checked + label:hover,
.star-rating input:checked ~ label:hover,
.star-rating label:hover ~ input:checked ~ label,
.star-rating input:checked ~ label:hover ~ label {
    color: #ffc730;
}

/* Hero Gradient */
.hero-gradient {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 50%, rgba(255,255,255,0) 100%);
}

/* Remix Icon Fix */
:where([class^="ri-"])::before { 
    content: "\f3c2"; 
}
