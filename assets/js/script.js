// AIToolsHub - JavaScript Functions

document.addEventListener('DOMContentLoaded', function() {
    // Theme Toggle
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('change', function() {
            if (this.checked) {
                // Dark mode
                document.documentElement.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            } else {
                // Light mode
                document.documentElement.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            }
        });

        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            themeToggle.checked = true;
            document.documentElement.classList.add('dark');
        }
    }

    // Modal Control
    const modal = document.getElementById('tool-preview-modal');
    const closeModal = document.getElementById('close-modal');
    const toolCards = document.querySelectorAll('.tool-card');

    if (modal && closeModal) {
        toolCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // Don't open modal if clicking on buttons
                if (e.target.closest('button') || e.target.closest('a')) {
                    return;
                }
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });
        });

        closeModal.addEventListener('click', function() {
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        });

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        });
    }

    // Range Slider
    const rangeInputs = document.querySelectorAll('input[type="range"]');
    
    function handleInputChange(e) {
        let target = e.target;
        const min = target.min;
        const max = target.max;
        const val = target.value;
        target.style.backgroundSize = (val - min) * 100 / (max - min) + '% 100%';
    }

    rangeInputs.forEach(input => {
        input.addEventListener('input', handleInputChange);
        // Set initial value
        const min = input.min;
        const max = input.max;
        const val = input.value;
        input.style.backgroundSize = (val - min) * 100 / (max - min) + '% 100%';
    });

    // Mobile Menu Toggle
    const mobileMenuButton = document.querySelector('[data-mobile-menu]');
    const mobileMenu = document.querySelector('[data-mobile-menu-content]');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // Search Functionality
    const searchInput = document.querySelector('[data-search]');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const toolCards = document.querySelectorAll('.tool-card');
            
            toolCards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const description = card.querySelector('p').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }

    // Smooth Scrolling for Anchor Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Newsletter Form
    const newsletterForm = document.querySelector('[data-newsletter-form]');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            
            // Simple email validation
            if (email && email.includes('@')) {
                alert('Thank you for subscribing! We\'ll send you updates soon.');
                this.reset();
            } else {
                alert('Please enter a valid email address.');
            }
        });
    }

    // Bookmark Functionality
    document.querySelectorAll('[data-bookmark]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const icon = this.querySelector('i');
            
            if (icon.classList.contains('ri-bookmark-line')) {
                icon.classList.remove('ri-bookmark-line');
                icon.classList.add('ri-bookmark-fill');
                this.classList.add('text-primary');
            } else {
                icon.classList.remove('ri-bookmark-fill');
                icon.classList.add('ri-bookmark-line');
                this.classList.remove('text-primary');
            }
        });
    });

    // Share Functionality
    document.querySelectorAll('[data-share]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            
            if (navigator.share) {
                navigator.share({
                    title: 'AIToolsHub',
                    text: 'Check out this amazing AI tool!',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Link copied to clipboard!');
                });
            }
        });
    });
});
