<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include the Database class
require_once BASE_PATH . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

echo "Checking if transaction_receipts table exists...\n";
$checkTable = $db->query("SHOW TABLES LIKE 'transaction_receipts'");
$tableExists = $checkTable && $checkTable->rowCount() > 0;

if ($tableExists) {
    echo "transaction_receipts table already exists.\n";
} else {
    echo "Creating transaction_receipts table...\n";
    
    // Read the schema file
    $schema = file_get_contents(BASE_PATH . '/database/receipt_management_schema.sql');
    
    // Split the schema into individual statements
    $statements = explode(';', $schema);
    
    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $result = $db->query($statement);
            if ($result) {
                echo ".";
            } else {
                echo "x";
            }
        }
    }
    
    echo "\nSchema applied successfully!\n";
}

// Check if table exists
echo "\nChecking database table:\n";
$result = $db->query("SHOW TABLES LIKE 'transaction_receipts'");
$exists = $result && $result->rowCount() > 0;
echo "- transaction_receipts: " . ($exists ? "EXISTS" : "MISSING") . "\n";

if ($exists) {
    // Check table structure
    $result = $db->query("DESCRIBE transaction_receipts");
    $columns = $result->fetchAll(PDO::FETCH_COLUMN);
    echo "  Columns: " . implode(", ", $columns) . "\n";
}

echo "\nDone!\n";
