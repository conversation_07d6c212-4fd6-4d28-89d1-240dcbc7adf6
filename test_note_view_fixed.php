<?php
/**
 * Test the fixed note view
 */

// Start session and set up environment
session_start();

// Simulate logged-in user
$_SESSION['user'] = [
    'id' => 2,
    'name' => 'Test User',
    'email' => '<EMAIL>'
];

require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';
require_once 'src/utils/Session.php';
require_once 'src/utils/View.php';

try {
    $noteModel = new Note();
    $noteId = 2; // Test with note ID 2
    
    // Get note
    $note = $noteModel->find($noteId);
    
    if ($note) {
        echo "<h2>Testing Fixed Note View</h2>\n";
        echo "<strong>Note ID:</strong> " . $note['id'] . "<br>\n";
        echo "<strong>Title:</strong> " . htmlspecialchars($note['title']) . "<br>\n";
        echo "<strong>Content exists:</strong> " . (!empty($note['content']) ? 'YES' : 'NO') . "<br>\n";
        echo "<strong>Content length:</strong> " . strlen($note['content'] ?? '') . "<br>\n";
        
        // Test the exact condition from the view
        $contentCondition = !empty($note['content']) && trim($note['content']) !== '';
        echo "<strong>View condition result:</strong> " . ($contentCondition ? 'PASS - Content should show' : 'FAIL - Content will not show') . "<br>\n";
        
        if ($contentCondition) {
            echo "<h3>Content Preview (as it would appear in view):</h3>\n";
            echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>\n";
            echo nl2br(htmlspecialchars($note['content']));
            echo "</div>\n";
        }
        
        // Test with a few other notes
        echo "<h3>Testing Other Notes</h3>\n";
        $allNotes = $noteModel->getUserNotes(2, [], 5, 0);
        foreach ($allNotes as $testNote) {
            $condition = !empty($testNote['content']) && trim($testNote['content']) !== '';
            echo "<strong>Note {$testNote['id']}:</strong> {$testNote['title']} - ";
            echo "Content: " . ($condition ? 'WILL SHOW' : 'WILL NOT SHOW') . "<br>\n";
        }
        
    } else {
        echo "Note not found!<br>\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
