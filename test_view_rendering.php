<?php
/**
 * Test the actual view rendering for note 12
 */

// Start session and set up environment
session_start();

// Simulate logged-in user
$_SESSION['user'] = [
    'id' => 2,
    'name' => 'Test User',
    'email' => '<EMAIL>'
];

require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';
require_once 'src/utils/Session.php';
require_once 'src/utils/View.php';

try {
    $noteModel = new Note();
    $noteId = 12;
    
    // Get note
    $note = $noteModel->find($noteId);
    $relatedNotes = []; // Empty for testing
    
    if ($note) {
        echo "<h2>Testing View Rendering for Note 12</h2>\n";
        
        // Test the exact condition from the view
        $condition = !empty($note['content']) && trim($note['content']) !== '';
        echo "<strong>Content condition:</strong> " . ($condition ? 'TRUE' : 'FALSE') . "<br>\n";
        
        // Simulate the exact view rendering logic
        echo "<h3>Simulated View Output</h3>\n";
        echo "<div style='border: 2px solid red; padding: 20px; margin: 10px;'>\n";
        
        if (!empty($note['content']) && trim($note['content']) !== '') {
            echo "<div style='background: lightgreen; padding: 10px; margin: 5px;'>\n";
            echo "<strong>CONTENT SECTION SHOULD RENDER:</strong><br>\n";
            echo "<div style='background: white; padding: 10px; border: 1px solid #ccc;'>\n";
            echo nl2br(htmlspecialchars($note['content']));
            echo "</div>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: lightcoral; padding: 10px; margin: 5px;'>\n";
            echo "<strong>NO CONTENT SECTION WOULD RENDER</strong><br>\n";
            echo "This means the 'No content yet' message would show instead.\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
        
        // Test View::escape function
        echo "<h3>Testing View::escape Function</h3>\n";
        $escapedContent = View::escape($note['content']);
        echo "<strong>Original content length:</strong> " . strlen($note['content']) . "<br>\n";
        echo "<strong>Escaped content length:</strong> " . strlen($escapedContent) . "<br>\n";
        echo "<strong>Escaped content preview:</strong> " . substr($escapedContent, 0, 100) . "...<br>\n";
        
        // Test nl2br function
        echo "<h3>Testing nl2br Function</h3>\n";
        $nlbrContent = nl2br($escapedContent);
        echo "<strong>After nl2br length:</strong> " . strlen($nlbrContent) . "<br>\n";
        echo "<strong>After nl2br preview:</strong> " . substr($nlbrContent, 0, 100) . "...<br>\n";
        
        // Show the exact HTML that would be generated
        echo "<h3>Exact HTML Output</h3>\n";
        echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
        echo htmlspecialchars(nl2br(View::escape($note['content'])));
        echo "</pre>\n";
        
    } else {
        echo "Note not found!<br>\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
