<?php
/**
 * YouTube Agent Controller
 * 
 * Handles the YouTube Agent functionality
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/YouTubeSearch.php';
require_once __DIR__ . '/../models/YouTubeVideo.php';
require_once __DIR__ . '/../models/YouTubeAnalysis.php';
require_once __DIR__ . '/../models/YouTubeSavedVideo.php';
require_once __DIR__ . '/../models/YouTubeAPIQuota.php';
require_once __DIR__ . '/../utils/Session.php';
require_once __DIR__ . '/../utils/Config.php';

class YouTubeAgentController extends BaseController {
    protected $searchModel;
    protected $videoModel;
    protected $analysisModel;
    protected $savedVideoModel;
    protected $quotaModel;
    protected $config;
    
    public function __construct() {
        parent::__construct();
        $this->searchModel = new YouTubeSearch();
        $this->videoModel = new YouTubeVideo();
        $this->analysisModel = new YouTubeAnalysis();
        $this->savedVideoModel = new YouTubeSavedVideo();
        $this->quotaModel = new YouTubeAPIQuota();
        $this->config = new Config();
    }
    
    /**
     * Display the YouTube Agent dashboard
     */
    public function index() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get recent searches
        $recentSearches = $this->searchModel->getUserSearches($userId, 5);
        
        // Get recent analyses
        $recentAnalyses = $this->analysisModel->getUserAnalyses($userId, 5);
        
        // Get saved videos
        $savedVideos = $this->savedVideoModel->getUserSavedVideos($userId, null, 5);
        
        // Get API quota usage
        $quotaUsage = $this->quotaModel->getQuotaUsage($userId);
        
        $this->view('youtube_agent/dashboard', [
            'title' => 'YouTube Agent',
            'recentSearches' => $recentSearches,
            'recentAnalyses' => $recentAnalyses,
            'savedVideos' => $savedVideos,
            'quotaUsage' => $quotaUsage
        ]);
    }
    
    /**
     * Display the search form
     */
    public function search() {
        $this->requireLogin();
        
        $this->view('youtube_agent/search', [
            'title' => 'YouTube Search'
        ]);
    }
    
    /**
     * Process the search form
     */
    public function executeSearch() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/youtube-agent/search');
            return;
        }
        
        $searchQuery = $_POST['search_query'] ?? '';
        $searchType = $_POST['search_type'] ?? 'keyword';
        
        if (empty($searchQuery)) {
            Session::setFlash('error', 'Search query is required');
            $this->redirect('/youtube-agent/search');
            return;
        }
        
        // Create the search
        $searchData = [
            'user_id' => $userId,
            'search_query' => $searchQuery,
            'search_type' => $searchType,
            'status' => 'pending'
        ];
        
        $searchId = $this->searchModel->create($searchData);
        
        if (!$searchId) {
            Session::setFlash('error', 'Failed to create search');
            $this->redirect('/youtube-agent/search');
            return;
        }
        
        // Get YouTube API key
        $apiKey = $this->config->get('youtube_api_key');
        
        if (empty($apiKey)) {
            Session::setFlash('error', 'YouTube API key is not configured');
            $this->redirect('/youtube-agent/search');
            return;
        }
        
        // Execute the search
        $success = $this->searchModel->executeSearch($searchId, $userId, $apiKey);
        
        if ($success) {
            Session::setFlash('success', 'Search completed successfully');
            $this->redirect('/youtube-agent/search-results/' . $searchId);
        } else {
            Session::setFlash('error', 'Failed to execute search');
            $this->redirect('/youtube-agent/search');
        }
    }
    
    /**
     * Display search results
     * 
     * @param int $searchId Search ID
     */
    public function searchResults($searchId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get the search
        $search = $this->searchModel->getSearch($searchId, $userId);
        
        if (!$search) {
            Session::setFlash('error', 'Search not found');
            $this->redirect('/youtube-agent');
            return;
        }
        
        // Get search videos
        $videos = $this->searchModel->getSearchVideos($searchId, $userId);
        
        $this->view('youtube_agent/search_results', [
            'title' => 'Search Results: ' . $search['search_query'],
            'search' => $search,
            'videos' => $videos
        ]);
    }
    
    /**
     * Display all searches
     */
    public function searches() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get all searches
        $searches = $this->searchModel->getUserSearches($userId);
        
        $this->view('youtube_agent/searches', [
            'title' => 'YouTube Searches',
            'searches' => $searches
        ]);
    }
    
    /**
     * Delete a search
     * 
     * @param int $searchId Search ID
     */
    public function deleteSearch($searchId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Delete the search
        $success = $this->searchModel->deleteIfOwner($searchId, $userId);
        
        if ($success) {
            Session::setFlash('success', 'Search deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete search');
        }
        
        $this->redirect('/youtube-agent/searches');
    }
    
    /**
     * Display video details
     * 
     * @param int $videoId Video ID
     */
    public function viewVideo($videoId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get the video
        $video = $this->videoModel->getVideo($videoId);
        
        if (!$video) {
            Session::setFlash('error', 'Video not found');
            $this->redirect('/youtube-agent');
            return;
        }
        
        // Check if video is saved
        $isSaved = $this->savedVideoModel->isVideoSaved($userId, $videoId);
        
        // Get video analyses
        $analyses = $this->analysisModel->getVideoAnalyses($videoId, $userId);
        
        // Get user folders for saved videos
        $folders = $this->savedVideoModel->getUserFolders($userId);
        
        $this->view('youtube_agent/view_video', [
            'title' => $video['title'],
            'video' => $video,
            'isSaved' => $isSaved,
            'analyses' => $analyses,
            'folders' => $folders
        ]);
    }
    
    /**
     * Save a video
     * 
     * @param int $videoId Video ID
     */
    public function saveVideo($videoId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/youtube-agent/view-video/' . $videoId);
            return;
        }
        
        $folder = $_POST['folder'] ?? null;
        $notes = $_POST['notes'] ?? null;
        
        // Save the video
        $savedData = [
            'user_id' => $userId,
            'video_id' => $videoId,
            'folder' => $folder,
            'notes' => $notes
        ];
        
        $savedId = $this->savedVideoModel->saveVideo($savedData);
        
        if ($savedId) {
            Session::setFlash('success', 'Video saved successfully');
        } else {
            Session::setFlash('error', 'Failed to save video');
        }
        
        $this->redirect('/youtube-agent/view-video/' . $videoId);
    }
    
    /**
     * Display saved videos
     */
    public function savedVideos() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get folder filter
        $folder = $_GET['folder'] ?? null;
        
        // Get all saved videos
        $savedVideos = $this->savedVideoModel->getUserSavedVideos($userId, $folder);
        
        // Get all folders
        $folders = $this->savedVideoModel->getUserFolders($userId);
        
        $this->view('youtube_agent/saved_videos', [
            'title' => 'Saved Videos',
            'savedVideos' => $savedVideos,
            'folders' => $folders,
            'currentFolder' => $folder
        ]);
    }
    
    /**
     * Delete a saved video
     * 
     * @param int $savedId Saved video ID
     */
    public function deleteSavedVideo($savedId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get the saved video
        $savedVideo = $this->savedVideoModel->getSavedById($savedId, $userId);
        
        if (!$savedVideo) {
            Session::setFlash('error', 'Saved video not found');
            $this->redirect('/youtube-agent/saved-videos');
            return;
        }
        
        // Delete the saved video
        $success = $this->savedVideoModel->deleteIfOwner($savedId, $userId);
        
        if ($success) {
            Session::setFlash('success', 'Saved video deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete saved video');
        }
        
        $this->redirect('/youtube-agent/saved-videos');
    }
    
    /**
     * Create a new analysis
     * 
     * @param int $videoId Video ID
     */
    public function createAnalysis($videoId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/youtube-agent/view-video/' . $videoId);
            return;
        }
        
        $analysisType = $_POST['analysis_type'] ?? 'money_making';
        
        // Create the analysis
        $analysisData = [
            'video_id' => $videoId,
            'user_id' => $userId,
            'analysis_type' => $analysisType,
            'status' => 'pending'
        ];
        
        $analysisId = $this->analysisModel->create($analysisData);
        
        if (!$analysisId) {
            Session::setFlash('error', 'Failed to create analysis');
            $this->redirect('/youtube-agent/view-video/' . $videoId);
            return;
        }
        
        // Get YouTube API key
        $apiKey = $this->config->get('youtube_api_key');
        
        if (empty($apiKey)) {
            Session::setFlash('error', 'YouTube API key is not configured');
            $this->redirect('/youtube-agent/view-video/' . $videoId);
            return;
        }
        
        // Execute the analysis
        $success = $this->analysisModel->executeAnalysis($analysisId, $userId, $apiKey);
        
        if ($success) {
            Session::setFlash('success', 'Analysis completed successfully');
            $this->redirect('/youtube-agent/view-analysis/' . $analysisId);
        } else {
            Session::setFlash('error', 'Failed to execute analysis');
            $this->redirect('/youtube-agent/view-video/' . $videoId);
        }
    }
    
    /**
     * Display analysis details
     * 
     * @param int $analysisId Analysis ID
     */
    public function viewAnalysis($analysisId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get the analysis
        $analysis = $this->analysisModel->getAnalysis($analysisId, $userId);
        
        if (!$analysis) {
            Session::setFlash('error', 'Analysis not found');
            $this->redirect('/youtube-agent');
            return;
        }
        
        $this->view('youtube_agent/view_analysis', [
            'title' => 'Analysis: ' . $analysis['video_title'],
            'analysis' => $analysis
        ]);
    }
    
    /**
     * Display all analyses
     */
    public function analyses() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get all analyses
        $analyses = $this->analysisModel->getUserAnalyses($userId);
        
        $this->view('youtube_agent/analyses', [
            'title' => 'YouTube Analyses',
            'analyses' => $analyses
        ]);
    }
    
    /**
     * Delete an analysis
     * 
     * @param int $analysisId Analysis ID
     */
    public function deleteAnalysis($analysisId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Delete the analysis
        $success = $this->analysisModel->deleteIfOwner($analysisId, $userId);
        
        if ($success) {
            Session::setFlash('success', 'Analysis deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete analysis');
        }
        
        $this->redirect('/youtube-agent/analyses');
    }
    
    /**
     * Display money making strategies
     */
    public function moneyMakingStrategies() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get brigade filter
        $brigade = $_GET['brigade'] ?? null;
        
        // Get strategies
        $strategyModel = new YouTubeMoneyMakingStrategy();
        
        if ($brigade) {
            $strategies = $strategyModel->getStrategiesByBrigade($brigade, $userId);
        } else {
            // Get all strategies from all analyses
            $analyses = $this->analysisModel->getUserAnalyses($userId);
            $strategies = [];
            
            foreach ($analyses as $analysis) {
                $analysisStrategies = $strategyModel->getStrategiesByAnalysis($analysis['id']);
                foreach ($analysisStrategies as $strategy) {
                    $strategy['video_title'] = $analysis['video_title'];
                    $strategy['youtube_id'] = $analysis['youtube_id'];
                    $strategy['thumbnail_url'] = $analysis['thumbnail_url'];
                    $strategies[] = $strategy;
                }
            }
        }
        
        $this->view('youtube_agent/money_making_strategies', [
            'title' => 'Money Making Strategies',
            'strategies' => $strategies,
            'currentBrigade' => $brigade
        ]);
    }
    
    /**
     * Display settings
     */
    public function settings() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Get API quota usage
        $quotaUsage = $this->quotaModel->getQuotaUsage($userId);
        
        // Get YouTube API key
        $apiKey = $this->config->get('youtube_api_key');
        
        $this->view('youtube_agent/settings', [
            'title' => 'YouTube Agent Settings',
            'quotaUsage' => $quotaUsage,
            'apiKey' => $apiKey
        ]);
    }
    
    /**
     * Save settings
     */
    public function saveSettings() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/youtube-agent/settings');
            return;
        }
        
        $apiKey = $_POST['api_key'] ?? '';
        $quotaLimit = (int)($_POST['quota_limit'] ?? 10000);
        
        // Save API key
        $this->config->set('youtube_api_key', $apiKey);
        
        // Update quota limit
        $this->quotaModel->updateQuotaLimit($userId, $quotaLimit);
        
        Session::setFlash('success', 'Settings saved successfully');
        $this->redirect('/youtube-agent/settings');
    }
    
    /**
     * Reset API quota
     */
    public function resetQuota() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];
        
        // Reset quota
        $success = $this->quotaModel->resetQuotaUsage($userId);
        
        if ($success) {
            Session::setFlash('success', 'API quota reset successfully');
        } else {
            Session::setFlash('error', 'Failed to reset API quota');
        }
        
        $this->redirect('/youtube-agent/settings');
    }
}
