<?php
/**
 * Note Controller
 *
 * Handles note-related functionality for the information hub.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Note.php';

class NoteController extends BaseController {
    private $noteModel;

    public function __construct() {
        $this->noteModel = new Note();
    }

    /**
     * Show note list with enhanced filtering and ADHD-friendly features
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Set pagination for performance (ADHD-friendly: not overwhelming)
        $page = max(1, intval($filters['page'] ?? 1));
        $limit = 20; // ADHD-friendly: manageable chunk size
        $offset = ($page - 1) * $limit;

        // Get notes based on filters with pagination (optimized)
        $notes = $this->noteModel->getUserNotes($userId, $filters, $limit, $offset);

        // Only load additional data if we have notes or it's needed for the interface
        $categories = [];
        $popularTags = [];
        $stats = ['total_notes' => 0, 'pinned_notes' => 0, 'high_priority' => 0, 'today_notes' => 0];

        // Load additional data asynchronously for better performance
        if (!empty($notes) || empty($filters)) {
            // Get note categories with counts
            $categories = $this->noteModel->getUserNoteCategories($userId);

            // Get popular tags for quick filtering
            $popularTags = $this->noteModel->getPopularTags($userId, 10);

            // Get note statistics for ADHD insights
            $stats = $this->noteModel->getNoteStats($userId);
        }

        // Separate pinned and regular notes for ADHD-friendly display
        $pinnedNotes = array_filter($notes, function($note) {
            return $note['is_pinned'] == 1;
        });

        $otherNotes = array_filter($notes, function($note) {
            return $note['is_pinned'] != 1;
        });

        $this->view('notes/index', [
            'notes' => $notes,
            'pinnedNotes' => $pinnedNotes,
            'otherNotes' => $otherNotes,
            'categories' => $categories,
            'popularTags' => $popularTags,
            'stats' => $stats,
            'filters' => $filters,
            'currentPage' => $page,
            'hasMorePages' => count($notes) === $limit
        ]);
    }

    /**
     * Show note creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note categories for dropdown
        $categories = $this->noteModel->getUserNoteCategories($userId);

        $this->view('notes/create', [
            'categories' => $categories
        ]);
    }

    /**
     * Process note creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            // Get note categories for dropdown
            $categories = $this->noteModel->getUserNoteCategories($userId);

            $this->view('notes/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $categories
            ]);
            return;
        }

        // Prepare enhanced note data with ADHD-friendly features
        $content = $data['content'] ?? '';
        $wordCount = str_word_count(strip_tags($content));
        $readingTime = max(1, round($wordCount / 200)); // Average reading speed

        $noteData = [
            'user_id' => $userId,
            'title' => $data['title'],
            'content' => $content,
            'category' => $data['category'] ?? null,
            'tags' => $data['tags'] ?? null,
            'is_pinned' => isset($data['is_pinned']) ? 1 : 0,
            'is_favorite' => isset($data['is_favorite']) ? 1 : 0,
            'priority_level' => $data['priority_level'] ?? 'medium',
            'color_code' => $data['color_code'] ?? null,
            'note_type' => $data['note_type'] ?? 'note',
            'word_count' => $wordCount,
            'reading_time' => $readingTime,
            'auto_saved' => 0, // This is a manual save
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create note
        $noteId = $this->noteModel->create($noteData);

        if ($noteId) {
            Session::setFlash('success', 'Note created successfully');
            $this->redirect('/notes');
        } else {
            Session::setFlash('error', 'Failed to create note');

            // Get note categories for dropdown
            $categories = $this->noteModel->getUserNoteCategories($userId);

            $this->view('notes/create', [
                'data' => $data,
                'categories' => $categories
            ]);
        }
    }

    /**
     * Show note details with analytics tracking
     */
    public function viewNote($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            Session::setFlash('error', 'Note not found');
            $this->redirect('/notes');
        }

        // Update last accessed time for analytics
        $this->noteModel->updateLastAccessed($id, $userId);

        // Get related notes for ADHD context switching
        $relatedNotes = $this->getRelatedNotes($note, $userId);

        $this->view('notes/view', [
            'note' => $note,
            'relatedNotes' => $relatedNotes
        ]);
    }

    /**
     * Show note edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            Session::setFlash('error', 'Note not found');
            $this->redirect('/notes');
        }

        // Get note categories for dropdown
        $categories = $this->noteModel->getUserNoteCategories($userId);

        // Extract category names for the datalist
        $categoryNames = array_column($categories, 'category');

        $this->view('notes/edit', [
            'note' => $note,
            'categories' => $categoryNames,
            'stylesheets' => [
                '/css/tools.css',
                '/css/gallery-enhanced.css'
            ]
        ]);
    }

    /**
     * Process note update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            Session::setFlash('error', 'Note not found');
            $this->redirect('/notes');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            // Get note categories for dropdown
            $categories = $this->noteModel->getUserNoteCategories($userId);
            $categoryNames = array_column($categories, 'category');

            $this->view('notes/edit', [
                'errors' => $errors,
                'note' => array_merge($note, $data),
                'categories' => $categoryNames,
                'stylesheets' => [
                    '/css/tools.css',
                    '/css/gallery-enhanced.css'
                ]
            ]);
            return;
        }

        // Prepare enhanced note data with ADHD-friendly features
        $content = $data['content'] ?? '';
        $wordCount = str_word_count(strip_tags($content));
        $readingTime = max(1, round($wordCount / 200)); // Average reading speed

        $noteData = [
            'title' => $data['title'],
            'content' => $content,
            'category' => $data['category'] ?? null,
            'tags' => $data['tags'] ?? null,
            'is_pinned' => isset($data['is_pinned']) ? 1 : 0,
            'is_favorite' => isset($data['is_favorite']) ? 1 : 0,
            'priority_level' => $data['priority_level'] ?? 'medium',
            'color_code' => $data['color_code'] ?? null,
            'word_count' => $wordCount,
            'reading_time' => $readingTime,
            'auto_saved' => 0, // This is a manual save
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update note
        $result = $this->noteModel->update($id, $noteData);

        if ($result) {
            Session::setFlash('success', 'Note updated successfully');
            $this->redirect('/notes/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update note');

            // Get note categories for dropdown
            $categories = $this->noteModel->getUserNoteCategories($userId);
            $categoryNames = array_column($categories, 'category');

            $this->view('notes/edit', [
                'note' => array_merge($note, $data),
                'categories' => $categoryNames,
                'stylesheets' => [
                    '/css/tools.css',
                    '/css/gallery-enhanced.css'
                ]
            ]);
        }
    }

    /**
     * Delete note
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            Session::setFlash('error', 'Note not found');
            $this->redirect('/notes');
        }

        // Delete note
        $result = $this->noteModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Note deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete note');
        }

        $this->redirect('/notes');
    }



    /**
     * Toggle pin status
     */
    public function togglePin($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Note not found'], 404);
            } else {
                Session::setFlash('error', 'Note not found');
                $this->redirect('/notes');
            }
            return;
        }

        // Toggle pin status
        $isPinned = $note['is_pinned'] ? 0 : 1;

        $result = $this->noteModel->update($id, [
            'is_pinned' => $isPinned,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            if ($this->isAjax()) {
                $this->json(['success' => true, 'is_pinned' => $isPinned]);
            } else {
                Session::setFlash('success', $isPinned ? 'Note pinned successfully' : 'Note unpinned successfully');
                $this->redirect('/notes');
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to update pin status'], 500);
            } else {
                Session::setFlash('error', 'Failed to update pin status');
                $this->redirect('/notes');
            }
        }
    }

    /**
     * Auto-save functionality for ADHD users
     */
    public function autoSave() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data) {
            $this->json(['success' => false, 'message' => 'No data provided'], 400);
            return;
        }

        $noteId = $data['note_id'] ?? null;

        try {
            $result = $this->noteModel->autoSave($userId, $noteId, $data);

            if ($result) {
                $this->json([
                    'success' => true,
                    'note_id' => $noteId ?: $result,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'message' => 'Auto-saved successfully'
                ]);
            } else {
                $this->json(['success' => false, 'message' => 'Auto-save failed'], 500);
            }
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'Auto-save error: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Enhanced search with ADHD-friendly features
     */
    public function search() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $query = $this->getQueryData();
        $searchTerm = $query['q'] ?? '';

        // If no search term, redirect to notes index with filters
        if (empty($searchTerm)) {
            $redirectUrl = '/notes';
            $params = [];

            if (!empty($query['category'])) {
                $params['category'] = $query['category'];
            }
            if (!empty($query['priority'])) {
                $params['priority'] = $query['priority'];
            }

            if (!empty($params)) {
                $redirectUrl .= '?' . http_build_query($params);
            }

            $this->redirect($redirectUrl);
            return;
        }

        $results = [];
        $categories = $this->noteModel->getUserNoteCategories($userId);
        $popularTags = $this->noteModel->getPopularTags($userId, 10);

        // Enhanced search with filters
        $filters = [
            'category' => $query['category'] ?? '',
            'priority' => $query['priority'] ?? ''
        ];

        $results = $this->noteModel->searchNotes($userId, $searchTerm, $filters);

        // Use the notes index view for search results
        $this->view('notes/index', [
            'notes' => $results,
            'categories' => $categories,
            'popularTags' => $popularTags,
            'filters' => $query,
            'searchTerm' => $searchTerm,
            'pinnedNotes' => array_filter($results, function($note) {
                return $note['is_pinned'] == 1;
            }),
            'otherNotes' => array_filter($results, function($note) {
                return $note['is_pinned'] != 1;
            }),
            'stats' => $this->noteModel->getNoteStats($userId),
            'currentPage' => 1,
            'hasMorePages' => false
        ]);
    }

    /**
     * Toggle favorite status (ADHD-friendly quick action)
     */
    public function toggleFavorite($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Note not found'], 404);
            } else {
                Session::setFlash('error', 'Note not found');
                $this->redirect('/notes');
            }
            return;
        }

        // Toggle favorite status
        $isFavorite = $note['is_favorite'] ? 0 : 1;

        $result = $this->noteModel->update($id, [
            'is_favorite' => $isFavorite,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            if ($this->isAjax()) {
                $this->json(['success' => true, 'is_favorite' => $isFavorite]);
            } else {
                Session::setFlash('success', $isFavorite ? 'Note added to favorites' : 'Note removed from favorites');
                $this->redirect('/notes');
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to update favorite status'], 500);
            } else {
                Session::setFlash('error', 'Failed to update favorite status');
                $this->redirect('/notes');
            }
        }
    }

    /**
     * Get related notes for ADHD context switching
     */
    private function getRelatedNotes($note, $userId) {
        $relatedNotes = [];

        // Find notes with similar tags
        if (!empty($note['tags'])) {
            $tags = explode(',', $note['tags']);
            $firstTag = trim($tags[0]);
            if (!empty($firstTag)) {
                $relatedByTag = $this->noteModel->getUserNotes($userId, ['tag' => $firstTag], 5);
                $relatedNotes = array_filter($relatedByTag, function($n) use ($note) {
                    return $n['id'] != $note['id'];
                });
            }
        }

        // Find notes in same category
        if (!empty($note['category']) && count($relatedNotes) < 3) {
            $relatedByCategory = $this->noteModel->getUserNotes($userId, ['category' => $note['category']], 5);
            $categoryNotes = array_filter($relatedByCategory, function($n) use ($note, $relatedNotes) {
                return $n['id'] != $note['id'] && !in_array($n['id'], array_column($relatedNotes, 'id'));
            });
            $relatedNotes = array_merge($relatedNotes, array_slice($categoryNotes, 0, 3 - count($relatedNotes)));
        }

        return array_slice($relatedNotes, 0, 5);
    }

    /**
     * Get note templates for quick creation
     */
    public function getTemplates() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Get public templates and user's custom templates
        $sql = "SELECT * FROM note_templates
                WHERE is_public = 1 OR user_id = ?
                ORDER BY usage_count DESC, name ASC";

        try {
            $templates = $this->noteModel->db->fetchAll($sql, [$userId]);
            $this->json(['success' => true, 'templates' => $templates]);
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'Failed to load templates'], 500);
        }
    }

    /**
     * Track note access for analytics
     */
    public function trackAccess($id) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Note not found'], 404);
            return;
        }

        // Update last accessed time
        $result = $this->noteModel->update($id, [
            'last_accessed' => date('Y-m-d H:i:s')
        ]);

        $this->json(['success' => $result]);
    }

    /**
     * Duplicate note for ADHD-friendly quick actions
     */
    public function duplicate($id) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Note not found'], 404);
            return;
        }

        try {
            // Create duplicate note data
            $duplicateData = [
                'title' => $note['title'] . ' (Copy)',
                'content' => $note['content'],
                'category' => $note['category'],
                'tags' => $note['tags'],
                'priority_level' => $note['priority_level'] ?? 'medium',
                'color_code' => $note['color_code'],
                'is_pinned' => 0, // Don't pin duplicates by default
                'is_favorite' => 0, // Don't favorite duplicates by default
                'word_count' => $note['word_count'] ?? 0,
                'reading_time' => $note['reading_time'] ?? 1,
                'user_id' => $userId,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $newNoteId = $this->noteModel->create($duplicateData);

            if ($newNoteId) {
                $this->json([
                    'success' => true,
                    'note_id' => $newNoteId,
                    'message' => 'Note duplicated successfully'
                ]);
            } else {
                $this->json(['success' => false, 'message' => 'Failed to duplicate note'], 500);
            }
        } catch (Exception $e) {
            $this->json(['success' => false, 'message' => 'Error duplicating note: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Create note from template
     */
    public function createFromTemplate() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();
        $templateId = $data['template_id'] ?? null;

        if (!$templateId) {
            $this->json(['success' => false, 'message' => 'Template ID required']);
            return;
        }

        $noteId = $this->noteModel->createFromTemplate($userId, $templateId, $data);

        if ($noteId) {
            $this->json([
                'success' => true,
                'note_id' => $noteId,
                'message' => 'Note created from template successfully'
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to create note from template']);
        }
    }

    /**
     * Create a new template
     */
    public function createTemplate() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        if ($this->isPost()) {
            $data = $this->getPostData();

            // Validate required fields
            if (empty($data['name'])) {
                $this->json(['success' => false, 'message' => 'Template name is required']);
                return;
            }

            $templateId = $this->noteModel->createTemplate($userId, $data);

            if ($templateId) {
                $this->json([
                    'success' => true,
                    'template_id' => $templateId,
                    'message' => 'Template created successfully'
                ]);
            } else {
                $this->json(['success' => false, 'message' => 'Failed to create template']);
            }
        } else {
            // Show create template form
            $this->view('notes/create_template', [
                'title' => 'Create Note Template'
            ]);
        }
    }

    /**
     * Advanced search with filters and analytics
     */
    public function advancedSearch() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $filters = $this->getQueryData();

        // Enhanced search with multiple criteria
        $searchResults = $this->noteModel->advancedSearch($userId, $filters);

        // Get search analytics
        $searchAnalytics = $this->noteModel->getSearchAnalytics($userId, $filters);

        $this->view('notes/advanced_search', [
            'title' => 'Advanced Note Search',
            'results' => $searchResults,
            'analytics' => $searchAnalytics,
            'filters' => $filters
        ]);
    }

    /**
     * Bulk operations on notes
     */
    public function bulkAction() {
        $this->requireLogin();

        if (!$this->isPost()) {
            $this->json(['success' => false, 'message' => 'Invalid request method'], 405);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();
        $action = $data['action'] ?? '';
        $noteIds = $data['note_ids'] ?? [];

        if (empty($action) || empty($noteIds)) {
            $this->json(['success' => false, 'message' => 'Action and note IDs required']);
            return;
        }

        $result = $this->noteModel->bulkAction($userId, $action, $noteIds);

        if ($result) {
            $this->json([
                'success' => true,
                'message' => "Bulk {$action} completed successfully",
                'affected_count' => count($noteIds)
            ]);
        } else {
            $this->json(['success' => false, 'message' => "Failed to perform bulk {$action}"]);
        }
    }
}
