<?php
/**
 * Clone Controller Class
 *
 * Handles functionality for clone tools that replicate and analyze popular platforms.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/PinterestClone.php';

class CloneController extends BaseController {
    protected $pinterestModel;

    public function __construct() {
        // Make sure the session is started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        // Use singleton pattern for PinterestClone
        $this->pinterestModel = PinterestClone::getInstance();
    }

    /**
     * Display the main clone tools dashboard
     */
    public function index() {
        $this->requireLogin();

        $this->view('clone/index', [
            'title' => 'Clone Research Tools'
        ]);
    }

    /**
     * Display the Pinterest clone dashboard
     */
    public function pinterestDashboard() {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get recent scrapes
        $recentScrapes = $this->pinterestModel->getRecentScrapes($userId, 5);

        // Get trending categories
        $trendingCategories = $this->pinterestModel->getTrendingCategories();

        $this->view('clone/pinterest/index', [
            'title' => 'Pinterest Clone Research Tool',
            'recentScrapes' => $recentScrapes,
            'trendingCategories' => $trendingCategories,
            'stylesheets' => [
                '/css/pinterest-clone.css'
            ],
            'scripts' => [
                '/js/pinterest-consolidated-fix.js'
            ]
        ]);
    }

    /**
     * Display the Pinterest scraper interface
     */
    public function pinterestScraper() {
        $this->requireLogin();

        $this->view('clone/pinterest/scraper', [
            'title' => 'Pinterest Scraper',
            'stylesheets' => [
                '/css/pinterest-clone.css'
            ],
            'scripts' => [
                '/js/pinterest-consolidated-fix.js'
            ]
        ]);
    }

    /**
     * Process a Pinterest scraping request
     */
    public function processPinterestScrape() {
        $this->requireLogin();

        // Make sure the session is started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Get POST data
        $data = $_POST; // Use $_POST directly to avoid any potential issues

        // Debug information
        error_log('Processing Pinterest scrape request');
        error_log('Is AJAX: ' . ($this->isAjax() ? 'Yes' : 'No'));
        error_log('Search Term: ' . ($data['search_term'] ?? 'Not provided'));
        error_log('POST data: ' . print_r($_POST, true));
        error_log('REQUEST_METHOD: ' . $_SERVER['REQUEST_METHOD']);
        error_log('CONTENT_TYPE: ' . ($_SERVER['CONTENT_TYPE'] ?? 'Not set'));
        error_log('HTTP_X_REQUESTED_WITH: ' . ($_SERVER['HTTP_X_REQUESTED_WITH'] ?? 'Not set'));

        // Always respond with JSON for this endpoint
        header('Content-Type: application/json');

        // Validate input
        if (empty($data['search_term'])) {
            error_log('Returning JSON error response for missing search term');
            echo json_encode(['success' => false, 'message' => 'Search term is required']);
            exit;
        }

        // Log user information
        error_log('User ID from session: ' . $userId);
        error_log('User data: ' . print_r($user, true));

        // Process the scrape request
        $result = $this->pinterestModel->processScrape($userId, $data);
        error_log('Process scrape result: ' . ($result ? $result : 'false'));

        if ($result) {
            // Log success response
            error_log('Returning JSON success response with scrape ID: ' . $result);

            // Prepare response data
            $responseData = ['success' => true, 'scrape_id' => $result];
            error_log('Response data: ' . print_r($responseData, true));

            // Send JSON response
            echo json_encode($responseData);
            exit;
        } else {
            // Log error response
            error_log('Returning JSON error response for failed scrape request');

            // Send JSON error response
            echo json_encode(['success' => false, 'message' => 'Failed to process scrape request']);
            exit;
        }
    }

    /**
     * View a specific Pinterest scrape
     *
     * @param int $scrapeId Scrape ID
     */
    public function viewPinterestScrape($scrapeId) {
        $this->requireLogin();

        // Make sure the session is started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Log user and scrape information
        error_log('Viewing Pinterest scrape - Scrape ID: ' . $scrapeId . ', User ID: ' . $userId);
        error_log('User data: ' . print_r($user, true));

        // Get the scrape
        $scrape = $this->pinterestModel->getScrape($scrapeId, $userId);

        // Log scrape result
        if ($scrape) {
            error_log('Scrape found: ' . print_r($scrape, true));
        } else {
            error_log('Scrape not found for ID: ' . $scrapeId . ' and User ID: ' . $userId);
        }

        if (!$scrape) {
            error_log('Redirecting to Pinterest dashboard due to scrape not found');
            Session::setFlash('error', 'Scrape not found');
            $this->redirect('/clone/pinterest');
            return;
        }

        // Get pins from this scrape
        $pins = $this->pinterestModel->getPinsFromScrape($scrapeId);
        error_log('Retrieved ' . count($pins) . ' pins for scrape ID: ' . $scrapeId);

        $this->view('clone/pinterest/view_scrape', [
            'title' => 'Scrape Results: ' . $scrape['search_term'],
            'scrape' => $scrape,
            'pins' => $pins
        ]);
    }

    /**
     * Display Pinterest trend analysis
     */
    public function pinterestTrends() {
        $this->requireLogin();

        // Get trend data
        $trends = $this->pinterestModel->getTrendData();

        $this->view('clone/pinterest/trends', [
            'title' => 'Pinterest Trends',
            'trends' => $trends
        ]);
    }

    /**
     * Display visual analysis dashboard
     */
    public function pinterestVisualAnalysis() {
        $this->requireLogin();

        $this->view('clone/pinterest/analysis', [
            'title' => 'Pinterest Visual Analysis'
        ]);
    }

    /**
     * Display Pinterest boards
     */
    public function pinterestBoards() {
        $this->requireLogin();

        // Get boards from the model
        $boards = $this->pinterestModel->getBoards();

        $this->view('clone/pinterest/boards', [
            'title' => 'Pinterest Boards',
            'boards' => $boards['boards'],
            'message' => $boards['message'] ?? ''
        ]);
    }

    /**
     * Display the create board form
     */
    public function createBoardForm() {
        $this->requireLogin();

        $this->view('clone/pinterest/create_board', [
            'title' => 'Create Pinterest Board'
        ]);
    }

    /**
     * Process the create board form
     */
    public function processCreateBoard() {
        $this->requireLogin();

        // Get form data
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $category = $_POST['category'] ?? 'other';

        // Validate form data
        if (empty($name)) {
            Session::setFlash('error', 'Board name is required');
            $this->redirect('/clone/pinterest/create-board');
            return;
        }

        // Create the board
        $result = $this->pinterestModel->createBoard($name, $description, $category);

        if ($result && isset($result['board_id'])) {
            Session::setFlash('success', $result['message']);
            $this->redirect('/clone/pinterest/boards');
        } else {
            Session::setFlash('error', 'Failed to create board');
            $this->redirect('/clone/pinterest/create-board');
        }
    }

    /**
     * Display the upload pin form
     */
    public function uploadPinForm() {
        $this->requireLogin();

        // Get boards for the select dropdown
        $boards = $this->pinterestModel->getBoards();

        $this->view('clone/pinterest/upload_pin', [
            'title' => 'Upload Pinterest Pin',
            'boards' => $boards['boards']
        ]);
    }

    /**
     * Process the upload pin form
     */
    public function processUploadPin() {
        $this->requireLogin();

        // Get form data
        $boardId = $_POST['board_id'] ?? '';
        $title = $_POST['title'] ?? '';
        $description = $_POST['description'] ?? '';
        $link = $_POST['link'] ?? '';

        // Validate form data
        if (empty($boardId) || empty($title)) {
            Session::setFlash('error', 'Board ID and title are required');
            $this->redirect('/clone/pinterest/upload-pin');
            return;
        }

        // Handle file upload
        if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
            Session::setFlash('error', 'Image upload failed');
            $this->redirect('/clone/pinterest/upload-pin');
            return;
        }

        // Create uploads directory if it doesn't exist
        $uploadDir = __DIR__ . '/../../uploads/pinterest/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Generate a unique filename
        $filename = uniqid('pin_') . '_' . basename($_FILES['image']['name']);
        $uploadPath = $uploadDir . $filename;

        // Move the uploaded file
        if (!move_uploaded_file($_FILES['image']['tmp_name'], $uploadPath)) {
            Session::setFlash('error', 'Failed to move uploaded file');
            $this->redirect('/clone/pinterest/upload-pin');
            return;
        }

        // Upload the pin
        $result = $this->pinterestModel->uploadPin($boardId, $uploadPath, $title, $description, $link);

        if ($result && isset($result['pin_id'])) {
            Session::setFlash('success', $result['message']);
            $this->redirect('/clone/pinterest/boards');
        } else {
            Session::setFlash('error', 'Failed to upload pin');
            $this->redirect('/clone/pinterest/upload-pin');
        }
    }

    /**
     * Export a Pinterest scrape
     *
     * @param int $scrapeId Scrape ID
     */
    public function exportPinterestScrape($scrapeId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get the scrape
        $scrape = $this->pinterestModel->getScrape($scrapeId, $userId);

        if (!$scrape) {
            Session::setFlash('error', 'Scrape not found');
            $this->redirect('/clone/pinterest');
            return;
        }

        // Get pins from this scrape
        $pins = $this->pinterestModel->getPinsFromScrape($scrapeId);

        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="pinterest_scrape_' . $scrapeId . '.csv"');

        // Create output stream
        $output = fopen('php://output', 'w');

        // Add CSV headers
        fputcsv($output, ['Pin ID', 'Title', 'Description', 'Board Name', 'Save Count', 'Comment Count', 'Created At', 'Pin URL']);

        // Add data rows
        foreach ($pins as $pin) {
            fputcsv($output, [
                $pin['id'],
                $pin['title'],
                $pin['description'],
                $pin['board_name'],
                $pin['save_count'],
                $pin['comment_count'],
                $pin['created_at'],
                $pin['pin_url']
            ]);
        }

        // Close the output stream
        fclose($output);
        exit;
    }

    /**
     * Analyze a Pinterest scrape
     *
     * @param int $scrapeId Scrape ID
     */
    public function analyzePinterestScrape($scrapeId) {
        $this->requireLogin();
        $user = Session::getUser();
        $userId = $user['id'];

        // Get the scrape
        $scrape = $this->pinterestModel->getScrape($scrapeId, $userId);

        if (!$scrape) {
            Session::setFlash('error', 'Scrape not found');
            $this->redirect('/clone/pinterest');
            return;
        }

        // Get pins from this scrape
        $pins = $this->pinterestModel->getPinsFromScrape($scrapeId);

        // Perform analysis on pins
        $analysis = [
            'total_pins' => count($pins),
            'total_saves' => array_sum(array_column($pins, 'save_count')),
            'total_comments' => array_sum(array_column($pins, 'comment_count')),
            'avg_saves' => count($pins) > 0 ? round(array_sum(array_column($pins, 'save_count')) / count($pins)) : 0,
            'avg_comments' => count($pins) > 0 ? round(array_sum(array_column($pins, 'comment_count')) / count($pins)) : 0,
            'top_boards' => [],
            'engagement_by_day' => [],
            'keyword_frequency' => []
        ];

        // Group pins by board
        $boards = [];
        foreach ($pins as $pin) {
            $boardName = $pin['board_name'];
            if (!isset($boards[$boardName])) {
                $boards[$boardName] = [
                    'name' => $boardName,
                    'count' => 0,
                    'total_saves' => 0,
                    'total_comments' => 0
                ];
            }
            $boards[$boardName]['count']++;
            $boards[$boardName]['total_saves'] += $pin['save_count'];
            $boards[$boardName]['total_comments'] += $pin['comment_count'];
        }

        // Sort boards by pin count
        usort($boards, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        // Take top 5 boards
        $analysis['top_boards'] = array_slice($boards, 0, 5);

        // Extract keywords from pin titles and descriptions
        $keywords = [];
        foreach ($pins as $pin) {
            $text = $pin['title'] . ' ' . $pin['description'];
            $text = strtolower($text);
            $text = preg_replace('/[^\w\s]/', '', $text);
            $words = explode(' ', $text);

            foreach ($words as $word) {
                $word = trim($word);
                if (strlen($word) > 3 && !in_array($word, ['this', 'that', 'with', 'from', 'have', 'your', 'what', 'when', 'where', 'which', 'there', 'their', 'about'])) {
                    if (!isset($keywords[$word])) {
                        $keywords[$word] = 0;
                    }
                    $keywords[$word]++;
                }
            }
        }

        // Sort keywords by frequency
        arsort($keywords);

        // Take top 20 keywords
        $analysis['keyword_frequency'] = array_slice($keywords, 0, 20, true);

        $this->view('clone/pinterest/analyze_scrape', [
            'title' => 'Analysis: ' . $scrape['search_term'],
            'scrape' => $scrape,
            'pins' => $pins,
            'analysis' => $analysis
        ]);
    }

    /**
     * Clear all Pinterest scrapes for the current user
     */
    public function clearPinterestScrapes() {
        $this->requireLogin();

        // Make sure the session is started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Clear all scrapes for this user
        $this->pinterestModel->clearAllScrapes($userId);

        // Set a flash message
        Session::setFlash('success', 'All Pinterest scrapes have been cleared');

        // Redirect back to the Pinterest dashboard
        $this->redirect('/clone/pinterest');
    }

    /**
     * Cancel a Pinterest scrape in progress
     */
    public function cancelPinterestScrape() {
        $this->requireLogin();

        // Make sure the session is started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Get the scrape ID from the request
        $scrapeId = isset($_POST['scrape_id']) ? intval($_POST['scrape_id']) : null;

        // If no scrape ID was provided, try to find the most recent in-progress scrape
        if (!$scrapeId) {
            $inProgressScrape = $this->pinterestModel->getInProgressScrape($userId);
            if ($inProgressScrape) {
                $scrapeId = $inProgressScrape['id'];
            }
        }

        // If we found a scrape to cancel, cancel it
        if ($scrapeId) {
            $this->pinterestModel->cancelScrape($scrapeId, $userId);
            echo json_encode(['success' => true, 'message' => 'Scrape canceled successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'No in-progress scrape found to cancel']);
        }

        exit;
    }
}
