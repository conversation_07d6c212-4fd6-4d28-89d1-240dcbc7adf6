<?php
/**
 * Debt Controller
 *
 * Handles debt-related operations.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Debt.php';

class DebtController extends BaseController {
    private $debtModel;

    public function __construct() {
        $this->debtModel = new Debt();
    }

    /**
     * Show debt dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get debts based on filters
        $debts = $this->debtModel->getUserDebts($userId, $filters);

        // Get debt summary
        $debtSummary = $this->debtModel->getDebtSummary($userId);

        $this->view('finances/debts', [
            'debts' => $debts,
            'debtSummary' => $debtSummary,
            'filters' => $filters
        ]);
    }

    /**
     * Show debt creation form
     */
    public function createDebt() {
        $this->requireLogin();

        $this->view('finances/create_debt');
    }

    /**
     * Process debt creation
     */
    public function storeDebt() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['type', 'person_entity', 'amount', 'due_date', 'status']);

        if (!empty($errors)) {
            $this->view('finances/create_debt', [
                'errors' => $errors,
                'data' => $data
            ]);
            return;
        }

        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            $errors['amount'] = 'Amount must be a positive number';
            $this->view('finances/create_debt', [
                'errors' => $errors,
                'data' => $data
            ]);
            return;
        }

        // Prepare debt data
        $debtData = [
            'user_id' => $userId,
            'type' => $data['type'],
            'person_entity' => $data['person_entity'],
            'amount' => $data['amount'],
            'amount_paid' => 0, // New debts start with 0 paid
            'due_date' => $data['due_date'],
            'status' => $data['status'],
            'notes' => $data['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create debt
        $debtId = $this->debtModel->create($debtData);

        if ($debtId) {
            Session::setFlash('success', 'Debt added successfully');
            $this->redirect('/finances/debts');
        } else {
            Session::setFlash('error', 'Failed to add debt');

            $this->view('finances/create_debt', [
                'data' => $data
            ]);
        }
    }

    /**
     * Show debt edit form
     */
    public function editDebt($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get debt
        $debt = $this->debtModel->find($id);

        // Verify debt exists and belongs to user
        if (!$debt || $debt['user_id'] != $userId) {
            Session::setFlash('error', 'Debt not found');
            $this->redirect('/finances/debts');
        }

        $this->view('finances/edit_debt', [
            'debt' => $debt
        ]);
    }

    /**
     * Process debt update
     */
    public function updateDebt($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get debt
        $debt = $this->debtModel->find($id);

        // Verify debt exists and belongs to user
        if (!$debt || $debt['user_id'] != $userId) {
            Session::setFlash('error', 'Debt not found');
            $this->redirect('/finances/debts');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['type', 'person_entity', 'amount', 'due_date', 'status']);

        if (!empty($errors)) {
            $this->view('finances/edit_debt', [
                'errors' => $errors,
                'debt' => array_merge($debt, $data)
            ]);
            return;
        }

        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            $errors['amount'] = 'Amount must be a positive number';
            $this->view('finances/edit_debt', [
                'errors' => $errors,
                'debt' => array_merge($debt, $data)
            ]);
            return;
        }

        // Prepare debt data
        $debtData = [
            'type' => $data['type'],
            'person_entity' => $data['person_entity'],
            'amount' => $data['amount'],
            'due_date' => $data['due_date'],
            'status' => $data['status'],
            'notes' => $data['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // If status is changed to paid, update amount_paid
        if ($data['status'] === 'paid' && $debt['status'] !== 'paid') {
            $debtData['amount_paid'] = $data['amount'];
        }

        // If status is changed from paid to something else, adjust amount_paid
        if ($data['status'] !== 'paid' && $debt['status'] === 'paid') {
            $debtData['amount_paid'] = 0;
        }

        // Update debt
        $success = $this->debtModel->update($id, $debtData);

        if ($success) {
            Session::setFlash('success', 'Debt updated successfully');
            $this->redirect('/finances/debts');
        } else {
            Session::setFlash('error', 'Failed to update debt');

            $this->view('finances/edit_debt', [
                'debt' => array_merge($debt, $data)
            ]);
        }
    }

    /**
     * Record a payment for a debt
     */
    public function recordPayment($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get debt
        $debt = $this->debtModel->find($id);

        // Verify debt exists and belongs to user
        if (!$debt || $debt['user_id'] != $userId) {
            Session::setFlash('error', 'Debt not found');
            $this->redirect('/finances/debts');
        }

        $data = $this->getPostData();

        // Validate payment amount
        if (!isset($data['payment_amount']) || !is_numeric($data['payment_amount']) || $data['payment_amount'] <= 0) {
            Session::setFlash('error', 'Invalid payment amount');
            $this->redirect('/finances/debts');
            return;
        }

        // Calculate remaining amount
        $remainingAmount = $debt['amount'] - $debt['amount_paid'];

        // Ensure payment amount doesn't exceed remaining amount
        $paymentAmount = min((float)$data['payment_amount'], $remainingAmount);

        // Record payment
        $success = $this->debtModel->recordPayment($id, $paymentAmount);

        if ($success) {
            Session::setFlash('success', 'Payment recorded successfully');
        } else {
            Session::setFlash('error', 'Failed to record payment');
        }

        $this->redirect('/finances/debts');
    }

    /**
     * Delete a debt
     */
    public function deleteDebt($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get debt
        $debt = $this->debtModel->find($id);

        // Verify debt exists and belongs to user
        if (!$debt || $debt['user_id'] != $userId) {
            Session::setFlash('error', 'Debt not found');
            $this->redirect('/finances/debts');
        }

        // Delete debt
        $success = $this->debtModel->delete($id);

        if ($success) {
            Session::setFlash('success', 'Debt deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete debt');
        }

        $this->redirect('/finances/debts');
    }

    /**
     * Validate required fields
     */
    protected function validateRequired($data, $requiredFields) {
        $errors = [];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || trim($data[$field]) === '') {
                $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
            }
        }

        return $errors;
    }

    /**
     * Get POST data
     */
    protected function getPostData() {
        return $_POST;
    }

    /**
     * Get query parameters
     */
    protected function getQueryData($key = null) {
        if ($key !== null) {
            return isset($_GET[$key]) ? $_GET[$key] : null;
        }
        return $_GET;
    }

    /**
     * Require user to be logged in
     */
    protected function requireLogin() {
        if (!Session::isLoggedIn()) {
            $this->redirect('/login');
        }
    }
}
