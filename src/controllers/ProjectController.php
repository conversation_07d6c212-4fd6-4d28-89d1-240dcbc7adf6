<?php
/**
 * Project Controller
 *
 * Handles project management functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Project.php';
require_once __DIR__ . '/../models/Task.php';
require_once __DIR__ . '/../models/Category.php';
require_once __DIR__ . '/../models/TaskDependency.php';

class ProjectController extends BaseController {
    private $projectModel;
    private $taskModel;
    private $categoryModel;
    private $dependencyModel;

    public function __construct() {
        $this->projectModel = new Project();
        $this->taskModel = new Task();
        $this->categoryModel = new Category();
        $this->dependencyModel = new TaskDependency();
    }

    /**
     * Show project list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Clean empty filter values
        foreach ($filters as $key => $value) {
            if ($value === '') {
                unset($filters[$key]);
            }
        }

        // Get projects based on filters
        $projects = $this->projectModel->getUserProjects($userId, $filters);

        $this->view('projects/index', [
            'projects' => $projects,
            'filters' => $filters
        ]);
    }

    /**
     * Show project creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get templates for dropdown
        $templates = $this->projectModel->getProjectTemplates($userId);

        $this->view('projects/create', [
            'templates' => $templates
        ]);
    }

    /**
     * Process project creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name']);

        if (!empty($errors)) {
            // Get templates for dropdown
            $templates = $this->projectModel->getProjectTemplates($userId);

            $this->view('projects/create', [
                'errors' => $errors,
                'data' => $data,
                'templates' => $templates
            ]);
            return;
        }

        // Check if creating from template
        if (!empty($data['template_id'])) {
            $projectId = $this->projectModel->createFromTemplate($data['template_id'], $userId, $data);

            if ($projectId) {
                Session::setFlash('success', 'Project created successfully from template');
                $this->redirect('/projects/view/' . $projectId);
            } else {
                Session::setFlash('error', 'Failed to create project from template');

                // Get templates for dropdown
                $templates = $this->projectModel->getProjectTemplates($userId);

                $this->view('projects/create', [
                    'data' => $data,
                    'templates' => $templates
                ]);
            }
            return;
        }

        // Prepare project data
        $projectData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'start_date' => !empty($data['start_date']) ? $data['start_date'] : date('Y-m-d'),
            'end_date' => !empty($data['end_date']) ? $data['end_date'] : null,
            'status' => $data['status'] ?? 'planning',
            'is_template' => isset($data['is_template']) && $data['is_template'] ? 1 : 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create project
        $projectId = $this->projectModel->create($projectData);

        if ($projectId) {
            Session::setFlash('success', 'Project created successfully');
            $this->redirect('/projects/view/' . $projectId);
        } else {
            Session::setFlash('error', 'Failed to create project');

            // Get templates for dropdown
            $templates = $this->projectModel->getProjectTemplates($userId);

            $this->view('projects/create', [
                'data' => $data,
                'templates' => $templates
            ]);
        }
    }

    /**
     * Show project details
     */
    public function viewProject($id) {
        // Add debugging
        error_log("ProjectController::viewProject called with ID: " . $id);

        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project
        $project = $this->projectModel->getProjectDetails($id, $userId);

        // Verify project exists and belongs to user
        if (!$project) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/projects');
        }

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($id);

        // Get categories for task creation
        $categories = $this->categoryModel->getUserCategories($userId);

        // Get task dependencies for visualization
        $dependencies = $this->dependencyModel->getProjectDependencies($id);

        // Get project members
        $projectMembers = $this->projectModel->getProjectMembers($id);

        // Get project comments
        $projectComments = $this->projectModel->getProjectComments($id);

        // Add debugging
        error_log("About to render view with project: " . $project['name']);

        $this->view('projects/view', [
            'project' => $project,
            'tasks' => $tasks,
            'categories' => $categories,
            'dependencies' => $dependencies,
            'projectMembers' => $projectMembers,
            'projectComments' => $projectComments,
            'userId' => $userId
        ]);
    }

    /**
     * Show project edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project
        $project = $this->projectModel->find($id);

        // Verify project exists and belongs to user
        if (!$project || $project['user_id'] != $userId) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/projects');
        }

        $this->view('projects/edit', [
            'project' => $project
        ]);
    }

    /**
     * Process project update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project
        $project = $this->projectModel->find($id);

        // Verify project exists and belongs to user
        if (!$project || $project['user_id'] != $userId) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/projects');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name']);

        if (!empty($errors)) {
            $this->view('projects/edit', [
                'errors' => $errors,
                'project' => array_merge($project, $data)
            ]);
            return;
        }

        // Prepare project data
        $projectData = [
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'start_date' => !empty($data['start_date']) ? $data['start_date'] : null,
            'end_date' => !empty($data['end_date']) ? $data['end_date'] : null,
            'status' => $data['status'] ?? 'planning',
            'is_template' => isset($data['is_template']) && $data['is_template'] ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update project
        $result = $this->projectModel->update($id, $projectData);

        if ($result) {
            Session::setFlash('success', 'Project updated successfully');
            $this->redirect('/projects/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update project');

            $this->view('projects/edit', [
                'project' => array_merge($project, $data)
            ]);
        }
    }

    /**
     * Delete project
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project
        $project = $this->projectModel->find($id);

        // Verify project exists and belongs to user
        if (!$project || $project['user_id'] != $userId) {
            Session::setFlash('error', 'Project not found');
            $this->redirect('/projects');
        }

        // Delete project
        $result = $this->projectModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Project deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete project');
        }

        $this->redirect('/projects');
    }

    /**
     * Add task to project
     */
    public function addTask($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get project
        $project = $this->projectModel->find($id);

        // Verify project exists and belongs to user
        if (!$project || $project['user_id'] != $userId) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Project not found'], 404);
            } else {
                Session::setFlash('error', 'Project not found');
                $this->redirect('/projects');
            }
            return;
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'errors' => $errors], 400);
            } else {
                Session::setFlash('error', 'Please provide a task title');
                $this->redirect('/projects/view/' . $id);
            }
            return;
        }

        // Prepare task data
        $taskData = [
            'user_id' => $userId,
            'project_id' => $id,
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
            'status' => $data['status'] ?? 'todo',
            'priority' => $data['priority'] ?? 'medium',
            'due_date' => !empty($data['due_date']) ? $data['due_date'] : null,
            'category_id' => !empty($data['category_id']) ? $data['category_id'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create task
        $taskId = $this->taskModel->create($taskData);

        if ($taskId) {
            // Update project progress
            $this->projectModel->updateProgress($id);

            if ($this->isAjax()) {
                $this->json(['success' => true, 'task_id' => $taskId]);
            } else {
                Session::setFlash('success', 'Task added successfully');
                $this->redirect('/projects/view/' . $id);
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to add task'], 500);
            } else {
                Session::setFlash('error', 'Failed to add task');
                $this->redirect('/projects/view/' . $id);
            }
        }
    }
}
