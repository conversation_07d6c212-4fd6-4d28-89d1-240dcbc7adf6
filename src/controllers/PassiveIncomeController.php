<?php
/**
 * Passive Income Controller
 *
 * Handles passive income stream-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/PassiveIncomeStream.php';
require_once __DIR__ . '/../models/IncomeOpportunity.php';
require_once __DIR__ . '/../utils/Session.php';
require_once __DIR__ . '/../utils/DateUtil.php';

class PassiveIncomeController extends BaseController {
    private $streamModel;
    private $opportunityModel;

    public function __construct() {
        $this->streamModel = new PassiveIncomeStream();
        $this->opportunityModel = new IncomeOpportunity();
    }

    /**
     * Show passive income streams dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get streams based on filters
        $streams = $this->streamModel->getUserStreams($userId, $filters);

        // Get streams summary
        $streamsSummary = $this->streamModel->getStreamsSummary($userId);

        // Get categories for filter dropdown
        $categories = $this->streamModel->getCategories($userId);

        // Get upcoming maintenance
        $upcomingMaintenance = $this->streamModel->getUpcomingMaintenance($userId, 30);

        $this->view('passive-income/index', [
            'streams' => $streams,
            'streamsSummary' => $streamsSummary,
            'categories' => $categories,
            'upcomingMaintenance' => $upcomingMaintenance,
            'filters' => $filters
        ]);
    }

    /**
     * Show stream creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get passive income opportunities for dropdown
        $passiveOpportunities = $this->opportunityModel->getUserOpportunities($userId, [
            'income_type' => 'passive'
        ]);

        $this->view('passive-income/create', [
            'opportunities' => $passiveOpportunities
        ]);
    }

    /**
     * Store a new passive income stream
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'category', 'status'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');

            // Get passive income opportunities for dropdown
            $passiveOpportunities = $this->opportunityModel->getUserOpportunities($userId, [
                'income_type' => 'passive'
            ]);

            $this->view('passive-income/create', [
                'data' => $data,
                'errors' => $errors,
                'opportunities' => $passiveOpportunities
            ]);
            return;
        }

        // Prepare stream data
        $streamData = [
            'user_id' => $userId,
            'opportunity_id' => !empty($data['opportunity_id']) ? $data['opportunity_id'] : null,
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'platform' => $data['platform'] ?? null,
            'category' => $data['category'],
            'setup_date' => !empty($data['setup_date']) ? $data['setup_date'] : null,
            'initial_investment' => !empty($data['initial_investment']) ? $data['initial_investment'] : null,
            'maintenance_hours_per_month' => !empty($data['maintenance_hours_per_month']) ? $data['maintenance_hours_per_month'] : null,
            'status' => $data['status'],
            'notes' => $data['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create stream
        $streamId = $this->streamModel->create($streamData);

        if ($streamId) {
            Session::setFlash('success', 'Passive income stream added successfully');
            $this->redirect('/passive-income');
        } else {
            Session::setFlash('error', 'Failed to add passive income stream');

            // Get passive income opportunities for dropdown
            $passiveOpportunities = $this->opportunityModel->getUserOpportunities($userId, [
                'income_type' => 'passive'
            ]);

            $this->view('passive-income/create', [
                'data' => $data,
                'opportunities' => $passiveOpportunities
            ]);
        }
    }

    /**
     * Show a passive income stream
     */
    public function viewStream($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get stream
        $stream = $this->streamModel->getStream($id, $userId);

        // Verify stream exists and belongs to user
        if (!$stream) {
            Session::setFlash('error', 'Passive income stream not found');
            $this->redirect('/passive-income');
            return;
        }

        // Get stream earnings
        $earnings = $this->streamModel->getStreamEarnings($id);

        // Get stream maintenance records
        $maintenanceRecords = $this->streamModel->getStreamMaintenance($id);

        // Get monthly earnings for chart
        $monthlyEarnings = $this->streamModel->getMonthlyEarnings($id);

        // Calculate total earnings
        $totalEarnings = 0;
        foreach ($earnings as $earning) {
            $totalEarnings += $earning['amount'];
        }

        // Calculate ROI if there's investment
        $roi = 0;
        if (!empty($stream['initial_investment']) && $stream['initial_investment'] > 0) {
            $roi = ($totalEarnings / $stream['initial_investment']) * 100;
        }

        $this->view('passive-income/view', [
            'stream' => $stream,
            'earnings' => $earnings,
            'maintenanceRecords' => $maintenanceRecords,
            'monthlyEarnings' => $monthlyEarnings,
            'totalEarnings' => $totalEarnings,
            'roi' => $roi
        ]);
    }

    /**
     * Show stream edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get stream
        $stream = $this->streamModel->getStream($id, $userId);

        // Verify stream exists and belongs to user
        if (!$stream) {
            Session::setFlash('error', 'Passive income stream not found');
            $this->redirect('/passive-income');
            return;
        }

        // Get passive income opportunities for dropdown
        $passiveOpportunities = $this->opportunityModel->getUserOpportunities($userId, [
            'income_type' => 'passive'
        ]);

        $this->view('passive-income/edit', [
            'stream' => $stream,
            'opportunities' => $passiveOpportunities
        ]);
    }

    /**
     * Update a passive income stream
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get stream
        $stream = $this->streamModel->getStream($id, $userId);

        // Verify stream exists and belongs to user
        if (!$stream) {
            Session::setFlash('error', 'Passive income stream not found');
            $this->redirect('/passive-income');
            return;
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'category', 'status'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');

            // Get passive income opportunities for dropdown
            $passiveOpportunities = $this->opportunityModel->getUserOpportunities($userId, [
                'income_type' => 'passive'
            ]);

            $this->view('passive-income/edit', [
                'stream' => array_merge($stream, $data),
                'errors' => $errors,
                'opportunities' => $passiveOpportunities
            ]);
            return;
        }

        // Prepare stream data
        $streamData = [
            'opportunity_id' => !empty($data['opportunity_id']) ? $data['opportunity_id'] : null,
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'platform' => $data['platform'] ?? null,
            'category' => $data['category'],
            'setup_date' => !empty($data['setup_date']) ? $data['setup_date'] : null,
            'initial_investment' => !empty($data['initial_investment']) ? $data['initial_investment'] : null,
            'maintenance_hours_per_month' => !empty($data['maintenance_hours_per_month']) ? $data['maintenance_hours_per_month'] : null,
            'status' => $data['status'],
            'notes' => $data['notes'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update stream
        $result = $this->streamModel->update($id, $streamData);

        if ($result) {
            Session::setFlash('success', 'Passive income stream updated successfully');
            $this->redirect('/passive-income/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update passive income stream');

            // Get passive income opportunities for dropdown
            $passiveOpportunities = $this->opportunityModel->getUserOpportunities($userId, [
                'income_type' => 'passive'
            ]);

            $this->view('passive-income/edit', [
                'stream' => array_merge($stream, $data),
                'opportunities' => $passiveOpportunities
            ]);
        }
    }

    /**
     * Delete a passive income stream
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get stream
        $stream = $this->streamModel->getStream($id, $userId);

        // Verify stream exists and belongs to user
        if (!$stream) {
            Session::setFlash('error', 'Passive income stream not found');
            $this->redirect('/passive-income');
            return;
        }

        // Delete stream
        $result = $this->streamModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Passive income stream deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete passive income stream');
        }

        $this->redirect('/passive-income');
    }

    /**
     * Add an earning to a passive income stream
     */
    public function addEarning($streamId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get stream
        $stream = $this->streamModel->getStream($streamId, $userId);

        // Verify stream exists and belongs to user
        if (!$stream) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Passive income stream not found']);
            } else {
                Session::setFlash('error', 'Passive income stream not found');
                $this->redirect('/passive-income');
            }
            return;
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['earning_date', 'amount'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Please fill in all required fields']);
            } else {
                Session::setFlash('error', 'Please fill in all required fields');
                $this->redirect('/passive-income/view/' . $streamId);
            }
            return;
        }

        // Prepare earning data
        $earningData = [
            'stream_id' => $streamId,
            'earning_date' => $data['earning_date'],
            'amount' => $data['amount'],
            'notes' => $data['notes'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Add earning
        $earningId = $this->streamModel->addEarning($earningData);

        if ($earningId) {
            if ($this->isAjax()) {
                $this->json(['success' => true]);
            } else {
                Session::setFlash('success', 'Earning added successfully');
                $this->redirect('/passive-income/view/' . $streamId);
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to add earning']);
            } else {
                Session::setFlash('error', 'Failed to add earning');
                $this->redirect('/passive-income/view/' . $streamId);
            }
        }
    }

    /**
     * Add a maintenance record to a passive income stream
     */
    public function addMaintenance($streamId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get stream
        $stream = $this->streamModel->getStream($streamId, $userId);

        // Verify stream exists and belongs to user
        if (!$stream) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Passive income stream not found']);
            } else {
                Session::setFlash('error', 'Passive income stream not found');
                $this->redirect('/passive-income');
            }
            return;
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['maintenance_date', 'hours_spent', 'tasks_performed'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Please fill in all required fields']);
            } else {
                Session::setFlash('error', 'Please fill in all required fields');
                $this->redirect('/passive-income/view/' . $streamId);
            }
            return;
        }

        // Prepare maintenance data
        $maintenanceData = [
            'stream_id' => $streamId,
            'maintenance_date' => $data['maintenance_date'],
            'hours_spent' => $data['hours_spent'],
            'tasks_performed' => $data['tasks_performed'],
            'next_maintenance_date' => !empty($data['next_maintenance_date']) ? $data['next_maintenance_date'] : null,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Add maintenance record
        $maintenanceId = $this->streamModel->addMaintenance($maintenanceData);

        if ($maintenanceId) {
            if ($this->isAjax()) {
                $this->json(['success' => true]);
            } else {
                Session::setFlash('success', 'Maintenance record added successfully');
                $this->redirect('/passive-income/view/' . $streamId);
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to add maintenance record']);
            } else {
                Session::setFlash('error', 'Failed to add maintenance record');
                $this->redirect('/passive-income/view/' . $streamId);
            }
        }
    }
}
