<?php
/**
 * Aegis Director Controller
 *
 * Handles Aegis Director agent functionality and project management
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/AIAgent.php';
require_once __DIR__ . '/../models/AIAgentTask.php';
require_once __DIR__ . '/../models/AIAgentInteraction.php';
require_once __DIR__ . '/../models/Project.php';
require_once __DIR__ . '/../models/Task.php';
require_once __DIR__ . '/../models/AegisDirectorProjectManager.php';

class AegisDirectorController extends BaseController {
    private $agentModel;
    private $agentTaskModel;
    private $interactionModel;
    private $projectModel;
    private $taskModel;
    private $projectManager;
    
    public function __construct() {
        parent::__construct();
        $this->agentModel = new AIAgent();
        $this->agentTaskModel = new AIAgentTask();
        $this->interactionModel = new AIAgentInteraction();
        $this->projectModel = new Project();
        $this->taskModel = new Task();
        $this->projectManager = new AegisDirectorProjectManager();
    }
    
    /**
     * Display the Aegis Director dashboard
     */
    public function index() {
        $userId = $this->getCurrentUserId();
        
        // Get the Aegis Director agent
        $aegisDirector = $this->projectManager->getAegisDirectorAgent($userId);
        
        if (!$aegisDirector) {
            $this->redirect('/momentum/create_aegis_director_agent.php');
            return;
        }
        
        // Get agent tasks
        $agentTasks = $this->agentTaskModel->getAgentTasks($aegisDirector['id']);
        
        // Get agent interactions
        $agentInteractions = $this->interactionModel->getAgentInteractions($aegisDirector['id'], 10);
        
        // Get projects managed by Aegis Director
        $managedProjects = $this->projectManager->getAegisDirectorProjects($userId);
        
        // Load the view
        $this->loadView('aegis_director/dashboard', [
            'agent' => $aegisDirector,
            'tasks' => $agentTasks,
            'interactions' => $agentInteractions,
            'projects' => $managedProjects
        ]);
    }
    
    /**
     * Create a new project
     */
    public function createProject() {
        $userId = $this->getCurrentUserId();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $projectName = $_POST['project_name'] ?? '';
            $projectDescription = $_POST['project_description'] ?? '';
            $projectType = $_POST['project_type'] ?? 'standard';
            $deadline = $_POST['deadline'] ?? date('Y-m-d', strtotime('+1 week'));
            
            if (empty($projectName)) {
                $this->setFlashMessage('error', 'Project name is required');
                $this->redirect('/momentum/aegis-director/projects');
                return;
            }
            
            $projectId = null;
            
            // Create the appropriate project type
            if ($projectType === 'rapid') {
                // Create a rapid implementation project
                $projectId = $this->projectManager->createRapidImplementationProject(
                    $userId,
                    $projectName,
                    $projectDescription,
                    $deadline
                );
            } elseif ($projectType === 'agent_army') {
                $brigadeType = $_POST['brigade_type'] ?? 'content_creation';
                
                // Create an AI Agent Army project
                $projectId = $this->projectManager->createAgentArmyProject(
                    $userId,
                    $brigadeType,
                    $projectName,
                    $projectDescription,
                    $deadline
                );
            } else {
                // Create a standard project
                $projectData = [
                    'user_id' => $userId,
                    'name' => $projectName,
                    'description' => $projectDescription,
                    'start_date' => date('Y-m-d'),
                    'end_date' => $deadline,
                    'status' => 'planning',
                    'progress' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $projectId = $this->projectModel->create($projectData);
            }
            
            if ($projectId) {
                $this->setFlashMessage('success', 'Project created successfully');
                $this->redirect('/momentum/projects/view/' . $projectId);
            } else {
                $this->setFlashMessage('error', 'Failed to create project');
                $this->redirect('/momentum/aegis-director/projects');
            }
        } else {
            // Display the project creation form
            $this->loadView('aegis_director/create_project', [
                'brigadeTypes' => [
                    'content_creation' => 'Content Creation Brigade',
                    'lead_generation' => 'Lead Generation Brigade',
                    'customer_support' => 'Customer Support Brigade',
                    'data_analysis' => 'Data Analysis Brigade'
                ]
            ]);
        }
    }
    
    /**
     * Create a 24-hour implementation plan for a project
     */
    public function createPlan($projectId) {
        $userId = $this->getCurrentUserId();
        
        // Check if the project exists and belongs to the user
        $project = $this->projectModel->getProjectDetails($projectId, $userId);
        
        if (!$project) {
            $this->setFlashMessage('error', 'Project not found');
            $this->redirect('/momentum/aegis-director/projects');
            return;
        }
        
        // Create the 24-hour implementation plan
        $success = $this->projectManager->create24HourImplementationPlan($projectId, $userId);
        
        if ($success) {
            $this->setFlashMessage('success', '24-hour implementation plan created successfully');
        } else {
            $this->setFlashMessage('error', 'Failed to create 24-hour implementation plan');
        }
        
        $this->redirect('/momentum/projects/view/' . $projectId);
    }
    
    /**
     * Generate a progress report for a project
     */
    public function generateReport($projectId) {
        $userId = $this->getCurrentUserId();
        
        // Check if the project exists and belongs to the user
        $project = $this->projectModel->getProjectDetails($projectId, $userId);
        
        if (!$project) {
            $this->setFlashMessage('error', 'Project not found');
            $this->redirect('/momentum/aegis-director/projects');
            return;
        }
        
        // Generate the progress report
        $report = $this->projectManager->generateProjectProgressReport($projectId, $userId);
        
        // Get the Aegis Director agent
        $aegisDirector = $this->projectManager->getAegisDirectorAgent($userId);
        
        if ($aegisDirector) {
            // Create an interaction with the report
            $this->interactionModel->createInteraction([
                'agent_id' => $aegisDirector['id'],
                'user_id' => $userId,
                'interaction_type' => 'system',
                'content' => "Generate progress report for project: {$project['name']}",
                'response' => $report,
                'success' => true,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        // Load the view
        $this->loadView('aegis_director/project_report', [
            'project' => $project,
            'report' => $report
        ]);
    }
    
    /**
     * Process an interaction with the Aegis Director agent
     */
    public function interact() {
        $userId = $this->getCurrentUserId();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $content = $_POST['interaction_content'] ?? '';
            $type = $_POST['interaction_type'] ?? 'command';
            
            if (empty($content)) {
                $this->setFlashMessage('error', 'Interaction content is required');
                $this->redirect('/momentum/aegis-director');
                return;
            }
            
            // Get the Aegis Director agent
            $aegisDirector = $this->projectManager->getAegisDirectorAgent($userId);
            
            if (!$aegisDirector) {
                $this->setFlashMessage('error', 'Aegis Director agent not found');
                $this->redirect('/momentum/aegis-director');
                return;
            }
            
            // Create the interaction
            $interactionId = $this->interactionModel->createInteraction([
                'agent_id' => $aegisDirector['id'],
                'user_id' => $userId,
                'interaction_type' => $type,
                'content' => $content,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($interactionId) {
                // Generate a response
                $response = $this->generateResponse($content, $type, $aegisDirector);
                
                // Update the interaction with the response
                $this->interactionModel->updateInteraction($interactionId, [
                    'response' => $response
                ]);
                
                $this->setFlashMessage('success', 'Interaction sent successfully');
            } else {
                $this->setFlashMessage('error', 'Failed to send interaction');
            }
        }
        
        $this->redirect('/momentum/aegis-director');
    }
    
    /**
     * Generate a response from the Aegis Director agent
     */
    private function generateResponse($content, $type, $agent) {
        // In a real implementation, this would call an AI API
        // For this demo, we'll use a simple rule-based approach
        
        // Convert content to lowercase for easier matching
        $contentLower = strtolower($content);
        
        // Check for project-related queries
        if (strpos($contentLower, 'create project') !== false || strpos($contentLower, 'new project') !== false) {
            return "I can help you create a new project. Please provide the following details:\n\n1. Project name\n2. Project description\n3. Deadline\n4. Project type (standard, rapid implementation, or AI Agent Army)\n\nFor AI Agent Army projects, also specify which brigade type you want to create.";
        }
        
        if (strpos($contentLower, '24 hour') !== false || strpos($contentLower, 'rapid implementation') !== false) {
            return "The 24-hour rapid implementation plan is designed to help you complete a project in just 24 hours. I'll break down the project into hourly blocks, focusing on the most critical features first. Would you like me to create a 24-hour implementation plan for one of your existing projects?";
        }
        
        if (strpos($contentLower, 'agent army') !== false || strpos($contentLower, 'brigade') !== false) {
            return "The AI Agent Army consists of specialized brigades, each focused on specific tasks:\n\n1. Content Creation Brigade - Generate high-quality content at scale\n2. Lead Generation Brigade - Identify and engage potential clients\n3. Customer Support Brigade - Provide automated customer support\n4. Data Analysis Brigade - Transform data into actionable insights\n\nWhich brigade would you like to implement?";
        }
        
        // Check for common patterns (reuse existing logic)
        if (strpos($contentLower, 'hello') !== false || strpos($contentLower, 'hi') !== false) {
            return "I am Aegis Director, your executive functioning partner. I don't engage in small talk. What project are we working on? What is the specific goal and deadline?";
        }
        
        if (strpos($contentLower, 'help') !== false) {
            return "I am designed to ensure you achieve rapid, tangible results. I can help you with:\n\n1. Creating and managing projects\n2. Implementing the 24-hour rapid implementation plan\n3. Building your AI Agent Army with specialized brigades\n4. Breaking down complex goals into actionable tasks\n5. Maintaining focus and accountability\n\nWhat would you like assistance with?";
        }
        
        if (strpos($contentLower, 'distracted') !== false || strpos($contentLower, 'focus') !== false) {
            return "Stop. Distractions are not permitted. Return to your current task immediately. Close all unrelated applications and websites. You have committed to completing this project by the deadline. Further deviation will jeopardize your goal.";
        }
        
        // Default response
        return "I require clear, specific information about your project goals and tasks. Please provide concrete details about what you need to accomplish, by when, and any obstacles you anticipate. I will then create a structured plan and hold you accountable for execution.";
    }
    
    /**
     * Get the current user ID
     */
    private function getCurrentUserId() {
        // In a real implementation, this would get the current user from the session
        // For this demo, we'll use a default user ID
        return 1;
    }
}
