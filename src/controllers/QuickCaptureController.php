<?php
/**
 * Quick Capture Controller
 *
 * Handles screenshot, note, and voice capture functionality
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/QuickCapture.php';
require_once __DIR__ . '/../utils/Session.php';

class QuickCaptureController extends BaseController {
    private $captureModel;
    private $uploadPath;

    public function __construct() {
        $this->captureModel = new QuickCapture();
        $this->uploadPath = BASE_PATH . '/public/uploads/captures/';

        // Create upload directory if it doesn't exist
        if (!is_dir($this->uploadPath)) {
            mkdir($this->uploadPath, 0755, true);
        }
    }

    /**
     * Show quick capture dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get capture statistics
        $stats = $this->captureModel->getCaptureStats($userId);

        // Get recent captures
        $recentCaptures = $this->captureModel->getRecentCaptures($userId, 12);

        // Get pinned captures
        $pinnedCaptures = $this->captureModel->getPinnedCaptures($userId);

        // Get available categories
        $categories = $this->captureModel->getAvailableCategories($userId);

        $this->view('quick_capture/index', [
            'stats' => $stats,
            'recentCaptures' => $recentCaptures,
            'pinnedCaptures' => $pinnedCaptures,
            'categories' => $categories,
            'stylesheets' => [
                '/momentum/css/quick-capture.css'
            ],
            'scripts' => [
                '/momentum/js/quick-capture.js'
            ]
        ]);
    }

    /**
     * Show capture gallery
     */
    public function gallery() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filters from query parameters
        $filters = $this->getQueryData();

        // Get captures
        $captures = $this->captureModel->getUserCaptures($userId, $filters);

        // Get available categories
        $categories = $this->captureModel->getAvailableCategories($userId);

        // Check if this is being called from tools
        $currentPath = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($currentPath, '/tools/') !== false) {
            $this->view('tools/gallery', [
                'captures' => $captures,
                'categories' => $categories,
                'filters' => $filters
            ]);
        } else {
            $this->view('quick_capture/gallery', [
                'captures' => $captures,
                'categories' => $categories,
                'filters' => $filters,
                'stylesheets' => [
                    '/momentum/css/quick-capture.css'
                ],
                'scripts' => [
                    '/momentum/js/quick-capture.js'
                ]
            ]);
        }
    }

    /**
     * Show screenshot capture interface
     */
    public function screenshot() {
        $this->requireLogin();

        // Check if this is being called from tools
        $currentPath = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($currentPath, '/tools/') !== false) {
            $this->view('tools/screenshot');
        } else {
            $this->view('quick_capture/screenshot', [
                'stylesheets' => [
                    '/momentum/css/quick-capture.css',
                    '/momentum/css/screenshot-tool.css'
                ],
                'scripts' => [
                    '/momentum/js/screenshot-capture.js'
                ]
            ]);
        }
    }

    /**
     * Handle screenshot upload
     */
    public function uploadScreenshot() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        if (!isset($_FILES['screenshot']) || $_FILES['screenshot']['error'] !== UPLOAD_ERR_OK) {
            $this->jsonResponse(['success' => false, 'error' => 'No file uploaded or upload error']);
            return;
        }

        $file = $_FILES['screenshot'];
        $data = $this->getPostData();

        // Validate file type
        $allowedTypes = ['image/png', 'image/jpeg', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            $this->jsonResponse(['success' => false, 'error' => 'Invalid file type']);
            return;
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('screenshot_') . '.' . $extension;
        $filePath = $this->uploadPath . $filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            $this->jsonResponse(['success' => false, 'error' => 'Failed to save file']);
            return;
        }

        // Generate thumbnail
        $thumbnailPath = $this->generateThumbnail($filePath, $filename);

        // Extract OCR text if enabled
        $ocrText = null;
        if (!empty($data['extract_text'])) {
            $ocrText = $this->extractTextFromImage($filePath);
        }

        // Create capture record
        $captureData = [
            'user_id' => $userId,
            'type' => 'screenshot',
            'title' => $data['title'] ?? 'Screenshot ' . date('Y-m-d H:i:s'),
            'content' => $data['description'] ?? null,
            'file_path' => $filePath,
            'file_type' => $file['type'],
            'file_size' => $file['size'],
            'thumbnail_path' => $thumbnailPath,
            'ocr_text' => $ocrText,
            'tags' => $data['tags'] ?? null,
            'category' => $data['category'] ?? null,
            'metadata' => json_encode([
                'original_name' => $file['name'],
                'upload_time' => date('Y-m-d H:i:s'),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ])
        ];

        $captureId = $this->captureModel->createCapture($captureData);

        if ($captureId) {
            $this->jsonResponse([
                'success' => true,
                'capture_id' => $captureId,
                'thumbnail_url' => '/momentum/public/uploads/captures/thumbnails/' . basename($thumbnailPath)
            ]);
        } else {
            // Clean up files if database insert failed
            if (file_exists($filePath)) unlink($filePath);
            if (file_exists($thumbnailPath)) unlink($thumbnailPath);

            $this->jsonResponse(['success' => false, 'error' => 'Failed to save capture']);
        }
    }

    /**
     * Create quick note
     */
    public function createNote() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Handle both JSON and form data
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') !== false) {
            $data = json_decode(file_get_contents('php://input'), true) ?? [];
        } else {
            $data = $_POST;
        }

        // Validate required fields
        if (empty($data['content'])) {
            $this->jsonResponse(['success' => false, 'error' => 'Note content is required']);
            return;
        }

        $captureData = [
            'user_id' => $userId,
            'type' => 'note',
            'title' => $data['title'] ?? 'Quick Note ' . date('Y-m-d H:i:s'),
            'content' => $data['content'],
            'tags' => $data['tags'] ?? null,
            'category' => $data['category'] ?? null,
            'is_pinned' => isset($data['is_pinned']) ? 1 : 0,
            'linked_prompt_id' => $data['linked_prompt_id'] ?? null,
            'linked_task_id' => $data['linked_task_id'] ?? null,
            'metadata' => json_encode([
                'created_via' => 'quick_note',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ])
        ];

        $captureId = $this->captureModel->createCapture($captureData);

        if ($captureId) {
            $this->jsonResponse(['success' => true, 'capture_id' => $captureId]);
        } else {
            $this->jsonResponse(['success' => false, 'error' => 'Failed to create note']);
        }
    }

    /**
     * View capture details
     */
    public function viewCapture($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $capture = $this->captureModel->find($id);

        if (!$capture || $capture['user_id'] != $userId) {
            Session::setFlash('error', 'Capture not found');
            $this->redirect('/quick-capture');
            return;
        }

        // Parse metadata
        $metadata = [];
        if ($capture['metadata']) {
            $metadata = json_decode($capture['metadata'], true) ?? [];
        }

        $this->view('quick_capture/view', [
            'capture' => $capture,
            'metadata' => $metadata,
            'stylesheets' => [
                '/momentum/css/quick-capture.css'
            ],
            'scripts' => [
                '/momentum/js/quick-capture.js'
            ]
        ]);
    }

    /**
     * Edit capture
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $capture = $this->captureModel->find($id);

        if (!$capture || $capture['user_id'] != $userId) {
            Session::setFlash('error', 'Capture not found');
            $this->redirect('/quick-capture');
            return;
        }

        // Get available categories
        $categories = $this->captureModel->getAvailableCategories($userId);

        $this->view('quick_capture/edit', [
            'capture' => $capture,
            'categories' => $categories,
            'stylesheets' => [
                '/momentum/css/quick-capture.css'
            ],
            'scripts' => [
                '/momentum/js/quick-capture.js'
            ]
        ]);
    }

    /**
     * Update capture
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $capture = $this->captureModel->find($id);

        if (!$capture || $capture['user_id'] != $userId) {
            Session::setFlash('error', 'Capture not found');
            $this->redirect('/quick-capture');
            return;
        }

        $data = $this->getPostData();

        $updateData = [
            'title' => $data['title'] ?? null,
            'content' => $data['content'] ?? null,
            'tags' => $data['tags'] ?? null,
            'category' => $data['category'] ?? null,
            'is_pinned' => isset($data['is_pinned']) ? 1 : 0,
            'linked_prompt_id' => $data['linked_prompt_id'] ?? null,
            'linked_task_id' => $data['linked_task_id'] ?? null
        ];

        $result = $this->captureModel->updateCapture($id, $updateData);

        if ($result) {
            Session::setFlash('success', 'Capture updated successfully');
            $this->redirect('/quick-capture/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update capture');
            $this->redirect('/quick-capture/edit/' . $id);
        }
    }

    /**
     * Delete capture
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $result = $this->captureModel->deleteCapture($id, $userId);

        if ($result) {
            $this->jsonResponse(['success' => true]);
        } else {
            $this->jsonResponse(['success' => false, 'error' => 'Failed to delete capture']);
        }
    }

    /**
     * Toggle pin status
     */
    public function togglePin($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $result = $this->captureModel->togglePin($id, $userId);

        if ($result) {
            $this->jsonResponse(['success' => true]);
        } else {
            $this->jsonResponse(['success' => false, 'error' => 'Failed to toggle pin']);
        }
    }

    /**
     * Search captures
     */
    public function search() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $query = $this->getQueryData()['q'] ?? '';
        $filters = $this->getQueryData();

        if (empty($query)) {
            $this->jsonResponse(['success' => false, 'error' => 'Search query is required']);
            return;
        }

        $results = $this->captureModel->searchCaptures($userId, $query, $filters);

        $this->jsonResponse(['success' => true, 'results' => $results]);
    }

    /**
     * Generate thumbnail for image
     */
    private function generateThumbnail($imagePath, $filename) {
        $thumbnailDir = $this->uploadPath . 'thumbnails/';
        if (!is_dir($thumbnailDir)) {
            mkdir($thumbnailDir, 0755, true);
        }

        $thumbnailPath = $thumbnailDir . 'thumb_' . $filename;

        // Simple thumbnail generation (you might want to use a more robust library)
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) return null;

        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $type = $imageInfo[2];

        // Calculate thumbnail dimensions (max 300px)
        $maxSize = 300;
        if ($width > $height) {
            $thumbWidth = $maxSize;
            $thumbHeight = ($height / $width) * $maxSize;
        } else {
            $thumbHeight = $maxSize;
            $thumbWidth = ($width / $height) * $maxSize;
        }

        // Create thumbnail based on image type
        $source = null;
        switch ($type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($imagePath);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($imagePath);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($imagePath);
                break;
            default:
                return null;
        }

        if (!$source) return null;

        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
        imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $thumbWidth, $thumbHeight, $width, $height);

        // Save thumbnail
        $success = false;
        switch ($type) {
            case IMAGETYPE_JPEG:
                $success = imagejpeg($thumbnail, $thumbnailPath, 85);
                break;
            case IMAGETYPE_PNG:
                $success = imagepng($thumbnail, $thumbnailPath);
                break;
            case IMAGETYPE_GIF:
                $success = imagegif($thumbnail, $thumbnailPath);
                break;
        }

        imagedestroy($source);
        imagedestroy($thumbnail);

        return $success ? $thumbnailPath : null;
    }

    /**
     * Extract text from image using OCR (placeholder - implement with Tesseract or cloud OCR)
     */
    private function extractTextFromImage($imagePath) {
        // Placeholder for OCR implementation
        // You would integrate with Tesseract OCR, Google Vision API, or similar service
        return null;
    }

    /**
     * Show note creation form
     */
    public function noteForm() {
        $this->requireLogin();

        // Check if this is being called from tools
        $currentPath = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($currentPath, '/tools/') !== false) {
            $this->view('tools/note_form');
        } else {
            $this->view('quick_capture/note_form', [
                'stylesheets' => [
                    '/momentum/css/quick-capture.css'
                ],
                'scripts' => [
                    '/momentum/js/quick-capture.js'
                ]
            ]);
        }
    }

    /**
     * Show voice recording form
     */
    public function voiceForm() {
        $this->requireLogin();

        // Check if this is being called from tools
        $currentPath = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($currentPath, '/tools/') !== false) {
            $this->view('tools/voice_form');
        } else {
            $this->view('quick_capture/voice_form', [
                'stylesheets' => [
                    '/momentum/css/quick-capture.css'
                ],
                'scripts' => [
                    '/momentum/js/voice-recorder.js'
                ]
            ]);
        }
    }
}
