<?php
/**
 * Base Controller Class
 *
 * Provides common functionality for all controllers.
 */

require_once __DIR__ . '/../utils/View.php';
require_once __DIR__ . '/../utils/Session.php';

abstract class BaseController {
    /**
     * Render a view
     */
    protected function view($view, $data = []) {
        // Set dark mode based on user preference if user is logged in
        if (Session::isLoggedIn() && !isset($data['darkMode'])) {
            $user = Session::getUser();
            $data['darkMode'] = isset($user['theme']) && $user['theme'] === 'dark';
        }

        echo View::renderWithLayout($view, 'default', $data);
    }

    /**
     * Redirect to a URL
     */
    protected function redirect($url) {
        // Add base path for subdirectory
        if (strpos($url, 'http') !== 0) {
            $url = '/momentum' . $url;
        }
        header("Location: {$url}");
        exit;
    }

    /**
     * Check if user is logged in, redirect if not
     */
    protected function requireLogin() {
        if (!Session::isLoggedIn()) {
            // For AJAX requests, return JSON response
            if ($this->isAjax()) {
                $this->jsonResponse([
                    'success' => false,
                    'error' => 'Authentication required',
                    'redirect' => '/momentum/login'
                ], 401);
            } else {
                // For regular requests, redirect to login
                Session::setFlash('error', 'Please log in to access this page');
                $this->redirect('/login');
            }
        }
    }

    /**
     * Get POST data
     * Handles both regular form submissions and JSON data from AJAX requests
     */
    protected function getPostData() {
        // Check if this is a JSON request
        $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';

        if (strpos($contentType, 'application/json') !== false) {
            // Get the raw POST data
            $json = file_get_contents('php://input');

            // Decode the JSON data
            $data = json_decode($json, true);

            // Return the decoded data or an empty array if decoding failed
            return $data ?: [];
        }

        // Return regular POST data for normal form submissions
        return $_POST;
    }

    /**
     * Get GET data
     *
     * @param string|null $key Optional key to retrieve specific value
     * @return mixed Returns the value for the specified key, or all GET data if no key provided
     */
    protected function getQueryData($key = null) {
        if ($key !== null) {
            return isset($_GET[$key]) ? $_GET[$key] : null;
        }
        return $_GET;
    }

    /**
     * Validate required fields
     */
    protected function validateRequired($data, $fields) {
        $errors = [];

        foreach ($fields as $field) {
            if (empty($data[$field])) {
                $errors[$field] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
            }
        }

        return $errors;
    }

    /**
     * Validate email
     */
    protected function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Return JSON response
     */
    protected function json($data, $statusCode = 200) {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }

    /**
     * Return JSON response (alias for json method)
     */
    protected function jsonResponse($data, $statusCode = 200) {
        $this->json($data, $statusCode);
    }

    /**
     * Handle AJAX request
     */
    protected function isAjax() {
        // Check for the X-Requested-With header (traditional AJAX)
        $isXhr = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        // Check for JSON content type (API requests)
        $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
        $isJson = strpos($contentType, 'application/json') !== false;

        // Return true if either condition is met
        return $isXhr || $isJson;
    }

    /**
     * Load a model
     *
     * @param string $modelName The name of the model to load
     * @return object The model instance
     */
    protected function loadModel($modelName) {
        // Check if the model class exists
        if (class_exists($modelName)) {
            return new $modelName();
        }

        // If not, try to include the model file
        $modelFile = __DIR__ . "/../models/{$modelName}.php";
        if (file_exists($modelFile)) {
            require_once $modelFile;
            return new $modelName();
        }

        // If model doesn't exist, throw an exception
        throw new Exception("Model {$modelName} not found");
    }
}

