<?php
/**
 * Dashboard Redesign Controller
 *
 * This controller handles the redesigned ADHD-friendly dashboard.
 */
class DashboardRedesignController extends BaseController {
    private $taskModel;
    private $categoryModel;
    private $ideaModel;
    private $financeModel;
    private $projectModel;
    private $subscriptionModel;
    private $adhdModel;
    private $incomeOpportunityModel;
    private $passiveIncomeModel;

    public function __construct() {
        require_once __DIR__ . '/../models/Task.php';
        require_once __DIR__ . '/../models/Category.php';
        require_once __DIR__ . '/../models/Idea.php';
        require_once __DIR__ . '/../models/Finance.php';
        require_once __DIR__ . '/../models/Project.php';
        require_once __DIR__ . '/../models/Subscription.php';
        require_once __DIR__ . '/../models/IncomeOpportunity.php';
        require_once __DIR__ . '/../models/PassiveIncomeStream.php';

        $this->taskModel = new Task();
        $this->categoryModel = new Category();
        $this->ideaModel = new Idea();
        $this->financeModel = new Finance();
        $this->projectModel = new Project();
        $this->subscriptionModel = new Subscription();
        $this->incomeOpportunityModel = new IncomeOpportunity();
        $this->passiveIncomeModel = new PassiveIncomeStream();

        // Check if ADHD model exists and load it
        if (file_exists(__DIR__ . '/../models/ADHDSymptom.php')) {
            require_once __DIR__ . '/../models/ADHDSymptom.php';
            $this->adhdModel = new ADHDSymptom();
        }
    }

    /**
     * Show redesigned dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if the current_focus_task_id column exists
        $db = Database::getInstance();
        $checkColumnSql = "SELECT COUNT(*) as column_exists
                          FROM information_schema.COLUMNS
                          WHERE TABLE_SCHEMA = DATABASE()
                          AND TABLE_NAME = 'users'
                          AND COLUMN_NAME = 'current_focus_task_id'";
        $columnExists = $db->fetchOne($checkColumnSql);

        // Get current focus task if set
        $currentFocusTask = null;

        if (!$columnExists || $columnExists['column_exists'] == 0) {
            // Column doesn't exist, add it
            $addColumnSql = "ALTER TABLE users ADD COLUMN current_focus_task_id INT DEFAULT NULL";
            $db->query($addColumnSql);
            // No need to do anything else since the column will be NULL by default
        } else if (!empty($user['current_focus_task_id'])) {
            $currentFocusTask = $this->taskModel->find($user['current_focus_task_id']);
            // Verify task exists and belongs to user
            if (!$currentFocusTask || $currentFocusTask['user_id'] != $userId) {
                $currentFocusTask = null;
                // Clear invalid focus task
                require_once __DIR__ . '/../models/User.php';
                $userModel = new User();
                $userModel->update($userId, [
                    'current_focus_task_id' => null,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // Update user in session
                $updatedUser = $userModel->find($userId);
                Session::setUser($updatedUser);
            } else {
                // Get category information
                if (!empty($currentFocusTask['category_id'])) {
                    $category = $this->categoryModel->find($currentFocusTask['category_id']);
                    if ($category) {
                        $currentFocusTask['category_name'] = $category['name'];
                        $currentFocusTask['category_color'] = $category['color'];
                    }
                }
            }
        }

        // Get today's tasks
        $todayTasks = $this->taskModel->getTodayTasks($userId) ?: [];

        // Get upcoming tasks
        $upcomingTasks = $this->taskModel->getUpcomingTasks($userId) ?: [];

        // Get overdue tasks
        $overdueTasks = $this->taskModel->getOverdueTasks($userId) ?: [];

        // Get recent completed tasks
        $completedTasks = $this->taskModel->getCompletedTasks($userId, 5) ?: [];

        // Get categories with task count
        $categories = $this->categoryModel->getCategoriesWithTaskCount($userId) ?: [];

        // Get recent ideas
        $recentIdeas = $this->ideaModel->getRecentIdeas($userId) ?: [];

        // Get financial summary for current month
        $startOfMonth = date('Y-m-01');
        $endOfMonth = date('Y-m-t');
        $financialSummary = $this->financeModel->getSummary($userId, $startOfMonth, $endOfMonth) ?: [];

        // Get recent transactions
        $recentTransactions = $this->financeModel->getRecentTransactions($userId, 5) ?: [];

        // Get upcoming subscriptions
        $upcomingSubscriptions = $this->subscriptionModel->getUpcomingSubscriptions($userId) ?: [];

        // Get total monthly subscription cost
        $monthlyCost = $this->subscriptionModel->getTotalMonthlyCost($userId) ?: 0;

        // Get active projects
        $activeProjects = $this->projectModel->getUserProjects($userId, [
            'status' => ['planning', 'in_progress', 'on_hold'],
            'is_template' => 0
        ]) ?: [];

        // Get project statistics
        $allProjects = $this->projectModel->getUserProjects($userId) ?: [];
        $inProgressProjects = $this->projectModel->getUserProjects($userId, ['status' => 'in_progress']) ?: [];
        $completedProjects = $this->projectModel->getUserProjects($userId, ['status' => 'completed']) ?: [];

        $projectStats = [
            'total' => count($allProjects),
            'in_progress' => count($inProgressProjects),
            'completed' => count($completedProjects)
        ];

        // Get ADHD data if available
        $adhdData = null;
        if ($this->adhdModel) {
            $adhdData = [
                'recentLogs' => $this->adhdModel->getRecentLogs($userId, 7) ?: [],
                'symptomTrends' => $this->adhdModel->getSymptomTrends($userId, 30) ?: [],
                'currentStreak' => $this->adhdModel->getCurrentStreak($userId) ?: 0,
                'hasLoggedToday' => $this->adhdModel->hasLoggedToday($userId) ?: false
            ];
        }

        // Get income opportunities data
        $activeOpportunities = $this->incomeOpportunityModel->getActiveOpportunities($userId, 5) ?: [];
        $opportunitySummary = $this->incomeOpportunityModel->getOpportunitySummary($userId) ?: [];

        // Get passive income data
        $activeStreams = $this->passiveIncomeModel->getActiveStreams($userId, 5) ?: [];
        $streamsSummary = $this->passiveIncomeModel->getStreamsSummary($userId) ?: [];

        // Render the redesigned dashboard view
        $this->view('dashboard/redesign', [
            'currentFocusTask' => $currentFocusTask,
            'todayTasks' => $todayTasks,
            'upcomingTasks' => $upcomingTasks,
            'overdueTasks' => $overdueTasks,
            'completedTasks' => $completedTasks,
            'categories' => $categories,
            'recentIdeas' => $recentIdeas,
            'financialSummary' => $financialSummary,
            'recentTransactions' => $recentTransactions,
            'upcomingSubscriptions' => $upcomingSubscriptions,
            'monthlyCost' => $monthlyCost,
            'activeProjects' => $activeProjects,
            'projectStats' => $projectStats,
            'adhdData' => $adhdData,
            'activeOpportunities' => $activeOpportunities,
            'opportunitySummary' => $opportunitySummary,
            'activeStreams' => $activeStreams,
            'streamsSummary' => $streamsSummary,
            'stylesheets' => [
                '/momentum/css/adhd-friendly.css',
                '/momentum/css/dashboard-layouts.css',
                '/momentum/css/layout-visual-styles.css',
                '/momentum/css/task-widgets.css',
                '/momentum/css/fixed-task-widgets.css'
            ],
            'scripts' => [
                '/momentum/js/modules/dashboard-layout.js',
                '/momentum/js/modules/task-manager.js',
                '/momentum/js/main.js'
            ]
        ]);
    }

    /**
     * Update dashboard layout
     */
    public function updateLayout() {
        $this->requireLogin();

        if ($this->isAjax()) {
            $data = json_decode(file_get_contents('php://input'), true);

            if (!$data || !isset($data['layout'])) {
                $this->json(['success' => false, 'message' => 'Invalid data'], 400);
                return;
            }

            $user = Session::getUser();
            $userId = $user['id'];

            require_once __DIR__ . '/../models/User.php';
            $userModel = new User();

            $result = $userModel->update($userId, [
                'dashboard_layout' => json_encode($data['layout']),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if ($result) {
                // Update user in session
                $updatedUser = $userModel->find($userId);
                Session::setUser($updatedUser);

                $this->json(['success' => true]);
            } else {
                $this->json(['success' => false, 'message' => 'Failed to update layout'], 500);
            }
        } else {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
        }
    }

    /**
     * Toggle theme
     */
    public function toggleTheme() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];
        $currentTheme = $user['theme'] ?? 'light';
        $newTheme = $currentTheme === 'light' ? 'dark' : 'light';

        require_once __DIR__ . '/../models/User.php';
        $userModel = new User();

        $result = $userModel->update($userId, [
            'theme' => $newTheme,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            // Update user in session
            $updatedUser = $userModel->find($userId);
            Session::setUser($updatedUser);

            if ($this->isAjax()) {
                $this->json(['success' => true, 'theme' => $newTheme]);
            } else {
                $this->redirect('/dashboard');
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to update theme'], 500);
            } else {
                Session::setFlash('error', 'Failed to update theme');
                $this->redirect('/dashboard');
            }
        }
    }

    /**
     * Set current focus task
     */
    public function setFocusTask() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request - not AJAX'], 400);
            return;
        }

        // Get the raw input
        $rawInput = file_get_contents('php://input');
        $data = json_decode($rawInput, true);

        if (!$data || empty($data['task_id'])) {
            $this->json(['success' => false, 'message' => 'Missing task ID'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];
        $taskId = $data['task_id'];

        // Verify task exists and belongs to user
        $task = $this->taskModel->find($taskId);
        if (!$task) {
            $this->json(['success' => false, 'message' => 'Task not found'], 404);
            return;
        }

        if ($task['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Task does not belong to user'], 403);
            return;
        }

        require_once __DIR__ . '/../models/User.php';
        $userModel = new User();

        // Get database instance
        $db = Database::getInstance();

        // Use direct SQL update to bypass any constraint issues
        $updateSql = "UPDATE users SET current_focus_task_id = ?, updated_at = ? WHERE id = ?";
        $updateResult = $db->query($updateSql, [$taskId, date('Y-m-d H:i:s'), $userId]);

        if ($updateResult) {
            $result = true;
        } else {
            $result = false;
        }

        if ($result) {
            // Update user in session
            $updatedUser = $userModel->find($userId);
            Session::setUser($updatedUser);

            // Get category information
            if (!empty($task['category_id'])) {
                $category = $this->categoryModel->find($task['category_id']);
                if ($category) {
                    $task['category_name'] = $category['name'];
                    $task['category_color'] = $category['color'];
                }
            }

            $this->json([
                'success' => true,
                'message' => 'Focus task set successfully',
                'task' => $task
            ]);
        } else {
            // Try to get more specific error information
            $errorInfo = $db->getConnection()->errorInfo();
            $errorMessage = "Failed to set focus task - database error";

            if ($errorInfo && isset($errorInfo[2])) {
                $errorMessage .= ": " . $errorInfo[2];
            }

            $this->json(['success' => false, 'message' => $errorMessage], 500);
        }
    }

    /**
     * Clear current focus task
     */
    public function clearFocusTask() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        require_once __DIR__ . '/../models/User.php';
        $userModel = new User();

        // Get database instance
        $db = Database::getInstance();

        // Use direct SQL update to bypass any constraint issues
        $updateSql = "UPDATE users SET current_focus_task_id = NULL, updated_at = ? WHERE id = ?";
        $updateResult = $db->query($updateSql, [date('Y-m-d H:i:s'), $userId]);

        if ($updateResult) {
            $result = true;
        } else {
            $result = false;
        }

        if ($result) {
            // Update user in session
            $updatedUser = $userModel->find($userId);
            Session::setUser($updatedUser);

            $this->json([
                'success' => true,
                'message' => 'Focus task cleared successfully'
            ]);
        } else {
            // Try to get more specific error information
            $errorInfo = $db->getConnection()->errorInfo();
            $errorMessage = "Failed to clear focus task";

            if ($errorInfo && isset($errorInfo[2])) {
                $errorMessage .= ": " . $errorInfo[2];
            }

            $this->json(['success' => false, 'message' => $errorMessage], 500);
        }
    }
}
