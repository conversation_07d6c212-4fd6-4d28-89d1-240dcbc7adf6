<?php
/**
 * AI Prompt Controller
 *
 * Handles AI prompt management and execution
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/AIPrompt.php';
require_once __DIR__ . '/../models/AIPromptCategory.php';
require_once __DIR__ . '/../utils/Session.php';

class AIPromptController extends BaseController {
    private $promptModel;
    private $categoryModel;

    public function __construct() {
        $this->promptModel = new AIPrompt();
        $this->categoryModel = new AIPromptCategory();
    }

    /**
     * Show AI prompt dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get prompt statistics
        $stats = $this->promptModel->getPromptStats($userId);

        // Get recent prompts
        $recentPrompts = $this->promptModel->getRecentPrompts($userId, 5);

        // Get favorite prompts
        $favoritePrompts = $this->promptModel->getFavoritePrompts($userId, 5);

        // Get popular prompts
        $popularPrompts = $this->promptModel->getPopularPrompts($userId, 5);

        // Get categories
        $categories = $this->categoryModel->getCategoriesWithCounts($userId);

        $this->view('ai_prompts/index', [
            'stats' => $stats,
            'recentPrompts' => $recentPrompts,
            'favoritePrompts' => $favoritePrompts,
            'popularPrompts' => $popularPrompts,
            'categories' => $categories,
            'stylesheets' => [
                '/momentum/css/ai-prompts.css'
            ],
            'scripts' => [
                '/momentum/js/ai-prompts.js'
            ]
        ]);
    }

    /**
     * Show prompt library
     */
    public function library() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filters from query parameters
        $filters = $this->getQueryData();

        // Get prompts
        $prompts = $this->promptModel->getUserPrompts($userId, $filters);

        // Get categories for filter dropdown
        $categories = $this->categoryModel->getUserCategories($userId);

        $this->view('ai_prompts/library', [
            'prompts' => $prompts,
            'categories' => $categories,
            'filters' => $filters,
            'stylesheets' => [
                '/momentum/css/ai-prompts.css'
            ],
            'scripts' => [
                '/momentum/js/ai-prompts.js'
            ]
        ]);
    }

    /**
     * Show create prompt form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get categories
        $categories = $this->categoryModel->getUserCategories($userId);

        $this->view('ai_prompts/create', [
            'categories' => $categories,
            'stylesheets' => [
                '/momentum/css/ai-prompts.css'
            ],
            'scripts' => [
                '/momentum/js/ai-prompt-builder.js'
            ]
        ]);
    }

    /**
     * Store new prompt
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title', 'prompt_text']);

        if (!empty($errors)) {
            $categories = $this->categoryModel->getUserCategories($userId);

            $this->view('ai_prompts/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $categories,
                'stylesheets' => ['/momentum/css/ai-prompts.css'],
                'scripts' => ['/momentum/js/ai-prompt-builder.js']
            ]);
            return;
        }

        // Parse variables if provided
        $variables = [];
        if (!empty($data['variables_json'])) {
            $variables = json_decode($data['variables_json'], true) ?? [];
        }

        // Prepare prompt data
        $promptData = [
            'user_id' => $userId,
            'category_id' => !empty($data['category_id']) ? $data['category_id'] : null,
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
            'prompt_text' => $data['prompt_text'],
            'variables' => $variables,
            'tags' => $data['tags'] ?? null,
            'is_template' => isset($data['is_template']) ? 1 : 0,
            'is_favorite' => isset($data['is_favorite']) ? 1 : 0,
            'is_public' => isset($data['is_public']) ? 1 : 0
        ];

        $promptId = $this->promptModel->createPrompt($promptData);

        if ($promptId) {
            Session::setFlash('success', 'Prompt created successfully');
            $this->redirect('/ai-prompts/view/' . $promptId);
        } else {
            Session::setFlash('error', 'Failed to create prompt');
            $this->redirect('/ai-prompts/create');
        }
    }

    /**
     * View prompt details
     */
    public function viewPrompt($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $prompt = $this->promptModel->find($id);

        if (!$prompt || $prompt['user_id'] != $userId) {
            Session::setFlash('error', 'Prompt not found');
            $this->redirect('/ai-prompts');
            return;
        }

        // Parse variables
        $variables = [];
        if ($prompt['variables']) {
            $variables = json_decode($prompt['variables'], true) ?? [];
        }

        // Get category info
        $category = null;
        if ($prompt['category_id']) {
            $category = $this->categoryModel->find($prompt['category_id']);
        }

        $this->view('ai_prompts/view', [
            'prompt' => $prompt,
            'variables' => $variables,
            'category' => $category,
            'stylesheets' => [
                '/momentum/css/ai-prompts.css'
            ],
            'scripts' => [
                '/momentum/js/ai-prompt-executor.js'
            ]
        ]);
    }

    /**
     * Show edit prompt form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $prompt = $this->promptModel->find($id);

        if (!$prompt || $prompt['user_id'] != $userId) {
            Session::setFlash('error', 'Prompt not found');
            $this->redirect('/ai-prompts');
            return;
        }

        // Get categories
        $categories = $this->categoryModel->getUserCategories($userId);

        // Parse variables for editing
        $variables = [];
        if ($prompt['variables']) {
            $variables = json_decode($prompt['variables'], true) ?? [];
        }

        $this->view('ai_prompts/edit', [
            'prompt' => $prompt,
            'variables' => $variables,
            'categories' => $categories,
            'stylesheets' => [
                '/momentum/css/ai-prompts.css'
            ],
            'scripts' => [
                '/momentum/js/ai-prompt-builder.js'
            ]
        ]);
    }

    /**
     * Update prompt
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $prompt = $this->promptModel->find($id);

        if (!$prompt || $prompt['user_id'] != $userId) {
            Session::setFlash('error', 'Prompt not found');
            $this->redirect('/ai-prompts');
            return;
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title', 'prompt_text']);

        if (!empty($errors)) {
            $categories = $this->categoryModel->getUserCategories($userId);
            $variables = [];
            if ($prompt['variables']) {
                $variables = json_decode($prompt['variables'], true) ?? [];
            }

            $this->view('ai_prompts/edit', [
                'errors' => $errors,
                'prompt' => array_merge($prompt, $data),
                'variables' => $variables,
                'categories' => $categories,
                'stylesheets' => ['/momentum/css/ai-prompts.css'],
                'scripts' => ['/momentum/js/ai-prompt-builder.js']
            ]);
            return;
        }

        // Parse variables if provided
        $variables = [];
        if (!empty($data['variables_json'])) {
            $variables = json_decode($data['variables_json'], true) ?? [];
        }

        // Prepare update data
        $updateData = [
            'category_id' => !empty($data['category_id']) ? $data['category_id'] : null,
            'title' => $data['title'],
            'description' => $data['description'] ?? null,
            'prompt_text' => $data['prompt_text'],
            'variables' => $variables,
            'tags' => $data['tags'] ?? null,
            'is_template' => isset($data['is_template']) ? 1 : 0,
            'is_favorite' => isset($data['is_favorite']) ? 1 : 0,
            'is_public' => isset($data['is_public']) ? 1 : 0
        ];

        $result = $this->promptModel->updatePrompt($id, $updateData);

        if ($result) {
            Session::setFlash('success', 'Prompt updated successfully');
            $this->redirect('/ai-prompts/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update prompt');
            $this->redirect('/ai-prompts/edit/' . $id);
        }
    }

    /**
     * Delete prompt
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $prompt = $this->promptModel->find($id);

        if (!$prompt || $prompt['user_id'] != $userId) {
            Session::setFlash('error', 'Prompt not found');
            $this->redirect('/ai-prompts');
            return;
        }

        $result = $this->promptModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Prompt deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete prompt');
        }

        $this->redirect('/ai-prompts');
    }

    /**
     * Toggle favorite status
     */
    public function toggleFavorite($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $result = $this->promptModel->toggleFavorite($id, $userId);

        if ($result) {
            $this->jsonResponse(['success' => true]);
        } else {
            $this->jsonResponse(['success' => false, 'error' => 'Failed to toggle favorite']);
        }
    }

    /**
     * Fork/duplicate prompt
     */
    public function fork($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();
        $newTitle = $data['title'] ?? null;

        $newPromptId = $this->promptModel->forkPrompt($id, $userId, $newTitle);

        if ($newPromptId) {
            Session::setFlash('success', 'Prompt duplicated successfully');
            $this->redirect('/ai-prompts/view/' . $newPromptId);
        } else {
            Session::setFlash('error', 'Failed to duplicate prompt');
            $this->redirect('/ai-prompts/view/' . $id);
        }
    }

    /**
     * Execute prompt (placeholder for future implementation)
     */
    public function execute($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $prompt = $this->promptModel->find($id);

        if (!$prompt || $prompt['user_id'] != $userId) {
            Session::setFlash('error', 'Prompt not found');
            $this->redirect('/ai-prompts');
            return;
        }

        // For now, redirect to view with execution interface
        $this->redirect('/ai-prompts/view/' . $id . '?execute=1');
    }

    /**
     * Search prompts
     */
    public function search() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $query = $this->getQueryData()['q'] ?? '';
        $filters = $this->getQueryData();

        if (empty($query)) {
            $this->jsonResponse(['success' => false, 'error' => 'Search query is required']);
            return;
        }

        $results = $this->promptModel->searchPrompts($userId, $query, $filters);

        $this->jsonResponse(['success' => true, 'results' => $results]);
    }
}
