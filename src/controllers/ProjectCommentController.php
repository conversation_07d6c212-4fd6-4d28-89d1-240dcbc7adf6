<?php
/**
 * Project Comment Controller
 *
 * Handles project comment-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Project.php';

class ProjectCommentController extends BaseController {
    private $projectModel;

    public function __construct() {
        $this->projectModel = new Project();
    }

    /**
     * Add comment to project
     */
    public function addComment() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['project_id']) || empty($data['content'])) {
            $this->json(['success' => false, 'message' => 'Missing required fields'], 400);
            return;
        }

        $projectId = $data['project_id'];
        $content = $data['content'];

        // Verify project exists and user has access
        $project = $this->projectModel->find($projectId);
        $projectMembers = $this->projectModel->getProjectMembers($projectId);
        
        $hasAccess = false;
        if ($project['user_id'] == $userId) {
            $hasAccess = true;
        } else {
            foreach ($projectMembers as $member) {
                if ($member['user_id'] == $userId) {
                    $hasAccess = true;
                    break;
                }
            }
        }

        if (!$project || !$hasAccess) {
            $this->json(['success' => false, 'message' => 'Project not found or access denied'], 404);
            return;
        }

        // Add comment
        $commentData = [
            'project_id' => $projectId,
            'user_id' => $userId,
            'content' => $content,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $db = Database::getInstance();
        $result = $db->insert('project_comments', $commentData);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Comment added successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to add comment'], 500);
        }
    }

    /**
     * Edit comment
     */
    public function editComment() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['comment_id']) || empty($data['content'])) {
            $this->json(['success' => false, 'message' => 'Missing required fields'], 400);
            return;
        }

        $commentId = $data['comment_id'];
        $content = $data['content'];

        // Get comment details
        $db = Database::getInstance();
        $comment = $db->fetchOne("SELECT * FROM project_comments WHERE id = ?", [$commentId]);

        if (!$comment) {
            $this->json(['success' => false, 'message' => 'Comment not found'], 404);
            return;
        }

        // Verify user owns the comment
        if ($comment['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'You can only edit your own comments'], 403);
            return;
        }

        // Update comment
        $result = $db->update('project_comments', $commentId, [
            'content' => $content,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Comment updated successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update comment'], 500);
        }
    }

    /**
     * Delete comment
     */
    public function deleteComment() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['comment_id'])) {
            $this->json(['success' => false, 'message' => 'Missing required fields'], 400);
            return;
        }

        $commentId = $data['comment_id'];

        // Get comment details
        $db = Database::getInstance();
        $comment = $db->fetchOne("SELECT * FROM project_comments WHERE id = ?", [$commentId]);

        if (!$comment) {
            $this->json(['success' => false, 'message' => 'Comment not found'], 404);
            return;
        }

        $projectId = $comment['project_id'];

        // Verify user owns the comment or is project owner/admin
        $canDelete = false;
        
        if ($comment['user_id'] == $userId) {
            $canDelete = true;
        } else {
            // Check if user is project owner or admin
            $projectMembers = $this->projectModel->getProjectMembers($projectId);
            foreach ($projectMembers as $member) {
                if ($member['user_id'] == $userId && ($member['role'] === 'owner' || $member['role'] === 'admin')) {
                    $canDelete = true;
                    break;
                }
            }
        }

        if (!$canDelete) {
            $this->json(['success' => false, 'message' => 'You can only delete your own comments or as a project admin/owner'], 403);
            return;
        }

        // Delete comment
        $result = $db->delete('project_comments', $commentId);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Comment deleted successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to delete comment'], 500);
        }
    }
}
