<?php
/**
 * Freelance Invoice Controller
 *
 * Handles freelance invoice management functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/FreelanceInvoice.php';
require_once __DIR__ . '/../models/FreelanceClient.php';
require_once __DIR__ . '/../models/FreelanceProject.php';
require_once __DIR__ . '/../utils/Session.php';

class FreelanceInvoiceController extends BaseController {
    private $invoiceModel;
    private $clientModel;
    private $projectModel;

    public function __construct() {
        $this->invoiceModel = new FreelanceInvoice();
        $this->clientModel = new FreelanceClient();
        $this->projectModel = new FreelanceProject();
    }

    /**
     * Show invoices list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get invoices based on filters
        $invoices = $this->invoiceModel->getUserInvoices($userId, $filters);

        // Get invoice summary
        $invoiceSummary = $this->invoiceModel->getInvoiceSummary($userId);

        // Get clients and projects for filter dropdowns
        $clients = $this->clientModel->getUserClients($userId);
        $projects = $this->projectModel->getUserProjects($userId);

        $this->view('freelance/invoices/index', [
            'invoices' => $invoices,
            'invoiceSummary' => $invoiceSummary,
            'clients' => $clients,
            'projects' => $projects,
            'filters' => $filters
        ]);
    }

    /**
     * Show invoice creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all clients for the user
        $clients = $this->clientModel->getUserClients($userId);

        // Get all projects for the user
        $projects = $this->projectModel->getUserProjects($userId);

        // Generate next invoice number
        $nextInvoiceNumber = $this->invoiceModel->generateNextInvoiceNumber($userId);

        // Check if there's a project_id in the query string
        $queryData = $this->getQueryData();
        $data = [];

        if (!empty($queryData['project_id'])) {
            $data['project_id'] = $queryData['project_id'];

            // Get project details to pre-fill client
            $project = $this->projectModel->getProjectDetails($queryData['project_id'], $userId);
            if ($project) {
                $data['client_id'] = $project['client_id'];
            }
        }

        if (!empty($queryData['client_id'])) {
            $data['client_id'] = $queryData['client_id'];
        }

        $this->view('freelance/invoices/create', [
            'clients' => $clients,
            'projects' => $projects,
            'next_invoice_number' => $nextInvoiceNumber,
            'data' => $data
        ]);
    }

    /**
     * Process invoice creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        if (empty($data['client_id']) || empty($data['invoice_number']) ||
            empty($data['invoice_date']) || empty($data['due_date'])) {
            Session::setFlash('error', 'Client, invoice number, invoice date, and due date are required');

            // Get clients and projects for dropdowns
            $clients = $this->clientModel->getUserClients($userId);
            $projects = $this->projectModel->getUserProjects($userId);

            $this->view('freelance/invoices/create', [
                'data' => $data,
                'clients' => $clients,
                'projects' => $projects
            ]);
            return;
        }

        // Validate invoice items
        if (empty($data['items']) || !is_array($data['items'])) {
            Session::setFlash('error', 'At least one invoice item is required');

            // Get clients and projects for dropdowns
            $clients = $this->clientModel->getUserClients($userId);
            $projects = $this->projectModel->getUserProjects($userId);

            $this->view('freelance/invoices/create', [
                'data' => $data,
                'clients' => $clients,
                'projects' => $projects
            ]);
            return;
        }

        // Prepare invoice data
        $invoiceData = [
            'user_id' => $userId,
            'client_id' => $data['client_id'],
            'project_id' => !empty($data['project_id']) ? $data['project_id'] : null,
            'invoice_number' => $data['invoice_number'],
            'issue_date' => $data['invoice_date'],
            'due_date' => $data['due_date'],
            'subtotal' => !empty($data['subtotal']) ? $data['subtotal'] : 0,
            'tax_rate' => !empty($data['tax_rate']) ? $data['tax_rate'] : 0,
            'tax_amount' => !empty($data['tax_amount']) ? $data['tax_amount'] : 0,
            'discount_amount' => !empty($data['discount_amount']) ? $data['discount_amount'] : 0,
            'total_amount' => !empty($data['total_amount']) ? $data['total_amount'] : 0,
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'status' => !empty($data['status']) ? $data['status'] : 'draft',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Prepare invoice items
        $items = [];
        foreach ($data['items'] as $item) {
            if (!empty($item['description']) && isset($item['quantity']) && isset($item['unit_price'])) {
                $items[] = [
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'amount' => $item['total'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
            }
        }

        // Create invoice with items
        $invoiceId = $this->invoiceModel->createWithItems($invoiceData, $items);

        if ($invoiceId) {
            Session::setFlash('success', 'Invoice created successfully');
            $this->redirect('/freelance/invoices/view/' . $invoiceId);
        } else {
            Session::setFlash('error', 'Failed to create invoice');

            // Get clients and projects for dropdowns
            $clients = $this->clientModel->getUserClients($userId);
            $projects = $this->projectModel->getUserProjects($userId);

            $this->view('freelance/invoices/create', [
                'data' => $data,
                'clients' => $clients,
                'projects' => $projects
            ]);
        }
    }

    /**
     * Show invoice details
     */
    public function viewInvoice($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get invoice details
        $invoice = $this->invoiceModel->getInvoiceDetails($id, $userId);

        // Verify invoice exists and belongs to user
        if (!$invoice) {
            Session::setFlash('error', 'Invoice not found');
            $this->redirect('/freelance/invoices');
            return;
        }

        $this->view('freelance/invoices/view', [
            'invoice' => $invoice
        ]);
    }

    /**
     * Show invoice edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get invoice details
        $invoice = $this->invoiceModel->getInvoiceDetails($id, $userId);

        // Verify invoice exists and belongs to user
        if (!$invoice) {
            Session::setFlash('error', 'Invoice not found');
            $this->redirect('/freelance/invoices');
            return;
        }

        // Get clients and projects for dropdowns
        $clients = $this->clientModel->getUserClients($userId);
        $projects = $this->projectModel->getUserProjects($userId);

        $this->view('freelance/invoices/edit', [
            'invoice' => $invoice,
            'clients' => $clients,
            'projects' => $projects
        ]);
    }

    /**
     * Process invoice update
     */
    public function update($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->redirect('/freelance/invoices/view/' . $id);
    }

    /**
     * Process invoice deletion
     */
    public function delete($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->redirect('/freelance/invoices');
    }

    /**
     * Print invoice
     */
    public function printInvoice($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get invoice details
        $invoice = $this->invoiceModel->getInvoiceDetails($id, $userId);

        // Verify invoice exists and belongs to user
        if (!$invoice) {
            Session::setFlash('error', 'Invoice not found');
            $this->redirect('/freelance/invoices');
            return;
        }

        $this->view('freelance/invoices/print', [
            'invoice' => $invoice
        ]);
    }

    /**
     * Send invoice by email
     */
    public function sendInvoice($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->redirect('/freelance/invoices/view/' . $id);
    }

    /**
     * Mark invoice as paid
     */
    public function markPaid($id) {
        $this->requireLogin();

        // This is a stub method - implementation will be added later
        $this->redirect('/freelance/invoices/view/' . $id);
    }
}
