<?php
/**
 * Productivity Controller
 *
 * Handles productivity-related functionality like focus timer, time blocking, and energy tracking.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Task.php';
require_once __DIR__ . '/../models/TimeBlock.php';
require_once __DIR__ . '/../models/Category.php';
require_once __DIR__ . '/../models/EnergyLevel.php';
require_once __DIR__ . '/../models/TaskBatch.php';
require_once __DIR__ . '/../models/BatchTemplate.php';

class ProductivityController extends BaseController {
    private $taskModel;
    private $timeBlockModel;
    private $categoryModel;
    private $energyLevelModel;
    private $taskBatchModel;
    private $batchTemplateModel;

    public function __construct() {
        $this->taskModel = new Task();
        $this->timeBlockModel = new TimeBlock();
        $this->categoryModel = new Category();
        $this->energyLevelModel = new EnergyLevel();
        $this->taskBatchModel = new TaskBatch();
        $this->batchTemplateModel = new BatchTemplate();
    }

    /**
     * Show focus timer
     */
    public function focusTimer() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task ID from query string if provided
        $taskId = $this->getQueryData()['task_id'] ?? null;
        $task = null;

        if ($taskId) {
            // Get task details
            $task = $this->taskModel->find($taskId);

            // Verify task exists and belongs to user
            if (!$task || $task['user_id'] != $userId) {
                Session::setFlash('error', 'Task not found');
                $this->redirect('/productivity/focus-timer');
            }
        }

        // Get today's tasks for selection
        $todayTasks = $this->taskModel->getTodayTasks($userId);

        $this->view('productivity/focus_timer', [
            'task' => $task,
            'todayTasks' => $todayTasks
        ]);
    }

    /**
     * Show focus mode
     */
    public function focusMode() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get task ID from query string if provided
        $taskId = $this->getQueryData()['task_id'] ?? null;
        $task = null;

        if ($taskId) {
            // Get task details
            $task = $this->taskModel->find($taskId);

            // Verify task exists and belongs to user
            if (!$task || $task['user_id'] != $userId) {
                Session::setFlash('error', 'Task not found');
                $this->redirect('/productivity/focus-mode');
            }
        } else {
            // Redirect to task selection if no task provided
            $this->redirect('/tasks?focus_mode=1');
        }

        $this->view('productivity/focus_mode', [
            'task' => $task
        ]);
    }

    /**
     * Log completed focus session
     */
    public function logFocusSession() {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || empty($data['task_id']) || empty($data['duration'])) {
            $this->json(['success' => false, 'message' => 'Missing required data'], 400);
            return;
        }

        $taskId = $data['task_id'];

        // Get task
        $task = $this->taskModel->find($taskId);

        // Verify task exists and belongs to user
        if (!$task || $task['user_id'] != $userId) {
            $this->json(['success' => false, 'message' => 'Task not found'], 404);
            return;
        }

        // In a real application, you would log the focus session in a separate table
        // For this demo, we'll just return success

        $this->json([
            'success' => true,
            'message' => 'Focus session logged successfully'
        ]);
    }

    /**
     * Show time blocking calendar
     */
    public function timeBlocking() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get view type (day, week, month)
        $viewType = $this->getQueryData()['view'] ?? 'week';

        // Get date
        $date = $this->getQueryData()['date'] ?? date('Y-m-d');

        // Calculate date range based on view type
        $startDate = null;
        $endDate = null;

        switch ($viewType) {
            case 'day':
                $startDate = $date . ' 00:00:00';
                $endDate = $date . ' 23:59:59';
                break;
            case 'week':
                $startOfWeek = date('Y-m-d', strtotime('monday this week', strtotime($date)));
                $endOfWeek = date('Y-m-d', strtotime('sunday this week', strtotime($date)));
                $startDate = $startOfWeek . ' 00:00:00';
                $endDate = $endOfWeek . ' 23:59:59';
                break;
            case 'month':
            default:
                $startOfMonth = date('Y-m-01', strtotime($date));
                $endOfMonth = date('Y-m-t', strtotime($date));
                $startDate = $startOfMonth . ' 00:00:00';
                $endDate = $endOfMonth . ' 23:59:59';
                break;
        }

        // Get time blocks for the date range
        $timeBlocks = $this->timeBlockModel->getTimeBlocksByDateRange($userId, $startDate, $endDate);

        // Get tasks for selection
        $tasks = $this->taskModel->getActiveTasks($userId);

        // Get categories for color coding
        $categories = $this->categoryModel->getUserCategories($userId);

        // Get energy level recommendations
        $energyRecommendations = $this->energyLevelModel->getEnergyLevelRecommendations($userId);

        // Get latest energy level
        $latestEnergyLevel = $this->energyLevelModel->getLatestEnergyLevel($userId);

        $this->view('productivity/time_blocking', [
            'timeBlocks' => $timeBlocks,
            'tasks' => $tasks,
            'categories' => $categories,
            'viewType' => $viewType,
            'date' => $date,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'energyRecommendations' => $energyRecommendations,
            'latestEnergyLevel' => $latestEnergyLevel
        ]);
    }

    /**
     * Create a new time block
     */
    public function createTimeBlock() {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/productivity/time-blocking');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $title = $_POST['title'] ?? '';
        $description = $_POST['description'] ?? '';
        $startDate = $_POST['start_date'] ?? '';
        $startTime = $_POST['start_time'] ?? '';
        $endDate = $_POST['end_date'] ?? '';
        $endTime = $_POST['end_time'] ?? '';
        $category = $_POST['category'] ?? '';
        $color = $_POST['color'] ?? '#4F46E5';
        $taskId = !empty($_POST['task_id']) ? $_POST['task_id'] : null;
        $isRecurring = isset($_POST['is_recurring']) && $_POST['is_recurring'] === 'on';
        $recurrencePattern = $isRecurring ? ($_POST['recurrence_pattern'] ?? '') : null;

        // Validate required fields
        if (empty($title) || empty($startDate) || empty($startTime) || empty($endDate) || empty($endTime)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/productivity/time-blocking');
            return;
        }

        // Format datetime strings
        $startDateTime = date('Y-m-d H:i:s', strtotime("$startDate $startTime"));
        $endDateTime = date('Y-m-d H:i:s', strtotime("$endDate $endTime"));

        // Validate end time is after start time
        if (strtotime($endDateTime) <= strtotime($startDateTime)) {
            Session::setFlash('error', 'End time must be after start time');
            $this->redirect('/productivity/time-blocking');
            return;
        }

        // Check for conflicts
        $conflicts = $this->timeBlockModel->checkConflicts($userId, $startDateTime, $endDateTime);
        if (!empty($conflicts)) {
            Session::setFlash('error', 'This time block conflicts with an existing time block');
            $this->redirect('/productivity/time-blocking');
            return;
        }

        // Create time block
        $timeBlockData = [
            'user_id' => $userId,
            'title' => $title,
            'description' => $description,
            'start_time' => $startDateTime,
            'end_time' => $endDateTime,
            'category' => $category,
            'color' => $color,
            'task_id' => $taskId,
            'is_recurring' => $isRecurring ? 1 : 0,
            'recurrence_pattern' => $recurrencePattern
        ];

        $result = $this->timeBlockModel->create($timeBlockData);

        if ($result) {
            Session::setFlash('success', 'Time block created successfully');
        } else {
            Session::setFlash('error', 'Failed to create time block');
        }

        // Redirect back to the time blocking page with the current view and date parameters
        $viewType = $this->getQueryData()['view'] ?? 'week';
        $date = $this->getQueryData()['date'] ?? date('Y-m-d');
        $this->redirect("/productivity/time-blocking?view={$viewType}&date={$date}");
    }

    /**
     * Update a time block
     */
    public function updateTimeBlock($id) {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/productivity/time-blocking');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if time block exists and belongs to user
        $timeBlock = $this->timeBlockModel->find($id);
        if (!$timeBlock || $timeBlock['user_id'] != $userId) {
            Session::setFlash('error', 'Time block not found');
            $this->redirect('/productivity/time-blocking');
            return;
        }

        $title = $_POST['title'] ?? '';
        $description = $_POST['description'] ?? '';
        $startDate = $_POST['start_date'] ?? '';
        $startTime = $_POST['start_time'] ?? '';
        $endDate = $_POST['end_date'] ?? '';
        $endTime = $_POST['end_time'] ?? '';
        $category = $_POST['category'] ?? '';
        $color = $_POST['color'] ?? '#4F46E5';
        $taskId = !empty($_POST['task_id']) ? $_POST['task_id'] : null;
        $isRecurring = isset($_POST['is_recurring']) && $_POST['is_recurring'] === 'on';
        $recurrencePattern = $isRecurring ? ($_POST['recurrence_pattern'] ?? '') : null;

        // Validate required fields
        if (empty($title) || empty($startDate) || empty($startTime) || empty($endDate) || empty($endTime)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->redirect('/productivity/time-blocking');
            return;
        }

        // Format datetime strings
        $startDateTime = date('Y-m-d H:i:s', strtotime("$startDate $startTime"));
        $endDateTime = date('Y-m-d H:i:s', strtotime("$endDate $endTime"));

        // Validate end time is after start time
        if (strtotime($endDateTime) <= strtotime($startDateTime)) {
            Session::setFlash('error', 'End time must be after start time');
            $this->redirect('/productivity/time-blocking');
            return;
        }

        // Check for conflicts (excluding this time block)
        $conflicts = $this->timeBlockModel->checkConflicts($userId, $startDateTime, $endDateTime, $id);
        if (!empty($conflicts)) {
            Session::setFlash('error', 'This time block conflicts with an existing time block');
            $this->redirect('/productivity/time-blocking');
            return;
        }

        // Update time block
        $timeBlockData = [
            'title' => $title,
            'description' => $description,
            'start_time' => $startDateTime,
            'end_time' => $endDateTime,
            'category' => $category,
            'color' => $color,
            'task_id' => $taskId,
            'is_recurring' => $isRecurring ? 1 : 0,
            'recurrence_pattern' => $recurrencePattern
        ];

        $result = $this->timeBlockModel->update($id, $timeBlockData);

        if ($result) {
            Session::setFlash('success', 'Time block updated successfully');
        } else {
            Session::setFlash('error', 'Failed to update time block');
        }

        // Redirect back to the time blocking page with the current view and date parameters
        $viewType = $this->getQueryData()['view'] ?? 'week';
        $date = $this->getQueryData()['date'] ?? date('Y-m-d');
        $this->redirect("/productivity/time-blocking?view={$viewType}&date={$date}");
    }

    /**
     * Delete a time block
     */
    public function deleteTimeBlock($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if time block exists and belongs to user
        if (!$this->timeBlockModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Time block not found');
            $this->redirect('/productivity/time-blocking');
            return;
        }

        $result = $this->timeBlockModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Time block deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete time block');
        }

        // Redirect back to the time blocking page with the current view and date parameters
        $viewType = $this->getQueryData()['view'] ?? 'week';
        $date = $this->getQueryData()['date'] ?? date('Y-m-d');
        $this->redirect("/productivity/time-blocking?view={$viewType}&date={$date}");
    }

    /**
     * Get time block data for editing
     */
    public function getTimeBlock($id) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if time block exists and belongs to user
        if (!$this->timeBlockModel->belongsToUser($id, $userId)) {
            $this->json(['success' => false, 'message' => 'Time block not found'], 404);
            return;
        }

        $timeBlock = $this->timeBlockModel->find($id);

        $this->json([
            'success' => true,
            'timeBlock' => $timeBlock
        ]);
    }

    /**
     * Show energy level tracking
     */
    public function energyTracking() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get time period from query string (default: week)
        $period = $this->getQueryData()['period'] ?? 'week';

        // Get energy levels based on period
        $energyLevels = [];
        switch ($period) {
            case 'day':
                $energyLevels = $this->energyLevelModel->getTodayEnergyLevels($userId);
                break;
            case 'month':
                $energyLevels = $this->energyLevelModel->getMonthEnergyLevels($userId);
                break;
            case 'week':
            default:
                $energyLevels = $this->energyLevelModel->getWeekEnergyLevels($userId);
                break;
        }

        // Get latest energy level
        $latestEnergyLevel = $this->energyLevelModel->getLatestEnergyLevel($userId);

        // Get average energy levels by hour
        $hourlyAverages = $this->energyLevelModel->getAverageEnergyLevelByHour($userId);

        // Get average energy levels by day of week
        $dayOfWeekAverages = $this->energyLevelModel->getAverageEnergyLevelByDayOfWeek($userId);

        // Get energy level recommendations
        $recommendations = $this->energyLevelModel->getEnergyLevelRecommendations($userId);

        $this->view('productivity/energy_tracking', [
            'energyLevels' => $energyLevels,
            'latestEnergyLevel' => $latestEnergyLevel,
            'hourlyAverages' => $hourlyAverages,
            'dayOfWeekAverages' => $dayOfWeekAverages,
            'recommendations' => $recommendations,
            'period' => $period
        ]);
    }

    /**
     * Record a new energy level
     */
    public function recordEnergyLevel() {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/productivity/energy-tracking');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $level = isset($_POST['level']) ? (int)$_POST['level'] : null;
        $notes = $_POST['notes'] ?? '';
        $recordedAt = $_POST['recorded_at'] ?? date('Y-m-d H:i:s');

        // Validate level
        if ($level === null || $level < 1 || $level > 10) {
            Session::setFlash('error', 'Please provide a valid energy level (1-10)');
            $this->redirect('/productivity/energy-tracking');
            return;
        }

        // Create energy level record
        $energyLevelData = [
            'user_id' => $userId,
            'level' => $level,
            'notes' => $notes,
            'recorded_at' => $recordedAt
        ];

        $result = $this->energyLevelModel->create($energyLevelData);

        if ($result) {
            Session::setFlash('success', 'Energy level recorded successfully');
        } else {
            Session::setFlash('error', 'Failed to record energy level');
        }

        $this->redirect('/productivity/energy-tracking');
    }

    /**
     * Update an energy level record
     */
    public function updateEnergyLevel($id) {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/productivity/energy-tracking');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if energy level record exists and belongs to user
        if (!$this->energyLevelModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Energy level record not found');
            $this->redirect('/productivity/energy-tracking');
            return;
        }

        $level = isset($_POST['level']) ? (int)$_POST['level'] : null;
        $notes = $_POST['notes'] ?? '';
        $recordedAt = $_POST['recorded_at'] ?? date('Y-m-d H:i:s');

        // Validate level
        if ($level === null || $level < 1 || $level > 10) {
            Session::setFlash('error', 'Please provide a valid energy level (1-10)');
            $this->redirect('/productivity/energy-tracking');
            return;
        }

        // Update energy level record
        $energyLevelData = [
            'level' => $level,
            'notes' => $notes,
            'recorded_at' => $recordedAt
        ];

        $result = $this->energyLevelModel->update($id, $energyLevelData);

        if ($result) {
            Session::setFlash('success', 'Energy level updated successfully');
        } else {
            Session::setFlash('error', 'Failed to update energy level');
        }

        $this->redirect('/productivity/energy-tracking');
    }

    /**
     * Delete an energy level record
     */
    public function deleteEnergyLevel($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if energy level record exists and belongs to user
        if (!$this->energyLevelModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Energy level record not found');
            $this->redirect('/productivity/energy-tracking');
            return;
        }

        $result = $this->energyLevelModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Energy level deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete energy level');
        }

        $this->redirect('/productivity/energy-tracking');
    }

    /**
     * Get energy level widget for dashboard
     */
    public function getEnergyLevelWidget() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get latest energy level
        $latestEnergyLevel = $this->energyLevelModel->getLatestEnergyLevel($userId);

        // Get today's energy levels
        $todayEnergyLevels = $this->energyLevelModel->getTodayEnergyLevels($userId);

        // Get recommendations
        $recommendations = $this->energyLevelModel->getEnergyLevelRecommendations($userId);

        $this->view('productivity/widgets/energy_level', [
            'latestEnergyLevel' => $latestEnergyLevel,
            'todayEnergyLevels' => $todayEnergyLevels,
            'recommendations' => $recommendations
        ]);
    }

    /**
     * Show task batching
     */
    public function taskBatching() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all batches with tasks
        $batches = $this->taskBatchModel->getBatchesWithTasks($userId);

        // Get tasks not in any batch
        $unbatchedTasks = $this->taskBatchModel->getTasksNotInBatch($userId);

        // Get batch statistics
        $statistics = $this->taskBatchModel->getBatchStatistics($userId);

        // Get latest energy level
        $latestEnergyLevel = $this->energyLevelModel->getLatestEnergyLevel($userId);

        // Get recommended batches based on current energy level
        $recommendedBatches = [];
        if ($latestEnergyLevel) {
            $recommendedBatches = $this->taskBatchModel->getRecommendedBatches($userId, $latestEnergyLevel['level']);
        }

        $this->view('productivity/task_batching', [
            'batches' => $batches,
            'unbatchedTasks' => $unbatchedTasks,
            'statistics' => $statistics,
            'latestEnergyLevel' => $latestEnergyLevel,
            'recommendedBatches' => $recommendedBatches
        ]);
    }

    /**
     * Create a new task batch
     */
    public function createBatch() {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/productivity/task-batching');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $energyLevel = $_POST['energy_level'] ?? 'medium';
        $estimatedTime = !empty($_POST['estimated_time']) ? (int)$_POST['estimated_time'] : null;
        $taskIds = isset($_POST['task_ids']) ? explode(',', $_POST['task_ids']) : [];

        // Validate required fields
        if (empty($name)) {
            Session::setFlash('error', 'Please provide a name for the batch');
            $this->redirect('/productivity/task-batching');
            return;
        }

        // Create batch
        $batchData = [
            'user_id' => $userId,
            'name' => $name,
            'description' => $description,
            'energy_level' => $energyLevel,
            'estimated_time' => $estimatedTime,
            'status' => 'active'
        ];

        $batchId = $this->taskBatchModel->create($batchData);

        if (!$batchId) {
            Session::setFlash('error', 'Failed to create batch');
            $this->redirect('/productivity/task-batching');
            return;
        }

        // Add tasks to batch
        if (!empty($taskIds)) {
            $this->taskBatchModel->addTasksToBatch($batchId, $taskIds);
        }

        Session::setFlash('success', 'Batch created successfully');
        $this->redirect('/productivity/task-batching');
    }

    /**
     * View a task batch
     */
    public function viewBatch($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if batch exists and belongs to user
        if (!$this->taskBatchModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Batch not found');
            $this->redirect('/productivity/task-batching');
            return;
        }

        // Get batch with tasks
        $batch = $this->taskBatchModel->getBatchWithTasks($id);

        // Get tasks not in this batch
        $unbatchedTasks = $this->taskBatchModel->getTasksNotInBatch($userId);

        $this->view('productivity/view_batch', [
            'batch' => $batch,
            'unbatchedTasks' => $unbatchedTasks
        ]);
    }

    /**
     * Update a task batch
     */
    public function updateBatch($id) {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/productivity/task-batching');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if batch exists and belongs to user
        if (!$this->taskBatchModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Batch not found');
            $this->redirect('/productivity/task-batching');
            return;
        }

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $energyLevel = $_POST['energy_level'] ?? 'medium';
        $estimatedTime = !empty($_POST['estimated_time']) ? (int)$_POST['estimated_time'] : null;
        $status = $_POST['status'] ?? 'active';

        // Validate required fields
        if (empty($name)) {
            Session::setFlash('error', 'Please provide a name for the batch');
            $this->redirect("/productivity/view-batch/$id");
            return;
        }

        // Update batch
        $batchData = [
            'name' => $name,
            'description' => $description,
            'energy_level' => $energyLevel,
            'estimated_time' => $estimatedTime,
            'status' => $status
        ];

        $result = $this->taskBatchModel->update($id, $batchData);

        if ($result) {
            Session::setFlash('success', 'Batch updated successfully');
        } else {
            Session::setFlash('error', 'Failed to update batch');
        }

        $this->redirect("/productivity/view-batch/$id");
    }

    /**
     * Delete a task batch
     */
    public function deleteBatch($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if batch exists and belongs to user
        if (!$this->taskBatchModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Batch not found');
            $this->redirect('/productivity/task-batching');
            return;
        }

        $result = $this->taskBatchModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Batch deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete batch');
        }

        $this->redirect('/productivity/task-batching');
    }

    /**
     * Add a task to a batch
     */
    public function addTaskToBatch($id) {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect("/productivity/view-batch/$id");
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if batch exists and belongs to user
        if (!$this->taskBatchModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Batch not found');
            $this->redirect('/productivity/task-batching');
            return;
        }

        $taskId = $_POST['task_id'] ?? null;

        if (!$taskId) {
            Session::setFlash('error', 'No task selected');
            $this->redirect("/productivity/view-batch/$id");
            return;
        }

        // Check if task exists and belongs to user
        $task = $this->taskModel->find($taskId);
        if (!$task || $task['user_id'] != $userId) {
            Session::setFlash('error', 'Task not found');
            $this->redirect("/productivity/view-batch/$id");
            return;
        }

        // Add task to batch
        $result = $this->taskBatchModel->addTasksToBatch($id, [$taskId]);

        if ($result) {
            Session::setFlash('success', 'Task added to batch successfully');
        } else {
            Session::setFlash('error', 'Failed to add task to batch');
        }

        $this->redirect("/productivity/view-batch/$id");
    }

    /**
     * Remove a task from a batch
     */
    public function removeTaskFromBatch($batchId, $taskId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if batch exists and belongs to user
        if (!$this->taskBatchModel->belongsToUser($batchId, $userId)) {
            Session::setFlash('error', 'Batch not found');
            $this->redirect('/productivity/task-batching');
            return;
        }

        // Check if task exists and belongs to user
        $task = $this->taskModel->find($taskId);
        if (!$task || $task['user_id'] != $userId) {
            Session::setFlash('error', 'Task not found');
            $this->redirect("/productivity/view-batch/$batchId");
            return;
        }

        // Remove task from batch
        $result = $this->taskBatchModel->removeTaskFromBatch($batchId, $taskId);

        if ($result) {
            Session::setFlash('success', 'Task removed from batch successfully');
        } else {
            Session::setFlash('error', 'Failed to remove task from batch');
        }

        $this->redirect("/productivity/view-batch/$batchId");
    }

    /**
     * Reorder tasks in a batch
     */
    public function reorderBatchTasks($id) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if batch exists and belongs to user
        if (!$this->taskBatchModel->belongsToUser($id, $userId)) {
            $this->json(['success' => false, 'message' => 'Batch not found'], 404);
            return;
        }

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || !isset($data['task_ids']) || !is_array($data['task_ids'])) {
            $this->json(['success' => false, 'message' => 'Missing required data'], 400);
            return;
        }

        $taskIds = $data['task_ids'];

        // Reorder tasks
        $result = $this->taskBatchModel->reorderBatchTasks($id, $taskIds);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Tasks reordered successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to reorder tasks'], 500);
        }
    }

    /**
     * Schedule a batch as a time block
     */
    public function scheduleBatch($id) {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect("/productivity/view-batch/$id");
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if batch exists and belongs to user
        if (!$this->taskBatchModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Batch not found');
            $this->redirect('/productivity/task-batching');
            return;
        }

        $startDate = $_POST['start_date'] ?? '';
        $startTime = $_POST['start_time'] ?? '';
        $duration = !empty($_POST['duration']) ? (int)$_POST['duration'] : 60; // Default to 60 minutes

        // Validate required fields
        if (empty($startDate) || empty($startTime)) {
            Session::setFlash('error', 'Please provide a start date and time');
            $this->redirect("/productivity/view-batch/$id");
            return;
        }

        // Format datetime string
        $startDateTime = date('Y-m-d H:i:s', strtotime("$startDate $startTime"));

        // Schedule batch
        $result = $this->taskBatchModel->scheduleBatch($id, $startDateTime, $duration);

        if ($result) {
            Session::setFlash('success', 'Batch scheduled successfully');
            $this->redirect('/productivity/time-blocking');
        } else {
            Session::setFlash('error', 'Failed to schedule batch');
            $this->redirect("/productivity/view-batch/$id");
        }
    }

    /**
     * Get task batch widget for dashboard
     */
    public function getTaskBatchWidget() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get latest energy level
        $latestEnergyLevel = $this->energyLevelModel->getLatestEnergyLevel($userId);

        // Get recommended batches based on current energy level
        $recommendedBatches = [];
        if ($latestEnergyLevel) {
            $recommendedBatches = $this->taskBatchModel->getRecommendedBatches($userId, $latestEnergyLevel['level']);
        }

        // Get batch statistics
        $statistics = $this->taskBatchModel->getBatchStatistics($userId);

        $this->view('productivity/widgets/task_batch', [
            'latestEnergyLevel' => $latestEnergyLevel,
            'recommendedBatches' => $recommendedBatches,
            'statistics' => $statistics
        ]);
    }

    /**
     * Show batch templates
     */
    public function batchTemplates() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get all templates with items
        $templates = $this->batchTemplateModel->getTemplatesWithItems($userId);

        // Get template statistics
        $statistics = $this->batchTemplateModel->getTemplateStatistics($userId);

        $this->view('productivity/batch_templates', [
            'templates' => $templates,
            'statistics' => $statistics
        ]);
    }

    /**
     * Create a new batch template
     */
    public function createTemplate() {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/productivity/batch-templates');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $energyLevel = $_POST['energy_level'] ?? 'medium';
        $estimatedTime = !empty($_POST['estimated_time']) ? (int)$_POST['estimated_time'] : null;
        $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;
        $recurrencePattern = $isRecurring ? ($_POST['recurrence_pattern'] ?? null) : null;

        // Validate required fields
        if (empty($name)) {
            Session::setFlash('error', 'Please provide a name for the template');
            $this->redirect('/productivity/batch-templates');
            return;
        }

        // Create template
        $templateData = [
            'user_id' => $userId,
            'name' => $name,
            'description' => $description,
            'energy_level' => $energyLevel,
            'estimated_time' => $estimatedTime,
            'is_recurring' => $isRecurring,
            'recurrence_pattern' => $recurrencePattern
        ];

        $templateId = $this->batchTemplateModel->create($templateData);

        if (!$templateId) {
            Session::setFlash('error', 'Failed to create template');
            $this->redirect('/productivity/batch-templates');
            return;
        }

        // Add items to template if provided
        $items = [];
        if (isset($_POST['task_types']) && is_array($_POST['task_types'])) {
            foreach ($_POST['task_types'] as $index => $taskType) {
                if (empty($taskType)) continue;

                $items[] = [
                    'task_type' => $taskType,
                    'description' => $_POST['item_descriptions'][$index] ?? '',
                    'estimated_time' => !empty($_POST['item_times'][$index]) ? (int)$_POST['item_times'][$index] : null
                ];
            }
        }

        if (!empty($items)) {
            $this->batchTemplateModel->addItemsToTemplate($templateId, $items);
        }

        Session::setFlash('success', 'Template created successfully');
        $this->redirect('/productivity/view-template/' . $templateId);
    }

    /**
     * View a batch template
     */
    public function viewTemplate($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if template exists and belongs to user
        if (!$this->batchTemplateModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Template not found');
            $this->redirect('/productivity/batch-templates');
            return;
        }

        // Get template with items
        $template = $this->batchTemplateModel->getTemplateWithItems($id);

        $this->view('productivity/view_template', [
            'template' => $template
        ]);
    }

    /**
     * Update a batch template
     */
    public function updateTemplate($id) {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/productivity/batch-templates');
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if template exists and belongs to user
        if (!$this->batchTemplateModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Template not found');
            $this->redirect('/productivity/batch-templates');
            return;
        }

        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $energyLevel = $_POST['energy_level'] ?? 'medium';
        $estimatedTime = !empty($_POST['estimated_time']) ? (int)$_POST['estimated_time'] : null;
        $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;
        $recurrencePattern = $isRecurring ? ($_POST['recurrence_pattern'] ?? null) : null;

        // Validate required fields
        if (empty($name)) {
            Session::setFlash('error', 'Please provide a name for the template');
            $this->redirect("/productivity/view-template/$id");
            return;
        }

        // Update template
        $templateData = [
            'name' => $name,
            'description' => $description,
            'energy_level' => $energyLevel,
            'estimated_time' => $estimatedTime,
            'is_recurring' => $isRecurring,
            'recurrence_pattern' => $recurrencePattern
        ];

        $result = $this->batchTemplateModel->update($id, $templateData);

        if ($result) {
            Session::setFlash('success', 'Template updated successfully');
        } else {
            Session::setFlash('error', 'Failed to update template');
        }

        $this->redirect("/productivity/view-template/$id");
    }

    /**
     * Delete a batch template
     */
    public function deleteTemplate($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if template exists and belongs to user
        if (!$this->batchTemplateModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Template not found');
            $this->redirect('/productivity/batch-templates');
            return;
        }

        $result = $this->batchTemplateModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Template deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete template');
        }

        $this->redirect('/productivity/batch-templates');
    }

    /**
     * Add an item to a template
     */
    public function addItemToTemplate($id) {
        $this->requireLogin();

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect("/productivity/view-template/$id");
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if template exists and belongs to user
        if (!$this->batchTemplateModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Template not found');
            $this->redirect('/productivity/batch-templates');
            return;
        }

        $taskType = $_POST['task_type'] ?? '';
        $description = $_POST['description'] ?? '';
        $estimatedTime = !empty($_POST['estimated_time']) ? (int)$_POST['estimated_time'] : null;

        if (empty($taskType)) {
            Session::setFlash('error', 'Please provide a task type');
            $this->redirect("/productivity/view-template/$id");
            return;
        }

        // Add item to template
        $items = [[
            'task_type' => $taskType,
            'description' => $description,
            'estimated_time' => $estimatedTime
        ]];

        $result = $this->batchTemplateModel->addItemsToTemplate($id, $items);

        if ($result) {
            Session::setFlash('success', 'Item added to template successfully');
        } else {
            Session::setFlash('error', 'Failed to add item to template');
        }

        $this->redirect("/productivity/view-template/$id");
    }

    /**
     * Remove an item from a template
     */
    public function removeItemFromTemplate($templateId, $itemId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if template exists and belongs to user
        if (!$this->batchTemplateModel->belongsToUser($templateId, $userId)) {
            Session::setFlash('error', 'Template not found');
            $this->redirect('/productivity/batch-templates');
            return;
        }

        $result = $this->batchTemplateModel->removeItemFromTemplate($itemId);

        if ($result) {
            Session::setFlash('success', 'Item removed from template successfully');
        } else {
            Session::setFlash('error', 'Failed to remove item from template');
        }

        $this->redirect("/productivity/view-template/$templateId");
    }

    /**
     * Reorder items in a template
     */
    public function reorderTemplateItems($id) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if template exists and belongs to user
        if (!$this->batchTemplateModel->belongsToUser($id, $userId)) {
            $this->json(['success' => false, 'message' => 'Template not found'], 404);
            return;
        }

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || !isset($data['item_ids']) || !is_array($data['item_ids'])) {
            $this->json(['success' => false, 'message' => 'Missing required data'], 400);
            return;
        }

        $itemIds = $data['item_ids'];

        // Reorder items
        $result = $this->batchTemplateModel->reorderTemplateItems($id, $itemIds);

        if ($result) {
            $this->json(['success' => true, 'message' => 'Items reordered successfully']);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to reorder items'], 500);
        }
    }

    /**
     * Create a batch from a template
     */
    public function createBatchFromTemplate($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if template exists and belongs to user
        if (!$this->batchTemplateModel->belongsToUser($id, $userId)) {
            Session::setFlash('error', 'Template not found');
            $this->redirect('/productivity/batch-templates');
            return;
        }

        // Create batch from template
        $batchId = $this->batchTemplateModel->createBatchFromTemplate($id, $userId);

        if ($batchId) {
            Session::setFlash('success', 'Batch created from template successfully');
            $this->redirect("/productivity/view-batch/$batchId");
        } else {
            Session::setFlash('error', 'Failed to create batch from template');
            $this->redirect("/productivity/view-template/$id");
        }
    }

    /**
     * Generate recurring batches
     * This method is called by a cron job
     */
    public function generateRecurringBatches() {
        // Generate batches from recurring templates
        $generatedCount = $this->batchTemplateModel->generateRecurringBatches();

        echo "Generated $generatedCount batches from recurring templates.";
    }

    /**
     * Update time block time via AJAX (for drag and drop)
     */
    public function updateTimeBlockTime($id) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if time block exists and belongs to user
        if (!$this->timeBlockModel->belongsToUser($id, $userId)) {
            $this->json(['success' => false, 'message' => 'Time block not found'], 404);
            return;
        }

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data) {
            $this->json(['success' => false, 'message' => 'Missing required data'], 400);
            return;
        }

        // Get the time block
        $timeBlock = $this->timeBlockModel->find($id);

        // Calculate duration in minutes
        $startTime = strtotime($timeBlock['start_time']);
        $endTime = strtotime($timeBlock['end_time']);
        $durationMinutes = ($endTime - $startTime) / 60;

        // Create new start time based on the data format
        $startDate = date('Y-m-d', $startTime);

        if (isset($data['hour']) && isset($data['minutes'])) {
            // New format with hour and minutes
            $newHour = isset($data['hour']) ? (int)$data['hour'] : 0;
            $newMinutes = isset($data['minutes']) ? (int)$data['minutes'] : 0;
            $newStartTime = date('Y-m-d H:i:s', strtotime("$startDate $newHour:$newMinutes:00"));
        } else if (isset($data['hour'])) {
            // Legacy format with just hour
            $newHour = (int)$data['hour'];
            $newStartTime = date('Y-m-d H:i:s', strtotime("$startDate $newHour:00:00"));
        } else {
            $this->json(['success' => false, 'message' => 'Invalid time format'], 400);
            return;
        }

        // Calculate new end time based on the duration
        $newEndTime = date('Y-m-d H:i:s', strtotime("$newStartTime + $durationMinutes minutes"));

        // Log for debugging
        error_log("Updating time block $id: Old start: {$timeBlock['start_time']}, New start: $newStartTime, New end: $newEndTime");

        // Check for conflicts with the new times
        $conflicts = $this->timeBlockModel->checkConflicts(
            $userId,
            $newStartTime,
            $newEndTime,
            $id
        );

        if (!empty($conflicts)) {
            $this->json([
                'success' => false,
                'message' => 'This update would conflict with another time block'
            ], 400);
            return;
        }

        // Update the time block
        $updateData = [
            'start_time' => $newStartTime,
            'end_time' => $newEndTime
        ];

        $result = $this->timeBlockModel->update($id, $updateData);

        if ($result) {
            $this->json([
                'success' => true,
                'message' => 'Time block updated successfully',
                'timeBlock' => [
                    'id' => $id,
                    'start_time' => $newStartTime,
                    'end_time' => $newEndTime
                ]
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to update time block'], 500);
        }
    }

    /**
     * Get today's time blocks for the dashboard widget
     */
    public function getTodayTimeBlocksWidget() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get today's time blocks
        $timeBlocks = $this->timeBlockModel->getTodayTimeBlocks($userId);

        // Return the time blocks as a partial view
        $this->view('productivity/widgets/today_time_blocks', [
            'timeBlocks' => $timeBlocks
        ]);
    }

    /**
     * Resize a time block via AJAX
     * Handles changing the duration of a time block
     */
    public function resizeTimeBlock($id) {
        $this->requireLogin();

        if (!$this->isAjax()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }

        $user = Session::getUser();
        $userId = $user['id'];

        // Check if time block exists and belongs to user
        if (!$this->timeBlockModel->belongsToUser($id, $userId)) {
            $this->json(['success' => false, 'message' => 'Time block not found'], 404);
            return;
        }

        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || !isset($data['duration'])) {
            $this->json(['success' => false, 'message' => 'Missing required data'], 400);
            return;
        }

        // Validate duration value
        if (!is_numeric($data['duration'])) {
            $this->json(['success' => false, 'message' => 'Duration must be a number'], 400);
            return;
        }

        $durationMinutes = (int)$data['duration'];

        // Log the received duration for debugging
        error_log("Received duration: " . $data['duration'] . " (converted to: $durationMinutes)");

        // Ensure duration is at least 15 minutes and at most 8 hours (480 minutes)
        if ($durationMinutes < 15) {
            error_log("Duration too small ($durationMinutes), setting to minimum (15)");
            $durationMinutes = 15;
        } else if ($durationMinutes > 480) {
            error_log("Duration too large ($durationMinutes), setting to maximum (480)");
            $durationMinutes = 480;
        }

        // Get the time block
        $timeBlock = $this->timeBlockModel->find($id);

        if (!$timeBlock) {
            $this->json(['success' => false, 'message' => 'Time block not found'], 404);
            return;
        }

        // Validate start time
        $startTime = strtotime($timeBlock['start_time']);
        if ($startTime === false) {
            $this->json(['success' => false, 'message' => 'Invalid start time format'], 400);
            return;
        }

        // Calculate new end time
        $newEndTime = date('Y-m-d H:i:s', strtotime("+{$durationMinutes} minutes", $startTime));

        // Validate end time calculation
        if (strtotime($newEndTime) === false) {
            $this->json(['success' => false, 'message' => 'Failed to calculate end time'], 500);
            return;
        }

        // Check for conflicts with the new end time
        $conflicts = $this->timeBlockModel->checkConflicts(
            $userId,
            $timeBlock['start_time'],
            $newEndTime,
            $id
        );

        // Log for debugging
        error_log("Resizing time block $id: Start time: {$timeBlock['start_time']}, New end time: $newEndTime, Duration: $durationMinutes minutes");

        if (!empty($conflicts)) {
            $this->json([
                'success' => false,
                'message' => 'This resize would conflict with another time block'
            ], 400);
            return;
        }

        // Update the time block
        $updateData = [
            'end_time' => $newEndTime
        ];

        $result = $this->timeBlockModel->update($id, $updateData);

        if ($result) {
            $this->json([
                'success' => true,
                'message' => 'Time block resized successfully',
                'timeBlock' => [
                    'id' => $id,
                    'start_time' => $timeBlock['start_time'],
                    'end_time' => $newEndTime,
                    'duration' => $durationMinutes
                ]
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'Failed to resize time block'], 500);
        }
    }
}
