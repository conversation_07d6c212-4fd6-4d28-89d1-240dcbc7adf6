<?php
/**
 * Freelance Report Controller
 *
 * Handles freelance reporting functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/FreelancePayment.php';
require_once __DIR__ . '/../models/FreelanceClient.php';
require_once __DIR__ . '/../models/FreelanceProject.php';
require_once __DIR__ . '/../models/FreelanceInvoice.php';

class FreelanceReportController extends BaseController {
    private $paymentModel;
    private $clientModel;
    private $projectModel;
    private $invoiceModel;

    public function __construct() {
        $this->paymentModel = new FreelancePayment();
        $this->clientModel = new FreelanceClient();
        $this->projectModel = new FreelanceProject();
        $this->invoiceModel = new FreelanceInvoice();
    }

    /**
     * Show income report
     */
    public function income() {
        $this->requireLogin();
        
        // This is a stub method - implementation will be added later
        $this->view('freelance/reports/income', []);
    }

    /**
     * Show clients report
     */
    public function clients() {
        $this->requireLogin();
        
        // This is a stub method - implementation will be added later
        $this->view('freelance/reports/clients', []);
    }

    /**
     * Show projects report
     */
    public function projects() {
        $this->requireLogin();
        
        // This is a stub method - implementation will be added later
        $this->view('freelance/reports/projects', []);
    }

    /**
     * Export report
     */
    public function export($type) {
        $this->requireLogin();
        
        // This is a stub method - implementation will be added later
        $this->redirect('/freelance/reports');
    }
}
