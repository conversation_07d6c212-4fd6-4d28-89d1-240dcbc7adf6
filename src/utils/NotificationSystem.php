<?php
/**
 * Notification System for ADHD Medication Reminders
 * 
 * Handles real-time notifications, browser notifications, and reminder management
 */

class NotificationSystem {
    private $db;
    private $medicationModel;
    
    public function __construct() {
        require_once __DIR__ . '/Database.php';
        require_once __DIR__ . '/../models/MedicationTracker.php';
        
        $this->db = Database::getInstance();
        $this->medicationModel = new MedicationTracker();
    }
    
    /**
     * Get pending medication reminders for a user
     */
    public function getPendingReminders($userId) {
        $currentTime = date('H:i:s');
        $today = date('Y-m-d');
        $dayOfWeek = date('N'); // 1 = Monday, 7 = Sunday
        
        $sql = "SELECT r.*, m.name, m.dosage, m.dosage_unit, m.instructions,
                TIMESTAMPDIFF(MINUTE, TIME(?), TIME(r.reminder_time)) as minutes_until,
                (SELECT COUNT(*) FROM medication_logs l
                 WHERE l.medication_id = m.id
                 AND l.log_date = ?
                 AND TIME(l.log_time) BETWEEN TIME(r.reminder_time) - INTERVAL 30 MINUTE
                 AND TIME(r.reminder_time) + INTERVAL 30 MINUTE) as taken_today
                FROM medication_reminders r
                JOIN medications m ON r.medication_id = m.id
                WHERE m.user_id = ?
                AND r.active = 1
                AND (r.days_of_week IS NULL OR r.days_of_week LIKE CONCAT('%', ?, '%'))
                AND TIME(r.reminder_time) BETWEEN TIME(?) AND TIME(DATE_ADD(?, INTERVAL 2 HOUR))
                HAVING taken_today = 0
                ORDER BY r.reminder_time ASC";
        
        return $this->db->fetchAll($sql, [$currentTime, $today, $userId, $dayOfWeek, $currentTime, $currentTime]);
    }
    
    /**
     * Get overdue medications for a user
     */
    public function getOverdueMedications($userId) {
        $currentTime = date('H:i:s');
        $today = date('Y-m-d');
        $dayOfWeek = date('N');
        
        $sql = "SELECT r.*, m.name, m.dosage, m.dosage_unit,
                TIMESTAMPDIFF(MINUTE, TIME(r.reminder_time), TIME(?)) as minutes_overdue,
                (SELECT COUNT(*) FROM medication_logs l
                 WHERE l.medication_id = m.id
                 AND l.log_date = ?
                 AND TIME(l.log_time) BETWEEN TIME(r.reminder_time) - INTERVAL 30 MINUTE
                 AND TIME(r.reminder_time) + INTERVAL 30 MINUTE) as taken_today
                FROM medication_reminders r
                JOIN medications m ON r.medication_id = m.id
                WHERE m.user_id = ?
                AND r.active = 1
                AND (r.days_of_week IS NULL OR r.days_of_week LIKE CONCAT('%', ?, '%'))
                AND TIME(r.reminder_time) < TIME(?)
                HAVING taken_today = 0 AND minutes_overdue > 15
                ORDER BY minutes_overdue DESC";
        
        return $this->db->fetchAll($sql, [$currentTime, $today, $userId, $dayOfWeek, $currentTime]);
    }
    
    /**
     * Create notification data for frontend
     */
    public function getNotificationData($userId) {
        $pending = $this->getPendingReminders($userId);
        $overdue = $this->getOverdueMedications($userId);
        
        $notifications = [];
        
        // Add pending reminders
        foreach ($pending as $reminder) {
            $notifications[] = [
                'id' => 'med_' . $reminder['id'],
                'type' => 'medication_reminder',
                'title' => 'Medication Reminder',
                'message' => "Time to take {$reminder['name']} ({$reminder['dosage']} {$reminder['dosage_unit']})",
                'time' => $reminder['reminder_time'],
                'minutes_until' => $reminder['minutes_until'],
                'medication_id' => $reminder['medication_id'],
                'reminder_id' => $reminder['id'],
                'priority' => $reminder['minutes_until'] <= 5 ? 'high' : 'normal',
                'actions' => [
                    ['label' => 'Mark as Taken', 'action' => 'mark_taken'],
                    ['label' => 'Snooze 15min', 'action' => 'snooze_15'],
                    ['label' => 'Skip', 'action' => 'skip']
                ]
            ];
        }
        
        // Add overdue medications
        foreach ($overdue as $reminder) {
            $notifications[] = [
                'id' => 'overdue_' . $reminder['id'],
                'type' => 'medication_overdue',
                'title' => 'Overdue Medication',
                'message' => "{$reminder['name']} is {$reminder['minutes_overdue']} minutes overdue",
                'time' => $reminder['reminder_time'],
                'minutes_overdue' => $reminder['minutes_overdue'],
                'medication_id' => $reminder['medication_id'],
                'reminder_id' => $reminder['id'],
                'priority' => 'urgent',
                'actions' => [
                    ['label' => 'Take Now', 'action' => 'mark_taken'],
                    ['label' => 'Skip Today', 'action' => 'skip_today']
                ]
            ];
        }
        
        return $notifications;
    }
    
    /**
     * Handle notification actions
     */
    public function handleAction($userId, $action, $data) {
        switch ($action) {
            case 'mark_taken':
                return $this->markMedicationTaken($userId, $data['medication_id'], $data['reminder_id']);
                
            case 'snooze_15':
                return $this->snoozeMedication($userId, $data['reminder_id'], 15);
                
            case 'skip':
            case 'skip_today':
                return $this->skipMedication($userId, $data['medication_id'], $data['reminder_id']);
                
            default:
                return ['success' => false, 'message' => 'Unknown action'];
        }
    }
    
    /**
     * Mark medication as taken
     */
    private function markMedicationTaken($userId, $medicationId, $reminderId) {
        // Verify medication belongs to user
        $medication = $this->medicationModel->getMedication($medicationId);
        if (!$medication || $medication['user_id'] != $userId) {
            return ['success' => false, 'message' => 'Medication not found'];
        }
        
        // Log the medication
        $logData = [
            'medication_id' => $medicationId,
            'log_date' => date('Y-m-d'),
            'log_time' => date('H:i:s'),
            'taken' => 1,
            'actual_dosage' => $medication['dosage'],
            'notes' => 'Logged via notification',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $result = $this->medicationModel->logMedication($logData);
        
        if ($result) {
            return ['success' => true, 'message' => 'Medication logged successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to log medication'];
        }
    }
    
    /**
     * Snooze medication reminder
     */
    private function snoozeMedication($userId, $reminderId, $minutes) {
        // For now, we'll just return success - in a full implementation,
        // this would create a temporary reminder or modify the existing one
        return ['success' => true, 'message' => "Reminder snoozed for {$minutes} minutes"];
    }
    
    /**
     * Skip medication for today
     */
    private function skipMedication($userId, $medicationId, $reminderId) {
        // Verify medication belongs to user
        $medication = $this->medicationModel->getMedication($medicationId);
        if (!$medication || $medication['user_id'] != $userId) {
            return ['success' => false, 'message' => 'Medication not found'];
        }
        
        // Log as skipped
        $logData = [
            'medication_id' => $medicationId,
            'log_date' => date('Y-m-d'),
            'log_time' => date('H:i:s'),
            'taken' => 0,
            'notes' => 'Skipped via notification',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $result = $this->medicationModel->logMedication($logData);
        
        if ($result) {
            return ['success' => true, 'message' => 'Medication marked as skipped'];
        } else {
            return ['success' => false, 'message' => 'Failed to log skip'];
        }
    }
    
    /**
     * Get notification statistics for dashboard
     */
    public function getNotificationStats($userId) {
        $today = date('Y-m-d');
        
        // Get today's medication schedule
        $todaySchedule = $this->medicationModel->getTodaySchedule($userId);
        
        $stats = [
            'total_medications' => count($todaySchedule),
            'taken' => 0,
            'pending' => 0,
            'overdue' => 0,
            'adherence_rate' => 0
        ];
        
        $currentTime = date('H:i:s');
        
        foreach ($todaySchedule as $med) {
            if ($med['taken'] > 0) {
                $stats['taken']++;
            } elseif (strtotime($med['reminder_time']) < strtotime($currentTime)) {
                $stats['overdue']++;
            } else {
                $stats['pending']++;
            }
        }
        
        if ($stats['total_medications'] > 0) {
            $stats['adherence_rate'] = round(($stats['taken'] / $stats['total_medications']) * 100);
        }
        
        return $stats;
    }
}
