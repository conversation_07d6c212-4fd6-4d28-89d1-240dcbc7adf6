<?php
/**
 * Cache Utility Class
 *
 * Provides methods for caching data in memory or file system.
 */

class Cache {
    private static $instance = null;
    private $memoryCache = [];
    private $cacheDir = '';
    private $enabled = true;

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct() {
        // Define base path if not already defined
        if (!defined('BASE_PATH')) {
            define('BASE_PATH', dirname(dirname(__DIR__)));
        }

        // Set cache directory
        $this->cacheDir = BASE_PATH . '/cache';

        // Create cache directory if it doesn't exist
        if (!file_exists($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }

        // Load app config
        $appConfigPath = __DIR__ . '/../config/app.php';
        if (file_exists($appConfigPath)) {
            $appConfig = require_once $appConfigPath;
            // Set enabled status from config
            $this->enabled = isset($appConfig['cache']['enabled']) ? $appConfig['cache']['enabled'] : true;
        } else {
            // Default value if config file doesn't exist
            $this->enabled = true;
        }
    }

    /**
     * Get cache instance (Singleton pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get a value from cache
     *
     * @param string $key Cache key
     * @param mixed $default Default value if key not found
     * @return mixed Cached value or default
     */
    public function get($key, $default = null) {
        if (!$this->enabled) {
            return $default;
        }

        // Check memory cache first
        if (isset($this->memoryCache[$key]) && $this->memoryCache[$key]['expires'] > time()) {
            return $this->memoryCache[$key]['value'];
        }

        // Check file cache
        $cacheFile = $this->getCacheFilePath($key);
        if (file_exists($cacheFile)) {
            $data = file_get_contents($cacheFile);
            $cacheData = unserialize($data);

            // Check if cache is still valid
            if ($cacheData && isset($cacheData['expires']) && $cacheData['expires'] > time()) {
                // Store in memory cache for faster access next time
                $this->memoryCache[$key] = $cacheData;
                return $cacheData['value'];
            }

            // Cache is expired, delete the file
            @unlink($cacheFile);
        }

        return $default;
    }

    /**
     * Store a value in cache
     *
     * @param string $key Cache key
     * @param mixed $value Value to cache
     * @param int $ttl Time to live in seconds
     * @return bool Success
     */
    public function set($key, $value, $ttl = 3600) {
        if (!$this->enabled) {
            return false;
        }

        $cacheData = [
            'value' => $value,
            'expires' => time() + $ttl
        ];

        // Store in memory cache
        $this->memoryCache[$key] = $cacheData;

        // Store in file cache
        $cacheFile = $this->getCacheFilePath($key);
        return file_put_contents($cacheFile, serialize($cacheData)) !== false;
    }

    /**
     * Check if a key exists in cache and is not expired
     *
     * @param string $key Cache key
     * @return bool True if key exists and is not expired
     */
    public function has($key) {
        if (!$this->enabled) {
            return false;
        }

        // Check memory cache first
        if (isset($this->memoryCache[$key]) && $this->memoryCache[$key]['expires'] > time()) {
            return true;
        }

        // Check file cache
        $cacheFile = $this->getCacheFilePath($key);
        if (file_exists($cacheFile)) {
            $data = file_get_contents($cacheFile);
            $cacheData = unserialize($data);

            // Check if cache is still valid
            if ($cacheData && isset($cacheData['expires']) && $cacheData['expires'] > time()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Remove a key from cache
     *
     * @param string $key Cache key
     * @return bool Success
     */
    public function forget($key) {
        // Remove from memory cache
        if (isset($this->memoryCache[$key])) {
            unset($this->memoryCache[$key]);
        }

        // Remove from file cache
        $cacheFile = $this->getCacheFilePath($key);
        if (file_exists($cacheFile)) {
            return @unlink($cacheFile);
        }

        return true;
    }

    /**
     * Clear all cache
     *
     * @return bool Success
     */
    public function clear() {
        // Clear memory cache
        $this->memoryCache = [];

        // Clear file cache
        $files = glob($this->cacheDir . '/*.cache');
        foreach ($files as $file) {
            @unlink($file);
        }

        return true;
    }

    /**
     * Get cache file path for a key
     *
     * @param string $key Cache key
     * @return string Cache file path
     */
    private function getCacheFilePath($key) {
        $safeKey = md5($key);
        return $this->cacheDir . '/' . $safeKey . '.cache';
    }

    /**
     * Remember a value in cache
     *
     * Gets a value from cache or executes the callback and stores the result
     *
     * @param string $key Cache key
     * @param int $ttl Time to live in seconds
     * @param callable $callback Function to execute if key not found
     * @return mixed Cached value or callback result
     */
    public function remember($key, $ttl, $callback) {
        // Check if value exists in cache
        $value = $this->get($key);

        // If not found, execute callback and store result
        if ($value === null) {
            $value = $callback();
            $this->set($key, $value, $ttl);
        }

        return $value;
    }
}
