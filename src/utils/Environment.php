<?php
/**
 * Environment Utility
 * 
 * Loads environment variables from .env file
 */

class Environment {
    private static $loaded = false;
    
    /**
     * Load environment variables from .env file
     */
    public static function load() {
        if (self::$loaded) {
            return;
        }
        
        $envFile = __DIR__ . '/../../.env';
        
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            
            foreach ($lines as $line) {
                // Skip comments
                if (strpos(trim($line), '#') === 0) {
                    continue;
                }
                
                // Parse line
                if (strpos($line, '=') !== false) {
                    list($name, $value) = explode('=', $line, 2);
                    $name = trim($name);
                    $value = trim($value);
                    
                    // Remove quotes if present
                    if (strpos($value, '"') === 0 && strrpos($value, '"') === strlen($value) - 1) {
                        $value = substr($value, 1, -1);
                    } else if (strpos($value, "'") === 0 && strrpos($value, "'") === strlen($value) - 1) {
                        $value = substr($value, 1, -1);
                    }
                    
                    // Set environment variable
                    putenv("{$name}={$value}");
                    
                    // Also set in $_ENV and $_SERVER for convenience
                    $_ENV[$name] = $value;
                    $_SERVER[$name] = $value;
                }
            }
        }
        
        self::$loaded = true;
    }
    
    /**
     * Get an environment variable
     * 
     * @param string $name Variable name
     * @param mixed $default Default value if not found
     * @return mixed
     */
    public static function get($name, $default = null) {
        self::load();
        
        $value = getenv($name);
        
        if ($value === false) {
            return $default;
        }
        
        return $value;
    }
}
