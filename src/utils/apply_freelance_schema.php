<?php
/**
 * Apply Freelance Management Schema
 *
 * This script applies the freelance management schema to the database.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', dirname(dirname(__DIR__)));

// Include the Database class
require_once BASE_PATH . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Check if freelance_clients table exists
$checkTable = $db->query("SHOW TABLES LIKE 'freelance_clients'");
$tableExists = $checkTable && $checkTable->rowCount() > 0;

if ($tableExists) {
    echo "freelance_clients table already exists.\n";
} else {
    echo "Creating freelance management tables...\n";

    // Read the schema file
    $schema = file_get_contents(BASE_PATH . '/database/freelance_management_schema.sql');

    // Split the schema into individual statements
    $statements = explode(';', $schema);

    // Execute each statement
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            $result = $db->query($statement);
            if ($result) {
                echo ".";
            } else {
                echo "x";
                echo "\nError: " . $db->error . "\n";
                echo "Statement: " . $statement . "\n";
            }
        }
    }

    echo "\nSchema applied successfully!\n";
}

// Check if tables exist
$tables = [
    'freelance_clients',
    'freelance_projects',
    'freelance_project_milestones',
    'freelance_invoices',
    'freelance_invoice_items',
    'freelance_payments',
    'freelance_time_entries'
];

echo "\nChecking tables:\n";
foreach ($tables as $table) {
    $checkTable = $db->query("SHOW TABLES LIKE '$table'");
    $exists = $checkTable && $checkTable->rowCount() > 0;
    echo "- $table: " . ($exists ? "EXISTS" : "MISSING") . "\n";
}

echo "\nDone!\n";
