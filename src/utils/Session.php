<?php
/**
 * Session Utility Class
 * 
 * Handles session management and user authentication.
 */

class Session {
    /**
     * Start the session if not already started
     */
    public static function start() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * Set a session variable
     */
    public static function set($key, $value) {
        self::start();
        $_SESSION[$key] = $value;
    }
    
    /**
     * Get a session variable
     */
    public static function get($key, $default = null) {
        self::start();
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }
    
    /**
     * Check if a session variable exists
     */
    public static function has($key) {
        self::start();
        return isset($_SESSION[$key]);
    }
    
    /**
     * Remove a session variable
     */
    public static function remove($key) {
        self::start();
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
        }
    }
    
    /**
     * Destroy the session
     */
    public static function destroy() {
        self::start();
        session_unset();
        session_destroy();
    }
    
    /**
     * Set a flash message
     */
    public static function setFlash($key, $message) {
        self::start();
        $_SESSION['flash'][$key] = $message;
    }
    
    /**
     * Get a flash message and remove it
     */
    public static function getFlash($key, $default = null) {
        self::start();
        $message = isset($_SESSION['flash'][$key]) ? $_SESSION['flash'][$key] : $default;
        
        if (isset($_SESSION['flash'][$key])) {
            unset($_SESSION['flash'][$key]);
        }
        
        return $message;
    }
    
    /**
     * Check if a flash message exists
     */
    public static function hasFlash($key) {
        self::start();
        return isset($_SESSION['flash'][$key]);
    }
    
    /**
     * Set user as logged in
     */
    public static function setUser($user) {
        // Remove password before storing in session
        if (isset($user['password'])) {
            unset($user['password']);
        }
        
        self::set('user', $user);
        self::set('logged_in', true);
    }
    
    /**
     * Get the logged in user
     */
    public static function getUser() {
        return self::get('user');
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        return self::get('logged_in', false);
    }
    
    /**
     * Log out the user
     */
    public static function logout() {
        self::remove('user');
        self::remove('logged_in');
    }
    
    /**
     * Regenerate session ID
     */
    public static function regenerate() {
        self::start();
        session_regenerate_id(true);
    }
}
