<?php
/**
 * Date Utility
 * 
 * Provides utility functions for date manipulation and formatting
 */

class DateUtil {
    /**
     * Get the first day of the current month
     * 
     * @param string|null $date Optional date string to use instead of current date
     * @return string Date in Y-m-d format
     */
    public static function getFirstDayOfMonth($date = null) {
        if ($date) {
            return date('Y-m-01', strtotime($date));
        }
        return date('Y-m-01');
    }
    
    /**
     * Get the last day of the current month
     * 
     * @param string|null $date Optional date string to use instead of current date
     * @return string Date in Y-m-d format
     */
    public static function getLastDayOfMonth($date = null) {
        if ($date) {
            return date('Y-m-t', strtotime($date));
        }
        return date('Y-m-t');
    }
    
    /**
     * Get the first day of the current year
     * 
     * @param string|null $date Optional date string to use instead of current date
     * @return string Date in Y-m-d format
     */
    public static function getFirstDayOfYear($date = null) {
        if ($date) {
            return date('Y-01-01', strtotime($date));
        }
        return date('Y-01-01');
    }
    
    /**
     * Get the last day of the current year
     * 
     * @param string|null $date Optional date string to use instead of current date
     * @return string Date in Y-m-d format
     */
    public static function getLastDayOfYear($date = null) {
        if ($date) {
            return date('Y-12-31', strtotime($date));
        }
        return date('Y-12-31');
    }
    
    /**
     * Get the first day of the week (Monday)
     * 
     * @param string|null $date Optional date string to use instead of current date
     * @return string Date in Y-m-d format
     */
    public static function getFirstDayOfWeek($date = null) {
        if ($date) {
            return date('Y-m-d', strtotime('monday this week', strtotime($date)));
        }
        return date('Y-m-d', strtotime('monday this week'));
    }
    
    /**
     * Get the last day of the week (Sunday)
     * 
     * @param string|null $date Optional date string to use instead of current date
     * @return string Date in Y-m-d format
     */
    public static function getLastDayOfWeek($date = null) {
        if ($date) {
            return date('Y-m-d', strtotime('sunday this week', strtotime($date)));
        }
        return date('Y-m-d', strtotime('sunday this week'));
    }
    
    /**
     * Get the first day of the quarter
     * 
     * @param string|null $date Optional date string to use instead of current date
     * @return string Date in Y-m-d format
     */
    public static function getFirstDayOfQuarter($date = null) {
        $timestamp = $date ? strtotime($date) : time();
        $month = date('n', $timestamp);
        $quarter = ceil($month / 3);
        $firstMonthOfQuarter = ($quarter - 1) * 3 + 1;
        return date('Y', $timestamp) . '-' . str_pad($firstMonthOfQuarter, 2, '0', STR_PAD_LEFT) . '-01';
    }
    
    /**
     * Get the last day of the quarter
     * 
     * @param string|null $date Optional date string to use instead of current date
     * @return string Date in Y-m-d format
     */
    public static function getLastDayOfQuarter($date = null) {
        $timestamp = $date ? strtotime($date) : time();
        $month = date('n', $timestamp);
        $quarter = ceil($month / 3);
        $lastMonthOfQuarter = $quarter * 3;
        return date('Y-m-t', strtotime(date('Y', $timestamp) . '-' . str_pad($lastMonthOfQuarter, 2, '0', STR_PAD_LEFT) . '-01'));
    }
    
    /**
     * Get date range for a specific period
     * 
     * @param string $period Period type (daily, weekly, monthly, quarterly, yearly)
     * @param string|null $date Optional date string to use instead of current date
     * @return array Associative array with start_date and end_date
     */
    public static function getDateRangeForPeriod($period, $date = null) {
        $range = [
            'start_date' => null,
            'end_date' => null
        ];
        
        switch ($period) {
            case 'daily':
                $range['start_date'] = $date ?? date('Y-m-d');
                $range['end_date'] = $date ?? date('Y-m-d');
                break;
                
            case 'weekly':
                $range['start_date'] = self::getFirstDayOfWeek($date);
                $range['end_date'] = self::getLastDayOfWeek($date);
                break;
                
            case 'monthly':
                $range['start_date'] = self::getFirstDayOfMonth($date);
                $range['end_date'] = self::getLastDayOfMonth($date);
                break;
                
            case 'quarterly':
                $range['start_date'] = self::getFirstDayOfQuarter($date);
                $range['end_date'] = self::getLastDayOfQuarter($date);
                break;
                
            case 'yearly':
                $range['start_date'] = self::getFirstDayOfYear($date);
                $range['end_date'] = self::getLastDayOfYear($date);
                break;
                
            default:
                $range['start_date'] = date('Y-m-d');
                $range['end_date'] = date('Y-m-d');
        }
        
        return $range;
    }
    
    /**
     * Format a date
     * 
     * @param string $date Date string
     * @param string $format Format string (default: Y-m-d)
     * @return string Formatted date
     */
    public static function formatDate($date, $format = 'Y-m-d') {
        if (!$date) {
            return '';
        }
        
        return date($format, strtotime($date));
    }
    
    /**
     * Get the difference between two dates in days
     * 
     * @param string $date1 First date
     * @param string $date2 Second date
     * @return int Number of days
     */
    public static function getDaysDifference($date1, $date2) {
        $timestamp1 = strtotime($date1);
        $timestamp2 = strtotime($date2);
        
        $diff = abs($timestamp2 - $timestamp1);
        return floor($diff / (60 * 60 * 24));
    }
    
    /**
     * Check if a date is in the past
     * 
     * @param string $date Date to check
     * @return bool True if date is in the past
     */
    public static function isPast($date) {
        return strtotime($date) < strtotime(date('Y-m-d'));
    }
    
    /**
     * Check if a date is in the future
     * 
     * @param string $date Date to check
     * @return bool True if date is in the future
     */
    public static function isFuture($date) {
        return strtotime($date) > strtotime(date('Y-m-d'));
    }
    
    /**
     * Check if a date is today
     * 
     * @param string $date Date to check
     * @return bool True if date is today
     */
    public static function isToday($date) {
        return date('Y-m-d', strtotime($date)) === date('Y-m-d');
    }
    
    /**
     * Add days to a date
     * 
     * @param string $date Base date
     * @param int $days Number of days to add
     * @return string New date
     */
    public static function addDays($date, $days) {
        return date('Y-m-d', strtotime($date . " +{$days} days"));
    }
    
    /**
     * Subtract days from a date
     * 
     * @param string $date Base date
     * @param int $days Number of days to subtract
     * @return string New date
     */
    public static function subtractDays($date, $days) {
        return date('Y-m-d', strtotime($date . " -{$days} days"));
    }
    
    /**
     * Get an array of months
     * 
     * @return array Array of month names
     */
    public static function getMonths() {
        return [
            1 => 'January',
            2 => 'February',
            3 => 'March',
            4 => 'April',
            5 => 'May',
            6 => 'June',
            7 => 'July',
            8 => 'August',
            9 => 'September',
            10 => 'October',
            11 => 'November',
            12 => 'December'
        ];
    }
    
    /**
     * Get an array of quarters
     * 
     * @return array Array of quarter names
     */
    public static function getQuarters() {
        return [
            1 => 'Q1 (Jan-Mar)',
            2 => 'Q2 (Apr-Jun)',
            3 => 'Q3 (Jul-Sep)',
            4 => 'Q4 (Oct-Dec)'
        ];
    }
    
    /**
     * Get the current quarter
     * 
     * @return int Current quarter (1-4)
     */
    public static function getCurrentQuarter() {
        return ceil(date('n') / 3);
    }
}
