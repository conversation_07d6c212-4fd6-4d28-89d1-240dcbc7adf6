<?php
/**
 * Router Class
 *
 * Handles routing of requests to appropriate controllers.
 */

class Router {
    private $routes = [];
    private $notFoundCallback;

    /**
     * Add a route
     */
    public function add($method, $path, $callback) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'callback' => $callback
        ];

        return $this;
    }

    /**
     * Add a GET route
     */
    public function get($path, $callback) {
        return $this->add('GET', $path, $callback);
    }

    /**
     * Add a POST route
     */
    public function post($path, $callback) {
        return $this->add('POST', $path, $callback);
    }

    /**
     * Add a PUT route
     */
    public function put($path, $callback) {
        return $this->add('PUT', $path, $callback);
    }

    /**
     * Add a DELETE route
     */
    public function delete($path, $callback) {
        return $this->add('DELETE', $path, $callback);
    }

    /**
     * Set the 404 not found callback
     */
    public function notFound($callback) {
        $this->notFoundCallback = $callback;
        return $this;
    }

    /**
     * Match the current request to a route
     */
    private function match($method, $path) {
        foreach ($this->routes as $route) {
            // Check if method matches
            if ($route['method'] !== $method) {
                continue;
            }

            // Convert route path to regex pattern
            $pattern = $this->pathToPattern($route['path']);

            // Check if path matches pattern
            if (preg_match($pattern, $path, $matches)) {
                // Remove the full match
                array_shift($matches);

                return [
                    'callback' => $route['callback'],
                    'params' => $matches
                ];
            }
        }

        return false;
    }

    /**
     * Convert a route path to a regex pattern
     */
    private function pathToPattern($path) {
        // Escape forward slashes
        $path = str_replace('/', '\/', $path);

        // Convert parameters (e.g., :id) to regex groups
        $path = preg_replace('/\:([a-zA-Z0-9_]+)/', '([^\/]+)', $path);

        // Add start and end anchors
        return '/^' . $path . '$/';
    }

    /**
     * Dispatch the request to the appropriate route
     */
    public function dispatch() {
        // Get the request method and path
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

        // Handle subdirectory
        $basePath = '/momentum';
        if (strpos($path, $basePath) === 0) {
            $path = substr($path, strlen($basePath));
        }

        // Remove trailing slash
        $path = rtrim($path, '/');

        // Add leading slash if not present
        if (empty($path)) {
            $path = '/';
        }

        // Match the request to a route
        $route = $this->match($method, $path);

        if ($route) {
            // Call the route callback with parameters
            call_user_func_array($route['callback'], $route['params']);
        } else if ($this->notFoundCallback) {
            // Call the 404 not found callback
            call_user_func($this->notFoundCallback);
        } else {
            // Default 404 response
            header('HTTP/1.1 404 Not Found');
            echo '404 Not Found';
        }
    }
}
