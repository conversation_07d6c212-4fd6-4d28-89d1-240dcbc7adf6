<?php
/**
 * Trigger-Symptom Integration System
 * 
 * Integrates trigger identification with symptom tracking for comprehensive analysis
 */

class TriggerSymptomIntegrator {
    private $db;
    
    public function __construct() {
        require_once __DIR__ . '/Database.php';
        $this->db = Database::getInstance();
    }
    
    /**
     * Get comprehensive trigger-symptom analysis
     */
    public function getIntegratedAnalysis($userId, $days = 30) {
        $triggers = $this->getTriggerData($userId, $days);
        $symptoms = $this->getSymptomData($userId, $days);
        $medications = $this->getMedicationData($userId, $days);
        
        return [
            'correlation_matrix' => $this->buildCorrelationMatrix($triggers, $symptoms),
            'timeline_analysis' => $this->analyzeTimeline($triggers, $symptoms, $medications),
            'predictive_insights' => $this->generatePredictiveInsights($triggers, $symptoms),
            'intervention_suggestions' => $this->suggestInterventions($triggers, $symptoms, $medications),
            'risk_assessment' => $this->assessRisk($triggers, $symptoms),
            'summary_stats' => $this->calculateSummaryStats($triggers, $symptoms, $medications)
        ];
    }
    
    /**
     * Build correlation matrix between triggers and symptoms
     */
    private function buildCorrelationMatrix($triggers, $symptoms) {
        $matrix = [];
        $triggerTypes = array_unique(array_column($triggers, 'name'));
        $symptomTypes = array_unique(array_column($symptoms, 'symptom_name'));
        
        foreach ($triggerTypes as $trigger) {
            $matrix[$trigger] = [];
            
            foreach ($symptomTypes as $symptom) {
                $correlation = $this->calculateTriggerSymptomCorrelation($trigger, $symptom, $triggers, $symptoms);
                $matrix[$trigger][$symptom] = $correlation;
            }
        }
        
        return $matrix;
    }
    
    /**
     * Calculate correlation between specific trigger and symptom
     */
    private function calculateTriggerSymptomCorrelation($triggerName, $symptomName, $triggers, $symptoms) {
        $triggerDates = [];
        $symptomSeverities = [];
        
        // Get dates when trigger occurred
        foreach ($triggers as $trigger) {
            if ($trigger['name'] === $triggerName) {
                $triggerDates[] = $trigger['occurrence_date'];
            }
        }
        
        // Get symptom severities on trigger days vs non-trigger days
        $triggerDaySeverities = [];
        $nonTriggerDaySeverities = [];
        
        foreach ($symptoms as $symptom) {
            if ($symptom['symptom_name'] === $symptomName) {
                if (in_array($symptom['log_date'], $triggerDates)) {
                    $triggerDaySeverities[] = $symptom['severity'];
                } else {
                    $nonTriggerDaySeverities[] = $symptom['severity'];
                }
            }
        }
        
        if (empty($triggerDaySeverities) || empty($nonTriggerDaySeverities)) {
            return [
                'correlation' => 0,
                'significance' => 'insufficient_data',
                'trigger_day_avg' => 0,
                'non_trigger_day_avg' => 0,
                'sample_size' => 0
            ];
        }
        
        $triggerAvg = array_sum($triggerDaySeverities) / count($triggerDaySeverities);
        $nonTriggerAvg = array_sum($nonTriggerDaySeverities) / count($nonTriggerDaySeverities);
        
        $difference = $triggerAvg - $nonTriggerAvg;
        $correlation = round($difference, 2);
        
        // Determine significance
        $sampleSize = min(count($triggerDaySeverities), count($nonTriggerDaySeverities));
        $significance = $this->determineSignificance($correlation, $sampleSize);
        
        return [
            'correlation' => $correlation,
            'significance' => $significance,
            'trigger_day_avg' => round($triggerAvg, 1),
            'non_trigger_day_avg' => round($nonTriggerAvg, 1),
            'sample_size' => $sampleSize
        ];
    }
    
    /**
     * Analyze timeline of triggers, symptoms, and medications
     */
    private function analyzeTimeline($triggers, $symptoms, $medications) {
        $timeline = [];
        
        // Combine all events by date
        $eventsByDate = [];
        
        // Add triggers
        foreach ($triggers as $trigger) {
            $date = $trigger['occurrence_date'];
            if (!isset($eventsByDate[$date])) {
                $eventsByDate[$date] = ['triggers' => [], 'symptoms' => [], 'medications' => []];
            }
            $eventsByDate[$date]['triggers'][] = $trigger;
        }
        
        // Add symptoms
        foreach ($symptoms as $symptom) {
            $date = $symptom['log_date'];
            if (!isset($eventsByDate[$date])) {
                $eventsByDate[$date] = ['triggers' => [], 'symptoms' => [], 'medications' => []];
            }
            $eventsByDate[$date]['symptoms'][] = $symptom;
        }
        
        // Add medications
        foreach ($medications as $medication) {
            $date = $medication['log_date'];
            if (!isset($eventsByDate[$date])) {
                $eventsByDate[$date] = ['triggers' => [], 'symptoms' => [], 'medications' => []];
            }
            $eventsByDate[$date]['medications'][] = $medication;
        }
        
        // Analyze patterns
        foreach ($eventsByDate as $date => $events) {
            $dayAnalysis = [
                'date' => $date,
                'trigger_count' => count($events['triggers']),
                'avg_symptom_severity' => $this->calculateAvgSeverity($events['symptoms']),
                'medication_adherence' => $this->calculateDayAdherence($events['medications']),
                'risk_level' => $this->calculateDayRisk($events),
                'events' => $events
            ];
            
            $timeline[] = $dayAnalysis;
        }
        
        // Sort by date
        usort($timeline, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });
        
        return array_slice($timeline, 0, 30); // Last 30 days
    }
    
    /**
     * Generate predictive insights
     */
    private function generatePredictiveInsights($triggers, $symptoms) {
        $insights = [];
        
        // Analyze trigger-to-symptom delay patterns
        $delayPatterns = $this->analyzeDelayPatterns($triggers, $symptoms);
        
        if (!empty($delayPatterns)) {
            $insights[] = [
                'type' => 'delay_pattern',
                'title' => 'Symptom Onset Prediction',
                'description' => 'Based on your patterns, symptoms typically worsen ' . 
                               $delayPatterns['average_delay'] . ' hours after trigger exposure.',
                'confidence' => $delayPatterns['confidence'],
                'actionable' => true,
                'action' => 'Use preventive strategies within ' . $delayPatterns['average_delay'] . ' hours of trigger exposure.'
            ];
        }
        
        // Analyze trigger accumulation effects
        $accumulationEffects = $this->analyzeAccumulationEffects($triggers, $symptoms);
        
        if ($accumulationEffects['significant']) {
            $insights[] = [
                'type' => 'accumulation',
                'title' => 'Trigger Accumulation Effect',
                'description' => 'Multiple triggers in a day increase symptom severity by an average of ' . 
                               $accumulationEffects['severity_increase'] . ' points.',
                'confidence' => $accumulationEffects['confidence'],
                'actionable' => true,
                'action' => 'Focus on preventing trigger combinations, especially on high-stress days.'
            ];
        }
        
        // Analyze recovery patterns
        $recoveryPatterns = $this->analyzeRecoveryPatterns($triggers, $symptoms);
        
        if (!empty($recoveryPatterns)) {
            $insights[] = [
                'type' => 'recovery',
                'title' => 'Recovery Time Prediction',
                'description' => 'You typically recover from trigger-induced symptoms within ' . 
                               $recoveryPatterns['average_recovery'] . ' hours.',
                'confidence' => $recoveryPatterns['confidence'],
                'actionable' => false,
                'action' => null
            ];
        }
        
        return $insights;
    }
    
    /**
     * Suggest targeted interventions
     */
    private function suggestInterventions($triggers, $symptoms, $medications) {
        $interventions = [];
        
        // Analyze most impactful triggers
        $triggerImpacts = $this->analyzeTriggerImpacts($triggers, $symptoms);
        
        foreach ($triggerImpacts as $trigger => $impact) {
            if ($impact['severity_increase'] > 2) {
                $interventions[] = [
                    'type' => 'trigger_specific',
                    'priority' => $impact['severity_increase'] > 4 ? 'high' : 'medium',
                    'target' => $trigger,
                    'title' => "Address High-Impact Trigger: {$trigger}",
                    'description' => "This trigger increases symptom severity by {$impact['severity_increase']} points on average.",
                    'strategies' => $this->getTriggerSpecificStrategies($trigger),
                    'expected_benefit' => "Reducing this trigger could improve symptoms by up to {$impact['severity_increase']} points."
                ];
            }
        }
        
        // Analyze medication timing optimization
        $timingOptimization = $this->analyzeMedicationTiming($medications, $symptoms);
        
        if ($timingOptimization['optimization_potential'] > 0) {
            $interventions[] = [
                'type' => 'medication_timing',
                'priority' => 'medium',
                'target' => 'medication_schedule',
                'title' => 'Optimize Medication Timing',
                'description' => 'Adjusting medication timing could improve symptom management.',
                'strategies' => $timingOptimization['suggestions'],
                'expected_benefit' => "Potential symptom improvement of {$timingOptimization['optimization_potential']} points."
            ];
        }
        
        // Suggest preventive measures
        $preventiveMeasures = $this->suggestPreventiveMeasures($triggers, $symptoms);
        
        $interventions = array_merge($interventions, $preventiveMeasures);
        
        return $interventions;
    }
    
    /**
     * Assess overall risk level
     */
    private function assessRisk($triggers, $symptoms) {
        $riskFactors = [];
        $riskScore = 0;
        
        // High-frequency triggers
        $triggerFrequency = $this->calculateTriggerFrequency($triggers);
        if ($triggerFrequency > 0.5) { // More than 0.5 triggers per day
            $riskFactors[] = 'High trigger frequency';
            $riskScore += 3;
        }
        
        // High symptom severity
        $avgSeverity = $this->calculateAvgSeverity($symptoms);
        if ($avgSeverity > 7) {
            $riskFactors[] = 'High average symptom severity';
            $riskScore += 4;
        } elseif ($avgSeverity > 5) {
            $riskFactors[] = 'Moderate symptom severity';
            $riskScore += 2;
        }
        
        // Increasing trend
        $trend = $this->calculateSymptomTrend($symptoms);
        if ($trend > 0.5) {
            $riskFactors[] = 'Worsening symptom trend';
            $riskScore += 3;
        }
        
        // Determine risk level
        $riskLevel = 'low';
        if ($riskScore >= 7) {
            $riskLevel = 'high';
        } elseif ($riskScore >= 4) {
            $riskLevel = 'medium';
        }
        
        return [
            'level' => $riskLevel,
            'score' => $riskScore,
            'factors' => $riskFactors,
            'recommendations' => $this->getRiskRecommendations($riskLevel, $riskFactors)
        ];
    }
    
    /**
     * Helper methods for data retrieval
     */
    private function getTriggerData($userId, $days) {
        $sql = "SELECT t.name, t.category, o.occurrence_date, o.occurrence_time, o.severity_level
                FROM adhd_triggers t
                JOIN trigger_occurrences o ON t.id = o.trigger_id
                WHERE t.user_id = ? AND o.occurrence_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY o.occurrence_date DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    private function getSymptomData($userId, $days) {
        $sql = "SELECT log_date, log_time, symptom_name, severity
                FROM adhd_symptom_logs
                WHERE user_id = ? AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY log_date DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    private function getMedicationData($userId, $days) {
        $sql = "SELECT l.log_date, l.log_time, l.taken, m.name
                FROM medication_logs l
                JOIN medications m ON l.medication_id = m.id
                WHERE m.user_id = ? AND l.log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY l.log_date DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Helper calculation methods
     */
    private function calculateAvgSeverity($symptoms) {
        if (empty($symptoms)) return 0;
        
        $severities = array_column($symptoms, 'severity');
        return round(array_sum($severities) / count($severities), 1);
    }
    
    private function calculateDayAdherence($medications) {
        if (empty($medications)) return 0;
        
        $taken = count(array_filter($medications, function($med) {
            return $med['taken'] == 1;
        }));
        
        return round(($taken / count($medications)) * 100, 1);
    }
    
    private function calculateDayRisk($events) {
        $risk = 0;
        
        // More triggers = higher risk
        $risk += count($events['triggers']) * 2;
        
        // Higher symptom severity = higher risk
        $avgSeverity = $this->calculateAvgSeverity($events['symptoms']);
        $risk += $avgSeverity;
        
        // Lower medication adherence = higher risk
        $adherence = $this->calculateDayAdherence($events['medications']);
        $risk += (100 - $adherence) / 10;
        
        // Normalize to 1-10 scale
        return min(10, max(1, round($risk)));
    }
    
    private function determineSignificance($correlation, $sampleSize) {
        if ($sampleSize < 5) return 'insufficient_data';
        if (abs($correlation) > 2 && $sampleSize >= 10) return 'high';
        if (abs($correlation) > 1 && $sampleSize >= 7) return 'medium';
        if (abs($correlation) > 0.5) return 'low';
        return 'none';
    }
    
    // Additional helper methods would be implemented here...
    // (Truncated for brevity - these would include the remaining analysis methods)
}
