<?php
/**
 * View Utility Class
 *
 * Handles rendering of views.
 */

require_once __DIR__ . '/AssetManager.php';

class View {
    private static $viewPath = __DIR__ . '/../views/';

    public static function render($view, $data = []) {
        // Extract data to make it available in the view
        extract($data);

        // Start output buffering
        ob_start();

        // Include the view file
        include self::$viewPath . $view . '.php';

        // Return the content
        return ob_get_clean();
    }

    public static function renderWithLayout($view, $layout, $data = []) {
        // Get the content of the view
        $content = self::render($view, $data);

        // Extract data again for the layout
        extract($data);

        // Add content to data for the layout
        $data['content'] = $content;

        // Start output buffering for the layout
        ob_start();

        // Include the layout file
        include self::$viewPath . 'layouts/' . $layout . '.php';

        // Return the complete page
        return ob_get_clean();
    }

    public static function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Format a date
     */
    public static function formatDate($date, $format = 'M j, Y') {
        if (!$date) {
            return '';
        }

        $timestamp = is_numeric($date) ? $date : strtotime($date);
        return date($format, $timestamp);
    }

    /**
     * Format a time
     */
    public static function formatTime($time, $format = 'g:i A') {
        if (!$time) {
            return '';
        }

        $timestamp = is_numeric($time) ? $time : strtotime($time);
        return date($format, $timestamp);
    }

    /**
     * Format a datetime
     */
    public static function formatDateTime($datetime, $format = 'M j, Y g:i A') {
        if (!$datetime) {
            return '';
        }

        $timestamp = is_numeric($datetime) ? $datetime : strtotime($datetime);
        return date($format, $timestamp);
    }

    /**
     * Format a currency amount
     */
    public static function formatCurrency($amount, $symbol = 'Rs ') {
        $amount = is_null($amount) ? 0 : (float)$amount;
        return $symbol . number_format($amount, 2);
    }

    /**
     * Get asset URL with version
     */
    public static function asset($path) {
        $assetManager = AssetManager::getInstance();
        return $assetManager->getAssetUrl($path);
    }

    /**
     * Get CSS tag with versioned URL
     */
    public static function css($path) {
        $assetManager = AssetManager::getInstance();
        return $assetManager->css($path);
    }

    /**
     * Get JavaScript tag with versioned URL
     */
    public static function js($path, $defer = true) {
        $assetManager = AssetManager::getInstance();
        return $assetManager->js($path, $defer);
    }

    /**
     * Get image tag with versioned URL
     */
    public static function img($path, $alt = '', $class = '', $attributes = []) {
        $assetManager = AssetManager::getInstance();
        return $assetManager->img($path, $alt, $class, $attributes);
    }
}





