<?php
/**
 * PDF Exporter
 *
 * Provides functionality for exporting data as HTML that can be printed as PDF
 */

class PDFExporter {
    /**
     * Generate printable HTML that can be saved as PDF
     *
     * @param string $html HTML content
     * @param string $filename Filename without extension
     */
    public static function generatePDF($html, $filename) {
        // Set headers for HTML
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: inline; filename="' . $filename . '.html"');

        // Add print instructions and styling
        echo '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>' . htmlspecialchars($filename) . '</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                .print-instructions {
                    background-color: #f8f9fa;
                    border: 1px solid #ddd;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 5px;
                }
                @media print {
                    .print-instructions {
                        display: none;
                    }
                    body {
                        padding: 0;
                    }
                }
                .btn-print {
                    background-color: #4f46e5;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                    margin-right: 10px;
                }
                .btn-print:hover {
                    background-color: #4338ca;
                }
                .btn-back {
                    background-color: #6b7280;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                    text-decoration: none;
                    display: inline-block;
                }
                .btn-back:hover {
                    background-color: #4b5563;
                }
            </style>
            <script>
                function printDocument() {
                    window.print();
                }
            </script>
        </head>
        <body>
            <div class="print-instructions">
                <h2>Print as PDF</h2>
                <p>To save this report as a PDF:</p>
                <ol>
                    <li>Click the "Print as PDF" button below, or press <strong>Ctrl+P</strong> (Windows/Linux) or <strong>⌘+P</strong> (Mac)</li>
                    <li>In the print dialog, select "Save as PDF" or "Microsoft Print to PDF" as the destination</li>
                    <li>Click "Save" or "Print"</li>
                </ol>
                <p>This instruction section will not appear in the printed document.</p>
                <div style="margin-top: 15px;">
                    <button class="btn-print" onclick="printDocument()">Print as PDF</button>
                    <a class="btn-back" href="javascript:history.back()">Back to Report</a>
                </div>
            </div>
            ' . $html . '
        </body>
        </html>';

        exit;
    }

    /**
     * Generate project progress report PDF
     *
     * @param array $project Project data
     * @param array $tasks Task data
     * @param array $taskStatusCounts Task status counts
     * @param array $taskPriorityCounts Task priority counts
     * @param array $taskCategoryCounts Task category counts
     * @return string HTML content for PDF
     */
    public static function generateProjectProgressHTML($project, $tasks, $taskStatusCounts, $taskPriorityCounts, $taskCategoryCounts) {
        // Calculate completion percentage
        $totalTasks = count($tasks);
        $completedTasks = $taskStatusCounts['done'] ?? 0;
        $completionPercentage = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0;

        // Generate HTML
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Project Progress Report</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                h1 {
                    font-size: 24px;
                    margin: 0 0 5px 0;
                }
                h2 {
                    font-size: 18px;
                    margin: 0 0 20px 0;
                    font-weight: normal;
                    color: #666;
                }
                .section {
                    margin-bottom: 30px;
                }
                .section-title {
                    font-size: 18px;
                    margin-bottom: 10px;
                    padding-bottom: 5px;
                    border-bottom: 1px solid #ddd;
                }
                .progress-bar {
                    background-color: #f0f0f0;
                    height: 20px;
                    border-radius: 10px;
                    margin-bottom: 10px;
                    overflow: hidden;
                }
                .progress-bar-fill {
                    background-color: #4f46e5;
                    height: 100%;
                }
                .stats-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                }
                .stat-box {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 15px;
                }
                .stat-title {
                    font-size: 14px;
                    color: #666;
                    margin-bottom: 5px;
                }
                .stat-value {
                    font-size: 24px;
                    font-weight: bold;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                table, th, td {
                    border: 1px solid #ddd;
                }
                th, td {
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Project Progress Report</h1>
                <h2>' . htmlspecialchars($project['name']) . '</h2>
                <p>Generated on ' . date('F j, Y, g:i a') . '</p>
            </div>

            <div class="section">
                <div class="section-title">Project Overview</div>
                <div class="stats-grid">
                    <div class="stat-box">
                        <div class="stat-title">Status</div>
                        <div class="stat-value">' . ucfirst($project['status']) . '</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-title">Progress</div>
                        <div class="progress-bar">
                            <div class="progress-bar-fill" style="width: ' . $project['progress'] . '%;"></div>
                        </div>
                        <div class="stat-value">' . $project['progress'] . '% Complete</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">Task Status Distribution</div>
                <table>
                    <tr>
                        <th>Status</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                    <tr>
                        <td>To Do</td>
                        <td>' . ($taskStatusCounts['todo'] ?? 0) . '</td>
                        <td>' . ($totalTasks > 0 ? round((($taskStatusCounts['todo'] ?? 0) / $totalTasks) * 100) : 0) . '%</td>
                    </tr>
                    <tr>
                        <td>In Progress</td>
                        <td>' . ($taskStatusCounts['in_progress'] ?? 0) . '</td>
                        <td>' . ($totalTasks > 0 ? round((($taskStatusCounts['in_progress'] ?? 0) / $totalTasks) * 100) : 0) . '%</td>
                    </tr>
                    <tr>
                        <td>Done</td>
                        <td>' . ($taskStatusCounts['done'] ?? 0) . '</td>
                        <td>' . ($totalTasks > 0 ? round((($taskStatusCounts['done'] ?? 0) / $totalTasks) * 100) : 0) . '%</td>
                    </tr>
                </table>
            </div>

            <div class="section">
                <div class="section-title">Task Priority Distribution</div>
                <table>
                    <tr>
                        <th>Priority</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                    <tr>
                        <td>Low</td>
                        <td>' . ($taskPriorityCounts['low'] ?? 0) . '</td>
                        <td>' . ($totalTasks > 0 ? round((($taskPriorityCounts['low'] ?? 0) / $totalTasks) * 100) : 0) . '%</td>
                    </tr>
                    <tr>
                        <td>Medium</td>
                        <td>' . ($taskPriorityCounts['medium'] ?? 0) . '</td>
                        <td>' . ($totalTasks > 0 ? round((($taskPriorityCounts['medium'] ?? 0) / $totalTasks) * 100) : 0) . '%</td>
                    </tr>
                    <tr>
                        <td>High</td>
                        <td>' . ($taskPriorityCounts['high'] ?? 0) . '</td>
                        <td>' . ($totalTasks > 0 ? round((($taskPriorityCounts['high'] ?? 0) / $totalTasks) * 100) : 0) . '%</td>
                    </tr>
                    <tr>
                        <td>Urgent</td>
                        <td>' . ($taskPriorityCounts['urgent'] ?? 0) . '</td>
                        <td>' . ($totalTasks > 0 ? round((($taskPriorityCounts['urgent'] ?? 0) / $totalTasks) * 100) : 0) . '%</td>
                    </tr>
                </table>
            </div>

            <div class="footer">
                <p>Generated by Momentum Project Management System</p>
            </div>
        </body>
        </html>';

        return $html;
    }
}
