<?php
/**
 * ADHD Trigger Pattern Analyzer
 * 
 * Advanced pattern recognition for identifying trigger patterns and correlations
 */

class TriggerPatternAnalyzer {
    private $db;
    
    public function __construct() {
        require_once __DIR__ . '/Database.php';
        $this->db = Database::getInstance();
    }
    
    /**
     * Analyze trigger patterns for a user
     */
    public function analyzeTriggerPatterns($userId, $days = 30) {
        $triggers = $this->getTriggerData($userId, $days);
        $symptoms = $this->getSymptomData($userId, $days);
        
        return [
            'frequency_analysis' => $this->analyzeFrequency($triggers),
            'temporal_patterns' => $this->analyzeTemporalPatterns($triggers),
            'severity_correlation' => $this->analyzeSeverityCorrelation($triggers, $symptoms),
            'trigger_combinations' => $this->analyzeTriggerCombinations($triggers),
            'environmental_factors' => $this->analyzeEnvironmentalFactors($triggers),
            'recommendations' => $this->generateTriggerRecommendations($triggers, $symptoms),
            'period' => $days
        ];
    }
    
    /**
     * Get trigger occurrence data
     */
    private function getTriggerData($userId, $days) {
        $sql = "SELECT t.name, t.category, t.severity_impact,
                       o.occurrence_date, o.occurrence_time, o.severity_level,
                       o.context, o.duration_minutes, o.notes
                FROM adhd_triggers t
                JOIN trigger_occurrences o ON t.id = o.trigger_id
                WHERE t.user_id = ?
                AND o.occurrence_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY o.occurrence_date DESC, o.occurrence_time DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Get symptom data for correlation
     */
    private function getSymptomData($userId, $days) {
        $sql = "SELECT log_date, log_time, symptom_name, severity, notes
                FROM adhd_symptom_logs
                WHERE user_id = ?
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY log_date DESC, log_time DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Analyze trigger frequency patterns
     */
    private function analyzeFrequency($triggers) {
        $frequency = [];
        $categories = [];
        $totalOccurrences = count($triggers);
        
        foreach ($triggers as $trigger) {
            // Count by trigger name
            if (!isset($frequency[$trigger['name']])) {
                $frequency[$trigger['name']] = 0;
            }
            $frequency[$trigger['name']]++;
            
            // Count by category
            if (!isset($categories[$trigger['category']])) {
                $categories[$trigger['category']] = 0;
            }
            $categories[$trigger['category']]++;
        }
        
        // Sort by frequency
        arsort($frequency);
        arsort($categories);
        
        // Calculate percentages
        foreach ($frequency as $name => $count) {
            $frequency[$name] = [
                'count' => $count,
                'percentage' => round(($count / $totalOccurrences) * 100, 1)
            ];
        }
        
        foreach ($categories as $category => $count) {
            $categories[$category] = [
                'count' => $count,
                'percentage' => round(($count / $totalOccurrences) * 100, 1)
            ];
        }
        
        return [
            'by_trigger' => $frequency,
            'by_category' => $categories,
            'total_occurrences' => $totalOccurrences
        ];
    }
    
    /**
     * Analyze temporal patterns (time of day, day of week)
     */
    private function analyzeTemporalPatterns($triggers) {
        $hourly = array_fill(0, 24, 0);
        $daily = array_fill(1, 7, 0); // 1=Monday, 7=Sunday
        $monthly = [];
        
        foreach ($triggers as $trigger) {
            // Hour analysis
            $hour = (int)date('H', strtotime($trigger['occurrence_time']));
            $hourly[$hour]++;
            
            // Day of week analysis
            $dayOfWeek = (int)date('N', strtotime($trigger['occurrence_date']));
            $daily[$dayOfWeek]++;
            
            // Monthly analysis
            $month = date('Y-m', strtotime($trigger['occurrence_date']));
            if (!isset($monthly[$month])) {
                $monthly[$month] = 0;
            }
            $monthly[$month]++;
        }
        
        return [
            'hourly_distribution' => $hourly,
            'daily_distribution' => $daily,
            'monthly_trends' => $monthly,
            'peak_hours' => $this->findPeakHours($hourly),
            'peak_days' => $this->findPeakDays($daily)
        ];
    }
    
    /**
     * Analyze correlation between triggers and symptom severity
     */
    private function analyzeSeverityCorrelation($triggers, $symptoms) {
        $correlations = [];
        
        // Group symptoms by date
        $symptomsByDate = [];
        foreach ($symptoms as $symptom) {
            $date = $symptom['log_date'];
            if (!isset($symptomsByDate[$date])) {
                $symptomsByDate[$date] = [];
            }
            $symptomsByDate[$date][] = $symptom;
        }
        
        // Group triggers by date
        $triggersByDate = [];
        foreach ($triggers as $trigger) {
            $date = $trigger['occurrence_date'];
            if (!isset($triggersByDate[$date])) {
                $triggersByDate[$date] = [];
            }
            $triggersByDate[$date][] = $trigger;
        }
        
        // Calculate correlations
        foreach ($triggersByDate as $date => $dayTriggers) {
            if (!isset($symptomsByDate[$date])) continue;
            
            $daySymptoms = $symptomsByDate[$date];
            
            foreach ($dayTriggers as $trigger) {
                $triggerName = $trigger['name'];
                
                if (!isset($correlations[$triggerName])) {
                    $correlations[$triggerName] = [
                        'symptom_increases' => [],
                        'average_impact' => 0,
                        'occurrences' => 0
                    ];
                }
                
                // Find symptoms within 4 hours of trigger
                $triggerTime = strtotime($trigger['occurrence_date'] . ' ' . $trigger['occurrence_time']);
                
                foreach ($daySymptoms as $symptom) {
                    $symptomTime = strtotime($symptom['log_date'] . ' ' . $symptom['log_time']);
                    $timeDiff = abs($symptomTime - $triggerTime) / 3600; // hours
                    
                    if ($timeDiff <= 4) {
                        if (!isset($correlations[$triggerName]['symptom_increases'][$symptom['symptom_name']])) {
                            $correlations[$triggerName]['symptom_increases'][$symptom['symptom_name']] = [];
                        }
                        $correlations[$triggerName]['symptom_increases'][$symptom['symptom_name']][] = $symptom['severity'];
                    }
                }
                
                $correlations[$triggerName]['occurrences']++;
            }
        }
        
        // Calculate average impacts
        foreach ($correlations as $triggerName => &$correlation) {
            $totalImpact = 0;
            $symptomCount = 0;
            
            foreach ($correlation['symptom_increases'] as $symptomName => $severities) {
                if (!empty($severities)) {
                    $avgSeverity = array_sum($severities) / count($severities);
                    $correlation['symptom_increases'][$symptomName] = round($avgSeverity, 1);
                    $totalImpact += $avgSeverity;
                    $symptomCount++;
                }
            }
            
            $correlation['average_impact'] = $symptomCount > 0 ? round($totalImpact / $symptomCount, 1) : 0;
        }
        
        return $correlations;
    }
    
    /**
     * Analyze trigger combinations that occur together
     */
    private function analyzeTriggerCombinations($triggers) {
        $combinations = [];
        $triggersByDate = [];
        
        // Group triggers by date
        foreach ($triggers as $trigger) {
            $date = $trigger['occurrence_date'];
            if (!isset($triggersByDate[$date])) {
                $triggersByDate[$date] = [];
            }
            $triggersByDate[$date][] = $trigger['name'];
        }
        
        // Find combinations
        foreach ($triggersByDate as $date => $dayTriggers) {
            $uniqueTriggers = array_unique($dayTriggers);
            
            if (count($uniqueTriggers) > 1) {
                sort($uniqueTriggers);
                $combination = implode(' + ', $uniqueTriggers);
                
                if (!isset($combinations[$combination])) {
                    $combinations[$combination] = 0;
                }
                $combinations[$combination]++;
            }
        }
        
        arsort($combinations);
        return array_slice($combinations, 0, 10); // Top 10 combinations
    }
    
    /**
     * Analyze environmental factors
     */
    private function analyzeEnvironmentalFactors($triggers) {
        $contexts = [];
        $durations = [];
        
        foreach ($triggers as $trigger) {
            // Context analysis
            if (!empty($trigger['context'])) {
                $context = strtolower(trim($trigger['context']));
                if (!isset($contexts[$context])) {
                    $contexts[$context] = 0;
                }
                $contexts[$context]++;
            }
            
            // Duration analysis
            if ($trigger['duration_minutes'] > 0) {
                $durations[] = $trigger['duration_minutes'];
            }
        }
        
        arsort($contexts);
        
        return [
            'common_contexts' => array_slice($contexts, 0, 10),
            'duration_stats' => [
                'average' => !empty($durations) ? round(array_sum($durations) / count($durations), 1) : 0,
                'min' => !empty($durations) ? min($durations) : 0,
                'max' => !empty($durations) ? max($durations) : 0,
                'median' => !empty($durations) ? $this->calculateMedian($durations) : 0
            ]
        ];
    }
    
    /**
     * Generate AI-powered recommendations
     */
    private function generateTriggerRecommendations($triggers, $symptoms) {
        $recommendations = [];
        
        // Analyze patterns for recommendations
        $frequency = $this->analyzeFrequency($triggers);
        $temporal = $this->analyzeTemporalPatterns($triggers);
        
        // High-frequency trigger recommendations
        foreach ($frequency['by_trigger'] as $triggerName => $data) {
            if ($data['percentage'] > 20) {
                $recommendations[] = [
                    'type' => 'high_frequency',
                    'priority' => 'high',
                    'title' => "Address High-Frequency Trigger: {$triggerName}",
                    'description' => "This trigger occurs in {$data['percentage']}% of cases. Consider developing specific coping strategies.",
                    'strategies' => $this->getCopingStrategies($triggerName)
                ];
            }
        }
        
        // Temporal pattern recommendations
        $peakHour = array_search(max($temporal['hourly_distribution']), $temporal['hourly_distribution']);
        if ($peakHour !== false) {
            $recommendations[] = [
                'type' => 'temporal',
                'priority' => 'medium',
                'title' => "Peak Trigger Time: {$peakHour}:00",
                'description' => "Most triggers occur around {$peakHour}:00. Consider preventive measures during this time.",
                'strategies' => [
                    'Set preventive reminders 30 minutes before peak time',
                    'Practice mindfulness or breathing exercises',
                    'Modify your schedule to reduce stressors during this time'
                ]
            ];
        }
        
        // General recommendations
        $recommendations[] = [
            'type' => 'general',
            'priority' => 'low',
            'title' => 'Continue Tracking',
            'description' => 'Keep logging triggers to identify more patterns and improve management strategies.',
            'strategies' => [
                'Log triggers immediately when they occur',
                'Include context and environmental factors',
                'Rate severity levels consistently'
            ]
        ];
        
        return $recommendations;
    }
    
    /**
     * Get coping strategies for specific triggers
     */
    private function getCopingStrategies($triggerName) {
        $strategies = [
            'stress' => [
                'Practice deep breathing exercises',
                'Use progressive muscle relaxation',
                'Take short breaks every hour',
                'Identify and address stress sources'
            ],
            'noise' => [
                'Use noise-canceling headphones',
                'Find quieter environments',
                'Use white noise or calming sounds',
                'Practice grounding techniques'
            ],
            'overwhelm' => [
                'Break tasks into smaller steps',
                'Use the Pomodoro Technique',
                'Prioritize tasks using the Eisenhower Matrix',
                'Ask for help when needed'
            ],
            'default' => [
                'Practice mindfulness meditation',
                'Use grounding techniques (5-4-3-2-1 method)',
                'Take a short walk or do light exercise',
                'Talk to a trusted friend or therapist'
            ]
        ];
        
        $triggerLower = strtolower($triggerName);
        
        foreach ($strategies as $key => $strategyList) {
            if (strpos($triggerLower, $key) !== false) {
                return $strategyList;
            }
        }
        
        return $strategies['default'];
    }
    
    /**
     * Helper functions
     */
    private function findPeakHours($hourly) {
        $peaks = [];
        $max = max($hourly);
        
        foreach ($hourly as $hour => $count) {
            if ($count >= $max * 0.8) { // Within 80% of max
                $peaks[] = $hour;
            }
        }
        
        return $peaks;
    }
    
    private function findPeakDays($daily) {
        $days = ['', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        $peaks = [];
        $max = max($daily);
        
        foreach ($daily as $day => $count) {
            if ($count >= $max * 0.8) {
                $peaks[] = $days[$day];
            }
        }
        
        return $peaks;
    }
    
    private function calculateMedian($numbers) {
        sort($numbers);
        $count = count($numbers);
        $middle = floor($count / 2);
        
        if ($count % 2 == 0) {
            return ($numbers[$middle - 1] + $numbers[$middle]) / 2;
        } else {
            return $numbers[$middle];
        }
    }
}
