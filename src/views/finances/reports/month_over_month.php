<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Month-over-Month Analysis</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/reports" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
                <button id="export-pdf" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-pdf mr-2"></i> Export PDF
                </button>
                <button id="export-csv" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-csv mr-2"></i> Export CSV
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/finances/reports/month-over-month" method="GET" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4">
                    <div class="flex-1">
                        <label for="months" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Number of Months</label>
                        <select id="months" name="months" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                            <option value="3" <?= $filters['months'] == 3 ? 'selected' : '' ?>>Last 3 Months</option>
                            <option value="6" <?= $filters['months'] == 6 ? 'selected' : '' ?>>Last 6 Months</option>
                            <option value="12" <?= $filters['months'] == 12 ? 'selected' : '' ?>>Last 12 Months</option>
                            <option value="24" <?= $filters['months'] == 24 ? 'selected' : '' ?>>Last 24 Months</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <label for="chart_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Chart Type</label>
                        <select id="chart_type" name="chart_type" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                            <option value="bar" <?= $filters['chart_type'] == 'bar' ? 'selected' : '' ?>>Bar Chart</option>
                            <option value="line" <?= $filters['chart_type'] == 'line' ? 'selected' : '' ?>>Line Chart</option>
                        </select>
                    </div>
                    <div>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Month-over-Month Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Month-over-Month Comparison</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($monthlyData)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No data available for the selected period</p>
                    </div>
                <?php else: ?>
                    <div class="h-80">
                        <canvas id="month-over-month-chart"></canvas>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Monthly Growth Rates -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Monthly Growth Rates</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($growthRates) || count($growthRates) < 2): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">Not enough data to calculate growth rates</p>
                    </div>
                <?php else: ?>
                    <div class="h-60">
                        <canvas id="growth-rate-chart"></canvas>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Detailed Monthly Data Table -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Detailed Monthly Data</h2>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <?php if (empty($monthlyData)): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-500 dark:text-gray-400">No data available for the selected period</p>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Month</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Income</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">MoM Income Change</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expenses</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">MoM Expense Change</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Net</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php 
                                $prevIncome = null;
                                $prevExpense = null;
                                foreach (array_reverse($monthlyData) as $index => $data): 
                                    $income = $data['total_income'] ?? 0;
                                    $expense = $data['total_expense'] ?? 0;
                                    $net = $income - $expense;
                                    
                                    // Calculate month-over-month changes
                                    $incomeChange = $prevIncome !== null ? (($income - $prevIncome) / $prevIncome) * 100 : null;
                                    $expenseChange = $prevExpense !== null ? (($expense - $prevExpense) / $prevExpense) * 100 : null;
                                    
                                    $prevIncome = $income;
                                    $prevExpense = $expense;
                                ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($data['month_name']) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= View::formatCurrency($income) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right <?= $incomeChange === null ? 'text-gray-500 dark:text-gray-400' : ($incomeChange >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400') ?>">
                                            <?= $incomeChange === null ? '-' : (number_format($incomeChange, 1) . '%') ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white"><?= View::formatCurrency($expense) ?></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right <?= $expenseChange === null ? 'text-gray-500 dark:text-gray-400' : ($expenseChange <= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400') ?>">
                                            <?= $expenseChange === null ? '-' : (number_format($expenseChange, 1) . '%') ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-right <?= $net >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                            <?= View::formatCurrency($net) ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($monthlyData)): ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('month-over-month-chart').getContext('2d');
        
        // Extract data from PHP
        const monthlyData = <?= json_encode($monthlyData) ?>;
        const labels = monthlyData.map(item => item.month_name);
        const incomeData = monthlyData.map(item => parseFloat(item.total_income || 0));
        const expenseData = monthlyData.map(item => parseFloat(item.total_expense || 0));
        const netData = monthlyData.map(item => parseFloat((item.total_income || 0) - (item.total_expense || 0)));
        
        new Chart(ctx, {
            type: '<?= $filters['chart_type'] ?>',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Income',
                        data: incomeData,
                        backgroundColor: 'rgba(34, 197, 94, 0.2)',
                        borderColor: 'rgba(34, 197, 94, 1)',
                        borderWidth: 2,
                        tension: 0.4
                    },
                    {
                        label: 'Expenses',
                        data: expenseData,
                        backgroundColor: 'rgba(239, 68, 68, 0.2)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 2,
                        tension: 0.4
                    },
                    {
                        label: 'Net',
                        data: netData,
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 2,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rs ' + context.raw.toLocaleString();
                                return label;
                            }
                        }
                    }
                }
            }
        });

        <?php if (!empty($growthRates) && count($growthRates) >= 2): ?>
        // Growth Rate Chart
        const growthCtx = document.getElementById('growth-rate-chart').getContext('2d');
        const growthRates = <?= json_encode($growthRates) ?>;
        const growthLabels = growthRates.map(item => item.month);
        const incomeGrowthData = growthRates.map(item => parseFloat(item.income_growth || 0));
        const expenseGrowthData = growthRates.map(item => parseFloat(item.expense_growth || 0));
        
        new Chart(growthCtx, {
            type: 'line',
            data: {
                labels: growthLabels,
                datasets: [
                    {
                        label: 'Income Growth %',
                        data: incomeGrowthData,
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        borderColor: 'rgba(34, 197, 94, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Expense Growth %',
                        data: expenseGrowthData,
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.raw.toFixed(1) + '%';
                                return label;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>
    });

    // Export buttons
    document.getElementById('export-pdf').addEventListener('click', function() {
        window.location.href = '/momentum/finances/reports/export/month-over-month/pdf?months=<?= $filters['months'] ?>&chart_type=<?= $filters['chart_type'] ?>';
    });

    document.getElementById('export-csv').addEventListener('click', function() {
        window.location.href = '/momentum/finances/reports/export/month-over-month/csv?months=<?= $filters['months'] ?>&chart_type=<?= $filters['chart_type'] ?>';
    });
</script>
<?php endif; ?>
