<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Narrower container for focus -->
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/goals" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Create New Financial Goal
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/finances/goals/create" method="POST" id="goal-form" class="p-6">
                <!-- Display validation errors if any -->
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="mb-4 bg-red-50 dark:bg-red-900 p-4 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Please fix the following errors:
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Goal Information -->
                <div class="space-y-6">
                    <!-- Goal Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Goal Name</label>
                        <input type="text" name="name" id="name" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="e.g., New Car, Emergency Fund, Vacation" value="<?= isset($data['name']) ? View::escape($data['name']) : '' ?>" required>
                    </div>

                    <!-- Goal Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description (Optional)</label>
                        <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="Describe your financial goal..."><?= isset($data['description']) ? View::escape($data['description']) : '' ?></textarea>
                    </div>

                    <!-- Target Amount -->
                    <div>
                        <label for="target_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Amount (Rs)</label>
                        <div class="mt-1 currency-input-wrapper">
                            <div class="currency-symbol">
                                <span>Rs</span>
                            </div>
                            <input type="number" name="target_amount" id="target_amount" class="currency-input block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="0.00" min="0" step="0.01" value="<?= isset($data['target_amount']) ? View::escape($data['target_amount']) : '' ?>" required>
                        </div>
                    </div>

                    <!-- Current Amount -->
                    <div>
                        <label for="current_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current Amount (Rs) - Optional</label>
                        <div class="mt-1 currency-input-wrapper">
                            <div class="currency-symbol">
                                <span>Rs</span>
                            </div>
                            <input type="number" name="current_amount" id="current_amount" class="currency-input block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="0.00" min="0" step="0.01" value="<?= isset($data['current_amount']) ? View::escape($data['current_amount']) : '0' ?>">
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">If you've already saved some money toward this goal</p>
                    </div>

                    <!-- Date Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                            <input type="date" name="start_date" id="start_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" value="<?= isset($data['start_date']) ? View::escape($data['start_date']) : date('Y-m-d') ?>" required>
                        </div>
                        <div>
                            <label for="target_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Date</label>
                            <input type="date" name="target_date" id="target_date" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" value="<?= isset($data['target_date']) ? View::escape($data['target_date']) : date('Y-m-d', strtotime('+1 year')) ?>" required>
                        </div>
                    </div>

                    <!-- Category and Priority -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category (Optional)</label>
                            <select name="category" id="category" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select a category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= View::escape($category) ?>" <?= isset($data['category']) && $data['category'] === $category ? 'selected' : '' ?>><?= View::escape($category) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priority</label>
                            <select name="priority" id="priority" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="low" <?= isset($data['priority']) && $data['priority'] === 'low' ? 'selected' : '' ?>>Low</option>
                                <option value="medium" <?= isset($data['priority']) && $data['priority'] === 'medium' ? 'selected' : (!isset($data['priority']) ? 'selected' : '') ?>>Medium</option>
                                <option value="high" <?= isset($data['priority']) && $data['priority'] === 'high' ? 'selected' : '' ?>>High</option>
                            </select>
                        </div>
                    </div>

                    <!-- Icon Selection -->
                    <div>
                        <label for="icon" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Icon</label>
                        <select name="icon" id="icon" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="fa-piggy-bank" <?= (!isset($data['icon']) || $data['icon'] === 'fa-piggy-bank') ? 'selected' : '' ?>>💰 Piggy Bank</option>
                            <option value="fa-car" <?= isset($data['icon']) && $data['icon'] === 'fa-car' ? 'selected' : '' ?>>🚗 Car</option>
                            <option value="fa-home" <?= isset($data['icon']) && $data['icon'] === 'fa-home' ? 'selected' : '' ?>>🏠 Home</option>
                            <option value="fa-plane" <?= isset($data['icon']) && $data['icon'] === 'fa-plane' ? 'selected' : '' ?>>✈️ Travel</option>
                            <option value="fa-graduation-cap" <?= isset($data['icon']) && $data['icon'] === 'fa-graduation-cap' ? 'selected' : '' ?>>🎓 Education</option>
                            <option value="fa-heartbeat" <?= isset($data['icon']) && $data['icon'] === 'fa-heartbeat' ? 'selected' : '' ?>>❤️ Health</option>
                            <option value="fa-gift" <?= isset($data['icon']) && $data['icon'] === 'fa-gift' ? 'selected' : '' ?>>🎁 Gift</option>
                            <option value="fa-laptop" <?= isset($data['icon']) && $data['icon'] === 'fa-laptop' ? 'selected' : '' ?>>💻 Electronics</option>
                            <option value="fa-ring" <?= isset($data['icon']) && $data['icon'] === 'fa-ring' ? 'selected' : '' ?>>💍 Wedding</option>
                            <option value="fa-baby" <?= isset($data['icon']) && $data['icon'] === 'fa-baby' ? 'selected' : '' ?>>👶 Baby</option>
                            <option value="fa-umbrella-beach" <?= isset($data['icon']) && $data['icon'] === 'fa-umbrella-beach' ? 'selected' : '' ?>>🏖️ Vacation</option>
                            <option value="fa-shield-alt" <?= isset($data['icon']) && $data['icon'] === 'fa-shield-alt' ? 'selected' : '' ?>>🛡️ Emergency Fund</option>
                        </select>
                    </div>

                    <!-- Milestones Section -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Milestones (Optional)</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Add key milestones to track your progress toward this goal</p>

                        <div id="milestones-container">
                            <!-- Milestone template - will be cloned by JavaScript -->
                            <div class="milestone-template hidden">
                                <div class="milestone-item border border-gray-200 dark:border-gray-700 rounded-md p-4 mb-4">
                                    <div class="flex justify-between mb-2">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Milestone</h4>
                                        <button type="button" class="remove-milestone text-red-500 hover:text-red-700">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                                            <input type="text" name="milestone_names[]" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="e.g., 25% Complete">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Amount (Rs)</label>
                                            <div class="mt-1 currency-input-wrapper">
                                                <div class="currency-symbol">
                                                    <span>Rs</span>
                                                </div>
                                                <input type="number" name="milestone_amounts[]" class="currency-input block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="0.00" min="0" step="0.01">
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Target Date (Optional)</label>
                                            <input type="date" name="milestone_dates[]" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes (Optional)</label>
                                            <textarea name="milestone_notes[]" rows="2" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500" placeholder="Additional details about this milestone..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="button" id="add-milestone" class="mt-2 inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-2"></i> Add Milestone
                        </button>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end pt-5 border-t border-gray-200 dark:border-gray-700">
                        <a href="/momentum/finances/goals" class="bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Cancel
                        </a>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Create Goal
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const milestonesContainer = document.getElementById('milestones-container');
    const addMilestoneButton = document.getElementById('add-milestone');
    const milestoneTemplate = document.querySelector('.milestone-template');

    // Add milestone
    addMilestoneButton.addEventListener('click', function() {
        const newMilestone = milestoneTemplate.cloneNode(true);
        newMilestone.classList.remove('hidden', 'milestone-template');
        milestonesContainer.appendChild(newMilestone);

        // Add event listener to remove button
        const removeButton = newMilestone.querySelector('.remove-milestone');
        removeButton.addEventListener('click', function() {
            milestonesContainer.removeChild(newMilestone);
        });
    });

    // Calculate suggested milestones based on target amount
    const targetAmountInput = document.getElementById('target_amount');
    targetAmountInput.addEventListener('change', function() {
        const targetAmount = parseFloat(this.value);
        if (targetAmount > 0 && milestonesContainer.children.length <= 1) { // Only if no milestones added yet
            // Suggest 25%, 50%, and 75% milestones
            const milestones = [
                { name: '25% Complete', amount: targetAmount * 0.25 },
                { name: '50% Complete', amount: targetAmount * 0.5 },
                { name: '75% Complete', amount: targetAmount * 0.75 }
            ];

            if (confirm('Would you like to add suggested milestones at 25%, 50%, and 75% of your target amount?')) {
                milestones.forEach(milestone => {
                    const newMilestone = milestoneTemplate.cloneNode(true);
                    newMilestone.classList.remove('hidden', 'milestone-template');

                    const nameInput = newMilestone.querySelector('input[name="milestone_names[]"]');
                    const amountInput = newMilestone.querySelector('input[name="milestone_amounts[]"]');

                    nameInput.value = milestone.name;
                    amountInput.value = milestone.amount.toFixed(2);

                    milestonesContainer.appendChild(newMilestone);

                    // Add event listener to remove button
                    const removeButton = newMilestone.querySelector('.remove-milestone');
                    removeButton.addEventListener('click', function() {
                        milestonesContainer.removeChild(newMilestone);
                    });
                });
            }
        }
    });
});
</script>
