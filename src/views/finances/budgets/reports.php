<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Budget Reports & Analysis</h1>
        <div>
            <a href="/momentum/finances/budgets" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Budgets
            </a>
        </div>
    </div>

    <!-- Budget Performance Overview -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Budget Performance Overview</h2>
        </div>
        <div class="px-4 py-5 sm:p-6">
            <?php if (empty($performanceMetrics['total_budgets'])): ?>
                <div class="text-center py-4">
                    <p class="text-gray-500 dark:text-gray-400">No historical budget data available yet. Complete at least one budget cycle to see performance metrics.</p>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Budget Success Rate -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Budget Success Rate</h3>
                        <div class="flex items-baseline">
                            <span class="text-3xl font-semibold text-gray-900 dark:text-white"><?= number_format($performanceMetrics['under_budget_percentage'], 1) ?>%</span>
                            <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">(<?= $performanceMetrics['under_budget_count'] ?> of <?= $performanceMetrics['total_budgets'] ?> budgets)</span>
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Percentage of budgets where spending stayed under budget</p>
                    </div>

                    <!-- Average Budget Utilization -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Average Budget Utilization</h3>
                        <div class="flex items-baseline">
                            <span class="text-3xl font-semibold text-gray-900 dark:text-white"><?= number_format($performanceMetrics['average_budget_utilization'], 1) ?>%</span>
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Average percentage of budget used across all completed budgets</p>
                    </div>

                    <!-- Total Budgets Tracked -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Total Budgets Tracked</h3>
                        <div class="flex items-baseline">
                            <span class="text-3xl font-semibold text-gray-900 dark:text-white"><?= $performanceMetrics['total_budgets'] ?></span>
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Number of completed budget cycles</p>
                    </div>
                </div>

                <!-- Category Performance -->
                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Most Over Budget Categories -->
                    <div>
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Categories Most Over Budget</h3>
                        <?php if (empty($performanceMetrics['categories_most_over_budget'])): ?>
                            <p class="text-gray-500 dark:text-gray-400">No categories have gone over budget</p>
                        <?php else: ?>
                            <ul class="space-y-3">
                                <?php foreach ($performanceMetrics['categories_most_over_budget'] as $category => $data): ?>
                                    <?php if ($data['average_percentage'] > 100): ?>
                                        <li>
                                            <div class="flex justify-between mb-1">
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= View::escape($category) ?></span>
                                                <span class="text-sm font-medium text-red-600 dark:text-red-400"><?= number_format($data['average_percentage'], 1) ?>%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                                <div class="h-1.5 rounded-full bg-red-600" style="width: <?= min(100, $data['average_percentage']) ?>%"></div>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Over budget <?= number_format($data['over_budget_rate'], 0) ?>% of the time</p>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>

                    <!-- Most Under Budget Categories -->
                    <div>
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">Categories Most Under Budget</h3>
                        <?php if (empty($performanceMetrics['categories_most_under_budget'])): ?>
                            <p class="text-gray-500 dark:text-gray-400">No categories have stayed under budget</p>
                        <?php else: ?>
                            <ul class="space-y-3">
                                <?php foreach ($performanceMetrics['categories_most_under_budget'] as $category => $data): ?>
                                    <?php if ($data['average_percentage'] < 100): ?>
                                        <li>
                                            <div class="flex justify-between mb-1">
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= View::escape($category) ?></span>
                                                <span class="text-sm font-medium text-green-600 dark:text-green-400"><?= number_format($data['average_percentage'], 1) ?>%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                                <div class="h-1.5 rounded-full bg-green-600" style="width: <?= $data['average_percentage'] ?>%"></div>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Under budget <?= number_format(100 - $data['over_budget_rate'], 0) ?>% of the time</p>
                                        </li>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Historical Budget Comparison -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Historical Budget Comparison</h2>
        </div>
        <div class="px-4 py-5 sm:p-6">
            <?php if (empty($historicalData)): ?>
                <div class="text-center py-4">
                    <p class="text-gray-500 dark:text-gray-400">No historical budget data available yet. Complete at least one budget cycle to see comparisons.</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-white sm:pl-6">Budget Period</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Total Budget</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Total Spent</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Utilization</th>
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Status</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                            <?php foreach ($historicalData as $data): ?>
                                <tr>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-white sm:pl-6">
                                        <?= View::escape($data['budget']['name']) ?><br>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                            <?= View::formatDate($data['budget']['start_date']) ?> to <?= View::formatDate($data['budget']['end_date']) ?>
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        <?= View::formatCurrency($data['progress']['total_budget']) ?>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        <?= View::formatCurrency($data['progress']['total_spent']) ?>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        <div class="flex items-center">
                                            <span class="mr-2"><?= number_format($data['progress']['percentage'], 1) ?>%</span>
                                            <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                                <div class="h-1.5 rounded-full <?= $data['progress']['percentage'] > 100 ? 'bg-red-600' : 'bg-green-600' ?>" style="width: <?= min(100, $data['progress']['percentage']) ?>%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-3 py-4 text-sm">
                                        <?php if ($data['progress']['percentage'] <= 100): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                Under Budget
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                                Over Budget
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Category Spending Trends -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">Category Spending Trends</h2>
        </div>
        <div class="px-4 py-5 sm:p-6">
            <?php if (empty($spendingTrends)): ?>
                <div class="text-center py-4">
                    <p class="text-gray-500 dark:text-gray-400">No spending trend data available yet. Add more transactions to see trends.</p>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php foreach ($spendingTrends as $category => $monthlyData): ?>
                        <?php if (!empty($monthlyData)): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3"><?= View::escape($category) ?></h3>
                                <div class="h-40 relative">
                                    <!-- This would be replaced with a chart in a real implementation -->
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <p class="text-gray-500 dark:text-gray-400">Chart would be displayed here</p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <table class="min-w-full text-xs">
                                        <thead>
                                            <tr>
                                                <th class="text-left text-gray-500 dark:text-gray-400">Month</th>
                                                <th class="text-right text-gray-500 dark:text-gray-400">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($monthlyData as $data): ?>
                                                <tr>
                                                    <td class="py-1 text-gray-700 dark:text-gray-300">
                                                        <?= date('M Y', strtotime($data['month'] . '-01')) ?>
                                                    </td>
                                                    <td class="py-1 text-right text-gray-700 dark:text-gray-300">
                                                        <?= View::formatCurrency($data['total_amount']) ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
