<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex items-center mb-6">
            <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Agent: <?= htmlspecialchars($agent['name']) ?></h1>
        </div>

        <!-- Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <?php if (isset($error)): ?>
                    <div class="mb-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4 dark:bg-red-900 dark:text-red-200" role="alert">
                        <p><?= $error ?></p>
                    </div>
                <?php endif; ?>

                <?php if (isset($success)): ?>
                    <div class="mb-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 dark:bg-green-900 dark:text-green-200" role="alert">
                        <p><?= $success ?></p>
                    </div>
                <?php endif; ?>

                <form action="/momentum/ai-agents/edit/<?= $agent['id'] ?>" method="post">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Left Column -->
                        <div class="space-y-6">
                            <!-- Basic Information -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>

                                <div class="mb-4">
                                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                                    <input type="text" name="name" id="name" value="<?= htmlspecialchars($agent['name']) ?>" required
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                </div>

                                <div class="mb-4">
                                    <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category</label>
                                    <select name="category_id" id="category_id"
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="">-- Select Category --</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>" <?= $agent['category_id'] == $category['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($category['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                                    <textarea name="description" id="description" rows="3" required
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white"><?= htmlspecialchars($agent['description']) ?></textarea>
                                </div>

                                <div class="mb-4">
                                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                                    <select name="status" id="status" required
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="active" <?= $agent['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                        <option value="inactive" <?= $agent['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        <option value="training" <?= $agent['status'] === 'training' ? 'selected' : '' ?>>Training</option>
                                        <option value="error" <?= $agent['status'] === 'error' ? 'selected' : '' ?>>Error</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Capabilities and Personality -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Capabilities & Personality</h3>

                                <div class="mb-4">
                                    <label for="capabilities" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Capabilities (one per line)</label>
                                    <textarea name="capabilities" id="capabilities" rows="4"
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white"><?= htmlspecialchars($agent['capabilities']) ?></textarea>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter each capability on a new line</p>
                                </div>

                                <div class="mb-4">
                                    <label for="personality_traits" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Personality Traits (one per line)</label>
                                    <textarea name="personality_traits" id="personality_traits" rows="4"
                                        class="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-white"><?= htmlspecialchars($agent['personality_traits']) ?></textarea>
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter each trait on a new line</p>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-6">
                            <!-- Ratings -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Agent Ratings</h3>

                                <div class="mb-4">
                                    <label for="intelligence_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Intelligence Level (1-10)</label>
                                    <div class="mt-1 flex items-center">
                                        <input type="range" name="intelligence_level" id="intelligence_level" min="1" max="10" step="1" value="<?= $agent['intelligence_level'] ?>"
                                            class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                        <span id="intelligence_value" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-8 text-center"><?= $agent['intelligence_level'] ?></span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="efficiency_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Efficiency Rating (1-10)</label>
                                    <div class="mt-1 flex items-center">
                                        <input type="range" name="efficiency_rating" id="efficiency_rating" min="1" max="10" step="0.1" value="<?= $agent['efficiency_rating'] ?>"
                                            class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                        <span id="efficiency_value" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-8 text-center"><?= number_format($agent['efficiency_rating'], 1) ?></span>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="reliability_score" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Reliability Score (1-10)</label>
                                    <div class="mt-1 flex items-center">
                                        <input type="range" name="reliability_score" id="reliability_score" min="1" max="10" step="0.1" value="<?= $agent['reliability_score'] ?>"
                                            class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                        <span id="reliability_value" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-8 text-center"><?= number_format($agent['reliability_score'], 1) ?></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Skills -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Agent Skills</h3>

                                <?php
                                // Debug: Output the structure of the first agent skill
                                if (!empty($agentSkills)) {
                                    echo '<!-- Debug: First agent skill structure: ' . json_encode(reset($agentSkills)) . ' -->';
                                }
                                ?>

                                <div class="space-y-3">
                                    <?php foreach ($allSkills as $skill): ?>
                                        <?php
                                            $hasSkill = false;
                                            $proficiency = 5;

                                            // For this agent skill, we're checking if it's in the agentSkills array
                                            // The key in agentSkills is the skill ID itself
                                            if (isset($agentSkills[$skill['id']])) {
                                                $agentSkill = $agentSkills[$skill['id']];
                                                $hasSkill = true;
                                                $proficiency = $agentSkill['proficiency_level'];
                                            }
                                        ?>
                                        <div class="border border-gray-200 dark:border-gray-700 rounded-md p-3">
                                            <div class="flex items-center justify-between mb-2">
                                                <div class="flex items-center">
                                                    <input type="checkbox" name="skills[<?= $skill['id'] ?>][selected]" id="skill_<?= $skill['id'] ?>" value="1" <?= $hasSkill ? 'checked' : '' ?>
                                                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded">
                                                    <label for="skill_<?= $skill['id'] ?>" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        <?= htmlspecialchars($skill['name']) ?>
                                                    </label>
                                                </div>
                                                <span class="text-xs text-gray-500 dark:text-gray-400 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                                                    <?= ucfirst($skill['skill_type']) ?>
                                                </span>
                                            </div>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mb-2"><?= htmlspecialchars($skill['description']) ?></p>
                                            <div class="flex items-center">
                                                <label for="skill_<?= $skill['id'] ?>_proficiency" class="text-xs text-gray-500 dark:text-gray-400 mr-2">Proficiency:</label>
                                                <input type="range" name="skills[<?= $skill['id'] ?>][proficiency]" id="skill_<?= $skill['id'] ?>_proficiency"
                                                    min="1" max="10" step="1" value="<?= $proficiency ?>"
                                                    class="w-full h-1.5 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                                <span class="ml-2 text-xs text-gray-500 dark:text-gray-400 w-6 text-center skill-proficiency-value"><?= $proficiency ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <a href="/momentum/ai-agents/view/<?= $agent['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // Update range input display values
    document.getElementById('intelligence_level').addEventListener('input', function() {
        document.getElementById('intelligence_value').textContent = this.value;
    });

    document.getElementById('efficiency_rating').addEventListener('input', function() {
        document.getElementById('efficiency_value').textContent = parseFloat(this.value).toFixed(1);
    });

    document.getElementById('reliability_score').addEventListener('input', function() {
        document.getElementById('reliability_value').textContent = parseFloat(this.value).toFixed(1);
    });

    // Update skill proficiency values
    document.querySelectorAll('input[type="range"][id^="skill_"]').forEach(function(range) {
        range.addEventListener('input', function() {
            this.nextElementSibling.textContent = this.value;
        });
    });
</script>
