<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Task Calendar</h1>
            <div class="flex space-x-2">
                <a href="/momentum/tasks" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <i class="fas fa-list mr-2"></i> List View
                </a>
                <a href="/momentum/tasks/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700">
                    <i class="fas fa-plus mr-2"></i> New Task
                </a>
            </div>
        </div>

        <!-- Calendar View Controls -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex space-x-2">
                    <a href="?view=month&date=<?= date('Y-m-d', strtotime('-1 month', strtotime($date))) ?>" class="btn">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <h2 class="text-lg font-medium"><?= date('F Y', strtotime($date)) ?></h2>
                    <a href="?view=month&date=<?= date('Y-m-d', strtotime('+1 month', strtotime($date))) ?>" class="btn">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
                <div class="flex space-x-2">
                    <a href="?view=month&date=<?= date('Y-m-d') ?>" class="px-3 py-1 text-sm rounded-md <?= $viewType === 'month' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Month</a>
                    <a href="?view=week&date=<?= date('Y-m-d') ?>" class="px-3 py-1 text-sm rounded-md <?= $viewType === 'week' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Week</a>
                    <a href="?view=day&date=<?= date('Y-m-d') ?>" class="px-3 py-1 text-sm rounded-md <?= $viewType === 'day' ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300' : 'text-gray-600 dark:text-gray-400' ?>">Day</a>
                </div>
            </div>
        </div>

        <!-- Calendar Grid -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <?php if ($viewType === 'month'): ?>
                <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700">
                    <?php
                    $days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
                    foreach ($days as $day): ?>
                        <div class="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300">
                            <?= $day ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700">
                    <?php
                    $firstDay = date('w', strtotime($startDate));
                    $lastDay = date('t', strtotime($startDate));

                    // Add empty cells for days before the first day of the month
                    for ($i = 0; $i < $firstDay; $i++): ?>
                        <div class="bg-white dark:bg-gray-800 p-2 min-h-[100px]"></div>
                    <?php endfor;

                    // Add cells for each day of the month
                    for ($day = 1; $day <= $lastDay; $day++):
                        $currentDate = date('Y-m-d', strtotime($startDate . ' + ' . ($day - 1) . ' days'));
                        $dayTasks = array_filter($tasks, function($task) use ($currentDate) {
                            return $task['due_date'] === $currentDate;
                        });
                    ?>
                        <div class="bg-white dark:bg-gray-800 p-2 min-h-[100px]">
                            <div class="font-medium text-sm <?= $currentDate === date('Y-m-d') ? 'text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300' ?>">
                                <?= $day ?>
                            </div>
                            <div class="mt-1 space-y-1">
                                <?php foreach ($dayTasks as $task): ?>
                                    <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="block text-xs truncate <?= $task['status'] === 'done' ? 'line-through text-gray-400 dark:text-gray-500' : 'text-gray-700 dark:text-gray-300' ?>">
                                        <?= View::escape($task['title']) ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Prevent dropdown errors -->
<script src="/momentum/public/js/dropdown-page-check.js"></script>