<?php
/**
 * Tools - Screenshot Capture Interface
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Screenshot Tool - Momentum Tools</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/momentum/public/css/tools.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .screenshot-controls {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            gap: 15px;
            z-index: 10000;
            backdrop-filter: blur(10px);
        }
        
        .control-btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-btn:hover {
            background: #4338ca;
            transform: translateY(-1px);
        }
        
        .control-btn.secondary {
            background: #6b7280;
        }
        
        .control-btn.secondary:hover {
            background: #4b5563;
        }
        
        .control-btn.danger {
            background: #ef4444;
        }
        
        .control-btn.danger:hover {
            background: #dc2626;
        }
        
        .status-text {
            color: #d1d5db;
            font-size: 14px;
        }
        
        .instructions {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 15px 25px;
            border-radius: 8px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .capture-area {
            position: absolute;
            border: 2px solid #4f46e5;
            background: rgba(79, 70, 229, 0.1);
            cursor: move;
            display: none;
        }
        
        .capture-area::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border: 1px dashed #8b5cf6;
            pointer-events: none;
        }
        
        #uploadForm {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            color: black;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
            display: none;
            z-index: 10001;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 25px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #4f46e5;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4338ca;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <!-- Screenshot Controls -->
    <div class="screenshot-controls">
        <span class="status-text">Screenshot Tool</span>
        <button id="captureBtn" class="control-btn">
            <i class="fas fa-camera"></i>
            Start Capture
        </button>
        <button id="fullScreenBtn" class="control-btn secondary">
            <i class="fas fa-expand"></i>
            Full Screen
        </button>
        <button id="uploadFileBtn" class="control-btn secondary">
            <i class="fas fa-upload"></i>
            Upload File
        </button>
        <button id="closeBtn" class="control-btn danger">
            <i class="fas fa-times"></i>
            Close
        </button>
    </div>

    <!-- Instructions -->
    <div class="instructions">
        <p><strong>Screenshot Instructions:</strong></p>
        <p>Click "Start Capture" and drag to select an area, or use "Full Screen" to capture everything.</p>
        <p>You can also upload an existing image file using "Upload File".</p>
    </div>

    <!-- Capture Area -->
    <div id="captureArea" class="capture-area"></div>

    <!-- Upload Form -->
    <div id="uploadForm">
        <h3 style="margin-top: 0; margin-bottom: 20px; color: #1f2937;">Save Screenshot</h3>
        
        <form id="screenshotForm">
            <div class="form-group">
                <label for="title">Title (Optional)</label>
                <input type="text" id="title" name="title" placeholder="Enter a title for your screenshot...">
            </div>
            
            <div class="form-group">
                <label for="description">Description (Optional)</label>
                <textarea id="description" name="description" placeholder="Add a description or notes..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="tags">Tags (Optional)</label>
                <input type="text" id="tags" name="tags" placeholder="Enter tags separated by commas...">
            </div>
            
            <div class="form-group">
                <label for="category">Category (Optional)</label>
                <input type="text" id="category" name="category" placeholder="Enter a category...">
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="extractText" name="extract_text" value="1" style="width: auto; margin-right: 8px;">
                    Extract text from image (OCR)
                </label>
            </div>
            
            <div class="form-actions">
                <button type="button" id="cancelBtn" class="btn btn-secondary">Cancel</button>
                <button type="submit" id="saveBtn" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Screenshot
                </button>
            </div>
        </form>
    </div>

    <!-- Hidden file input -->
    <input type="file" id="fileInput" accept="image/*" style="display: none;">

    <script>
        let isCapturing = false;
        let startX, startY, endX, endY;
        let capturedImage = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initializeScreenshotTool();
        });

        function initializeScreenshotTool() {
            // Button event listeners
            document.getElementById('captureBtn').addEventListener('click', startAreaCapture);
            document.getElementById('fullScreenBtn').addEventListener('click', captureFullScreen);
            document.getElementById('uploadFileBtn').addEventListener('click', () => {
                document.getElementById('fileInput').click();
            });
            document.getElementById('closeBtn').addEventListener('click', closeWindow);
            
            // Form event listeners
            document.getElementById('screenshotForm').addEventListener('submit', saveScreenshot);
            document.getElementById('cancelBtn').addEventListener('click', hideUploadForm);
            
            // File input listener
            document.getElementById('fileInput').addEventListener('change', handleFileUpload);
            
            // Mouse events for area selection
            document.addEventListener('mousedown', onMouseDown);
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
            
            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (isCapturing) {
                        cancelCapture();
                    } else {
                        closeWindow();
                    }
                }
            });
        }

        function startAreaCapture() {
            isCapturing = true;
            document.body.style.cursor = 'crosshair';
            document.getElementById('captureBtn').textContent = 'Cancel';
            document.getElementById('captureBtn').onclick = cancelCapture;
            
            // Hide instructions
            document.querySelector('.instructions').style.display = 'none';
        }

        function cancelCapture() {
            isCapturing = false;
            document.body.style.cursor = 'default';
            document.getElementById('captureBtn').innerHTML = '<i class="fas fa-camera"></i> Start Capture';
            document.getElementById('captureBtn').onclick = startAreaCapture;
            document.getElementById('captureArea').style.display = 'none';
            
            // Show instructions
            document.querySelector('.instructions').style.display = 'block';
        }

        function onMouseDown(e) {
            if (!isCapturing) return;
            
            startX = e.clientX;
            startY = e.clientY;
            
            const captureArea = document.getElementById('captureArea');
            captureArea.style.left = startX + 'px';
            captureArea.style.top = startY + 'px';
            captureArea.style.width = '0px';
            captureArea.style.height = '0px';
            captureArea.style.display = 'block';
        }

        function onMouseMove(e) {
            if (!isCapturing || startX === undefined) return;
            
            endX = e.clientX;
            endY = e.clientY;
            
            const captureArea = document.getElementById('captureArea');
            const left = Math.min(startX, endX);
            const top = Math.min(startY, endY);
            const width = Math.abs(endX - startX);
            const height = Math.abs(endY - startY);
            
            captureArea.style.left = left + 'px';
            captureArea.style.top = top + 'px';
            captureArea.style.width = width + 'px';
            captureArea.style.height = height + 'px';
        }

        function onMouseUp(e) {
            if (!isCapturing || startX === undefined) return;
            
            const width = Math.abs(endX - startX);
            const height = Math.abs(endY - startY);
            
            if (width > 10 && height > 10) {
                captureSelectedArea();
            } else {
                cancelCapture();
            }
        }

        async function captureSelectedArea() {
            try {
                // Use Screen Capture API if available
                if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                    const stream = await navigator.mediaDevices.getDisplayMedia({
                        video: { mediaSource: 'screen' }
                    });
                    
                    const video = document.createElement('video');
                    video.srcObject = stream;
                    video.play();
                    
                    video.addEventListener('loadedmetadata', () => {
                        const canvas = document.createElement('canvas');
                        canvas.width = video.videoWidth;
                        canvas.height = video.videoHeight;
                        
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(video, 0, 0);
                        
                        // Stop the stream
                        stream.getTracks().forEach(track => track.stop());
                        
                        // Convert to blob
                        canvas.toBlob((blob) => {
                            capturedImage = blob;
                            showUploadForm();
                        }, 'image/png');
                    });
                } else {
                    alert('Screen capture is not supported in this browser. Please use a modern browser like Chrome or Firefox.');
                }
            } catch (error) {
                console.error('Error capturing screen:', error);
                alert('Failed to capture screen. Please try again or upload a file instead.');
            }
            
            cancelCapture();
        }

        async function captureFullScreen() {
            try {
                if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                    const stream = await navigator.mediaDevices.getDisplayMedia({
                        video: { mediaSource: 'screen' }
                    });
                    
                    const video = document.createElement('video');
                    video.srcObject = stream;
                    video.play();
                    
                    video.addEventListener('loadedmetadata', () => {
                        const canvas = document.createElement('canvas');
                        canvas.width = video.videoWidth;
                        canvas.height = video.videoHeight;
                        
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(video, 0, 0);
                        
                        // Stop the stream
                        stream.getTracks().forEach(track => track.stop());
                        
                        // Convert to blob
                        canvas.toBlob((blob) => {
                            capturedImage = blob;
                            showUploadForm();
                        }, 'image/png');
                    });
                } else {
                    alert('Screen capture is not supported in this browser. Please use a modern browser like Chrome or Firefox.');
                }
            } catch (error) {
                console.error('Error capturing full screen:', error);
                alert('Failed to capture screen. Please try again or upload a file instead.');
            }
        }

        function handleFileUpload(e) {
            const file = e.target.files[0];
            if (file && file.type.startsWith('image/')) {
                capturedImage = file;
                showUploadForm();
            } else {
                alert('Please select a valid image file.');
            }
        }

        function showUploadForm() {
            document.getElementById('uploadForm').style.display = 'block';
            document.getElementById('title').focus();
        }

        function hideUploadForm() {
            document.getElementById('uploadForm').style.display = 'none';
            capturedImage = null;
        }

        async function saveScreenshot(e) {
            e.preventDefault();
            
            if (!capturedImage) {
                alert('No image to save. Please capture or upload an image first.');
                return;
            }
            
            const formData = new FormData();
            formData.append('screenshot', capturedImage, 'screenshot.png');
            formData.append('title', document.getElementById('title').value);
            formData.append('description', document.getElementById('description').value);
            formData.append('tags', document.getElementById('tags').value);
            formData.append('category', document.getElementById('category').value);
            formData.append('extract_text', document.getElementById('extractText').checked ? '1' : '0');
            
            // Disable save button
            const saveBtn = document.getElementById('saveBtn');
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            
            try {
                const response = await fetch('/momentum/quick-capture/screenshot/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('Screenshot saved successfully!');
                    
                    // Close window or redirect
                    if (window.opener) {
                        window.opener.location.reload();
                        window.close();
                    } else {
                        window.location.href = '/momentum/tools';
                    }
                } else {
                    alert('Failed to save screenshot: ' + (result.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error saving screenshot:', error);
                alert('Failed to save screenshot. Please try again.');
            } finally {
                // Re-enable save button
                saveBtn.disabled = false;
                saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Screenshot';
            }
        }

        function closeWindow() {
            if (window.opener) {
                window.close();
            } else {
                window.location.href = '/momentum/tools';
            }
        }
    </script>
</body>
</html>
