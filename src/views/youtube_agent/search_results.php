<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Search Results</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                <?= htmlspecialchars($search['search_query']) ?> 
                <span class="text-sm">(<?= ucfirst($search['search_type']) ?>)</span>
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="/momentum/youtube-agent/search" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-search mr-2"></i> New Search
            </a>
            <a href="/momentum/youtube-agent" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Search Status -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div class="flex items-center">
            <div class="mr-4">
                <span class="inline-flex items-center justify-center h-10 w-10 rounded-full <?= getStatusBgClass($search['status']) ?>">
                    <i class="fas <?= getStatusIconClass($search['status']) ?> text-white"></i>
                </span>
            </div>
            <div class="flex-1">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Search Status: <?= ucfirst($search['status']) ?>
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    <?= getStatusMessage($search) ?>
                </p>
            </div>
            <div class="ml-4">
                <a href="/momentum/youtube-agent/delete-search/<?= $search['id'] ?>" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this search?')">
                    <i class="fas fa-trash"></i>
                </a>
            </div>
        </div>
    </div>
    
    <?php if (empty($videos)): ?>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
            <div class="text-gray-500 dark:text-gray-400 mb-4">
                <i class="fas fa-search text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No videos found</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
                Try a different search query or search type.
            </p>
            <a href="/momentum/youtube-agent/search" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-search mr-2"></i> New Search
            </a>
        </div>
    <?php else: ?>
        <!-- Filter Controls -->
        <div class="flex flex-wrap items-center justify-between mb-4">
            <div class="flex items-center space-x-2 mb-2 sm:mb-0">
                <span class="text-sm text-gray-700 dark:text-gray-300">Sort by:</span>
                <select id="sort-select" class="text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    <option value="relevance">Relevance</option>
                    <option value="views">Views</option>
                    <option value="date">Date</option>
                </select>
            </div>
            <div class="flex items-center">
                <span class="text-sm text-gray-700 dark:text-gray-300 mr-2">Found <?= count($videos) ?> videos</span>
            </div>
        </div>
        
        <!-- Video Results -->
        <div class="grid grid-cols-1 gap-6" id="video-results">
            <?php foreach ($videos as $video): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-views="<?= $video['view_count'] ?>" data-date="<?= strtotime($video['published_at']) ?>">
                    <div class="md:flex">
                        <div class="md:flex-shrink-0">
                            <a href="/momentum/youtube-agent/view-video/<?= $video['id'] ?>" class="block">
                                <img class="h-48 w-full object-cover md:w-48" src="<?= $video['thumbnail_url'] ?>" alt="<?= htmlspecialchars($video['title']) ?>">
                            </a>
                        </div>
                        <div class="p-4 flex-1">
                            <a href="/momentum/youtube-agent/view-video/<?= $video['id'] ?>" class="block">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400"><?= htmlspecialchars($video['title']) ?></h3>
                            </a>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                <a href="https://www.youtube.com/channel/<?= $video['channel_id'] ?>" target="_blank" class="hover:text-primary-600 dark:hover:text-primary-400">
                                    <?= htmlspecialchars($video['channel_title']) ?>
                                </a>
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                <?= number_format($video['view_count']) ?> views • 
                                <?= date('M j, Y', strtotime($video['published_at'])) ?> • 
                                <?= $video['duration'] ?>
                            </p>
                            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                <?= htmlspecialchars($video['description']) ?>
                            </p>
                            
                            <?php if (!empty($video['tags'])): ?>
                                <div class="mt-2 flex flex-wrap gap-1">
                                    <?php foreach (array_slice($video['tags'], 0, 3) as $tag): ?>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                            <?= htmlspecialchars($tag) ?>
                                        </span>
                                    <?php endforeach; ?>
                                    <?php if (count($video['tags']) > 3): ?>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                            +<?= count($video['tags']) - 3 ?> more
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mt-3 flex space-x-2">
                                <a href="/momentum/youtube-agent/view-video/<?= $video['id'] ?>" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fas fa-chart-line mr-1"></i> Analyze
                                </a>
                                <a href="https://www.youtube.com/watch?v=<?= $video['youtube_id'] ?>" target="_blank" class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fab fa-youtube mr-1"></i> Watch
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sortSelect = document.getElementById('sort-select');
        const videoResults = document.getElementById('video-results');
        
        if (sortSelect && videoResults) {
            sortSelect.addEventListener('change', function() {
                const sortBy = this.value;
                const videos = Array.from(videoResults.children);
                
                videos.sort(function(a, b) {
                    if (sortBy === 'views') {
                        return parseInt(b.dataset.views) - parseInt(a.dataset.views);
                    } else if (sortBy === 'date') {
                        return parseInt(b.dataset.date) - parseInt(a.dataset.date);
                    }
                    // Default to original order (relevance)
                    return 0;
                });
                
                // Clear and re-append in new order
                videoResults.innerHTML = '';
                videos.forEach(function(video) {
                    videoResults.appendChild(video);
                });
            });
        }
    });
</script>

<?php
function getStatusBgClass($status) {
    switch ($status) {
        case 'pending':
            return 'bg-yellow-500';
        case 'in_progress':
            return 'bg-blue-500';
        case 'completed':
            return 'bg-green-500';
        case 'failed':
            return 'bg-red-500';
        default:
            return 'bg-gray-500';
    }
}

function getStatusIconClass($status) {
    switch ($status) {
        case 'pending':
            return 'fa-clock';
        case 'in_progress':
            return 'fa-spinner fa-spin';
        case 'completed':
            return 'fa-check';
        case 'failed':
            return 'fa-exclamation-triangle';
        default:
            return 'fa-question';
    }
}

function getStatusMessage($search) {
    switch ($search['status']) {
        case 'pending':
            return 'Your search is queued and will be processed shortly.';
        case 'in_progress':
            return 'Your search is currently being processed.';
        case 'completed':
            $count = $search['results_count'] ?? 0;
            return "Search completed with {$count} results. " . date('M j, Y g:i A', strtotime($search['updated_at']));
        case 'failed':
            return 'Search failed to complete. Please try again.';
        default:
            return 'Unknown status.';
    }
}
?>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
