<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Money Making Strategies</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                Discover profitable opportunities from analyzed videos
            </p>
        </div>
        <div class="flex space-x-2">
            <a href="/momentum/youtube-agent/search" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-search mr-2"></i> New Search
            </a>
            <a href="/momentum/youtube-agent" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-home mr-2"></i> Dashboard
            </a>
        </div>
    </div>
    
    <!-- Brigade Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Filter by Brigade</h2>
        <div class="flex flex-wrap gap-2">
            <a href="/momentum/youtube-agent/money-making-strategies" class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium <?= empty($currentBrigade) ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' ?>">
                All Strategies
            </a>
            <a href="/momentum/youtube-agent/money-making-strategies?brigade=content_creation" class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium <?= $currentBrigade === 'content_creation' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' ?>">
                Content Creation
            </a>
            <a href="/momentum/youtube-agent/money-making-strategies?brigade=lead_generation" class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium <?= $currentBrigade === 'lead_generation' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' ?>">
                Lead Generation
            </a>
            <a href="/momentum/youtube-agent/money-making-strategies?brigade=customer_support" class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium <?= $currentBrigade === 'customer_support' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' ?>">
                Customer Support
            </a>
            <a href="/momentum/youtube-agent/money-making-strategies?brigade=data_analysis" class="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium <?= $currentBrigade === 'data_analysis' ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600' ?>">
                Data Analysis
            </a>
        </div>
    </div>
    
    <?php if (empty($strategies)): ?>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center">
            <div class="text-gray-500 dark:text-gray-400 mb-4">
                <i class="fas fa-dollar-sign text-4xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No strategies found</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
                <?php if (!empty($currentBrigade)): ?>
                    No strategies found for the <?= ucfirst(str_replace('_', ' ', $currentBrigade)) ?> brigade.
                <?php else: ?>
                    You haven't analyzed any videos yet. Search for videos and analyze them to discover money-making strategies.
                <?php endif; ?>
            </p>
            <a href="/momentum/youtube-agent/search" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-search mr-2"></i> Search for Videos
            </a>
        </div>
    <?php else: ?>
        <div class="grid grid-cols-1 gap-6">
            <?php foreach ($strategies as $strategy): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="md:flex">
                        <div class="md:flex-shrink-0">
                            <?php if (!empty($strategy['thumbnail_url'])): ?>
                                <img class="h-48 w-full object-cover md:w-48" src="<?= $strategy['thumbnail_url'] ?>" alt="Video thumbnail">
                            <?php else: ?>
                                <div class="h-48 w-full md:w-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                    <i class="fas fa-video text-gray-400 dark:text-gray-500 text-4xl"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="p-4 flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($strategy['strategy_name']) ?></h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= getBrigadeBadgeClass($strategy['suitable_brigade']) ?>">
                                    <?= ucfirst(str_replace('_', ' ', $strategy['suitable_brigade'])) ?>
                                </span>
                            </div>
                            
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                <?= htmlspecialchars($strategy['description']) ?>
                            </p>
                            
                            <div class="grid grid-cols-2 gap-2 mb-3">
                                <div>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">Potential Revenue:</span>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($strategy['potential_revenue']) ?></p>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">Time Investment:</span>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($strategy['time_investment']) ?></p>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="text-xs text-gray-500 dark:text-gray-400 mr-1">Difficulty:</span>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium <?= getDifficultyBadgeClass($strategy['implementation_difficulty']) ?>">
                                        <?= ucfirst(str_replace('_', ' ', $strategy['implementation_difficulty'])) ?>
                                    </span>
                                </div>
                                
                                <div class="flex space-x-2">
                                    <?php if (!empty($strategy['youtube_id'])): ?>
                                        <a href="/momentum/youtube-agent/view-video/<?= $strategy['video_id'] ?>" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                            <i class="fas fa-video mr-1"></i> Video
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="/momentum/youtube-agent/view-analysis/<?= $strategy['analysis_id'] ?>" class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        <i class="fas fa-chart-line mr-1"></i> Analysis
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<?php
function getDifficultyBadgeClass($difficulty) {
    switch ($difficulty) {
        case 'low':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'medium':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
        case 'high':
            return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
        case 'very_high':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
}

function getBrigadeBadgeClass($brigade) {
    switch ($brigade) {
        case 'content_creation':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'lead_generation':
            return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
        case 'customer_support':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'data_analysis':
            return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
}
?>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
