<?php
/**
 * <PERSON><PERSON> Schedule - Detailed View
 */
?>

<link rel="stylesheet" href="/momentum/public/css/astrology.css">

<div class="astrology-container">
    <!-- Header -->
    <div class="astrology-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-clock text-purple-600"></i>
                <h1 class="title-gradient">Rahu <PERSON> Schedule</h1>
                <i class="fas fa-calendar text-purple-600"></i>
            </div>
            <p class="header-subtitle">Complete Weekly Schedule for Sri Lanka</p>
        </div>
    </div>

    <!-- Navigation Breadcrumb -->
    <div class="breadcrumb-nav">
        <div class="breadcrumb-content">
            <a href="/momentum/astrology" class="breadcrumb-link">
                <i class="fas fa-star"></i> Astrology
            </a>
            <i class="fas fa-chevron-right"></i>
            <span class="breadcrumb-current">Rahu <PERSON> Schedule</span>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Current Status -->
        <?php if ($isCurrentlyRahuKalaya): ?>
            <div class="alert alert-warning rahu-kalaya-alert">
                <div class="alert-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>Rahu Kalaya Active Now!</strong>
                        <p>Current time is within today's Rahu Kalaya period (<?= $rahuKalayaToday['formatted_time'] ?>).</p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Detailed Schedule Tables -->
        <div class="schedule-tables-container">
            <!-- Daytime Rahu Kalaya Table -->
            <div class="schedule-table-container">
                <div class="table-header">
                    <h2 class="table-title">
                        <i class="fas fa-sun"></i>
                        Daytime Rahu Kalaya Schedule
                    </h2>
                    <p class="table-subtitle">All times are in Sri Lanka Standard Time (UTC+5:30)</p>
                </div>

                <div class="rahu-kalaya-table">
                    <div class="table-header-row">
                        <div class="table-cell header-cell">Day</div>
                        <div class="table-cell header-cell">Start Time</div>
                        <div class="table-cell header-cell">End Time</div>
                        <div class="table-cell header-cell">Duration</div>
                        <div class="table-cell header-cell">Status</div>
                    </div>

                    <?php foreach ($weeklyRahuKalaya as $timing): ?>
                        <?php
                            $isToday = $timing['day'] === $currentDay;
                            $isActive = $isToday && $isCurrentlyRahuKalaya && $currentRahuKalayaStatus['type'] === 'day';
                            $startTime = DateTime::createFromFormat('H:i', $timing['start_time']);
                            $endTime = DateTime::createFromFormat('H:i', $timing['end_time']);
                            $duration = $startTime->diff($endTime);
                            $durationText = $duration->format('%h hours %i minutes');
                        ?>
                        <div class="table-row <?= $isToday ? 'current-day-row' : '' ?> <?= $isActive ? 'active-row' : '' ?>">
                            <div class="table-cell day-cell">
                                <span class="day-name"><?= $timing['day'] ?></span>
                                <?php if ($isToday): ?>
                                    <span class="today-badge">Today</span>
                                <?php endif; ?>
                            </div>
                            <div class="table-cell time-cell">
                                <?= date('g:i A', strtotime($timing['start_time'])) ?>
                            </div>
                            <div class="table-cell time-cell">
                                <?= date('g:i A', strtotime($timing['end_time'])) ?>
                            </div>
                            <div class="table-cell duration-cell">
                                <?= $durationText ?>
                            </div>
                            <div class="table-cell status-cell">
                                <?php if ($isActive): ?>
                                    <span class="status-badge active">
                                        <i class="fas fa-circle"></i> Active Now
                                    </span>
                                <?php elseif ($isToday): ?>
                                    <span class="status-badge today">
                                        <i class="fas fa-calendar-day"></i> Today
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge inactive">
                                        <i class="fas fa-clock"></i> Scheduled
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Nighttime Rahu Kalaya Table -->
            <div class="schedule-table-container night-table">
                <div class="table-header">
                    <h2 class="table-title">
                        <i class="fas fa-moon"></i>
                        Nighttime Rahu Kalaya Schedule (Ratri Rahu Kaalaya)
                    </h2>
                    <p class="table-subtitle">All times are in Sri Lanka Standard Time (UTC+5:30)</p>
                </div>

                <div class="rahu-kalaya-table">
                    <div class="table-header-row night-header">
                        <div class="table-cell header-cell">Day (Night of)</div>
                        <div class="table-cell header-cell">Start Time</div>
                        <div class="table-cell header-cell">End Time</div>
                        <div class="table-cell header-cell">Duration</div>
                        <div class="table-cell header-cell">Status</div>
                    </div>

                    <?php foreach ($weeklyNightRahuKalaya as $timing): ?>
                        <?php
                            $isToday = $timing['day'] === $currentDay;
                            $isActive = $isToday && $isCurrentlyRahuKalaya && $currentRahuKalayaStatus['type'] === 'night';
                            $startTime = DateTime::createFromFormat('H:i', $timing['start_time']);
                            $endTime = DateTime::createFromFormat('H:i', $timing['end_time']);

                            // Handle midnight crossing for duration calculation
                            if ($timing['end_time'] === '24:00') {
                                $endTime = DateTime::createFromFormat('H:i', '23:59');
                                $duration = $startTime->diff($endTime);
                                $durationText = '1 hour 30 minutes';
                            } elseif ($timing['start_time'] === '00:00') {
                                $durationText = '1 hour 30 minutes';
                            } else {
                                $duration = $startTime->diff($endTime);
                                $durationText = $duration->format('%h hours %i minutes');
                            }
                        ?>
                        <div class="table-row night-row <?= $isToday ? 'current-day-row' : '' ?> <?= $isActive ? 'active-row' : '' ?>">
                            <div class="table-cell day-cell">
                                <span class="day-name"><?= $timing['day'] ?> Night</span>
                                <?php if ($isToday): ?>
                                    <span class="today-badge">Tonight</span>
                                <?php endif; ?>
                            </div>
                            <div class="table-cell time-cell">
                                <?= $timing['start_time'] === '24:00' ? '12:00 AM' : date('g:i A', strtotime($timing['start_time'])) ?>
                            </div>
                            <div class="table-cell time-cell">
                                <?= $timing['end_time'] === '24:00' ? '12:00 AM' : date('g:i A', strtotime($timing['end_time'])) ?>
                            </div>
                            <div class="table-cell duration-cell">
                                <?= $durationText ?>
                            </div>
                            <div class="table-cell status-cell">
                                <?php if ($isActive): ?>
                                    <span class="status-badge active">
                                        <i class="fas fa-circle"></i> Active Now
                                    </span>
                                <?php elseif ($isToday): ?>
                                    <span class="status-badge today">
                                        <i class="fas fa-moon"></i> Tonight
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge inactive">
                                        <i class="fas fa-clock"></i> Scheduled
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Information Cards -->
        <div class="info-cards-grid">
            <!-- Calculation Method -->
            <div class="info-card">
                <div class="info-card-header">
                    <i class="fas fa-calculator"></i>
                    <h3>Calculation Method</h3>
                </div>
                <div class="info-card-content">
                    <p>Rahu Kalaya timings are calculated by dividing the time between sunrise and sunset into eight equal segments. Each day of the week is assigned a specific segment as the Rahu Kalaya period.</p>
                </div>
            </div>

            <!-- What to Avoid -->
            <div class="info-card">
                <div class="info-card-header">
                    <i class="fas fa-ban"></i>
                    <h3>Activities to Avoid</h3>
                </div>
                <div class="info-card-content">
                    <ul class="avoid-list">
                        <li>Starting new business ventures</li>
                        <li>Signing important contracts</li>
                        <li>Beginning long journeys</li>
                        <li>Making major purchases</li>
                        <li>Conducting interviews</li>
                        <li>Starting new projects</li>
                    </ul>
                </div>
            </div>

            <!-- Exceptions -->
            <div class="info-card">
                <div class="info-card-header">
                    <i class="fas fa-info-circle"></i>
                    <h3>Important Notes</h3>
                </div>
                <div class="info-card-content">
                    <p>Emergency activities and continuing ongoing work are generally acceptable during Rahu Kalaya. The restriction mainly applies to initiating new activities.</p>
                    <p class="mt-2"><strong>Location:</strong> These timings are calculated for Sri Lanka. Times may vary for other locations.</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3 class="actions-title">Quick Actions</h3>
            <div class="actions-grid">
                <a href="/momentum/astrology" class="action-card">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Dashboard</span>
                </a>
                <a href="/momentum/astrology/info" class="action-card">
                    <i class="fas fa-book"></i>
                    <span>Learn More</span>
                </a>
                <button onclick="window.print()" class="action-card">
                    <i class="fas fa-print"></i>
                    <span>Print Schedule</span>
                </button>
                <button onclick="exportToCalendar()" class="action-card">
                    <i class="fas fa-calendar-plus"></i>
                    <span>Export to Calendar</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Additional styles for the detailed table view */
.schedule-tables-container {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.night-table {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 1px solid rgba(139, 92, 246, 0.5);
}

.night-table .table-title {
    color: #111827 !important;
    font-weight: 700;
}

.night-table .table-subtitle {
    color: #374151 !important;
    font-weight: 600;
}

.night-header {
    background: linear-gradient(135deg, #1e1b4b, #4c1d95) !important;
    color: #f9fafb !important;
}

.night-row {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.night-row:hover {
    background: rgba(249, 250, 251, 0.98) !important;
}

.night-row .day-name {
    color: #111827 !important;
    font-weight: 700;
}

.night-row .time-cell {
    color: #1f2937 !important;
    font-weight: 600;
}

.night-row .duration-cell {
    color: #374151 !important;
    font-weight: 600;
}

.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
}

.breadcrumb-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.breadcrumb-link:hover {
    color: white;
    text-decoration: none;
}

.breadcrumb-current {
    color: white;
    font-weight: 500;
}

.schedule-table-container {
    background: rgba(255, 255, 255, 0.98) !important;
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.table-header {
    text-align: center;
    margin-bottom: 2rem;
}

.table-title {
    font-size: 1.75rem;
    font-weight: bold;
    color: #1f2937 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 0 0 0.5rem 0;
}

.table-subtitle {
    color: #374151 !important;
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
}

.rahu-kalaya-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-header-row, .table-row {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr 1fr 1.2fr;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    align-items: center;
}

.table-header-row {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: bold;
}

.table-row {
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid rgba(139, 92, 246, 0.2);
    transition: all 0.3s ease;
}

.table-row:hover {
    background: rgba(249, 250, 251, 0.98) !important;
    transform: translateX(4px);
}

.current-day-row {
    background: linear-gradient(135deg, #e0e7ff, #c7d2fe) !important;
    border-color: #6366f1;
}

.active-row {
    background: linear-gradient(135deg, #fef3c7, #fde68a) !important;
    border-color: #f59e0b;
    animation: pulse 2s infinite;
}

.day-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.day-name {
    font-weight: 600;
    color: #1f2937 !important;
}

.today-badge {
    background: #6366f1 !important;
    color: white !important;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
    font-weight: 600;
}

.time-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #111827 !important;
}

.duration-cell {
    color: #374151 !important;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-badge.active {
    background: #fef3c7 !important;
    color: #92400e !important;
    border: 1px solid #f59e0b;
}

.status-badge.today {
    background: #e0e7ff !important;
    color: #3730a3 !important;
    border: 1px solid #6366f1;
}

.status-badge.inactive {
    background: #f3f4f6 !important;
    color: #6b7280 !important;
    border: 1px solid #d1d5db;
}

/* Night table status badges */
.night-row .status-badge.active {
    background: #fef3c7 !important;
    color: #92400e !important;
    border: 1px solid #f59e0b;
}

.night-row .status-badge.today {
    background: #e0e7ff !important;
    color: #3730a3 !important;
    border: 1px solid #6366f1;
}

.night-row .status-badge.inactive {
    background: #f3f4f6 !important;
    color: #6b7280 !important;
    border: 1px solid #d1d5db;
}

.info-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.info-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-card-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #4c1d95;
}

.info-card-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: bold;
}

.info-card-content {
    color: #374151;
    line-height: 1.6;
}

.avoid-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.avoid-list li {
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.avoid-list li::before {
    content: '×';
    position: absolute;
    left: 0;
    color: #ef4444;
    font-weight: bold;
    font-size: 1.125rem;
}

@media (max-width: 768px) {
    .table-header-row, .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        text-align: center;
    }

    .table-cell {
        padding: 0.25rem 0;
    }

    .header-cell::before {
        content: attr(data-label) ': ';
        font-weight: normal;
        color: rgba(255, 255, 255, 0.8);
    }
}
</style>

<script>
function exportToCalendar() {
    // Simple calendar export functionality
    alert('Calendar export feature coming soon!');
}

// Auto-refresh every minute
setInterval(() => {
    location.reload();
}, 60000);
</script>
