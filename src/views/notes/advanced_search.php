<!-- Advanced Note Search -->
<div class="py-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center mb-4">
                <a href="/momentum/notes" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mr-4">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    <i class="fas fa-search mr-3 text-blue-600"></i>Advanced Note Search
                </h1>
            </div>
            <p class="text-gray-600 dark:text-gray-300">Find exactly what you're looking for with powerful search filters</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Search Filters Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 sticky top-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-filter mr-2 text-blue-500"></i>Search Filters
                    </h3>
                    
                    <form id="advancedSearchForm" class="space-y-4">
                        <!-- Search Query -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Terms</label>
                            <input type="text" name="q" value="<?= View::escape($filters['q'] ?? '') ?>"
                                   class="w-full px-3 py-2 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                                   placeholder="Keywords, phrases...">
                        </div>

                        <!-- Category Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                            <select name="category" class="w-full px-3 py-2 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">All Categories</option>
                                <option value="Work" <?= ($filters['category'] ?? '') === 'Work' ? 'selected' : '' ?>>Work</option>
                                <option value="Personal" <?= ($filters['category'] ?? '') === 'Personal' ? 'selected' : '' ?>>Personal</option>
                                <option value="Projects" <?= ($filters['category'] ?? '') === 'Projects' ? 'selected' : '' ?>>Projects</option>
                                <option value="Learning" <?= ($filters['category'] ?? '') === 'Learning' ? 'selected' : '' ?>>Learning</option>
                                <option value="Health" <?= ($filters['category'] ?? '') === 'Health' ? 'selected' : '' ?>>Health</option>
                                <option value="Finance" <?= ($filters['category'] ?? '') === 'Finance' ? 'selected' : '' ?>>Finance</option>
                            </select>
                        </div>

                        <!-- Priority Filter -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Priority</label>
                            <select name="priority" class="w-full px-3 py-2 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">Any Priority</option>
                                <option value="high" <?= ($filters['priority'] ?? '') === 'high' ? 'selected' : '' ?>>High</option>
                                <option value="medium" <?= ($filters['priority'] ?? '') === 'medium' ? 'selected' : '' ?>>Medium</option>
                                <option value="low" <?= ($filters['priority'] ?? '') === 'low' ? 'selected' : '' ?>>Low</option>
                            </select>
                        </div>

                        <!-- Date Range -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</label>
                            <div class="space-y-2">
                                <input type="date" name="date_from" value="<?= View::escape($filters['date_from'] ?? '') ?>"
                                       class="w-full px-3 py-2 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                                <input type="date" name="date_to" value="<?= View::escape($filters['date_to'] ?? '') ?>"
                                       class="w-full px-3 py-2 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            </div>
                        </div>

                        <!-- Status Filters -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_favorite" value="1" <?= ($filters['is_favorite'] ?? '') === '1' ? 'checked' : '' ?>
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Favorites only</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="is_pinned" value="1" <?= ($filters['is_pinned'] ?? '') === '1' ? 'checked' : '' ?>
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Pinned only</span>
                                </label>
                            </div>
                        </div>

                        <!-- Search Button -->
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-4 py-2 font-medium transition-colors duration-200">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>

                        <!-- Clear Filters -->
                        <button type="button" onclick="clearFilters()" class="w-full bg-gray-600 hover:bg-gray-700 text-white rounded-lg px-4 py-2 font-medium transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i>Clear Filters
                        </button>
                    </form>
                </div>
            </div>

            <!-- Search Results -->
            <div class="lg:col-span-3">
                <!-- Search Analytics -->
                <?php if (isset($analytics)): ?>
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-chart-bar mr-2 text-green-500"></i>Search Results
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?= $analytics['total_matches'] ?? 0 ?></div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Total Matches</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?= count($analytics['by_category'] ?? []) ?></div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Categories</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400"><?= !empty($filters['q']) ? 'Yes' : 'No' ?></div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Text Search</div>
                        </div>
                    </div>
                    
                    <?php if (!empty($analytics['by_category'])): ?>
                    <div class="mt-4">
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Results by Category</h4>
                        <div class="flex flex-wrap gap-2">
                            <?php foreach ($analytics['by_category'] as $cat): ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    <?= View::escape($cat['category'] ?: 'Uncategorized') ?>: <?= $cat['count'] ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Results List -->
                <?php if (!empty($results)): ?>
                    <div class="space-y-4">
                        <?php foreach ($results as $note): ?>
                            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-2">
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                                <a href="/momentum/notes/view/<?= $note['id'] ?>" class="hover:text-blue-600 dark:hover:text-blue-400">
                                                    <?= View::escape($note['title']) ?>
                                                </a>
                                            </h3>
                                            <?php if ($note['is_pinned']): ?>
                                                <i class="fas fa-thumbtack text-yellow-500" title="Pinned"></i>
                                            <?php endif; ?>
                                            <?php if ($note['is_favorite']): ?>
                                                <i class="fas fa-heart text-red-500" title="Favorite"></i>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="text-gray-600 dark:text-gray-300 mb-3 line-clamp-3">
                                            <?= View::escape(substr($note['content'], 0, 200)) ?>...
                                        </div>
                                        
                                        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                            <?php if ($note['category']): ?>
                                                <span class="inline-flex items-center">
                                                    <i class="fas fa-folder mr-1"></i><?= View::escape($note['category']) ?>
                                                </span>
                                            <?php endif; ?>
                                            <span class="inline-flex items-center">
                                                <i class="fas fa-calendar mr-1"></i><?= date('M j, Y', strtotime($note['updated_at'])) ?>
                                            </span>
                                            <?php if (isset($note['relevance_score'])): ?>
                                                <span class="inline-flex items-center">
                                                    <i class="fas fa-star mr-1"></i>Relevance: <?= $note['relevance_score'] ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if (!empty($note['tags'])): ?>
                                            <div class="flex flex-wrap gap-1 mt-2">
                                                <?php 
                                                $tags = array_slice(explode(',', $note['tags']), 0, 5);
                                                foreach ($tags as $tag):
                                                    $tag = trim($tag);
                                                    if (!empty($tag)):
                                                ?>
                                                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                                        #<?= View::escape($tag) ?>
                                                    </span>
                                                <?php 
                                                    endif;
                                                endforeach; 
                                                ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="ml-4 flex flex-col gap-2">
                                        <a href="/momentum/notes/view/<?= $note['id'] ?>" 
                                           class="inline-flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors duration-200">
                                            <i class="fas fa-eye mr-1"></i>View
                                        </a>
                                        <a href="/momentum/notes/edit/<?= $note['id'] ?>" 
                                           class="inline-flex items-center px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors duration-200">
                                            <i class="fas fa-edit mr-1"></i>Edit
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center">
                        <i class="fas fa-search text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No notes found</h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-6">Try adjusting your search criteria or create a new note.</p>
                        <a href="/momentum/notes/create" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>Create New Note
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('advancedSearchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            params.append(key, value);
        }
    }
    
    window.location.href = '/momentum/notes/advanced-search?' + params.toString();
});

function clearFilters() {
    document.getElementById('advancedSearchForm').reset();
    window.location.href = '/momentum/notes/advanced-search';
}

// Auto-submit on filter change for better UX
document.querySelectorAll('#advancedSearchForm select, #advancedSearchForm input[type="checkbox"]').forEach(element => {
    element.addEventListener('change', function() {
        document.getElementById('advancedSearchForm').dispatchEvent(new Event('submit'));
    });
});
</script>
