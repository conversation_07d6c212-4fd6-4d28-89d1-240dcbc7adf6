<?php
// Enhanced Note Card Component with ADHD-friendly features
$isPinned = $note['is_pinned'] ?? false;
$isFavorite = $note['is_favorite'] ?? false;
$priority = $note['priority_level'] ?? 'medium';
$colorCode = $note['color_code'] ?? null;
$wordCount = $note['word_count'] ?? 0;
$readingTime = $note['reading_time'] ?? 1;

// Priority colors and icons
$priorityConfig = [
    'high' => ['color' => 'red', 'icon' => 'fas fa-exclamation-circle', 'bg' => 'bg-red-50 dark:bg-red-900/20', 'border' => 'border-red-200 dark:border-red-800'],
    'medium' => ['color' => 'yellow', 'icon' => 'fas fa-minus-circle', 'bg' => 'bg-yellow-50 dark:bg-yellow-900/20', 'border' => 'border-yellow-200 dark:border-yellow-800'],
    'low' => ['color' => 'green', 'icon' => 'fas fa-check-circle', 'bg' => 'bg-green-50 dark:bg-green-900/20', 'border' => 'border-green-200 dark:border-green-800']
];

$priorityInfo = $priorityConfig[$priority] ?? $priorityConfig['medium'];
?>

<div class="group relative bg-white dark:bg-gray-800 rounded-xl shadow-sm border-2 <?= $priorityInfo['border'] ?> hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 <?= $isPinned ? 'ring-2 ring-yellow-400 dark:ring-yellow-500' : '' ?>"
     data-note-id="<?= $note['id'] ?>"
     <?= $colorCode ? 'style="border-left: 4px solid ' . $colorCode . ';"' : '' ?>>
     
    <!-- Priority Indicator -->
    <div class="absolute top-3 right-3 flex items-center space-x-2">
        <?php if ($isFavorite): ?>
            <button onclick="toggleFavorite(<?= $note['id'] ?>)" class="text-pink-500 hover:text-pink-600 transition-colors duration-200" title="Remove from favorites">
                <i class="fas fa-heart"></i>
            </button>
        <?php else: ?>
            <button onclick="toggleFavorite(<?= $note['id'] ?>)" class="text-gray-300 hover:text-pink-500 transition-colors duration-200" title="Add to favorites">
                <i class="far fa-heart"></i>
            </button>
        <?php endif; ?>
        
        <div class="flex items-center text-<?= $priorityInfo['color'] ?>-500" title="<?= ucfirst($priority) ?> priority">
            <i class="<?= $priorityInfo['icon'] ?> text-sm"></i>
        </div>
        
        <?php if ($isPinned): ?>
            <button onclick="togglePin(<?= $note['id'] ?>)" class="text-yellow-500 hover:text-yellow-600 transition-colors duration-200" title="Unpin note">
                <i class="fas fa-thumbtack"></i>
            </button>
        <?php else: ?>
            <button onclick="togglePin(<?= $note['id'] ?>)" class="text-gray-300 hover:text-yellow-500 transition-colors duration-200" title="Pin note">
                <i class="fas fa-thumbtack"></i>
            </button>
        <?php endif; ?>
    </div>

    <div class="p-6">
        <!-- Note Header -->
        <div class="mb-4">
            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 pr-16 line-clamp-2">
                <a href="/momentum/notes/view/<?= $note['id'] ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                    <?= View::escape($note['title']) ?>
                </a>
            </h3>
            
            <!-- Metadata Row -->
            <div class="flex flex-wrap items-center gap-2 text-xs">
                <?php if (!empty($note['category'])): ?>
                    <span class="inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 font-medium">
                        <i class="fas fa-folder mr-1"></i><?= View::escape($note['category']) ?>
                    </span>
                <?php endif; ?>
                
                <span class="inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300">
                    <i class="fas fa-clock mr-1"></i><?= $readingTime ?> min read
                </span>
                
                <span class="inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300">
                    <i class="fas fa-file-word mr-1"></i><?= number_format($wordCount) ?> words
                </span>
            </div>
        </div>

        <!-- Note Content Preview -->
        <div class="mb-4">
            <div class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3 leading-relaxed">
                <?= nl2br(View::escape(substr($note['content'] ?? '', 0, 200))) ?><?= strlen($note['content'] ?? '') > 200 ? '...' : '' ?>
            </div>
        </div>

        <!-- Tags -->
        <?php if (!empty($note['tags'])): ?>
            <div class="mb-4">
                <div class="flex flex-wrap gap-1">
                    <?php 
                    $tags = array_slice(explode(',', $note['tags']), 0, 3); // Show max 3 tags
                    foreach ($tags as $tag):
                        $tag = trim($tag);
                        if (!empty($tag)):
                    ?>
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                            #<?= View::escape($tag) ?>
                        </span>
                    <?php 
                        endif;
                    endforeach; 
                    
                    $totalTags = count(explode(',', $note['tags']));
                    if ($totalTags > 3):
                    ?>
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                            +<?= $totalTags - 3 ?> more
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer with Actions -->
        <div class="flex justify-between items-center pt-4 border-t border-gray-100 dark:border-gray-700">
            <div class="text-xs text-gray-500 dark:text-gray-400">
                <i class="far fa-clock mr-1"></i>
                <span title="<?= date('M j, Y \a\t g:i A', strtotime($note['updated_at'])) ?>">
                    <?= View::formatDateTime($note['updated_at']) ?>
                </span>
                <?php if ($note['auto_saved'] ?? false): ?>
                    <span class="ml-2 text-orange-500" title="Auto-saved">
                        <i class="fas fa-save"></i>
                    </span>
                <?php endif; ?>
            </div>
            
            <!-- Quick Actions -->
            <div class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <a href="/momentum/notes/view/<?= $note['id'] ?>" 
                   class="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200" 
                   title="View note">
                    <i class="fas fa-eye"></i>
                </a>
                <a href="/momentum/notes/edit/<?= $note['id'] ?>" 
                   class="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors duration-200" 
                   title="Edit note">
                    <i class="fas fa-edit"></i>
                </a>
                <button onclick="duplicateNote(<?= $note['id'] ?>)" 
                        class="p-2 text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200" 
                        title="Duplicate note">
                    <i class="fas fa-copy"></i>
                </button>
                <button onclick="deleteNote(<?= $note['id'] ?>)" 
                        class="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors duration-200" 
                        title="Delete note">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Auto-save indicator -->
    <?php if ($note['auto_saved'] ?? false): ?>
        <div class="absolute top-0 left-0 w-2 h-2 bg-orange-400 rounded-full transform -translate-x-1 -translate-y-1" title="Auto-saved"></div>
    <?php endif; ?>
</div>
