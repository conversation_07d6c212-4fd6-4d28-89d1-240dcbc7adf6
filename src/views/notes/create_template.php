<!-- Create Note Template -->
<div class="py-6 bg-gradient-to-br from-purple-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center mb-4">
                <a href="/momentum/notes" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mr-4">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    <i class="fas fa-file-alt mr-3 text-purple-600"></i>Create Note Template
                </h1>
            </div>
            <p class="text-gray-600 dark:text-gray-300">Create reusable templates to speed up note creation</p>
        </div>

        <!-- Template Form -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <form id="templateForm" class="p-6 space-y-6">
                <!-- Template Name -->
                <div>
                    <label for="templateName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-tag mr-2 text-purple-500"></i>Template Name *
                    </label>
                    <input type="text" id="templateName" name="name" required
                           class="w-full px-4 py-3 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                           placeholder="e.g., Meeting Notes, Daily Journal, Project Planning">
                </div>

                <!-- Description -->
                <div>
                    <label for="templateDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-info-circle mr-2 text-blue-500"></i>Description
                    </label>
                    <textarea id="templateDescription" name="description" rows="3"
                              class="w-full px-4 py-3 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-700 dark:text-white resize-none"
                              placeholder="Brief description of when to use this template"></textarea>
                </div>

                <!-- Category -->
                <div>
                    <label for="templateCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-folder mr-2 text-green-500"></i>Category
                    </label>
                    <select id="templateCategory" name="category"
                            class="w-full px-4 py-3 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-700 dark:text-white">
                        <option value="General">General</option>
                        <option value="Work">Work</option>
                        <option value="Personal">Personal</option>
                        <option value="Projects">Projects</option>
                        <option value="Meetings">Meetings</option>
                        <option value="Learning">Learning</option>
                        <option value="Health">Health</option>
                        <option value="Finance">Finance</option>
                    </select>
                </div>

                <!-- Template Content -->
                <div>
                    <label for="templateContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-edit mr-2 text-orange-500"></i>Template Content
                    </label>
                    <div class="mb-2">
                        <div class="flex flex-wrap gap-2 text-xs text-gray-500 dark:text-gray-400">
                            <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">💡 Tip: Use placeholders like [Date], [Name], [Topic]</span>
                        </div>
                    </div>
                    <textarea id="templateContent" name="template_content" rows="12"
                              class="w-full px-4 py-3 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-700 dark:text-white font-mono text-sm"
                              placeholder="Enter your template content here...

Example:
# [Topic] - [Date]

## Agenda
- 

## Notes
- 

## Action Items
- [ ] 

## Next Steps
- "></textarea>
                </div>

                <!-- Default Tags -->
                <div>
                    <label for="templateTags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-tags mr-2 text-pink-500"></i>Default Tags
                    </label>
                    <input type="text" id="templateTags" name="default_tags"
                           class="w-full px-4 py-3 rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                           placeholder="template, work, meeting (comma-separated)">
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">These tags will be automatically added to notes created from this template</p>
                </div>

                <!-- Public Template Option -->
                <div class="flex items-center">
                    <input type="checkbox" id="isPublic" name="is_public" value="1"
                           class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                    <label for="isPublic" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        <i class="fas fa-globe mr-1 text-blue-500"></i>Make this template public (other users can use it)
                    </label>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200 dark:border-gray-600">
                    <button type="submit" 
                            class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                        <i class="fas fa-save mr-2"></i>Create Template
                    </button>
                    <button type="button" onclick="previewTemplate()"
                            class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                        <i class="fas fa-eye mr-2"></i>Preview
                    </button>
                    <a href="/momentum/notes" 
                       class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-semibold transition-all duration-200">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Template Examples -->
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>Template Examples
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">Meeting Notes</h4>
                    <pre class="text-xs text-gray-600 dark:text-gray-300 whitespace-pre-wrap"># Meeting: [Topic] - [Date]

**Attendees:** [Names]
**Duration:** [Time]

## Agenda
- 

## Discussion Points
- 

## Decisions Made
- 

## Action Items
- [ ] [Task] - [Assignee] - [Due Date]

## Next Meeting
- **Date:** [Next Date]
- **Topics:** </pre>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">Daily Journal</h4>
                    <pre class="text-xs text-gray-600 dark:text-gray-300 whitespace-pre-wrap"># Daily Journal - [Date]

## Today's Goals
- [ ] 
- [ ] 
- [ ] 

## What Happened
- 

## Wins & Achievements
- 

## Challenges
- 

## Lessons Learned
- 

## Tomorrow's Priorities
- 
- 
- 

## Mood: [1-10]
## Energy Level: [1-10]</pre>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div id="previewModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Template Preview</h3>
            <button onclick="closePreview()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6 overflow-y-auto max-h-[70vh]">
            <div id="previewContent" class="prose dark:prose-invert max-w-none"></div>
        </div>
        <div class="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-600">
            <button onclick="closePreview()" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg">Close</button>
        </div>
    </div>
</div>

<script>
document.getElementById('templateForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await fetch('/momentum/notes/create-template', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Template created successfully!');
            window.location.href = '/momentum/notes';
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('Error creating template: ' + error.message);
    }
});

function previewTemplate() {
    const content = document.getElementById('templateContent').value;
    const name = document.getElementById('templateName').value || 'Untitled Template';
    
    if (!content.trim()) {
        alert('Please enter some template content to preview');
        return;
    }
    
    // Simple markdown-like preview
    let html = content
        .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
        .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mb-3">$1</h2>')
        .replace(/^### (.*$)/gm, '<h3 class="text-lg font-medium mb-2">$1</h3>')
        .replace(/^\* (.*$)/gm, '<li class="ml-4">$1</li>')
        .replace(/^- (.*$)/gm, '<li class="ml-4">$1</li>')
        .replace(/^- \[ \] (.*$)/gm, '<li class="ml-4"><input type="checkbox" class="mr-2" disabled> $1</li>')
        .replace(/\n/g, '<br>');
    
    document.getElementById('previewContent').innerHTML = `
        <h2 class="text-xl font-bold mb-4">${name}</h2>
        <div class="border-l-4 border-purple-500 pl-4">
            ${html}
        </div>
    `;
    
    document.getElementById('previewModal').classList.remove('hidden');
}

function closePreview() {
    document.getElementById('previewModal').classList.add('hidden');
}

// Auto-resize textarea
document.getElementById('templateContent').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
</script>
