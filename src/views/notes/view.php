<!-- Clean Note View - No Height Constraints -->
<style>
    /* Minimal CSS - Only fix specific height issues */
    .note-view-container {
        height: auto;
        min-height: 0;
    }

    .note-content-area {
        height: auto;
        min-height: 0;
    }

    /* Ensure content containers have controlled heights */
    .content-preview {
        max-height: 200px;
        overflow-y: auto;
        min-height: 50px;
        padding: 10px;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
    }

    .content-expanded {
        max-height: none;
        overflow-y: visible;
        min-height: 50px;
        padding: 10px;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
    }

    /* Ensure the note content is visible */
    .note-content {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: #374151 !important;
        line-height: 1.6 !important;
    }

    /* Dark mode support */
    .dark .note-content {
        color: #d1d5db !important;
    }
</style>

<!-- Clean Compact Note View -->
<div class="note-view-container py-3 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
    <div class="max-w-6xl mx-auto px-3 sm:px-4 lg:px-6">
        <!-- Minimal Header -->
        <div class="mb-2">
            <div class="flex items-center justify-between mb-2">
                <!-- Title Section -->
                <div class="flex items-center flex-1 min-w-0">
                    <a href="/momentum/notes" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2 text-sm">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div class="flex-1 min-w-0">
                        <h1 class="text-lg font-bold text-gray-900 dark:text-white truncate">
                            <?= View::escape($note['title']) ?>
                        </h1>
                        <!-- Inline Metadata -->
                        <div class="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-300">
                            <span><?= date('M j', strtotime($note['updated_at'])) ?></span>
                            <?php if (!empty($note['word_count'])): ?>
                            <span>•</span>
                            <span><?= number_format($note['word_count']) ?> words</span>
                            <?php endif; ?>
                            <?php if (!empty($note['reading_time'])): ?>
                            <span>•</span>
                            <span><?= $note['reading_time'] ?> min</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Inline Action Buttons -->
                <div class="flex gap-1 ml-2">
                    <a href="/momentum/notes/edit/<?= $note['id'] ?>"
                       class="inline-flex items-center px-2 py-1 rounded text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button onclick="togglePin(<?= $note['id'] ?>)"
                            class="inline-flex items-center px-2 py-1 rounded text-xs font-medium text-white <?= $note['is_pinned'] ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-gray-600 hover:bg-gray-700' ?> transition-colors">
                        <i class="fas fa-thumbtack"></i>
                    </button>
                    <button onclick="toggleFavorite(<?= $note['id'] ?>)"
                            class="inline-flex items-center px-2 py-1 rounded text-xs font-medium text-white <?= ($note['is_favorite'] ?? 0) ? 'bg-pink-600 hover:bg-pink-700' : 'bg-gray-600 hover:bg-gray-700' ?> transition-colors">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Clean Main Content Area -->
        <div class="note-content-area grid grid-cols-1 lg:grid-cols-4 gap-3">
            <!-- Main Content Column -->
            <div class="lg:col-span-3">
                <!-- Inline Status Bar -->
                <div class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 p-2 mb-2">
                    <div class="flex flex-wrap items-center gap-1 text-xs">
                        <!-- Priority Badge -->
                        <?php
                        $priorityColors = [
                            'high' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                            'medium' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                            'low' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        ];
                        $priority = $note['priority_level'] ?? 'medium';
                        $priorityEmojis = ['high' => '🔴', 'medium' => '🟡', 'low' => '🟢'];
                        ?>
                        <span class="inline-flex items-center px-1 py-0.5 rounded <?= $priorityColors[$priority] ?>">
                            <?= $priorityEmojis[$priority] ?> <?= ucfirst($priority) ?>
                        </span>

                        <!-- Status Badges -->
                        <?php if ($note['is_pinned']): ?>
                            <span class="inline-flex items-center px-1 py-0.5 rounded bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                📌 Pinned
                            </span>
                        <?php endif; ?>

                        <?php if ($note['is_favorite'] ?? 0): ?>
                            <span class="inline-flex items-center px-1 py-0.5 rounded bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200">
                                ❤️ Favorite
                            </span>
                        <?php endif; ?>

                        <!-- Category Badge -->
                        <?php if (!empty($note['category'])): ?>
                            <a href="/momentum/notes?category=<?= urlencode($note['category']) ?>"
                               class="inline-flex items-center px-1 py-0.5 rounded bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
                                📁 <?= View::escape($note['category']) ?>
                            </a>
                        <?php endif; ?>

                        <!-- Color Indicator -->
                        <?php if (!empty($note['color_code'])): ?>
                            <span class="inline-flex items-center px-1 py-0.5 rounded border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                                <span class="w-2 h-2 rounded-full mr-1" style="background-color: <?= $note['color_code'] ?>"></span>
                                Color
                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Note Content -->
                <?php if (!empty($note['content']) && trim($note['content']) !== ''): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 mb-3">
                        <div class="flex items-center justify-between px-3 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">📄 Content</span>
                            <button onclick="toggleContent()" class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800" title="Toggle content size">
                                <i class="fas fa-expand-alt" id="expand-icon"></i>
                            </button>
                        </div>
                        <div class="p-3">
                            <div id="note-content-container" class="content-preview bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                                <div class="note-content text-gray-800 dark:text-gray-200 whitespace-pre-wrap text-sm leading-relaxed <?= !empty($note['color_code']) ? 'border-l-4 pl-3' : '' ?>"
                                     style="<?= !empty($note['color_code']) ? 'border-left-color: ' . $note['color_code'] . ';' : '' ?>">
                                    <?= nl2br(View::escape($note['content'])) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-3 text-center">
                        <div class="text-gray-400 dark:text-gray-500">
                            <i class="fas fa-file-alt text-3xl mb-3"></i>
                            <p class="font-medium">No content yet</p>
                            <p class="text-sm mt-1">Click "Edit" to add content!</p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Inline Tags Section -->
                <?php if (!empty($note['tags'])): ?>
                    <div class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 mb-2">
                        <div class="p-2">
                            <div class="flex flex-wrap items-center gap-1">
                                <span class="text-xs font-medium text-gray-600 dark:text-gray-400">🏷️ Tags:</span>
                                <?php
                                $tags = explode(',', $note['tags']);
                                foreach ($tags as $tag):
                                    $tag = trim($tag);
                                    if (!empty($tag)):
                                ?>
                                    <a href="/momentum/notes?tag=<?= urlencode($tag) ?>"
                                       class="inline-flex items-center px-1 py-0.5 rounded text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 hover:bg-purple-200 dark:hover:bg-purple-800 transition-colors">
                                        #<?= View::escape($tag) ?>
                                    </a>
                                <?php
                                    endif;
                                endforeach;
                                ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Clean Sidebar -->
            <div class="lg:col-span-1" id="sidebar">
                <!-- Micro Quick Actions -->
                <div class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 mb-2">
                    <div class="bg-gray-50 dark:bg-gray-700/50 px-2 py-1 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-xs font-medium text-gray-700 dark:text-gray-300">⚡ Actions</h3>
                    </div>
                    <div class="p-2 space-y-1">
                        <a href="/momentum/notes/edit/<?= $note['id'] ?>"
                           class="w-full inline-flex items-center justify-center px-2 py-1 rounded text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                            <i class="fas fa-edit mr-1"></i>
                            Edit
                        </a>

                        <button onclick="duplicateNote(<?= $note['id'] ?>)"
                                class="w-full inline-flex items-center justify-center px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                            <i class="fas fa-copy mr-1"></i>
                            Copy
                        </button>

                        <button onclick="shareNote(<?= $note['id'] ?>)"
                                class="w-full inline-flex items-center justify-center px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                            <i class="fas fa-share mr-1"></i>
                            Share
                        </button>

                        <button onclick="deleteNote(<?= $note['id'] ?>)"
                                class="w-full inline-flex items-center justify-center px-2 py-1 rounded text-xs font-medium text-white bg-red-600 hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-1"></i>
                            Delete
                        </button>
                    </div>
                </div>

                <!-- Micro Statistics -->
                <div class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 mb-2">
                    <div class="bg-gray-50 dark:bg-gray-700/50 px-2 py-1 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-xs font-medium text-gray-700 dark:text-gray-300">📊 Stats</h3>
                    </div>
                    <div class="p-2 space-y-1">
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-gray-600 dark:text-gray-400">Created</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                <?= date('M j', strtotime($note['created_at'])) ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-gray-600 dark:text-gray-400">Updated</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                <?= date('M j', strtotime($note['updated_at'])) ?>
                            </span>
                        </div>
                        <?php if (!empty($note['word_count'])): ?>
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-gray-600 dark:text-gray-400">Words</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                <?= number_format($note['word_count']) ?>
                            </span>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($note['reading_time'])): ?>
                        <div class="flex items-center justify-between text-xs">
                            <span class="text-gray-600 dark:text-gray-400">Read</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                <?= $note['reading_time'] ?>m
                            </span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Micro Related Notes -->
                <?php if (!empty($relatedNotes)): ?>
                <div class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700">
                    <div class="bg-gray-50 dark:bg-gray-700/50 px-2 py-1 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-xs font-medium text-gray-700 dark:text-gray-300">🔗 Related</h3>
                    </div>
                    <div class="p-2 space-y-1">
                        <?php foreach (array_slice($relatedNotes, 0, 2) as $relatedNote): ?>
                        <a href="/momentum/notes/view/<?= $relatedNote['id'] ?>"
                           class="block p-1 rounded border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <div class="font-medium text-gray-900 dark:text-white text-xs truncate">
                                <?= View::escape($relatedNote['title']) ?>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                <?= date('M j', strtotime($relatedNote['updated_at'])) ?>
                            </div>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript for ADHD-friendly interactions -->
<script>
class NoteViewManager {
    constructor() {
        this.noteId = <?= $note['id'] ?>;
        this.init();
    }

    init() {
        this.setupKeyboardShortcuts();
        this.trackLastAccessed();
        this.setupReadingControls();
    }

    setupReadingControls() {
        // Load saved reading preferences
        const savedFontSize = localStorage.getItem('adhd-font-size') || '18';
        const savedLineHeight = localStorage.getItem('adhd-line-height') || '2.0';

        this.applyReadingSettings(savedFontSize, savedLineHeight);
    }

    applyReadingSettings(fontSize, lineHeight) {
        const contentDiv = document.querySelector('.note-content');
        if (contentDiv) {
            contentDiv.style.fontSize = fontSize + 'px';
            contentDiv.style.lineHeight = lineHeight;
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // E key for edit
            if (e.key === 'e' && !e.ctrlKey && !e.metaKey && !this.isInputFocused()) {
                e.preventDefault();
                window.location.href = `/momentum/notes/edit/${this.noteId}`;
            }

            // Escape key to go back
            if (e.key === 'Escape') {
                e.preventDefault();
                window.location.href = '/momentum/notes';
            }

            // D key for duplicate
            if (e.key === 'd' && !e.ctrlKey && !e.metaKey && !this.isInputFocused()) {
                e.preventDefault();
                this.duplicateNote();
            }
        });
    }

    isInputFocused() {
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.contentEditable === 'true'
        );
    }

    async trackLastAccessed() {
        try {
            await fetch(`/momentum/notes/track-access/${this.noteId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
        } catch (error) {
            console.log('Could not track access time');
        }
    }

    async duplicateNote() {
        try {
            const response = await fetch(`/momentum/notes/duplicate/${this.noteId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Note duplicated successfully!', 'success');
                setTimeout(() => {
                    window.location.href = `/momentum/notes/edit/${result.note_id}`;
                }, 1000);
            } else {
                this.showNotification('Failed to duplicate note', 'error');
            }
        } catch (error) {
            this.showNotification('Error duplicating note', 'error');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg text-white font-medium z-50 transition-all duration-300`;

        switch (type) {
            case 'success':
                notification.classList.add('bg-green-500');
                break;
            case 'error':
                notification.classList.add('bg-red-500');
                break;
            default:
                notification.classList.add('bg-blue-500');
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
                ${message}
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('opacity-0', 'translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Global functions for button actions
async function togglePin(noteId) {
    try {
        const response = await fetch(`/momentum/notes/toggle-pin/${noteId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            location.reload();
        } else {
            alert('Failed to toggle pin status');
        }
    } catch (error) {
        alert('Error toggling pin status');
    }
}

async function toggleFavorite(noteId) {
    try {
        const response = await fetch(`/momentum/notes/toggle-favorite/${noteId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            location.reload();
        } else {
            alert('Failed to toggle favorite status');
        }
    } catch (error) {
        alert('Error toggling favorite status');
    }
}

function duplicateNote(noteId) {
    if (window.noteViewManager) {
        window.noteViewManager.duplicateNote();
    }
}

function shareNote(noteId) {
    if (navigator.share) {
        navigator.share({
            title: document.title,
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(window.location.href);
        alert('Note URL copied to clipboard!');
    }
}

function deleteNote(noteId) {
    if (confirm('Are you sure you want to delete this note? This action cannot be undone.')) {
        window.location.href = `/momentum/notes/delete/${noteId}`;
    }
}

// ADHD Reading Control Functions
function adjustFontSize(direction) {
    const contentDiv = document.querySelector('.note-content');
    if (!contentDiv) return;

    const currentSize = parseInt(window.getComputedStyle(contentDiv).fontSize);
    let newSize = currentSize;

    if (direction === 'larger' && currentSize < 28) {
        newSize = currentSize + 2;
    } else if (direction === 'smaller' && currentSize > 12) {
        newSize = currentSize - 2;
    }

    contentDiv.style.fontSize = newSize + 'px';
    localStorage.setItem('adhd-font-size', newSize);

    // Show feedback
    showReadingFeedback(`Font size: ${newSize}px`);
}

function adjustLineSpacing(direction) {
    const contentDiv = document.querySelector('.note-content');
    if (!contentDiv) return;

    const currentHeight = parseFloat(window.getComputedStyle(contentDiv).lineHeight);
    let newHeight = currentHeight;

    if (direction === 'looser' && currentHeight < 3.0) {
        newHeight = currentHeight + 0.2;
    } else if (direction === 'tighter' && currentHeight > 1.2) {
        newHeight = currentHeight - 0.2;
    }

    contentDiv.style.lineHeight = newHeight;
    localStorage.setItem('adhd-line-height', newHeight);

    // Show feedback
    showReadingFeedback(`Line spacing: ${newHeight.toFixed(1)}`);
}

// Clean toggle function for content
function toggleContent() {
    const container = document.getElementById('note-content-container');
    const icon = document.getElementById('expand-icon');

    if (!container || !icon) return;

    if (container.classList.contains('content-preview')) {
        // Expand to larger view
        container.classList.remove('content-preview');
        container.classList.add('content-expanded');
        icon.classList.remove('fa-expand-alt');
        icon.classList.add('fa-compress-alt');
    } else {
        // Collapse to preview
        container.classList.remove('content-expanded');
        container.classList.add('content-preview');
        icon.classList.remove('fa-compress-alt');
        icon.classList.add('fa-expand-alt');
    }
}

// Clean focus mode toggle
function toggleFocusMode() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.lg\\:col-span-3');

    if (!sidebar || !mainContent) return;

    if (sidebar.style.display === 'none') {
        // Exit focus mode
        sidebar.style.display = 'block';
        mainContent.classList.remove('lg:col-span-4');
        mainContent.classList.add('lg:col-span-3');
    } else {
        // Enter focus mode
        sidebar.style.display = 'none';
        mainContent.classList.remove('lg:col-span-3');
        mainContent.classList.add('lg:col-span-4');
    }
}

function showReadingFeedback(message) {
    // Remove existing feedback
    const existing = document.querySelector('.reading-feedback');
    if (existing) {
        existing.remove();
    }

    // Create new feedback
    const feedback = document.createElement('div');
    feedback.className = 'reading-feedback fixed top-20 right-4 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg shadow-lg z-50 transition-all duration-300';
    feedback.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-check mr-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(feedback);

    // Auto-remove after 2 seconds
    setTimeout(() => {
        feedback.classList.add('opacity-0', 'translate-x-full');
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 300);
    }, 2000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.noteViewManager = new NoteViewManager();
});
</script>

<!-- Include enhanced notes JavaScript for additional functionality -->
<script src="/momentum/public/js/notes-enhanced.js"></script>
