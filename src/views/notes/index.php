<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Notes</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <form action="/momentum/notes/search" method="GET" class="flex">
                    <input type="text" name="q" placeholder="Search notes..." class="rounded-l-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($searchTerm) ? View::escape($searchTerm) : '' ?>">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-r-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                <a href="/momentum/notes/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Note
                </a>
            </div>
        </div>

        <!-- Category Filter -->
        <?php if (!empty($categories)): ?>
            <div class="mb-6 flex flex-wrap gap-2">
                <a href="/momentum/notes" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?= empty($filters['category']) ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600' ?> transition-colors duration-200">
                    All Notes
                </a>
                <?php foreach ($categories as $category): ?>
                    <a href="/momentum/notes?category=<?= urlencode($category['category']) ?>" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?= isset($filters['category']) && $filters['category'] === $category['category'] ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600' ?> transition-colors duration-200">
                        <?= View::escape($category['category']) ?>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Search Results -->
        <?php if (isset($searchTerm)): ?>
            <div class="mb-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        Search Results for "<?= View::escape($searchTerm) ?>"
                    </h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                        Found <?= count($notes) ?> note<?= count($notes) !== 1 ? 's' : '' ?>
                    </p>
                    <a href="/momentum/notes" class="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-times mr-1"></i> Clear Search
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Notes Grid -->
        <?php if (empty($notes)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">
                        <i class="fas fa-sticky-note text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No notes found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        <?php if (isset($searchTerm) || !empty($filters)): ?>
                            Try adjusting your search or filters, or
                        <?php endif; ?>
                        create a new note to get started.
                    </p>
                    <a href="/momentum/notes/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Create Note
                    </a>
                </div>
            </div>
        <?php else: ?>
            <!-- Pinned Notes -->
            <?php 
            $pinnedNotes = array_filter($notes, function($note) {
                return $note['is_pinned'] == 1;
            });
            
            if (!empty($pinnedNotes)): 
            ?>
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                        <i class="fas fa-thumbtack mr-2"></i> Pinned Notes
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($pinnedNotes as $note): ?>
                            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200 border-t-4 border-yellow-400">
                                <div class="px-4 py-5 sm:p-6">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                            <a href="/momentum/notes/view/<?= $note['id'] ?>" class="hover:text-primary-600 dark:hover:text-primary-400">
                                                <?= View::escape($note['title']) ?>
                                            </a>
                                        </h3>
                                        <div class="flex space-x-2">
                                            <a href="/momentum/notes/toggle-pin/<?= $note['id'] ?>" class="text-yellow-500 hover:text-yellow-600 dark:text-yellow-400 dark:hover:text-yellow-300" title="Unpin">
                                                <i class="fas fa-thumbtack"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <?php if (!empty($note['category'])): ?>
                                        <div class="mb-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                <?= View::escape($note['category']) ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-4 line-clamp-3">
                                        <?= nl2br(View::escape(substr($note['content'] ?? '', 0, 150))) ?><?= strlen($note['content'] ?? '') > 150 ? '...' : '' ?>
                                    </div>
                                    <div class="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                                        <span><i class="far fa-clock mr-1"></i> <?= View::formatDateTime($note['updated_at']) ?></span>
                                        <div class="flex space-x-2">
                                            <a href="/momentum/notes/edit/<?= $note['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/momentum/notes/delete/<?= $note['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete" onclick="return confirm('Are you sure you want to delete this note?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Other Notes -->
            <?php 
            $otherNotes = array_filter($notes, function($note) {
                return $note['is_pinned'] != 1;
            });
            
            if (!empty($otherNotes)): 
            ?>
                <div>
                    <?php if (!empty($pinnedNotes)): ?>
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                            Other Notes
                        </h2>
                    <?php endif; ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($otherNotes as $note): ?>
                            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200">
                                <div class="px-4 py-5 sm:p-6">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                            <a href="/momentum/notes/view/<?= $note['id'] ?>" class="hover:text-primary-600 dark:hover:text-primary-400">
                                                <?= View::escape($note['title']) ?>
                                            </a>
                                        </h3>
                                        <div class="flex space-x-2">
                                            <a href="/momentum/notes/toggle-pin/<?= $note['id'] ?>" class="text-gray-400 hover:text-yellow-500 dark:text-gray-500 dark:hover:text-yellow-400" title="Pin">
                                                <i class="fas fa-thumbtack"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <?php if (!empty($note['category'])): ?>
                                        <div class="mb-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                <?= View::escape($note['category']) ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-4 line-clamp-3">
                                        <?= nl2br(View::escape(substr($note['content'] ?? '', 0, 150))) ?><?= strlen($note['content'] ?? '') > 150 ? '...' : '' ?>
                                    </div>
                                    <div class="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                                        <span><i class="far fa-clock mr-1"></i> <?= View::formatDateTime($note['updated_at']) ?></span>
                                        <div class="flex space-x-2">
                                            <a href="/momentum/notes/edit/<?= $note['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/momentum/notes/delete/<?= $note['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete" onclick="return confirm('Are you sure you want to delete this note?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>
