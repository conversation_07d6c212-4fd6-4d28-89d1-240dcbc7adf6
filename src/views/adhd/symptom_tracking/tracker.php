<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php
        // Include back button
        $url = '/momentum/adhd';
        $text = 'Back to ADHD Dashboard';
        include(dirname(__DIR__, 2) . '/partials/back_button.php');
        ?>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 mr-2"></i> Symptom Tracker
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/adhd/symptom-tracker/log" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Log Symptoms
                </a>
                <a href="/momentum/adhd/symptom-tracker/event" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-exclamation-circle mr-1"></i> Log Event
                </a>
                <a href="/momentum/adhd/symptom-tracker/reports" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-file-alt mr-1"></i> Reports
                </a>
            </div>
        </div>

        <!-- Symptom Logs -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Recent Symptom Logs
                </h2>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (empty($recentLogs)): ?>
                    <div class="text-center py-4">
                        <div class="text-gray-500 dark:text-gray-400 mb-2">No symptom logs yet</div>
                        <a href="/momentum/adhd/symptom-tracker/log" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-1"></i> Log Symptoms
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Focus</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Productivity</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Consistency</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Organization</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Impulsivity</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Emotional</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($recentLogs as $log): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= date('M j, Y', strtotime($log['log_date'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $log['focus_score'] >= 7 ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : ($log['focus_score'] >= 4 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100') ?>">
                                                <?= $log['focus_score'] ?>/10
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $log['productivity_score'] >= 7 ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : ($log['productivity_score'] >= 4 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100') ?>">
                                                <?= $log['productivity_score'] ?>/10
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $log['consistency_score'] >= 7 ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : ($log['consistency_score'] >= 4 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100') ?>">
                                                <?= $log['consistency_score'] ?>/10
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $log['organization_score'] >= 7 ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : ($log['organization_score'] >= 4 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100') ?>">
                                                <?= $log['organization_score'] ?>/10
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $log['impulsivity_score'] >= 7 ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : ($log['impulsivity_score'] >= 4 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100') ?>">
                                                <?= $log['impulsivity_score'] ?>/10
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $log['emotional_regulation_score'] >= 7 ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : ($log['emotional_regulation_score'] >= 4 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100') ?>">
                                                <?= $log['emotional_regulation_score'] ?>/10
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <a href="/momentum/adhd/symptom-tracker/log?date=<?= $log['log_date'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Events -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Recent Symptom Events
                </h2>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (empty($recentEvents)): ?>
                    <div class="text-center py-4">
                        <div class="text-gray-500 dark:text-gray-400 mb-2">No symptom events logged yet</div>
                        <a href="/momentum/adhd/symptom-tracker/event" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-1"></i> Log Event
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach (array_slice($recentEvents, 0, 5) as $event): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="flex justify-between items-start mb-2">
                                    <div>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                            <?= ucfirst($event['symptom_type']) ?>
                                        </span>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $event['intensity'] >= 7 ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : ($event['intensity'] >= 4 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100') ?>">
                                            Intensity: <?= $event['intensity'] ?>/10
                                        </span>
                                    </div>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">
                                        <?= date('M j, Y g:i A', strtotime($event['timestamp'])) ?>
                                    </span>
                                </div>
                                <div class="mt-2">
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white">Situation:</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-300"><?= View::escape($event['situation']) ?></p>
                                </div>
                                <?php if (!empty($event['impact'])): ?>
                                    <div class="mt-2">
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Impact:</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-300"><?= View::escape($event['impact']) ?></p>
                                    </div>
                                <?php endif; ?>
                                <?php if (!empty($event['coping_strategy'])): ?>
                                    <div class="mt-2">
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Coping Strategy:</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-300"><?= View::escape($event['coping_strategy']) ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
