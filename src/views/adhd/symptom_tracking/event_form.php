<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php
        // Include back button
        $url = '/momentum/adhd/symptom-tracker';
        $text = 'Back to Symptom Tracker';
        include(dirname(__DIR__, 2) . '/partials/back_button.php');
        ?>

        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-exclamation-circle text-primary-600 dark:text-primary-400 mr-2"></i> Log Symptom Event
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Use this form to log specific ADHD symptom events as they happen. This helps identify patterns and triggers.
                </p>

                <form action="/momentum/adhd/symptom-tracker/event" method="POST">
                    <div class="space-y-6">
                        <!-- Symptom Type -->
                        <div>
                            <label for="symptom_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Symptom Type
                            </label>
                            <div class="mt-1">
                                <select id="symptom_type" name="symptom_type" required
                                    class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="">Select a symptom type</option>
                                    <option value="focus">Focus/Attention Issue</option>
                                    <option value="productivity">Productivity Challenge</option>
                                    <option value="consistency">Consistency/Routine Difficulty</option>
                                    <option value="organization">Organization Problem</option>
                                    <option value="impulsivity">Impulsivity/Distraction</option>
                                    <option value="emotional">Emotional Regulation</option>
                                </select>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Select the category that best describes this symptom event
                            </p>
                        </div>

                        <!-- Situation -->
                        <div>
                            <label for="situation" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Situation
                            </label>
                            <div class="mt-1">
                                <textarea id="situation" name="situation" rows="3" required
                                    class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md"
                                    placeholder="Describe what was happening when the symptom occurred..."></textarea>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                What were you doing? Where were you? What was happening around you?
                            </p>
                        </div>

                        <!-- Intensity -->
                        <div>
                            <label for="intensity" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Intensity (1-10)
                            </label>
                            <div class="mt-1">
                                <div class="flex items-center">
                                    <input type="range" id="intensity" name="intensity" min="1" max="10" value="5"
                                        class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="intensity_display" class="ml-3 text-lg font-medium text-gray-900 dark:text-white min-w-[2rem] text-center">5</span>
                                </div>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    How intense or disruptive was this symptom?
                                </p>
                                <div class="mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400">
                                    <span>Mild</span>
                                    <span>Severe</span>
                                </div>
                            </div>
                        </div>

                        <!-- Impact -->
                        <div>
                            <label for="impact" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Impact (Optional)
                            </label>
                            <div class="mt-1">
                                <textarea id="impact" name="impact" rows="2"
                                    class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md"
                                    placeholder="How did this affect you or your activities?"></textarea>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Describe how this symptom affected your productivity, mood, relationships, etc.
                            </p>
                        </div>

                        <!-- Coping Strategy -->
                        <div>
                            <label for="coping_strategy" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Coping Strategy (Optional)
                            </label>
                            <div class="mt-1">
                                <textarea id="coping_strategy" name="coping_strategy" rows="2"
                                    class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-700 dark:bg-gray-700 dark:text-white rounded-md"
                                    placeholder="What helped or could have helped in this situation?"></textarea>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                What strategies did you use or could have used to manage this symptom?
                            </p>
                        </div>

                        <div class="flex justify-end">
                            <a href="/momentum/adhd" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-3">
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                Log Event
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update intensity display when slider changes
        const intensityInput = document.getElementById('intensity');
        const intensityDisplay = document.getElementById('intensity_display');

        intensityInput.addEventListener('input', function() {
            intensityDisplay.textContent = this.value;

            // Update color based on value
            if (this.value <= 3) {
                intensityDisplay.className = 'ml-3 text-lg font-medium text-green-600 dark:text-green-400 min-w-[2rem] text-center';
            } else if (this.value <= 7) {
                intensityDisplay.className = 'ml-3 text-lg font-medium text-yellow-600 dark:text-yellow-400 min-w-[2rem] text-center';
            } else {
                intensityDisplay.className = 'ml-3 text-lg font-medium text-red-600 dark:text-red-400 min-w-[2rem] text-center';
            }
        });

        // Trigger input event to set initial color
        intensityInput.dispatchEvent(new Event('input'));
    });
</script>
