<?php
/**
 * Medication Effectiveness Dashboard
 * 
 * Advanced analytics and visualization for medication effectiveness
 */

require_once '../../../utils/MedicationEffectivenessAnalyzer.php';

$analyzer = new MedicationEffectivenessAnalyzer();
$userId = Session::get('user_id');

// Get analysis data
$days = isset($_GET['days']) ? (int)$_GET['days'] : 30;
$analysis = $analyzer->getEffectivenessAnalysis($userId, $days);
$healthcareReport = $analyzer->generateHealthcareReport($userId, $days);
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Medication Effectiveness</h1>
                <p class="mt-2 text-gray-600 dark:text-gray-300">
                    Advanced analytics showing how your medications correlate with symptom improvements
                </p>
            </div>
            
            <div class="flex items-center space-x-4">
                <!-- Time Period Selector -->
                <select id="time-period" onchange="updateTimePeriod(this.value)" 
                        class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                    <option value="7" <?= $days == 7 ? 'selected' : '' ?>>Last 7 days</option>
                    <option value="30" <?= $days == 30 ? 'selected' : '' ?>>Last 30 days</option>
                    <option value="90" <?= $days == 90 ? 'selected' : '' ?>>Last 90 days</option>
                </select>
                
                <!-- Export Report Button -->
                <button onclick="exportHealthcareReport()" 
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
            </div>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-pills text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                Total Medications
                            </dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                <?= count($analysis['medications']) ?>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chart-line text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                Overall Effectiveness
                            </dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                <?= $analysis['overall_correlation'] ?>%
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-clock text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                Avg Adherence
                            </dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                <?php
                                $avgAdherence = 0;
                                if (!empty($analysis['medications'])) {
                                    $adherenceRates = array_column($analysis['medications'], 'adherence_rate');
                                    $avgAdherence = round(array_sum($adherenceRates) / count($adherenceRates), 1);
                                }
                                echo $avgAdherence;
                                ?>%
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-bullseye text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                Timing Consistency
                            </dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white">
                                <?php
                                $avgTiming = 0;
                                if (!empty($analysis['medications'])) {
                                    $timingScores = array_column($analysis['medications'], 'timing_consistency');
                                    $avgTiming = round(array_sum($timingScores) / count($timingScores), 1);
                                }
                                echo $avgTiming;
                                ?>%
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Medication Analysis Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <?php foreach ($analysis['medications'] as $medication): ?>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    <?= htmlspecialchars($medication['medication']['name']) ?>
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                    <?= htmlspecialchars($medication['medication']['dosage']) ?>
                </p>
            </div>
            
            <div class="px-6 py-4">
                <!-- Effectiveness Score -->
                <div class="mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Effectiveness Score</span>
                        <span class="text-sm font-bold text-gray-900 dark:text-white">
                            <?= $medication['effectiveness_score'] ?>%
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" 
                             style="width: <?= $medication['effectiveness_score'] ?>%"></div>
                    </div>
                </div>

                <!-- Metrics Grid -->
                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= $medication['adherence_rate'] ?>%
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Adherence</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= $medication['timing_consistency'] ?>%
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Timing</div>
                    </div>
                    <div class="text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= $medication['symptom_correlation']['overall'] ?>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Correlation</div>
                    </div>
                </div>

                <!-- Symptom Correlations -->
                <?php if (!empty($medication['symptom_correlation']['by_symptom'])): ?>
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Symptom Impact</h4>
                    <div class="space-y-2">
                        <?php foreach ($medication['symptom_correlation']['by_symptom'] as $symptom => $impact): ?>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400 capitalize">
                                <?= htmlspecialchars($symptom) ?>
                            </span>
                            <span class="text-sm font-medium <?= $impact > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                <?= $impact > 0 ? '+' : '' ?><?= $impact ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Insights -->
                <?php if (!empty($medication['insights'])): ?>
                <div class="space-y-2">
                    <?php foreach ($medication['insights'] as $insight): ?>
                    <div class="p-3 rounded-md <?= $insight['type'] === 'success' ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200' : 
                                                   ($insight['type'] === 'warning' ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200' : 
                                                    'bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200') ?>">
                        <p class="text-sm"><?= htmlspecialchars($insight['message']) ?></p>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Recommendations -->
    <?php if (!empty($analysis['recommendations'])): ?>
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recommendations</h3>
        </div>
        <div class="px-6 py-4">
            <div class="space-y-4">
                <?php foreach ($analysis['recommendations'] as $recommendation): ?>
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-6 h-6 rounded-full flex items-center justify-center <?= 
                            $recommendation['priority'] === 'high' ? 'bg-red-100 dark:bg-red-900/20' :
                            ($recommendation['priority'] === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/20' : 'bg-blue-100 dark:bg-blue-900/20') ?>">
                            <i class="fas fa-lightbulb text-xs <?= 
                                $recommendation['priority'] === 'high' ? 'text-red-600 dark:text-red-400' :
                                ($recommendation['priority'] === 'medium' ? 'text-yellow-600 dark:text-yellow-400' : 'text-blue-600 dark:text-blue-400') ?>"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                            <?= htmlspecialchars($recommendation['title']) ?>
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                            <?= htmlspecialchars($recommendation['description']) ?>
                        </p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function updateTimePeriod(days) {
    window.location.href = `?days=${days}`;
}

function exportHealthcareReport() {
    window.open(`/momentum/adhd/medication/healthcare-report?days=<?= $days ?>&format=pdf`, '_blank');
}
</script>
