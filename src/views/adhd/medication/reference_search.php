<?php
/**
 * ADHD Medication Reference Search Results View
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-search text-primary-600 dark:text-primary-400 mr-2"></i> Search Results: "<?= htmlspecialchars($searchTerm) ?>"
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/adhd/medication/reference" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Reference
                </a>
            </div>
        </div>

        <!-- Search Bar -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <form action="/momentum/adhd/medication/reference/search" method="GET" class="flex flex-col sm:flex-row gap-2">
                <div class="flex-grow">
                    <label for="search" class="sr-only">Search Medications</label>
                    <div class="relative rounded-md shadow-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" name="search" id="search" value="<?= htmlspecialchars($searchTerm) ?>" class="focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Search by medication name (generic or brand)">
                    </div>
                </div>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    Search
                </button>
            </form>
        </div>

        <!-- Search Results -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-list text-primary-600 dark:text-primary-400 mr-2"></i>
                    <?= count($searchResults) ?> Result<?= count($searchResults) !== 1 ? 's' : '' ?> Found
                </h2>
            </div>

            <?php if (empty($searchResults)): ?>
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">
                        <i class="fas fa-search text-5xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">No medications found</h3>
                        <p class="mt-1">Try searching with different terms or browse the medication reference.</p>
                    </div>
                    <a href="/momentum/adhd/medication/reference" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-book-medical mr-1"></i> Browse All Medications
                    </a>
                </div>
            <?php else: ?>
                <div class="px-4 py-5 sm:p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php foreach ($searchResults as $medication): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex justify-between items-start">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                        <?= htmlspecialchars($medication['generic_name']) ?>
                                    </h3>
                                    <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                        <?= htmlspecialchars($medication['medication_class']) ?>
                                    </span>
                                </div>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    <strong>Brand Names:</strong> <?= htmlspecialchars($medication['brand_names']) ?>
                                </p>
                                <div class="mt-4 flex justify-end">
                                    <a href="/momentum/adhd/medication/reference/view/<?= $medication['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                                        View Details <i class="fas fa-arrow-right ml-1"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Disclaimer -->
        <div class="bg-yellow-50 dark:bg-yellow-900 border-l-4 border-yellow-400 p-4 mt-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700 dark:text-yellow-200">
                        <strong>Medical Disclaimer:</strong> This information is provided for educational purposes only and is not intended as medical advice. The substances listed include prescription medications, over-the-counter supplements, and natural compounds with varying levels of scientific evidence and safety profiles. Many substances can interact with medications or have side effects. Always consult with a healthcare professional before starting, stopping, or changing any medication or supplement regimen, especially if you have underlying health conditions or take other medications.
                    </p>
                    <p class="text-sm text-yellow-700 dark:text-yellow-200 mt-2">
                        <strong>Important:</strong> Prescription medications should only be used under the supervision of a licensed healthcare provider. Performance-enhancing substances may be prohibited in competitive sports. Quality and potency of supplements can vary significantly between brands and batches.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
