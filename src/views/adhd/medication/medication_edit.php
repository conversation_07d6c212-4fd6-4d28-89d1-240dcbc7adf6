<?php
/**
 * Medication Edit Form
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-pills text-primary-600 dark:text-primary-400 mr-2"></i> Edit Medication
            </h1>
            <a href="/momentum/adhd/medication/view/<?= $medication['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Medication Information
                </h2>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Update the details of your medication below.
                </p>
            </div>
            <form action="/momentum/adhd/medication/update/<?= $medication['id'] ?>" method="POST" class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6">
                    <!-- Medication Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Medication Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" required value="<?= htmlspecialchars($medication['name']) ?>"
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                    </div>

                    <!-- Dosage -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="dosage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Dosage <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="dosage" id="dosage" step="0.01" required value="<?= htmlspecialchars($medication['dosage']) ?>"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="dosage_unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Unit <span class="text-red-500">*</span>
                            </label>
                            <select name="dosage_unit" id="dosage_unit" required
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="mg" <?= $medication['dosage_unit'] === 'mg' ? 'selected' : '' ?>>mg (milligrams)</option>
                                <option value="mcg" <?= $medication['dosage_unit'] === 'mcg' ? 'selected' : '' ?>>mcg (micrograms)</option>
                                <option value="g" <?= $medication['dosage_unit'] === 'g' ? 'selected' : '' ?>>g (grams)</option>
                                <option value="ml" <?= $medication['dosage_unit'] === 'ml' ? 'selected' : '' ?>>ml (milliliters)</option>
                                <option value="tablet" <?= $medication['dosage_unit'] === 'tablet' ? 'selected' : '' ?>>tablet(s)</option>
                                <option value="capsule" <?= $medication['dosage_unit'] === 'capsule' ? 'selected' : '' ?>>capsule(s)</option>
                                <option value="patch" <?= $medication['dosage_unit'] === 'patch' ? 'selected' : '' ?>>patch(es)</option>
                                <option value="spray" <?= $medication['dosage_unit'] === 'spray' ? 'selected' : '' ?>>spray(s)</option>
                                <option value="drop" <?= $medication['dosage_unit'] === 'drop' ? 'selected' : '' ?>>drop(s)</option>
                                <option value="unit" <?= $medication['dosage_unit'] === 'unit' ? 'selected' : '' ?>>unit(s)</option>
                            </select>
                        </div>
                    </div>

                    <!-- Frequency -->
                    <div>
                        <label for="frequency" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Frequency <span class="text-red-500">*</span>
                        </label>
                        <select name="frequency" id="frequency" required
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            <option value="daily" <?= $medication['frequency'] === 'daily' ? 'selected' : '' ?>>Daily</option>
                            <option value="twice_daily" <?= $medication['frequency'] === 'twice_daily' ? 'selected' : '' ?>>Twice Daily</option>
                            <option value="three_times_daily" <?= $medication['frequency'] === 'three_times_daily' ? 'selected' : '' ?>>Three Times Daily</option>
                            <option value="four_times_daily" <?= $medication['frequency'] === 'four_times_daily' ? 'selected' : '' ?>>Four Times Daily</option>
                            <option value="weekly" <?= $medication['frequency'] === 'weekly' ? 'selected' : '' ?>>Weekly</option>
                            <option value="as_needed" <?= $medication['frequency'] === 'as_needed' ? 'selected' : '' ?>>As Needed</option>
                            <option value="custom" <?= $medication['frequency'] === 'custom' ? 'selected' : '' ?>>Custom</option>
                        </select>
                    </div>

                    <!-- Instructions -->
                    <div>
                        <label for="instructions" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Instructions
                        </label>
                        <textarea name="instructions" id="instructions" rows="3"
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                            placeholder="E.g., Take with food, Take before bedtime"><?= htmlspecialchars($medication['instructions'] ?? '') ?></textarea>
                    </div>

                    <!-- Prescriber and Pharmacy -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="prescriber" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Prescriber
                            </label>
                            <input type="text" name="prescriber" id="prescriber" value="<?= htmlspecialchars($medication['prescriber'] ?? '') ?>"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="pharmacy" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Pharmacy
                            </label>
                            <input type="text" name="pharmacy" id="pharmacy" value="<?= htmlspecialchars($medication['pharmacy'] ?? '') ?>"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>

                    <!-- Start and End Dates -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Start Date
                            </label>
                            <input type="date" name="start_date" id="start_date" value="<?= $medication['start_date'] ?? date('Y-m-d') ?>"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                End Date (if applicable)
                            </label>
                            <input type="date" name="end_date" id="end_date" value="<?= $medication['end_date'] ?? '' ?>"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Notes
                        </label>
                        <textarea name="notes" id="notes" rows="3"
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                            placeholder="Any additional notes about this medication"><?= htmlspecialchars($medication['notes'] ?? '') ?></textarea>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <a href="/momentum/adhd/medication/view/<?= $medication['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-3">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Update Medication
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
