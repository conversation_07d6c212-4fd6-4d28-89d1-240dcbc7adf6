<?php
/**
 * Medication Log Form View
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-clipboard-check text-primary-600 dark:text-primary-400 mr-2"></i> Log Medication
            </h1>
            <a href="/momentum/adhd/medication" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back
            </a>
        </div>

        <?php if (empty($medications)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <div class="text-gray-500 dark:text-gray-400 mb-4">
                    <i class="fas fa-pills text-5xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">No medications added yet</h3>
                    <p class="mt-1">You need to add medications before you can log them.</p>
                </div>
                <a href="/momentum/adhd/medication/new" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Add Your First Medication
                </a>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        Medication Log
                    </h2>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Record when you take your medication and track its effectiveness.
                    </p>
                </div>
                <form action="/momentum/adhd/medication/log/save" method="POST" class="px-4 py-5 sm:p-6">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Medication Selection -->
                        <div>
                            <label for="medication_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Medication <span class="text-red-500">*</span>
                            </label>
                            <select name="medication_id" id="medication_id" required
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="">Select a medication</option>
                                <?php foreach ($medications as $medication): ?>
                                    <option value="<?= $medication['id'] ?>">
                                        <?= htmlspecialchars($medication['name']) ?> (<?= htmlspecialchars($medication['dosage']) ?> <?= htmlspecialchars($medication['dosage_unit']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Date and Time -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="log_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Date <span class="text-red-500">*</span>
                                </label>
                                <input type="date" name="log_date" id="log_date" value="<?= date('Y-m-d') ?>" required
                                    class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                            <div>
                                <label for="log_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Time <span class="text-red-500">*</span>
                                </label>
                                <input type="time" name="log_time" id="log_time" value="<?= date('H:i') ?>" required
                                    class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                        </div>

                        <!-- Taken Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Did you take this medication? <span class="text-red-500">*</span>
                            </label>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <input id="taken_yes" name="taken" type="radio" value="1" checked
                                        class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 dark:border-gray-600">
                                    <label for="taken_yes" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                        Yes
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input id="taken_no" name="taken" type="radio" value="0"
                                        class="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 dark:border-gray-600">
                                    <label for="taken_no" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                        No (missed)
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Actual Dosage -->
                        <div class="taken-dependent">
                            <label for="actual_dosage" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Actual Dosage Taken (if different from prescribed)
                            </label>
                            <input type="number" name="actual_dosage" id="actual_dosage" step="0.01"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                Leave blank if you took the prescribed dosage.
                            </p>
                        </div>

                        <!-- Effectiveness Rating -->
                        <div class="taken-dependent">
                            <label for="effectiveness_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Effectiveness Rating (1-10)
                            </label>
                            <div class="mt-1 flex items-center">
                                <input type="range" name="effectiveness_rating" id="effectiveness_rating" min="1" max="10" value="5"
                                    class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <span id="effectiveness_value" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-8 text-center">5</span>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                1 = Not effective, 10 = Extremely effective
                            </p>
                        </div>

                        <!-- Side Effects -->
                        <div class="taken-dependent">
                            <label for="side_effects" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Side Effects
                            </label>
                            <textarea name="side_effects" id="side_effects" rows="2"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                                placeholder="Describe any side effects you experienced"></textarea>
                        </div>

                        <!-- Side Effects Severity -->
                        <div class="taken-dependent">
                            <label for="side_effects_severity" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Side Effects Severity (1-10)
                            </label>
                            <div class="mt-1 flex items-center">
                                <input type="range" name="side_effects_severity" id="side_effects_severity" min="0" max="10" value="0"
                                    class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <span id="severity_value" class="ml-2 text-sm text-gray-700 dark:text-gray-300 w-8 text-center">0</span>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                0 = None, 10 = Severe
                            </p>
                        </div>

                        <!-- Notes -->
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Notes
                            </label>
                            <textarea name="notes" id="notes" rows="3"
                                class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                                placeholder="Any additional notes about this medication dose"></textarea>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i> Save Log
                        </button>
                    </div>
                </form>
            </div>

            <script>
                // Update effectiveness value display
                const effectivenessSlider = document.getElementById('effectiveness_rating');
                const effectivenessValue = document.getElementById('effectiveness_value');
                effectivenessSlider.addEventListener('input', function() {
                    effectivenessValue.textContent = this.value;
                });

                // Update side effects severity value display
                const severitySlider = document.getElementById('side_effects_severity');
                const severityValue = document.getElementById('severity_value');
                severitySlider.addEventListener('input', function() {
                    severityValue.textContent = this.value;
                });

                // Show/hide taken-dependent fields
                const takenRadios = document.querySelectorAll('input[name="taken"]');
                const takenDependentFields = document.querySelectorAll('.taken-dependent');

                function updateTakenDependentFields() {
                    const isTaken = document.getElementById('taken_yes').checked;
                    takenDependentFields.forEach(field => {
                        field.style.display = isTaken ? 'block' : 'none';
                    });
                }

                takenRadios.forEach(radio => {
                    radio.addEventListener('change', updateTakenDependentFields);
                });

                // Initial update
                updateTakenDependentFields();
            </script>
        <?php endif; ?>
    </div>
</div>
