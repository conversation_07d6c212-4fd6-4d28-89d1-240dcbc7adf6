<?php
/**
 * Executive Function Exercises Index View
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-brain text-primary-600 dark:text-primary-400 mr-2"></i> Executive Function Exercises
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/adhd/executive-function/progress" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    <i class="fas fa-chart-line mr-1"></i> View Progress
                </a>
                <a href="/momentum/adhd" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to ADHD Dashboard
                </a>
            </div>
        </div>

        <!-- Progress Overview -->
        <?php if (!empty($progressByCategory)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:px-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400 mr-2"></i> Your Executive Function Progress
                    </h2>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        <?php
                        $categoryIcons = [
                            'working_memory' => 'fas fa-memory',
                            'task_initiation' => 'fas fa-play',
                            'planning' => 'fas fa-clipboard-list',
                            'organization' => 'fas fa-folder',
                            'time_management' => 'fas fa-clock',
                            'emotional_regulation' => 'fas fa-heart'
                        ];

                        $categoryColors = [
                            'working_memory' => 'blue',
                            'task_initiation' => 'green',
                            'planning' => 'purple',
                            'organization' => 'indigo',
                            'time_management' => 'yellow',
                            'emotional_regulation' => 'pink'
                        ];

                        foreach ($progressByCategory as $category => $score):
                            $icon = $categoryIcons[$category] ?? 'fas fa-brain';
                            $color = $categoryColors[$category] ?? 'gray';
                        ?>
                            <div class="bg-<?= $color ?>-50 dark:bg-<?= $color ?>-900/20 border border-<?= $color ?>-200 dark:border-<?= $color ?>-800 rounded-lg p-4 text-center">
                                <div class="text-<?= $color ?>-500 dark:text-<?= $color ?>-400 mb-2">
                                    <i class="<?= $icon ?> text-2xl"></i>
                                </div>
                                <div class="text-xl font-bold text-<?= $color ?>-600 dark:text-<?= $color ?>-400 mb-1">
                                    <?= number_format($score['avg_score'], 1) ?>/10
                                </div>
                                <div class="text-xs text-<?= $color ?>-700 dark:text-<?= $color ?>-300">
                                    <?= ucfirst(str_replace('_', ' ', $category)) ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="mt-4 text-center">
                        <a href="/momentum/adhd/executive-function/progress" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                            View Detailed Progress <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Recent Results -->
        <?php if (!empty($recentResults)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:px-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-history text-primary-600 dark:text-primary-400 mr-2"></i> Recent Exercise Results
                    </h2>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Exercise</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Score</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time Taken</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($recentResults as $result): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            <?= isset($result['name']) ? htmlspecialchars($result['name']) : 'Unknown Exercise' ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= isset($result['category']) ? ucfirst(str_replace('_', ' ', $result['category'])) : 'Unknown Category' ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= date('M j, Y', strtotime($result['completion_date'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <div class="flex items-center">
                                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: <?= ($result['score'] * 10) ?>%"></div>
                                                </div>
                                                <span class="ml-2"><?= $result['score'] ?>/10</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $result['time_taken'] ?> seconds
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Exercises by Category -->
        <?php if (!empty($exercisesByCategory)): ?>
            <div class="space-y-6">
                <?php
                $categoryTitles = [
                    'working_memory' => 'Working Memory Exercises',
                    'task_initiation' => 'Task Initiation Exercises',
                    'planning' => 'Planning Exercises',
                    'organization' => 'Organization Exercises',
                    'time_management' => 'Time Management Exercises',
                    'emotional_regulation' => 'Emotional Regulation Exercises'
                ];

                $categoryDescriptions = [
                    'working_memory' => 'Improve your ability to hold and manipulate information in your mind.',
                    'task_initiation' => 'Strengthen your ability to begin tasks without procrastination.',
                    'planning' => 'Enhance your ability to create and follow through with plans.',
                    'organization' => 'Develop better organizational skills and systems.',
                    'time_management' => 'Improve your awareness and management of time.',
                    'emotional_regulation' => 'Build skills to manage emotions effectively.'
                ];

                foreach ($exercisesByCategory as $category => $exercises):
                    $title = $categoryTitles[$category] ?? ucfirst(str_replace('_', ' ', $category)) . ' Exercises';
                    $description = $categoryDescriptions[$category] ?? 'Exercises to improve your ' . str_replace('_', ' ', $category) . ' skills.';
                    $icon = $categoryIcons[$category] ?? 'fas fa-brain';
                    $color = $categoryColors[$category] ?? 'gray';
                ?>
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                        <div class="px-4 py-5 sm:px-6 flex items-center">
                            <i class="<?= $icon ?> text-<?= $color ?>-500 dark:text-<?= $color ?>-400 mr-2 text-xl"></i>
                            <div>
                                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                                    <?= $title ?>
                                </h2>
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    <?= $description ?>
                                </p>
                            </div>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                                <?php foreach ($exercises as $exercise): ?>
                                    <div class="bg-<?= $color ?>-50 dark:bg-<?= $color ?>-900/20 border border-<?= $color ?>-200 dark:border-<?= $color ?>-800 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                        <div class="flex justify-between items-start">
                                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($exercise['name']) ?>
                                            </h3>
                                            <span class="px-2 py-1 text-xs rounded-full bg-<?= $color ?>-100 dark:bg-<?= $color ?>-800 text-<?= $color ?>-800 dark:text-<?= $color ?>-100">
                                                Level <?= $exercise['difficulty_level'] ?>
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                                            <?= htmlspecialchars($exercise['description']) ?>
                                        </p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <i class="fas fa-clock mr-1"></i> <?= $exercise['estimated_time'] ?? '5-10' ?> minutes
                                        </p>
                                        <div class="mt-4 flex justify-end space-x-2">
                                            <a href="/momentum/adhd/executive-function/view/<?= $exercise['id'] ?>" class="text-<?= $color ?>-600 dark:text-<?= $color ?>-400 hover:text-<?= $color ?>-800 dark:hover:text-<?= $color ?>-300 text-sm">
                                                <i class="fas fa-info-circle mr-1"></i> Details
                                            </a>
                                            <a href="/momentum/adhd/executive-function/practice/<?= $exercise['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                                                <i class="fas fa-play mr-1"></i> Practice
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <div class="text-gray-500 dark:text-gray-400 mb-4">
                    <i class="fas fa-brain text-5xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">No exercises available yet</h3>
                    <p class="mt-1">Executive function exercises are coming soon!</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
