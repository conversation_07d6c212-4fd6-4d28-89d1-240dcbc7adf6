<?php
/**
 * Trigger Occurrence Edit Form
 * 
 * Form for editing occurrences of ADHD triggers
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back navigation -->
        <div class="mb-6">
            <a href="/momentum/adhd/triggers/view/<?= $trigger['id'] ?>" class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Trigger
            </a>
        </div>
        
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6">
                <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-edit text-primary-500 mr-2"></i> Edit Trigger Occurrence
                </h1>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Update the details of this trigger occurrence.
                </p>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <form action="/momentum/adhd/triggers/occurrence/update/<?= $occurrence['id'] ?>" method="POST" class="space-y-6">
                    <!-- Trigger Selection -->
                    <div>
                        <label for="trigger_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Trigger</label>
                        <div class="mt-1">
                            <select id="trigger_id" name="trigger_id" required class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <?php foreach ($triggers as $t): ?>
                                    <option value="<?= $t['id'] ?>" <?= ($occurrence['trigger_id'] == $t['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($t['name']) ?> (<?= ucfirst($t['category']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Date and Time -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="occurrence_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date</label>
                            <div class="mt-1">
                                <input type="date" name="occurrence_date" id="occurrence_date" required value="<?= $occurrence['occurrence_date'] ?>" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                        </div>
                        
                        <div>
                            <label for="occurrence_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Time (Optional)</label>
                            <div class="mt-1">
                                <input type="time" name="occurrence_time" id="occurrence_time" value="<?= $occurrence['occurrence_time'] ?>" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Impact Rating -->
                    <div>
                        <label for="impact_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Impact Rating (1-10)</label>
                        <div class="mt-1">
                            <input type="range" id="impact_rating" name="impact_rating" min="1" max="10" value="<?= $occurrence['impact_rating'] ?>" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 px-1">
                                <span>1</span>
                                <span>2</span>
                                <span>3</span>
                                <span>4</span>
                                <span>5</span>
                                <span>6</span>
                                <span>7</span>
                                <span>8</span>
                                <span>9</span>
                                <span>10</span>
                            </div>
                            <p class="text-center mt-2 text-sm font-medium" id="impact-display"><?= $occurrence['impact_rating'] ?></p>
                        </div>
                    </div>
                    
                    <!-- Symptoms Experienced -->
                    <div>
                        <label for="symptoms_experienced" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Symptoms Experienced</label>
                        <div class="mt-1">
                            <textarea id="symptoms_experienced" name="symptoms_experienced" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Describe the ADHD symptoms you experienced (e.g., difficulty focusing, impulsivity, emotional dysregulation)"><?= htmlspecialchars($occurrence['symptoms_experienced'] ?? '') ?></textarea>
                        </div>
                    </div>
                    
                    <!-- Context -->
                    <div>
                        <label for="context" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Context</label>
                        <div class="mt-1">
                            <textarea id="context" name="context" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Describe the situation or environment when the trigger occurred"><?= htmlspecialchars($occurrence['context'] ?? '') ?></textarea>
                        </div>
                    </div>
                    
                    <!-- Coping Strategy Used -->
                    <div>
                        <label for="coping_strategy_used" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Coping Strategy Used (if any)</label>
                        <div class="mt-1">
                            <textarea id="coping_strategy_used" name="coping_strategy_used" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Describe any strategies you used to cope with the trigger"><?= htmlspecialchars($occurrence['coping_strategy_used'] ?? '') ?></textarea>
                        </div>
                    </div>
                    
                    <!-- Coping Effectiveness -->
                    <div>
                        <label for="coping_effectiveness" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Coping Effectiveness (1-10, if applicable)</label>
                        <div class="mt-1">
                            <select id="coping_effectiveness" name="coping_effectiveness" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="">Not applicable</option>
                                <?php for ($i = 1; $i <= 10; $i++): ?>
                                    <option value="<?= $i ?>" <?= (isset($occurrence['coping_effectiveness']) && $occurrence['coping_effectiveness'] == $i) ? 'selected' : '' ?>>
                                        <?= $i ?> - <?= $i <= 3 ? 'Not effective' : ($i <= 7 ? 'Somewhat effective' : 'Very effective') ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Additional Notes</label>
                        <div class="mt-1">
                            <textarea id="notes" name="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Any additional observations or thoughts"><?= htmlspecialchars($occurrence['notes'] ?? '') ?></textarea>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-save mr-1"></i> Update Occurrence
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update impact rating display
    const impactRating = document.getElementById('impact_rating');
    const impactDisplay = document.getElementById('impact-display');
    
    impactRating.addEventListener('input', function() {
        impactDisplay.textContent = this.value;
    });
});
</script>
