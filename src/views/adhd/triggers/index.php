<?php
/**
 * Trigger Identification Index View
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-exclamation-triangle text-primary-600 dark:text-primary-400 mr-2"></i> Trigger Identification
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/adhd/triggers/new" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Add Trigger
                </a>
                <a href="/momentum/adhd/triggers/occurrence" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-clipboard-check mr-1"></i> Log Occurrence
                </a>
                <a href="/momentum/adhd" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to ADHD Dashboard
                </a>
            </div>
        </div>

        <?php if (empty($triggers)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <div class="text-gray-500 dark:text-gray-400 mb-4">
                    <i class="fas fa-exclamation-triangle text-5xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">No triggers added yet</h3>
                    <p class="mt-1">Start identifying your ADHD triggers to develop effective coping strategies.</p>
                </div>
                <a href="/momentum/adhd/triggers/new" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Add Your First Trigger
                </a>
            </div>
        <?php else: ?>
            <!-- High-Impact Triggers -->
            <?php if (!empty($highImpactTriggers)): ?>
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                            <i class="fas fa-fire text-red-500 mr-2"></i> High-Impact Triggers
                        </h2>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            Triggers with average impact rating of 7+
                        </span>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php foreach ($highImpactTriggers as $trigger): ?>
                                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                                    <div class="flex justify-between items-start">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($trigger['name']) ?>
                                        </h3>
                                        <span class="px-2 py-1 text-xs rounded-full bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-100">
                                            Impact: <?= round($trigger['avg_impact'], 1) ?>/10
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        <span class="font-medium">Category:</span> <?= ucfirst(str_replace('_', ' ', $trigger['category'])) ?>
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        <span class="font-medium">Occurrences:</span> <?= $trigger['occurrence_count'] ?>
                                    </p>
                                    <div class="mt-4 flex justify-end">
                                        <a href="/momentum/adhd/triggers/view/<?= $trigger['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                                            View Details <i class="fas fa-arrow-right ml-1"></i>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Trigger Categories -->
            <?php if (!empty($triggerCategories)): ?>
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                            <i class="fas fa-chart-pie text-primary-600 dark:text-primary-400 mr-2"></i> Trigger Categories
                        </h2>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            <?php foreach ($triggerCategories as $category): ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                                    <div class="text-xl font-bold text-primary-600 dark:text-primary-400 mb-1">
                                        <?= $category['count'] ?>
                                    </div>
                                    <div class="text-sm text-gray-700 dark:text-gray-300">
                                        <?= ucfirst(str_replace('_', ' ', $category['category'])) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Effective Coping Strategies -->
            <?php if (!empty($effectiveStrategies)): ?>
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                            <i class="fas fa-shield-alt text-green-600 dark:text-green-400 mr-2"></i> Most Effective Coping Strategies
                        </h2>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Strategy</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">For Trigger</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Effectiveness</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <?php foreach ($effectiveStrategies as $strategy): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($strategy['strategy_name']) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                <a href="/momentum/adhd/triggers/view/<?= $strategy['trigger_id'] ?>" class="text-primary-600 dark:text-primary-400 hover:underline">
                                                    <?= htmlspecialchars($strategy['trigger_name']) ?>
                                                </a>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                <?= ucfirst(str_replace('_', ' ', $strategy['category'])) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                <div class="flex items-center">
                                                    <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                        <div class="bg-green-500 h-2.5 rounded-full" style="width: <?= ($strategy['effectiveness_rating'] * 10) ?>%"></div>
                                                    </div>
                                                    <span class="ml-2"><?= $strategy['effectiveness_rating'] ?>/10</span>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- All Triggers -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-list text-primary-600 dark:text-primary-400 mr-2"></i> All Triggers
                    </h2>
                    <a href="/momentum/adhd/triggers/new" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        Add New <i class="fas fa-plus ml-1"></i>
                    </a>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Trigger</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Avg. Impact</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Occurrences</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($triggers as $trigger): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($trigger['name']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= ucfirst(str_replace('_', ' ', $trigger['category'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?php if (isset($trigger['avg_impact']) && $trigger['avg_impact'] > 0): ?>
                                                <div class="flex items-center">
                                                    <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                        <div class="<?= $trigger['avg_impact'] >= 7 ? 'bg-red-500' : 'bg-yellow-500' ?> h-2.5 rounded-full" style="width: <?= ($trigger['avg_impact'] * 10) ?>%"></div>
                                                    </div>
                                                    <span class="ml-2"><?= round($trigger['avg_impact'], 1) ?>/10</span>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-gray-400 dark:text-gray-500">No data</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $trigger['occurrence_count'] ?? 0 ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <a href="/momentum/adhd/triggers/view/<?= $trigger['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mr-3">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="/momentum/adhd/triggers/occurrence/<?= $trigger['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                                <i class="fas fa-plus-circle"></i> Log
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
