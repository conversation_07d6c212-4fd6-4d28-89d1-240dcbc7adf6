<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/adhd/cbt/thought-records" class="inline-flex items-center text-sm text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 mr-4">
                <i class="fas fa-arrow-left mr-1"></i> Back to Thought Records
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <h1 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-comments text-primary-600 dark:text-primary-400 mr-2"></i> New Thought Record
                </h1>

                <form action="/momentum/adhd/cbt/thought-records/save" method="POST">
                    <div class="space-y-6">
                        <!-- Situation -->
                        <div>
                            <label for="situation" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Situation
                            </label>
                            <div class="mt-1">
                                <textarea id="situation" name="situation" rows="3" required class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What happened? When? Where? Who with?</p>
                        </div>

                        <!-- Emotions -->
                        <div>
                            <label for="emotions" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Emotions
                            </label>
                            <div class="mt-1">
                                <input type="text" id="emotions" name="emotions" required class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What emotions did you feel? (e.g., anxiety, frustration, shame)</p>
                        </div>

                        <!-- Emotion Intensity -->
                        <div>
                            <label for="emotion_intensity" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Emotion Intensity (1-100)
                            </label>
                            <div class="mt-1">
                                <div class="flex items-center">
                                    <input type="range" id="emotion_intensity" name="emotion_intensity" min="1" max="100" value="50"
                                        class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="emotion_intensity_display" class="ml-3 text-lg font-medium text-gray-900 dark:text-white min-w-[3rem] text-center">
                                        50
                                    </span>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How intense were these emotions? (1 = very mild, 100 = extremely intense)</p>
                            </div>
                        </div>

                        <!-- Automatic Thoughts -->
                        <div>
                            <label for="automatic_thoughts" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Automatic Thoughts
                            </label>
                            <div class="mt-1">
                                <textarea id="automatic_thoughts" name="automatic_thoughts" rows="3" required class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What thoughts automatically went through your mind?</p>
                        </div>

                        <!-- Cognitive Distortions -->
                        <div>
                            <label for="cognitive_distortions" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Cognitive Distortions (optional)
                            </label>
                            <div class="mt-1">
                                <select id="cognitive_distortions" name="cognitive_distortions" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="">Select a thinking pattern...</option>
                                    <?php foreach ($cognitiveDistortions as $distortion): ?>
                                        <option value="<?= View::escape($distortion['name']) ?>"><?= View::escape($distortion['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What thinking patterns might be present in these thoughts?</p>
                        </div>

                        <!-- Evidence For -->
                        <div>
                            <label for="evidence_for" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Evidence For (optional)
                            </label>
                            <div class="mt-1">
                                <textarea id="evidence_for" name="evidence_for" rows="2" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What facts support this thought?</p>
                        </div>

                        <!-- Evidence Against -->
                        <div>
                            <label for="evidence_against" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Evidence Against (optional)
                            </label>
                            <div class="mt-1">
                                <textarea id="evidence_against" name="evidence_against" rows="2" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What facts don't support this thought?</p>
                        </div>

                        <!-- Balanced Thought -->
                        <div>
                            <label for="balanced_thought" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Balanced Thought (optional)
                            </label>
                            <div class="mt-1">
                                <textarea id="balanced_thought" name="balanced_thought" rows="2" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What's a more balanced/realistic perspective?</p>
                        </div>

                        <!-- Outcome Emotion -->
                        <div>
                            <label for="outcome_emotion" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Outcome Emotion (optional)
                            </label>
                            <div class="mt-1">
                                <input type="text" id="outcome_emotion" name="outcome_emotion" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How do you feel now after completing this thought record?</p>
                        </div>

                        <!-- Outcome Intensity -->
                        <div>
                            <label for="outcome_intensity" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Outcome Intensity (1-100) (optional)
                            </label>
                            <div class="mt-1">
                                <div class="flex items-center">
                                    <input type="range" id="outcome_intensity" name="outcome_intensity" min="1" max="100" value="50"
                                        class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="outcome_intensity_display" class="ml-3 text-lg font-medium text-gray-900 dark:text-white min-w-[3rem] text-center">
                                        50
                                    </span>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How intense is this emotion now? (1 = very mild, 100 = extremely intense)</p>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                Save Thought Record
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set up range input displays
    const emotionIntensityInput = document.getElementById('emotion_intensity');
    const emotionIntensityDisplay = document.getElementById('emotion_intensity_display');

    emotionIntensityInput.addEventListener('input', function() {
        emotionIntensityDisplay.textContent = this.value;
    });

    const outcomeIntensityInput = document.getElementById('outcome_intensity');
    const outcomeIntensityDisplay = document.getElementById('outcome_intensity_display');

    outcomeIntensityInput.addEventListener('input', function() {
        outcomeIntensityDisplay.textContent = this.value;
    });
});
</script>
