<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/adhd/cbt/thought-records" class="inline-flex items-center text-sm text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 mr-4">
                <i class="fas fa-arrow-left mr-1"></i> Back to Thought Records
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-start mb-6">
                    <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
                        <i class="fas fa-comments text-primary-600 dark:text-primary-400 mr-2"></i> Thought Record
                    </h1>
                    <span class="text-sm text-gray-500 dark:text-gray-400">
                        <?= date('M j, Y g:i A', strtotime($thoughtRecord['created_at'])) ?>
                    </span>
                </div>
                
                <div class="space-y-6">
                    <!-- Situation -->
                    <div>
                        <h2 class="text-sm font-medium text-gray-700 dark:text-gray-300">Situation</h2>
                        <div class="mt-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?= nl2br(View::escape($thoughtRecord['situation'])) ?></p>
                        </div>
                    </div>

                    <!-- Emotions -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h2 class="text-sm font-medium text-gray-700 dark:text-gray-300">Initial Emotions</h2>
                            <div class="mt-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                                <p class="text-sm text-gray-600 dark:text-gray-300"><?= View::escape($thoughtRecord['emotions']) ?></p>
                                <div class="mt-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $thoughtRecord['emotion_intensity'] >= 70 ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : ($thoughtRecord['emotion_intensity'] >= 40 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100') ?>">
                                        Intensity: <?= $thoughtRecord['emotion_intensity'] ?>/100
                                    </span>
                                </div>
                            </div>
                        </div>

                        <?php if (!empty($thoughtRecord['outcome_emotion'])): ?>
                            <div>
                                <h2 class="text-sm font-medium text-gray-700 dark:text-gray-300">Outcome Emotions</h2>
                                <div class="mt-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                                    <p class="text-sm text-gray-600 dark:text-gray-300"><?= View::escape($thoughtRecord['outcome_emotion']) ?></p>
                                    <?php if (!empty($thoughtRecord['outcome_intensity'])): ?>
                                        <div class="mt-2">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $thoughtRecord['outcome_intensity'] >= 70 ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : ($thoughtRecord['outcome_intensity'] >= 40 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100' : 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100') ?>">
                                                Intensity: <?= $thoughtRecord['outcome_intensity'] ?>/100
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Automatic Thoughts -->
                    <div>
                        <h2 class="text-sm font-medium text-gray-700 dark:text-gray-300">Automatic Thoughts</h2>
                        <div class="mt-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                            <p class="text-sm text-gray-600 dark:text-gray-300"><?= nl2br(View::escape($thoughtRecord['automatic_thoughts'])) ?></p>
                        </div>
                    </div>

                    <!-- Cognitive Distortions -->
                    <?php if (!empty($thoughtRecord['cognitive_distortions'])): ?>
                        <div>
                            <h2 class="text-sm font-medium text-gray-700 dark:text-gray-300">Cognitive Distortions</h2>
                            <div class="mt-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                                <p class="text-sm text-gray-600 dark:text-gray-300"><?= View::escape($thoughtRecord['cognitive_distortions']) ?></p>
                                
                                <?php 
                                // Find the matching distortion to show the description and example
                                $distortionInfo = null;
                                foreach ($cognitiveDistortions as $distortion) {
                                    if ($distortion['name'] === $thoughtRecord['cognitive_distortions']) {
                                        $distortionInfo = $distortion;
                                        break;
                                    }
                                }
                                
                                if ($distortionInfo): 
                                ?>
                                    <div class="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                                        <p class="text-xs text-gray-500 dark:text-gray-400"><?= View::escape($distortionInfo['description']) ?></p>
                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                            <span class="font-medium">Example:</span> <?= View::escape($distortionInfo['adhd_example']) ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Evidence For and Against -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <?php if (!empty($thoughtRecord['evidence_for'])): ?>
                            <div>
                                <h2 class="text-sm font-medium text-gray-700 dark:text-gray-300">Evidence For</h2>
                                <div class="mt-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                                    <p class="text-sm text-gray-600 dark:text-gray-300"><?= nl2br(View::escape($thoughtRecord['evidence_for'])) ?></p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($thoughtRecord['evidence_against'])): ?>
                            <div>
                                <h2 class="text-sm font-medium text-gray-700 dark:text-gray-300">Evidence Against</h2>
                                <div class="mt-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                                    <p class="text-sm text-gray-600 dark:text-gray-300"><?= nl2br(View::escape($thoughtRecord['evidence_against'])) ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Balanced Thought -->
                    <?php if (!empty($thoughtRecord['balanced_thought'])): ?>
                        <div>
                            <h2 class="text-sm font-medium text-gray-700 dark:text-gray-300">Balanced Thought</h2>
                            <div class="mt-1 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                                <p class="text-sm text-gray-600 dark:text-gray-300"><?= nl2br(View::escape($thoughtRecord['balanced_thought'])) ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
