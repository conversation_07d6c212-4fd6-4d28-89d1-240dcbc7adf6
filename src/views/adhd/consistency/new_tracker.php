<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/adhd/consistency/trackers" class="inline-flex items-center text-sm text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 mr-4">
                <i class="fas fa-arrow-left mr-1"></i> Back to Trackers
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <h1 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-calendar-plus text-primary-600 dark:text-primary-400 mr-2"></i> New Consistency Tracker
                </h1>
                
                <form action="/momentum/adhd/consistency/trackers/new" method="POST">
                    <div class="space-y-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Tracker Name
                            </label>
                            <div class="mt-1">
                                <input type="text" id="name" name="name" required class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What habit or routine do you want to track?</p>
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Description (optional)
                            </label>
                            <div class="mt-1">
                                <textarea id="description" name="description" rows="2" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Additional details about this habit or routine</p>
                        </div>

                        <!-- Frequency -->
                        <div>
                            <label for="frequency" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Frequency
                            </label>
                            <div class="mt-1">
                                <select id="frequency" name="frequency" required class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="daily">Daily</option>
                                    <option value="weekdays">Weekdays Only</option>
                                    <option value="weekends">Weekends Only</option>
                                    <option value="custom">Custom Days</option>
                                </select>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How often do you want to track this habit?</p>
                        </div>

                        <!-- Custom Days (hidden by default) -->
                        <div id="customDaysContainer" class="hidden">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Select Days
                            </label>
                            <div class="mt-1 space-y-2">
                                <div class="flex flex-wrap gap-3">
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="custom_days[]" value="monday" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Monday</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="custom_days[]" value="tuesday" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Tuesday</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="custom_days[]" value="wednesday" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Wednesday</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="custom_days[]" value="thursday" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Thursday</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="custom_days[]" value="friday" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Friday</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="custom_days[]" value="saturday" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Saturday</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="custom_days[]" value="sunday" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Sunday</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Reminder -->
                        <div>
                            <label for="reminder_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Reminder Time (optional)
                            </label>
                            <div class="mt-1">
                                <input type="time" id="reminder_time" name="reminder_time" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Set a daily reminder time (requires browser notifications)</p>
                        </div>

                        <!-- Notes -->
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Notes (optional)
                            </label>
                            <div class="mt-1">
                                <textarea id="notes" name="notes" rows="2" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Any additional notes or motivation for this habit</p>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                Create Tracker
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const frequencySelect = document.getElementById('frequency');
    const customDaysContainer = document.getElementById('customDaysContainer');
    
    // Show/hide custom days based on frequency selection
    frequencySelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDaysContainer.classList.remove('hidden');
        } else {
            customDaysContainer.classList.add('hidden');
        }
    });
});
</script>
