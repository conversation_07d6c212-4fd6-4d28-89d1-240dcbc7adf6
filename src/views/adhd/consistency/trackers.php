<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php
        // Include back button
        $url = '/momentum/adhd';
        $text = 'Back to ADHD Dashboard';
        include(dirname(__DIR__, 2) . '/partials/back_button.php');
        ?>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-calendar-check text-primary-600 dark:text-primary-400 mr-2"></i> Consistency Trackers
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/adhd/consistency/trackers/new" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> New Tracker
                </a>
            </div>
        </div>

        <!-- Today's Trackers -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Today's Trackers
                </h2>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Track your daily habits and routines
                </p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (empty($todayTrackers)): ?>
                    <div class="text-center py-4">
                        <div class="text-gray-500 dark:text-gray-400 mb-2">No active trackers for today</div>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                            Create a new tracker to start building consistency
                        </p>
                        <a href="/momentum/adhd/consistency/trackers/new" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-1"></i> Create Tracker
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($todayTrackers as $tracker): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                    <div class="mb-3 md:mb-0">
                                        <h3 class="text-md font-medium text-gray-900 dark:text-white"><?= View::escape($tracker['name']) ?></h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">
                                            <?= View::escape($tracker['description']) ?>
                                        </p>
                                    </div>

                                    <div class="flex items-center space-x-4">
                                        <?php if ($tracker['completed_today']): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                <i class="fas fa-check mr-1"></i> Completed
                                            </span>
                                        <?php else: ?>
                                            <form action="/momentum/adhd/consistency/trackers/log" method="POST" class="inline">
                                                <input type="hidden" name="tracker_id" value="<?= $tracker['id'] ?>">
                                                <button type="submit" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                                    <i class="fas fa-check mr-1"></i> Mark Complete
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <a href="/momentum/adhd/consistency/trackers/view/<?= $tracker['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                            <i class="fas fa-chart-bar mr-1"></i> View Stats
                                        </a>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <div class="flex items-center">
                                        <div class="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                            <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $tracker['completion_rate'] ?>%"></div>
                                        </div>
                                        <span class="ml-2 text-xs font-medium text-gray-500 dark:text-gray-400"><?= $tracker['completion_rate'] ?>%</span>
                                    </div>
                                    <div class="mt-1 flex justify-between text-xs text-gray-500 dark:text-gray-400">
                                        <span>Current streak: <?= $tracker['current_streak'] ?> days</span>
                                        <span>Best streak: <?= $tracker['best_streak'] ?> days</span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- All Trackers -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    All Trackers
                </h2>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (empty($allTrackers)): ?>
                    <div class="text-center py-4">
                        <div class="text-gray-500 dark:text-gray-400">No trackers found</div>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Frequency</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Completion Rate</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Current Streak</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($allTrackers as $tracker): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            <?= View::escape($tracker['name']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= ucfirst($tracker['frequency']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <div class="flex items-center">
                                                <div class="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5 mr-2">
                                                    <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $tracker['completion_rate'] ?>%"></div>
                                                </div>
                                                <span><?= $tracker['completion_rate'] ?>%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $tracker['current_streak'] ?> days
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <a href="/momentum/adhd/consistency/trackers/view/<?= $tracker['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mr-3">
                                                <i class="fas fa-chart-bar"></i>
                                            </a>
                                            <a href="/momentum/adhd/consistency/trackers/delete/<?= $tracker['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this tracker? All history will be lost.')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
