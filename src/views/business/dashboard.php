<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <div class="flex items-center">
                <a href="/momentum/online-business" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    <i class="fas fa-arrow-left"></i>
                    <span class="ml-1">Back to Businesses</span>
                </a>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($venture['name']) ?></h1>
            </div>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($venture['business_type']) ?> • <?= ucfirst($venture['status']) ?></p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
            <a href="/momentum/online-business/metrics/<?= $venture['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-chart-line mr-2"></i> Update Metrics
            </a>
            <a href="/momentum/online-business/edit/<?= $venture['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-edit mr-2"></i> Edit Business
            </a>
        </div>
    </div>

    <!-- Date Range Selector -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-4">
        <form action="/momentum/online-business/dashboard/<?= $venture['id'] ?>" method="get" class="flex flex-col sm:flex-row items-center gap-4">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                <input type="date" id="start_date" name="start_date" value="<?= $startDate ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
            </div>
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                <input type="date" id="end_date" name="end_date" value="<?= $endDate ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
            </div>
            <div>
                <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Period</label>
                <select id="period" name="period" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                    <option value="daily" <?= $period === 'daily' ? 'selected' : '' ?>>Daily</option>
                    <option value="weekly" <?= $period === 'weekly' ? 'selected' : '' ?>>Weekly</option>
                    <option value="monthly" <?= $period === 'monthly' ? 'selected' : '' ?>>Monthly</option>
                </select>
            </div>
            <div class="self-end mt-1">
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Apply
                </button>
            </div>
        </form>
    </div>

    <!-- Key Metrics Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Key Metrics Summary</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <?= date('M j, Y', strtotime($startDate)) ?> - <?= date('M j, Y', strtotime($endDate)) ?>
            </p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Revenue -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                            <i class="fas fa-money-bill-wave text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Revenue</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white">Rs <?= number_format($metricsSummary['total_revenue'] ?? 0, 2) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Expenses -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                            <i class="fas fa-file-invoice text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Expenses</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white">Rs <?= number_format($metricsSummary['total_expenses'] ?? 0, 2) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profit -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <i class="fas fa-chart-line text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Profit</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white">Rs <?= number_format($metricsSummary['total_profit'] ?? 0, 2) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sales Count -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                            <i class="fas fa-shopping-cart text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Sales</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white"><?= number_format($metricsSummary['total_sales'] ?? 0) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- New Customers -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                            <i class="fas fa-users text-yellow-600 dark:text-yellow-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">New Customers</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white"><?= number_format($metricsSummary['total_new_customers'] ?? 0) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Average Order Value -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
                            <i class="fas fa-tag text-indigo-600 dark:text-indigo-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg. Order Value</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white">Rs <?= number_format($metricsSummary['avg_order_value'] ?? 0, 2) ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue & Profit Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Revenue & Profit Trends</h3>
        </div>
        <div class="p-6">
            <div class="h-80">
                <canvas id="revenue-profit-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Sales & Customers Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Sales & Customer Acquisition</h3>
        </div>
        <div class="p-6">
            <div class="h-80">
                <canvas id="sales-customers-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Metrics Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Detailed Metrics</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Revenue</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expenses</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Profit</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Sales</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">New Customers</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php if (empty($metrics)): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                No metrics data available for the selected period.
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($metrics as $metric): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <?= date('M j, Y', strtotime($metric['metric_date'])) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                                    Rs <?= number_format($metric['revenue'], 2) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                                    Rs <?= number_format($metric['expenses'], 2) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right <?= $metric['profit'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                    Rs <?= number_format($metric['profit'], 2) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                                    <?= number_format($metric['sales_count']) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                                    <?= number_format($metric['new_customers']) ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Prepare data for charts
        const metrics = <?= json_encode($metrics) ?>;
        const dates = metrics.map(m => new Date(m.metric_date).toLocaleDateString());
        const revenues = metrics.map(m => parseFloat(m.revenue));
        const profits = metrics.map(m => parseFloat(m.profit));
        const sales = metrics.map(m => parseInt(m.sales_count));
        const customers = metrics.map(m => parseInt(m.new_customers));
        
        // Revenue & Profit Chart
        const revenueCtx = document.getElementById('revenue-profit-chart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: 'Revenue',
                        data: revenues,
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    },
                    {
                        label: 'Profit',
                        data: profits,
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            boxWidth: 6
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += 'Rs ' + context.parsed.y.toFixed(2);
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rs ' + value;
                            }
                        }
                    }
                }
            }
        });
        
        // Sales & Customers Chart
        const salesCtx = document.getElementById('sales-customers-chart').getContext('2d');
        new Chart(salesCtx, {
            type: 'bar',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: 'Sales',
                        data: sales,
                        backgroundColor: 'rgba(124, 58, 237, 0.7)',
                        borderColor: '#7C3AED',
                        borderWidth: 1
                    },
                    {
                        label: 'New Customers',
                        data: customers,
                        backgroundColor: 'rgba(245, 158, 11, 0.7)',
                        borderColor: '#F59E0B',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            boxWidth: 6
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    });
</script>
