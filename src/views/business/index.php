<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Online Business Dashboard</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Monitor and manage your online business ventures</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="/momentum/online-business/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-plus mr-2"></i> Add Business Venture
            </a>
        </div>
    </div>

    <!-- Business Summary -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Business Summary</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Total Ventures -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                            <i class="fas fa-store text-primary-600 dark:text-primary-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Ventures</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white"><?= $ventureSummary['total'] ?? 0 ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Revenue -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                            <i class="fas fa-chart-line text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Revenue</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white">Rs <?= number_format($ventureSummary['monthly_revenue'] ?? 0, 2) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Expenses -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                            <i class="fas fa-money-bill-wave text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Expenses</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white">Rs <?= number_format($ventureSummary['monthly_expenses'] ?? 0, 2) ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Profit -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <i class="fas fa-piggy-bank text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Profit</h4>
                            <div class="mt-1 flex items-baseline">
                                <span class="text-2xl font-semibold text-gray-900 dark:text-white">Rs <?= number_format($ventureSummary['monthly_profit'] ?? 0, 2) ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Breakdown -->
            <div class="mt-6">
                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">Ventures by Status</h4>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white"><?= $ventureSummary['planning'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Planning</div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white"><?= $ventureSummary['startup'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Startup</div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white"><?= $ventureSummary['operational'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Operational</div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white"><?= $ventureSummary['growing'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Growing</div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white"><?= $ventureSummary['declining'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Declining</div>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                        <div class="text-lg font-semibold text-gray-900 dark:text-white"><?= $ventureSummary['inactive'] ?? 0 ?></div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">Inactive</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Business Ventures List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700 flex flex-col md:flex-row md:items-center md:justify-between">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Your Business Ventures</h3>

            <!-- Filters -->
            <div class="mt-3 md:mt-0 flex flex-col sm:flex-row gap-3">
                <select id="status-filter" class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                    <option value="">All Statuses</option>
                    <option value="planning" <?= isset($filters['status']) && $filters['status'] === 'planning' ? 'selected' : '' ?>>Planning</option>
                    <option value="startup" <?= isset($filters['status']) && $filters['status'] === 'startup' ? 'selected' : '' ?>>Startup</option>
                    <option value="operational" <?= isset($filters['status']) && $filters['status'] === 'operational' ? 'selected' : '' ?>>Operational</option>
                    <option value="growing" <?= isset($filters['status']) && $filters['status'] === 'growing' ? 'selected' : '' ?>>Growing</option>
                    <option value="declining" <?= isset($filters['status']) && $filters['status'] === 'declining' ? 'selected' : '' ?>>Declining</option>
                    <option value="inactive" <?= isset($filters['status']) && $filters['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>

                <div class="relative">
                    <input type="text" id="search-filter" placeholder="Search ventures..." class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50 pl-10" value="<?= $filters['search'] ?? '' ?>">
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>

                <button id="apply-filters" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Apply Filters
                </button>
            </div>
        </div>

        <?php if (empty($ventures)): ?>
            <div class="p-6 text-center">
                <p class="text-gray-500 dark:text-gray-400">You don't have any business ventures yet.</p>
                <a href="/momentum/online-business/create" class="mt-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus mr-2"></i> Add Your First Business
                </a>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Business Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Start Date</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($ventures as $venture): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                            <i class="fas fa-store text-primary-600 dark:text-primary-400"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($venture['name']) ?></div>
                                            <?php if (!empty($venture['website'])): ?>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    <a href="<?= htmlspecialchars($venture['website']) ?>" target="_blank" class="hover:text-primary-600 dark:hover:text-primary-400">
                                                        <i class="fas fa-external-link-alt mr-1"></i> <?= htmlspecialchars($venture['website']) ?>
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($venture['business_type']) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                    $statusClasses = [
                                        'planning' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
                                        'startup' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                        'operational' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                        'growing' => 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
                                        'declining' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                        'inactive' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                    ];
                                    $statusClass = $statusClasses[$venture['status']] ?? $statusClasses['planning'];
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusClass ?>">
                                        <?= ucfirst(htmlspecialchars($venture['status'])) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <?= $venture['start_date'] ? date('M j, Y', strtotime($venture['start_date'])) : 'Not set' ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/momentum/online-business/dashboard/<?= $venture['id'] ?>" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3">
                                        <i class="fas fa-chart-bar mr-1"></i> Dashboard
                                    </a>
                                    <a href="/momentum/online-business/edit/<?= $venture['id'] ?>" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3">
                                        <i class="fas fa-edit mr-1"></i> Edit
                                    </a>
                                    <a href="#" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 delete-venture" data-id="<?= $venture['id'] ?>" data-name="<?= htmlspecialchars($venture['name']) ?>">
                                        <i class="fas fa-trash-alt mr-1"></i> Delete
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                <div class="px-6 py-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if ($pagination['current_page'] > 1): ?>
                                <a href="?page=<?= $pagination['current_page'] - 1 ?><?= !empty($filters['status']) ? '&status=' . urlencode($filters['status']) : '' ?><?= !empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '' ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    Previous
                                </a>
                            <?php else: ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 cursor-not-allowed">
                                    Previous
                                </span>
                            <?php endif; ?>

                            <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                <a href="?page=<?= $pagination['current_page'] + 1 ?><?= !empty($filters['status']) ? '&status=' . urlencode($filters['status']) : '' ?><?= !empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '' ?>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    Next
                                </a>
                            <?php else: ?>
                                <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 cursor-not-allowed">
                                    Next
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700 dark:text-gray-300">
                                    Showing <span class="font-medium"><?= (($pagination['current_page'] - 1) * $pagination['per_page']) + 1 ?></span> to <span class="font-medium"><?= min($pagination['current_page'] * $pagination['per_page'], $pagination['total']) ?></span> of <span class="font-medium"><?= $pagination['total'] ?></span> results
                                </p>
                            </div>

                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <!-- Previous Page -->
                                    <?php if ($pagination['current_page'] > 1): ?>
                                        <a href="?page=<?= $pagination['current_page'] - 1 ?><?= !empty($filters['status']) ? '&status=' . urlencode($filters['status']) : '' ?><?= !empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '' ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                                            <span class="sr-only">Previous</span>
                                            <i class="fas fa-chevron-left h-5 w-5"></i>
                                        </a>
                                    <?php else: ?>
                                        <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 cursor-not-allowed">
                                            <span class="sr-only">Previous</span>
                                            <i class="fas fa-chevron-left h-5 w-5"></i>
                                        </span>
                                    <?php endif; ?>

                                    <!-- Page Numbers -->
                                    <?php
                                    $startPage = max(1, $pagination['current_page'] - 2);
                                    $endPage = min($pagination['total_pages'], $pagination['current_page'] + 2);

                                    // Always show first page
                                    if ($startPage > 1) {
                                        echo '<a href="?page=1' . (!empty($filters['status']) ? '&status=' . urlencode($filters['status']) : '') . (!empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '') . '" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">1</a>';

                                        if ($startPage > 2) {
                                            echo '<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200">...</span>';
                                        }
                                    }

                                    // Page links
                                    for ($i = $startPage; $i <= $endPage; $i++) {
                                        if ($i == $pagination['current_page']) {
                                            echo '<span aria-current="page" class="relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 dark:bg-primary-900 text-sm font-medium text-primary-600 dark:text-primary-300">' . $i . '</span>';
                                        } else {
                                            echo '<a href="?page=' . $i . (!empty($filters['status']) ? '&status=' . urlencode($filters['status']) : '') . (!empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '') . '" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">' . $i . '</a>';
                                        }
                                    }

                                    // Always show last page
                                    if ($endPage < $pagination['total_pages']) {
                                        if ($endPage < $pagination['total_pages'] - 1) {
                                            echo '<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200">...</span>';
                                        }

                                        echo '<a href="?page=' . $pagination['total_pages'] . (!empty($filters['status']) ? '&status=' . urlencode($filters['status']) : '') . (!empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '') . '" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">' . $pagination['total_pages'] . '</a>';
                                    }
                                    ?>

                                    <!-- Next Page -->
                                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                        <a href="?page=<?= $pagination['current_page'] + 1 ?><?= !empty($filters['status']) ? '&status=' . urlencode($filters['status']) : '' ?><?= !empty($filters['search']) ? '&search=' . urlencode($filters['search']) : '' ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                                            <span class="sr-only">Next</span>
                                            <i class="fas fa-chevron-right h-5 w-5"></i>
                                        </a>
                                    <?php else: ?>
                                        <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 cursor-not-allowed">
                                            <span class="sr-only">Next</span>
                                            <i class="fas fa-chevron-right h-5 w-5"></i>
                                        </span>
                                    <?php endif; ?>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:max-w-lg sm:w-full sm:p-6">
        <div>
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
            </div>
            <div class="mt-3 text-center sm:mt-5">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">Delete Business Venture</h3>
                <div class="mt-2">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Are you sure you want to delete <span id="venture-name" class="font-semibold"></span>? This action cannot be undone.</p>
                </div>
            </div>
        </div>
        <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button type="button" id="confirm-delete" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:col-start-2 sm:text-sm">
                Delete
            </button>
            <button type="button" id="cancel-delete" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                Cancel
            </button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter functionality
        document.getElementById('apply-filters').addEventListener('click', function() {
            const status = document.getElementById('status-filter').value;
            const search = document.getElementById('search-filter').value;

            let url = '/momentum/online-business';
            const params = [];

            if (status) {
                params.push(`status=${encodeURIComponent(status)}`);
            }

            if (search) {
                params.push(`search=${encodeURIComponent(search)}`);
            }

            if (params.length > 0) {
                url += '?' + params.join('&');
            }

            window.location.href = url;
        });

        // Delete functionality
        const deleteButtons = document.querySelectorAll('.delete-venture');
        const deleteModal = document.getElementById('delete-modal');
        const ventureName = document.getElementById('venture-name');
        const confirmDelete = document.getElementById('confirm-delete');
        const cancelDelete = document.getElementById('cancel-delete');
        let ventureId = null;

        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                ventureId = this.dataset.id;
                ventureName.textContent = this.dataset.name;
                deleteModal.classList.remove('hidden');
            });
        });

        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
            ventureId = null;
        });

        confirmDelete.addEventListener('click', function() {
            if (ventureId) {
                window.location.href = `/momentum/online-business/delete/${ventureId}`;
            }
        });
    });
</script>
