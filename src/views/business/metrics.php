<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex items-center mb-8">
        <a href="/momentum/online-business/dashboard/<?= $venture['id'] ?>" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <i class="fas fa-arrow-left"></i>
            <span class="ml-1">Back to Dashboard</span>
        </a>
        <h1 class="text-3xl font-bold">Update Business Metrics</h1>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div class="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white"><?= htmlspecialchars($venture['name']) ?> - Metrics</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Enter the key performance metrics for your business.</p>
        </div>
        
        <form action="/momentum/online-business/save-metrics/<?= $venture['id'] ?>" method="post" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Metric Date -->
                <div>
                    <label for="metric_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date <span class="text-red-600">*</span></label>
                    <input type="date" name="metric_date" id="metric_date" value="<?= $data['metric_date'] ?? date('Y-m-d') ?>" required class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                    <?php if (isset($errors['metric_date'])): ?>
                        <p class="mt-1 text-sm text-red-600"><?= $errors['metric_date'] ?></p>
                    <?php endif; ?>
                </div>

                <!-- Revenue -->
                <div>
                    <label for="revenue" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Revenue (Rs)</label>
                    <input type="number" name="revenue" id="revenue" value="<?= $data['revenue'] ?? ($latestMetrics['revenue'] ?? 0) ?>" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                </div>

                <!-- Expenses -->
                <div>
                    <label for="expenses" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Expenses (Rs)</label>
                    <input type="number" name="expenses" id="expenses" value="<?= $data['expenses'] ?? ($latestMetrics['expenses'] ?? 0) ?>" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                </div>

                <!-- Profit (Calculated) -->
                <div>
                    <label for="profit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Profit (Rs)</label>
                    <input type="number" name="profit" id="profit" value="<?= $data['profit'] ?? ($latestMetrics['profit'] ?? 0) ?>" step="0.01" readonly class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm bg-gray-50 dark:bg-gray-600">
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Automatically calculated from revenue and expenses</p>
                </div>

                <!-- Sales Count -->
                <div>
                    <label for="sales_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Number of Sales</label>
                    <input type="number" name="sales_count" id="sales_count" value="<?= $data['sales_count'] ?? ($latestMetrics['sales_count'] ?? 0) ?>" min="0" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                </div>

                <!-- New Customers -->
                <div>
                    <label for="new_customers" class="block text-sm font-medium text-gray-700 dark:text-gray-300">New Customers</label>
                    <input type="number" name="new_customers" id="new_customers" value="<?= $data['new_customers'] ?? ($latestMetrics['new_customers'] ?? 0) ?>" min="0" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                </div>

                <!-- Website Visits -->
                <div>
                    <label for="website_visits" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Website Visits</label>
                    <input type="number" name="website_visits" id="website_visits" value="<?= $data['website_visits'] ?? ($latestMetrics['website_visits'] ?? '') ?>" min="0" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                </div>

                <!-- Conversion Rate -->
                <div>
                    <label for="conversion_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Conversion Rate (%)</label>
                    <input type="number" name="conversion_rate" id="conversion_rate" value="<?= $data['conversion_rate'] ?? ($latestMetrics['conversion_rate'] ?? '') ?>" step="0.01" min="0" max="100" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                </div>

                <!-- Average Order Value (Calculated) -->
                <div>
                    <label for="average_order_value" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Average Order Value (Rs)</label>
                    <input type="number" name="average_order_value" id="average_order_value" value="<?= $data['average_order_value'] ?? ($latestMetrics['average_order_value'] ?? 0) ?>" step="0.01" readonly class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm bg-gray-50 dark:bg-gray-600">
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Automatically calculated from revenue and sales count</p>
                </div>

                <!-- Notes -->
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                    <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50"><?= $data['notes'] ?? ($latestMetrics['notes'] ?? '') ?></textarea>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <a href="/momentum/online-business/dashboard/<?= $venture['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-3">
                    Cancel
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Save Metrics
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calculate profit and average order value automatically
        const revenueInput = document.getElementById('revenue');
        const expensesInput = document.getElementById('expenses');
        const profitInput = document.getElementById('profit');
        const salesCountInput = document.getElementById('sales_count');
        const avgOrderValueInput = document.getElementById('average_order_value');
        
        function calculateProfit() {
            const revenue = parseFloat(revenueInput.value) || 0;
            const expenses = parseFloat(expensesInput.value) || 0;
            const profit = revenue - expenses;
            profitInput.value = profit.toFixed(2);
        }
        
        function calculateAvgOrderValue() {
            const revenue = parseFloat(revenueInput.value) || 0;
            const salesCount = parseInt(salesCountInput.value) || 0;
            const avgOrderValue = salesCount > 0 ? revenue / salesCount : 0;
            avgOrderValueInput.value = avgOrderValue.toFixed(2);
        }
        
        // Initial calculations
        calculateProfit();
        calculateAvgOrderValue();
        
        // Add event listeners
        revenueInput.addEventListener('input', function() {
            calculateProfit();
            calculateAvgOrderValue();
        });
        
        expensesInput.addEventListener('input', calculateProfit);
        
        salesCountInput.addEventListener('input', calculateAvgOrderValue);
    });
</script>
