<?php
/**
 * AI Assistant Widget
 *
 * This widget displays AI prompt system and quick capture tools on the main dashboard
 */

// Debug: Check if data is available
if (!isset($aiAssistantData)) {
    echo "<!-- DEBUG: aiAssistantData not set -->";
    $aiAssistantData = [
        'promptStats' => ['total_prompts' => 0],
        'captureStats' => ['total_captures' => 0],
        'weeklyUsage' => 0,
        'favoriteCount' => 0,
        'recentActivity' => [],
        'favoritePrompts' => []
    ];
} else {
    echo "<!-- DEBUG: aiAssistantData is available -->";
}
?>
<div class="px-5 py-5">
    <div class="flex justify-between items-center mb-4">
        <div class="flex items-center">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-magic text-purple-500 mr-2.5"></i> AI Assistant
            </h2>
            <span class="ml-3 px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded-full">
                New Feature
            </span>
        </div>
        <div class="flex space-x-2">
            <button onclick="openQuickCapture()" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                <i class="fas fa-camera mr-1"></i> Quick Capture
            </button>
            <a href="/momentum/ai-prompts" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                <i class="fas fa-cog mr-1"></i> Manage
            </a>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <!-- Total Prompts -->
        <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-2">
                    <i class="fas fa-brain text-purple-600 dark:text-purple-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                        Prompts
                    </p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-white">
                        <?= $aiAssistantData['promptStats']['total_prompts'] ?? 0 ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Captures -->
        <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-2">
                    <i class="fas fa-camera text-blue-600 dark:text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                        Captures
                    </p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-white">
                        <?= $aiAssistantData['captureStats']['total_captures'] ?? 0 ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Usage This Week -->
        <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-2">
                    <i class="fas fa-chart-line text-green-600 dark:text-green-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                        This Week
                    </p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-white">
                        <?= $aiAssistantData['weeklyUsage'] ?? 0 ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Favorites -->
        <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-2">
                    <i class="fas fa-star text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                        Favorites
                    </p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-white">
                        <?= $aiAssistantData['favoriteCount'] ?? 0 ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-4">
        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
            <button onclick="openPromptBuilder()" class="flex items-center justify-center p-3 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/40 rounded-lg border border-purple-200 dark:border-purple-700 transition-colors duration-200">
                <div class="text-center">
                    <i class="fas fa-plus-circle text-purple-600 dark:text-purple-400 text-lg mb-1"></i>
                    <p class="text-xs font-medium text-purple-700 dark:text-purple-300">New Prompt</p>
                </div>
            </button>

            <button onclick="openScreenshotTool()" class="flex items-center justify-center p-3 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 rounded-lg border border-blue-200 dark:border-blue-700 transition-colors duration-200">
                <div class="text-center">
                    <i class="fas fa-camera text-blue-600 dark:text-blue-400 text-lg mb-1"></i>
                    <p class="text-xs font-medium text-blue-700 dark:text-blue-300">Screenshot</p>
                </div>
            </button>

            <button onclick="openQuickNote()" class="flex items-center justify-center p-3 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/40 rounded-lg border border-green-200 dark:border-green-700 transition-colors duration-200">
                <div class="text-center">
                    <i class="fas fa-sticky-note text-green-600 dark:text-green-400 text-lg mb-1"></i>
                    <p class="text-xs font-medium text-green-700 dark:text-green-300">Quick Note</p>
                </div>
            </button>

            <button onclick="openPromptLibrary()" class="flex items-center justify-center p-3 bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/40 rounded-lg border border-orange-200 dark:border-orange-700 transition-colors duration-200">
                <div class="text-center">
                    <i class="fas fa-book text-orange-600 dark:text-orange-400 text-lg mb-1"></i>
                    <p class="text-xs font-medium text-orange-700 dark:text-orange-300">Library</p>
                </div>
            </button>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="mb-4">
        <div class="flex justify-between items-center mb-3">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white">Recent Activity</h3>
            <a href="/momentum/ai-prompts/history" class="text-xs text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300">View All</a>
        </div>

        <?php if (!empty($aiAssistantData['recentActivity'])): ?>
            <div class="space-y-2">
                <?php foreach (array_slice($aiAssistantData['recentActivity'], 0, 3) as $activity): ?>
                    <div class="flex items-center p-2 bg-gray-50 dark:bg-gray-750 rounded-lg">
                        <div class="flex-shrink-0">
                            <?php if ($activity['type'] === 'prompt'): ?>
                                <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                                    <i class="fas fa-brain text-purple-600 dark:text-purple-400 text-sm"></i>
                                </div>
                            <?php elseif ($activity['type'] === 'capture'): ?>
                                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                    <i class="fas fa-camera text-blue-600 dark:text-blue-400 text-sm"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="ml-3 flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                <?= htmlspecialchars($activity['title']) ?>
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <?= $activity['action'] ?> • <?= date('M j, g:i A', strtotime($activity['created_at'])) ?>
                            </p>
                        </div>
                        <div class="flex-shrink-0">
                            <a href="<?= $activity['url'] ?>" class="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300">
                                <i class="fas fa-external-link-alt text-xs"></i>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <i class="fas fa-magic text-gray-400 text-2xl mb-2"></i>
                <p class="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
                <p class="text-xs text-gray-400 dark:text-gray-500">Start by creating your first prompt or capture</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Favorite Prompts -->
    <?php if (!empty($aiAssistantData['favoritePrompts'])): ?>
        <div>
            <div class="flex justify-between items-center mb-3">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">Favorite Prompts</h3>
                <a href="/momentum/ai-prompts?filter=favorites" class="text-xs text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300">View All</a>
            </div>

            <div class="space-y-2">
                <?php foreach (array_slice($aiAssistantData['favoritePrompts'], 0, 3) as $prompt): ?>
                    <div class="flex items-center p-2 bg-gray-50 dark:bg-gray-750 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background-color: <?= $prompt['category_color'] ?? '#6366F1' ?>20;">
                                <i class="fas fa-star text-yellow-500 text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-3 flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                <?= htmlspecialchars($prompt['title']) ?>
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <?= $prompt['category_name'] ?? 'Uncategorized' ?> • Used <?= $prompt['usage_count'] ?> times
                            </p>
                        </div>
                        <div class="flex-shrink-0 flex space-x-1">
                            <button onclick="executePrompt(<?= $prompt['id'] ?>)" class="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300" title="Execute Prompt">
                                <i class="fas fa-play text-xs"></i>
                            </button>
                            <a href="/momentum/ai-prompts/view/<?= $prompt['id'] ?>" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300" title="View Details">
                                <i class="fas fa-external-link-alt text-xs"></i>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Quick Capture Modal -->
<div id="quickCaptureModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Quick Capture</h3>
                <button onclick="closeQuickCapture()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="space-y-3">
                <button onclick="startScreenshot()" class="w-full flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 rounded-lg border border-blue-200 dark:border-blue-700 transition-colors duration-200">
                    <i class="fas fa-camera text-blue-600 dark:text-blue-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-blue-700 dark:text-blue-300">Take Screenshot</p>
                        <p class="text-xs text-blue-600 dark:text-blue-400">Capture and annotate screen content</p>
                    </div>
                </button>

                <button onclick="createQuickNote()" class="w-full flex items-center p-3 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/40 rounded-lg border border-green-200 dark:border-green-700 transition-colors duration-200">
                    <i class="fas fa-sticky-note text-green-600 dark:text-green-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-green-700 dark:text-green-300">Quick Note</p>
                        <p class="text-xs text-green-600 dark:text-green-400">Create a text note instantly</p>
                    </div>
                </button>

                <button onclick="startVoiceNote()" class="w-full flex items-center p-3 bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/40 rounded-lg border border-orange-200 dark:border-orange-700 transition-colors duration-200">
                    <i class="fas fa-microphone text-orange-600 dark:text-orange-400 mr-3"></i>
                    <div class="text-left">
                        <p class="font-medium text-orange-700 dark:text-orange-300">Voice Note</p>
                        <p class="text-xs text-orange-600 dark:text-orange-400">Record audio with transcription</p>
                    </div>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Quick Capture Modal Functions
function openQuickCapture() {
    document.getElementById('quickCaptureModal').classList.remove('hidden');
}

function closeQuickCapture() {
    document.getElementById('quickCaptureModal').classList.add('hidden');
}

function startScreenshot() {
    closeQuickCapture();
    window.open('/momentum/quick-capture/screenshot', '_blank', 'width=1200,height=800');
}

function createQuickNote() {
    closeQuickCapture();
    // Open quick note modal or redirect to note creation
    window.location.href = '/momentum/quick-capture/note';
}

function startVoiceNote() {
    closeQuickCapture();
    // Open voice recording interface
    window.location.href = '/momentum/quick-capture/voice';
}

// Other widget functions
function openPromptBuilder() {
    window.location.href = '/momentum/ai-prompts/create';
}

function openScreenshotTool() {
    window.open('/momentum/quick-capture/screenshot', '_blank', 'width=1200,height=800');
}

function openQuickNote() {
    window.location.href = '/momentum/quick-capture/note';
}

function openPromptLibrary() {
    window.location.href = '/momentum/ai-prompts/library';
}

function executePrompt(promptId) {
    window.location.href = '/momentum/ai-prompts/execute/' + promptId;
}

// Close modal when clicking outside
document.getElementById('quickCaptureModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuickCapture();
    }
});
</script>
