<div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <div>
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Passive Income Portfolio</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                Track and manage your passive income streams
            </p>
        </div>
        <a href="/momentum/passive-income" class="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
            <i class="fas fa-external-link-alt mr-1"></i> View All
        </a>
    </div>
    
    <?php if (empty($activeStreams)): ?>
        <div class="p-6 text-center">
            <p class="text-gray-500 dark:text-gray-400">No active passive income streams yet.</p>
            <a href="/momentum/passive-income/create" class="inline-flex items-center justify-center px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i> Add Stream
            </a>
        </div>
    <?php else: ?>
        <div class="p-4">
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
                    <p class="text-xs text-green-500 dark:text-green-400 font-medium">Total Earnings</p>
                    <p class="text-2xl font-bold text-green-700 dark:text-green-300">Rs <?= number_format($streamsSummary['total_earnings'] ?? 0, 2) ?></p>
                </div>
                <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                    <p class="text-xs text-blue-500 dark:text-blue-400 font-medium">Active Streams</p>
                    <p class="text-2xl font-bold text-blue-700 dark:text-blue-300"><?= ($streamsSummary['growing_streams'] ?? 0) + ($streamsSummary['stable_streams'] ?? 0) ?></p>
                </div>
            </div>
            
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Top Performing Streams</h4>
            <ul class="space-y-3">
                <?php foreach ($activeStreams as $stream): ?>
                    <li class="border-l-4 border-green-500 pl-3 py-2">
                        <a href="/momentum/passive-income/view/<?= $stream['id'] ?>" class="block hover:text-primary-600 dark:hover:text-primary-400">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($stream['name']) ?></h5>
                                    <p class="text-xs text-gray-500 dark:text-gray-400"><?= htmlspecialchars($stream['category']) ?></p>
                                </div>
                                <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                    Rs <?= number_format($stream['total_earnings'], 2) ?>
                                </span>
                            </div>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
            
            <div class="mt-4 text-center">
                <a href="/momentum/passive-income/create" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                    <i class="fas fa-plus-circle mr-1"></i> Add New Stream
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>
