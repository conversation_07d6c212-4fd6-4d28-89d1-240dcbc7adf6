<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 mr-2"></i> Parameter History: <?= htmlspecialchars($parameterName) ?>
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/medical/reports" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Reports
                </a>
            </div>
        </div>

        <!-- Parameter History -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Parameter History
                </h2>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Tracking changes in <?= htmlspecialchars($parameterName) ?> over time
                </p>
            </div>
            
            <?php if (empty($history)): ?>
                <div class="px-4 py-5 sm:p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No history found for this parameter</p>
                </div>
            <?php else: ?>
                <!-- Parameter Chart -->
                <div class="px-4 py-5 sm:p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">
                        Trend Chart
                    </h3>
                    
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div class="h-64 flex items-center justify-center">
                            <div class="text-center text-gray-500 dark:text-gray-400">
                                <i class="fas fa-chart-line text-4xl mb-2"></i>
                                <p>Chart visualization will be implemented in a future update.</p>
                                <p class="text-sm mt-2">For now, please refer to the data table below.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Parameter Data Table -->
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">
                        Data Table
                    </h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Value
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Unit
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Reference Range
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Report
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($history as $record): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= date('M j, Y', strtotime($record['report_date'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($record['parameter_value']) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= htmlspecialchars($record['unit'] ?? '') ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?php if (!empty($record['reference_range_min']) && !empty($record['reference_range_max'])): ?>
                                                <?= htmlspecialchars($record['reference_range_min']) ?> - <?= htmlspecialchars($record['reference_range_max']) ?>
                                            <?php elseif (!empty($record['reference_range_min'])): ?>
                                                > <?= htmlspecialchars($record['reference_range_min']) ?>
                                            <?php elseif (!empty($record['reference_range_max'])): ?>
                                                < <?= htmlspecialchars($record['reference_range_max']) ?>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($record['is_abnormal']): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                                                    Abnormal
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                    Normal
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <a href="/momentum/medical/reports/view/<?= $record['report_id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                                <?= htmlspecialchars($record['report_title']) ?>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Statistics -->
                <div class="px-4 py-5 sm:p-6 border-t border-gray-200 dark:border-gray-700">
                    <h3 class="text-md font-medium text-gray-900 dark:text-white mb-4">
                        Statistics
                    </h3>
                    
                    <?php
                    // Calculate statistics
                    $values = array_column($history, 'parameter_value');
                    $numericValues = array_filter($values, 'is_numeric');
                    
                    if (!empty($numericValues)) {
                        $min = min($numericValues);
                        $max = max($numericValues);
                        $avg = array_sum($numericValues) / count($numericValues);
                        $latest = reset($numericValues); // First element (most recent)
                        $oldest = end($numericValues);   // Last element (oldest)
                        $change = $latest - $oldest;
                        $percentChange = $oldest != 0 ? ($change / abs($oldest)) * 100 : 0;
                    }
                    ?>
                    
                    <?php if (!empty($numericValues)): ?>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                                    Average
                                </div>
                                <div class="text-xl font-semibold text-gray-900 dark:text-white">
                                    <?= number_format($avg, 2) ?> <?= htmlspecialchars($history[0]['unit'] ?? '') ?>
                                </div>
                            </div>
                            
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                                    Minimum
                                </div>
                                <div class="text-xl font-semibold text-gray-900 dark:text-white">
                                    <?= number_format($min, 2) ?> <?= htmlspecialchars($history[0]['unit'] ?? '') ?>
                                </div>
                            </div>
                            
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                                    Maximum
                                </div>
                                <div class="text-xl font-semibold text-gray-900 dark:text-white">
                                    <?= number_format($max, 2) ?> <?= htmlspecialchars($history[0]['unit'] ?? '') ?>
                                </div>
                            </div>
                            
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                                    Change
                                </div>
                                <div class="flex items-center">
                                    <div class="text-xl font-semibold text-gray-900 dark:text-white">
                                        <?= number_format($change, 2) ?> <?= htmlspecialchars($history[0]['unit'] ?? '') ?>
                                    </div>
                                    <div class="ml-2">
                                        <?php if ($change > 0): ?>
                                            <span class="text-green-600 dark:text-green-400">
                                                <i class="fas fa-arrow-up"></i> <?= number_format(abs($percentChange), 1) ?>%
                                            </span>
                                        <?php elseif ($change < 0): ?>
                                            <span class="text-red-600 dark:text-red-400">
                                                <i class="fas fa-arrow-down"></i> <?= number_format(abs($percentChange), 1) ?>%
                                            </span>
                                        <?php else: ?>
                                            <span class="text-gray-500 dark:text-gray-400">
                                                <i class="fas fa-minus"></i> 0%
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                            <p class="text-gray-500 dark:text-gray-400">
                                Statistics cannot be calculated for non-numeric values
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Recommendations -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Recommendations
                </h2>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Based on your parameter history
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-md">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800 dark:text-blue-300">Note</h3>
                            <div class="text-sm text-blue-700 dark:text-blue-200">
                                <p>Personalized recommendations based on your medical data will be available in a future update. Please consult with your healthcare provider for medical advice.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
