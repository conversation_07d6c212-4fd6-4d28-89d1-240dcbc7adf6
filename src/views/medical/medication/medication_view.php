<?php
/**
 * Medical Medication View
 *
 * Displays details of a specific medication
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back navigation -->
        <div class="mb-6">
            <a href="/momentum/medical/medication" class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Medications
            </a>
        </div>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-pills text-primary-600 dark:text-primary-400 mr-2"></i> <?= htmlspecialchars($medication['name']) ?>
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/medical/medication/edit/<?= $medication['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-edit mr-1"></i> Edit
                </a>
                <a href="/momentum/medical/medication/log/<?= $medication['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-clipboard-check mr-1"></i> Log Intake
                </a>
            </div>
        </div>

        <!-- Medication Details -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Medication Details
                </h2>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Dosage</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <?= htmlspecialchars($medication['dosage']) ?> <?= htmlspecialchars($medication['dosage_unit']) ?>
                        </dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Frequency</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <?php
                            $frequencyMap = [
                                'daily' => 'Daily',
                                'twice_daily' => 'Twice Daily',
                                'three_times_daily' => 'Three Times Daily',
                                'four_times_daily' => 'Four Times Daily',
                                'weekly' => 'Weekly',
                                'as_needed' => 'As Needed',
                                'custom' => 'Custom Schedule'
                            ];
                            echo $frequencyMap[$medication['frequency']] ?? $medication['frequency'];
                            ?>
                        </dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Start Date</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <?= date('F j, Y', strtotime($medication['start_date'])) ?>
                        </dd>
                    </div>

                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">End Date</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <?= $medication['end_date'] ? date('F j, Y', strtotime($medication['end_date'])) : 'Ongoing' ?>
                        </dd>
                    </div>

                    <?php if (!empty($medication['prescriber'])): ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Prescriber</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <?= htmlspecialchars($medication['prescriber']) ?>
                        </dd>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($medication['pharmacy'])): ?>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Pharmacy</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <?= htmlspecialchars($medication['pharmacy']) ?>
                        </dd>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($medication['instructions'])): ?>
                    <div class="md:col-span-2">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Instructions</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <?= nl2br(htmlspecialchars($medication['instructions'])) ?>
                        </dd>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($medication['notes'])): ?>
                    <div class="md:col-span-2">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <?= nl2br(htmlspecialchars($medication['notes'])) ?>
                        </dd>
                    </div>
                    <?php endif; ?>
                </dl>
            </div>
        </div>

        <!-- Medication Reminders -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-bell text-primary-600 dark:text-primary-400 mr-2"></i> Reminders
                </h2>
                <a href="/momentum/medical/medication/reminder/new/<?= $medication['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Add Reminder
                </a>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <?php if (empty($reminders)): ?>
                    <div class="px-4 py-5 sm:p-6 text-center">
                        <p class="text-gray-500 dark:text-gray-400">No reminders set for this medication.</p>
                        <a href="/momentum/medical/medication/reminder/new/<?= $medication['id'] ?>" class="inline-flex items-center mt-3 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Add Reminder
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Days</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($reminders as $reminder): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            <?= date('g:i A', strtotime($reminder['reminder_time'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?php
                                            if (empty($reminder['days_of_week'])) {
                                                echo 'Every day';
                                            } else {
                                                $days = explode(',', $reminder['days_of_week']);
                                                $dayNames = [
                                                    '1' => 'Sun',
                                                    '2' => 'Mon',
                                                    '3' => 'Tue',
                                                    '4' => 'Wed',
                                                    '5' => 'Thu',
                                                    '6' => 'Fri',
                                                    '7' => 'Sat'
                                                ];
                                                $dayLabels = [];
                                                foreach ($days as $day) {
                                                    $dayLabels[] = $dayNames[$day] ?? $day;
                                                }
                                                echo implode(', ', $dayLabels);
                                            }
                                            ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <?php if ($reminder['active']): ?>
                                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                    <i class="fas fa-check mr-1"></i> Active
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                    <i class="fas fa-pause mr-1"></i> Inactive
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="/momentum/medical/medication/reminder/edit/<?= $reminder['id'] ?>" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="/momentum/medical/medication/reminder/delete/<?= $reminder['id'] ?>" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this reminder?');">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Logs -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-clipboard-list text-primary-600 dark:text-primary-400 mr-2"></i> Recent Logs
                </h2>
                <div>
                    <a href="/momentum/medical/medication/log/<?= $medication['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-2">
                        <i class="fas fa-plus mr-1"></i> Add Log
                    </a>
                    <a href="/momentum/medical/medication/reports" class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-xs font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-chart-bar mr-1"></i> View Reports
                    </a>
                </div>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <?php if (empty($logs)): ?>
                    <div class="px-4 py-5 sm:p-6 text-center">
                        <p class="text-gray-500 dark:text-gray-400">No logs recorded for this medication.</p>
                        <a href="/momentum/medical/medication/log/<?= $medication['id'] ?>" class="inline-flex items-center mt-3 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Add Log
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Effectiveness</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($logs as $log): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            <?= date('M j, Y', strtotime($log['log_date'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $log['log_time'] ? date('g:i A', strtotime($log['log_time'])) : 'N/A' ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <?php if ($log['taken'] == 1): ?>
                                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                                    <i class="fas fa-check mr-1"></i> Taken
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                                                    <i class="fas fa-times mr-1"></i> Missed
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $log['effectiveness_rating'] ? $log['effectiveness_rating'] . '/10' : 'N/A' ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="/momentum/medical/medication/log/edit/<?= $log['id'] ?>" class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="/momentum/medical/medication/log/delete/<?= $log['id'] ?>" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this log?');">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Effectiveness Chart -->
        <?php if (!empty($effectivenessData)): ?>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 mr-2"></i> Effectiveness Tracking
                </h2>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <div id="effectiveness-chart" class="h-80"></div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row sm:justify-between gap-3">
            <div>
                <a href="/momentum/medical/medication" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Medications
                </a>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <a href="/momentum/medical/medication/edit/<?= $medication['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-edit mr-1"></i> Edit Medication
                </a>
                <a href="/momentum/medical/medication/delete/<?= $medication['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200" onclick="return confirm('Are you sure you want to delete this medication? This will also delete all logs and reminders.');">
                    <i class="fas fa-trash mr-1"></i> Delete Medication
                </a>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($effectivenessData)): ?>
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Effectiveness Chart
    const effectivenessData = <?= json_encode($effectivenessData) ?>;

    // Process data for chart
    const dates = effectivenessData.map(item => item.log_date);
    const effectiveness = effectivenessData.map(item => item.effectiveness_rating ? parseFloat(item.effectiveness_rating) : null);
    const sideEffects = effectivenessData.map(item => item.side_effects_severity ? parseFloat(item.side_effects_severity) : null);

    const options = {
        series: [
            {
                name: 'Effectiveness',
                data: effectiveness
            },
            {
                name: 'Side Effects',
                data: sideEffects
            }
        ],
        chart: {
            type: 'line',
            height: 350,
            fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
            toolbar: {
                show: false
            },
            background: 'transparent'
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        xaxis: {
            categories: dates.map(date => {
                const d = new Date(date);
                return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }),
            labels: {
                style: {
                    colors: '#6B7280'
                }
            }
        },
        yaxis: {
            min: 0,
            max: 10,
            title: {
                text: 'Rating (1-10)',
                style: {
                    color: '#6B7280'
                }
            },
            labels: {
                style: {
                    colors: '#6B7280'
                }
            }
        },
        tooltip: {
            y: {
                formatter: function(val) {
                    return val + "/10";
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'center',
            labels: {
                colors: '#6B7280'
            }
        },
        grid: {
            borderColor: '#E5E7EB',
            row: {
                colors: ['transparent', 'transparent']
            }
        },
        colors: ['#3B82F6', '#EF4444'],
        theme: {
            mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
        }
    };

    const chart = new ApexCharts(document.querySelector("#effectiveness-chart"), options);
    chart.render();
});
</script>
<?php endif; ?>
