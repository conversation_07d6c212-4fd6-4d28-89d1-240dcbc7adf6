<?php
/**
 * Medical Medication Tracker Index View
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-pills text-primary-600 dark:text-primary-400 mr-2"></i> Medication Tracker
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/medical/medication/new" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Add Medication
                </a>
                <a href="/momentum/medical/medication/log" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-check-circle mr-1"></i> Log Medication
                </a>
                <a href="/momentum/medical/medication/reports" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    <i class="fas fa-chart-bar mr-1"></i> Reports
                </a>
                <a href="/momentum/adhd/medication/reference" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                    <i class="fas fa-book-medical mr-1"></i> Medication Reference
                </a>
                <a href="/momentum/medical" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Medical Dashboard
                </a>
            </div>
        </div>

        <?php if (empty($medications)): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <div class="text-gray-500 dark:text-gray-400 mb-4">
                    <i class="fas fa-pills text-5xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">No medications added yet</h3>
                    <p class="mt-1">Start tracking your medications to monitor effectiveness and adherence.</p>
                </div>
                <a href="/momentum/medical/medication/new" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Add Your First Medication
                </a>
            </div>
        <?php else: ?>
            <!-- Today's Medications -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-calendar-day text-primary-600 dark:text-primary-400 mr-2"></i> Today's Medications
                    </h2>
                    <a href="/momentum/medical/medication/log" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        Log Medication <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                    <?php if (empty($todayLogs)): ?>
                        <div class="text-center py-4">
                            <p class="text-gray-500 dark:text-gray-400">No medications logged for today.</p>
                            <a href="/momentum/medical/medication/log" class="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-plus mr-1"></i> Log Now
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Medication</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Effectiveness</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Side Effects</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <?php foreach ($todayLogs as $log): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($log['name']) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                <?= date('h:i A', strtotime($log['log_time'])) ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                <?php if ($log['taken']): ?>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100">
                                                        Taken
                                                    </span>
                                                <?php else: ?>
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-100">
                                                        Missed
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                <?php if ($log['effectiveness_rating']): ?>
                                                    <div class="flex items-center">
                                                        <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: <?= ($log['effectiveness_rating'] * 10) ?>%"></div>
                                                        </div>
                                                        <span class="ml-2"><?= $log['effectiveness_rating'] ?>/10</span>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-gray-400 dark:text-gray-500">Not rated</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                <?php if ($log['side_effects_severity']): ?>
                                                    <div class="flex items-center">
                                                        <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                            <div class="bg-yellow-500 h-2.5 rounded-full" style="width: <?= ($log['side_effects_severity'] * 10) ?>%"></div>
                                                        </div>
                                                        <span class="ml-2"><?= $log['side_effects_severity'] ?>/10</span>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-gray-400 dark:text-gray-500">None reported</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Medications List -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-list text-primary-600 dark:text-primary-400 mr-2"></i> Your Medications
                    </h2>
                    <a href="/momentum/medical/medication/new" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        Add New <i class="fas fa-plus ml-1"></i>
                    </a>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                        <?php foreach ($medications as $medication): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex justify-between items-start">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                        <?= htmlspecialchars($medication['name']) ?>
                                    </h3>
                                    <span class="px-2 py-1 text-xs rounded-full <?= ($medication['end_date'] && strtotime($medication['end_date']) < time()) ? 'bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300' : 'bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100' ?>">
                                        <?= ($medication['end_date'] && strtotime($medication['end_date']) < time()) ? 'Inactive' : 'Active' ?>
                                    </span>
                                </div>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    <?= htmlspecialchars($medication['dosage']) ?> <?= htmlspecialchars($medication['dosage_unit']) ?>
                                </p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    <strong>Frequency:</strong> <?= ucfirst(str_replace('_', ' ', $medication['frequency'])) ?>
                                </p>
                                <?php if (isset($adherenceRates[$medication['id']])): ?>
                                    <div class="mt-2">
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">Adherence Rate (30 days)</p>
                                        <div class="flex items-center">
                                            <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                                                <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $adherenceRates[$medication['id']] ?>%"></div>
                                            </div>
                                            <span class="ml-2 text-xs font-medium text-gray-700 dark:text-gray-300"><?= round($adherenceRates[$medication['id']]) ?>%</span>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="mt-4 flex justify-end">
                                    <a href="/momentum/medical/medication/view/<?= $medication['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 text-sm">
                                        View Details <i class="fas fa-arrow-right ml-1"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
