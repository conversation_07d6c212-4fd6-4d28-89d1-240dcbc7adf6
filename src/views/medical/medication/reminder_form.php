<?php
/**
 * Medical Medication Reminder Form
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-bell text-primary-600 dark:text-primary-400 mr-2"></i> Add Medication Reminder
            </h1>
            <a href="/momentum/medical/medication/view/<?= $medication['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    Reminder for <?= htmlspecialchars($medication['name']) ?>
                </h2>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Set up a reminder to take your medication.
                </p>
            </div>
            <form action="/momentum/medical/medication/reminder/save" method="POST" class="px-4 py-5 sm:p-6">
                <input type="hidden" name="medication_id" value="<?= $medication['id'] ?>">
                
                <div class="grid grid-cols-1 gap-6">
                    <!-- Reminder Time -->
                    <div>
                        <label for="reminder_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Reminder Time <span class="text-red-500">*</span>
                        </label>
                        <input type="time" name="reminder_time" id="reminder_time" required
                            class="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            The time of day when you want to be reminded to take this medication.
                        </p>
                    </div>

                    <!-- Days of Week -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Days of Week
                        </label>
                        <div class="grid grid-cols-4 gap-3">
                            <div class="flex items-center">
                                <input type="checkbox" name="days_of_week[]" value="1" id="day_1"
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                <label for="day_1" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Sunday
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="days_of_week[]" value="2" id="day_2"
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                <label for="day_2" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Monday
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="days_of_week[]" value="3" id="day_3"
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                <label for="day_3" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Tuesday
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="days_of_week[]" value="4" id="day_4"
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                <label for="day_4" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Wednesday
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="days_of_week[]" value="5" id="day_5"
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                <label for="day_5" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Thursday
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="days_of_week[]" value="6" id="day_6"
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                <label for="day_6" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Friday
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="days_of_week[]" value="7" id="day_7"
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                                <label for="day_7" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Saturday
                                </label>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Leave all unchecked to remind every day.
                        </p>
                    </div>

                    <!-- Active Status -->
                    <div class="flex items-center">
                        <input type="checkbox" name="active" id="active" checked
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded">
                        <label for="active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                            Active
                        </label>
                        <p class="ml-4 text-sm text-gray-500 dark:text-gray-400">
                            Uncheck to temporarily disable this reminder.
                        </p>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <a href="/momentum/medical/medication/view/<?= $medication['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-3">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Save Reminder
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
