<?php
/**
 * Pets Dashboard View
 *
 * Displays a list of pets and options to manage them
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-paw text-primary-600 dark:text-primary-400 mr-2"></i> Pet Management
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/medical/pets/new" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1"></i> Add New Pet
                </a>
                <a href="/momentum/medical" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to Medical
                </a>
            </div>
        </div>

        <!-- Pet Management Overview -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Pet Care Management
                </h2>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    Track and manage your pets' health, medications, treatments, and training progress. Keep all your pet care information organized in one place.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 dark:text-white mb-2">
                            <i class="fas fa-pills text-green-600 dark:text-green-400 mr-2"></i> Medications
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Track medications, dosages, and schedules for your pets.
                        </p>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 dark:text-white mb-2">
                            <i class="fas fa-syringe text-blue-600 dark:text-blue-400 mr-2"></i> Treatments
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Record vaccinations, checkups, and other medical treatments.
                        </p>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="font-medium text-gray-900 dark:text-white mb-2">
                            <i class="fas fa-graduation-cap text-purple-600 dark:text-purple-400 mr-2"></i> Training
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Log training sessions and track skill development progress.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pets List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-list text-primary-600 dark:text-primary-400 mr-2"></i> Your Pets
                </h2>
                <a href="/momentum/medical/pets/new" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                    Add New <i class="fas fa-plus ml-1"></i>
                </a>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <?php if (empty($pets)): ?>
                    <div class="px-4 py-5 sm:p-6 text-center">
                        <p class="text-gray-500 dark:text-gray-400">No pets found</p>
                        <a href="/momentum/medical/pets/new" class="mt-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Add Your First Pet
                        </a>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                        <?php foreach ($pets as $pet): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($pet['name']) ?>
                                        </h3>
                                        <?php
                                        $speciesIcon = 'paw';
                                        $iconColor = 'text-primary-600 dark:text-primary-400';
                                        
                                        if (strtolower($pet['species']) === 'dog') {
                                            $speciesIcon = 'dog';
                                            $iconColor = 'text-yellow-600 dark:text-yellow-400';
                                        } elseif (strtolower($pet['species']) === 'cat') {
                                            $speciesIcon = 'cat';
                                            $iconColor = 'text-gray-600 dark:text-gray-400';
                                        } elseif (strtolower($pet['species']) === 'bird') {
                                            $speciesIcon = 'dove';
                                            $iconColor = 'text-blue-600 dark:text-blue-400';
                                        } elseif (strtolower($pet['species']) === 'fish') {
                                            $speciesIcon = 'fish';
                                            $iconColor = 'text-blue-600 dark:text-blue-400';
                                        } elseif (strtolower($pet['species']) === 'rabbit') {
                                            $speciesIcon = 'rabbit';
                                            $iconColor = 'text-gray-600 dark:text-gray-400';
                                        }
                                        ?>
                                        <i class="fas fa-<?= $speciesIcon ?> <?= $iconColor ?> text-xl"></i>
                                    </div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                                        <span class="font-medium">Species:</span> <?= htmlspecialchars($pet['species']) ?>
                                    </div>
                                    <?php if (!empty($pet['breed'])): ?>
                                        <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                                            <span class="font-medium">Breed:</span> <?= htmlspecialchars($pet['breed']) ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (!empty($pet['birth_date'])): ?>
                                        <div class="text-sm text-gray-600 dark:text-gray-400 mb-1">
                                            <span class="font-medium">Age:</span> 
                                            <?php
                                            $birthDate = new DateTime($pet['birth_date']);
                                            $today = new DateTime();
                                            $age = $birthDate->diff($today);
                                            if ($age->y > 0) {
                                                echo $age->y . ' year' . ($age->y > 1 ? 's' : '');
                                                if ($age->m > 0) {
                                                    echo ', ' . $age->m . ' month' . ($age->m > 1 ? 's' : '');
                                                }
                                            } else {
                                                echo $age->m . ' month' . ($age->m > 1 ? 's' : '');
                                                if ($age->d > 0) {
                                                    echo ', ' . $age->d . ' day' . ($age->d > 1 ? 's' : '');
                                                }
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="mt-4 flex justify-end">
                                        <a href="/momentum/medical/pets/view/<?= $pet['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                            <i class="fas fa-eye mr-1"></i> View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
