<?php
/**
 * Pet Vet Visit View
 *
 * Displays a single vet visit record
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back navigation -->
        <div class="mb-6">
            <a href="/momentum/medical/pets/vet-visits/<?= $pet['id'] ?>" class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Vet Visits
            </a>
        </div>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0 high-contrast-text">
                <i class="fas fa-stethoscope text-primary-600 dark:text-primary-400 mr-2"></i> Vet Visit Details
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/medical/pets/vet-visit/edit/<?= $vetVisit['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow">
                    <i class="fas fa-edit mr-1"></i> Edit
                </a>
                <a href="/momentum/medical/pets/vet-visit/delete/<?= $vetVisit['id'] ?>" 
                   onclick="return confirm('Are you sure you want to delete this vet visit record?')" 
                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200 adhd-focus-glow">
                    <i class="fas fa-trash-alt mr-1"></i> Delete
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <?= date('F j, Y', strtotime($vetVisit['visit_date'])) ?>
                        <?= !empty($vetVisit['visit_time']) ? ' at ' . date('g:i A', strtotime($vetVisit['visit_time'])) : '' ?>
                    </h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        <?= !empty($vetVisit['clinic_name']) ? htmlspecialchars($vetVisit['clinic_name']) : 'Unknown Clinic' ?>
                        <?= !empty($vetVisit['vet_name']) ? ' • Dr. ' . htmlspecialchars($vetVisit['vet_name']) : '' ?>
                    </p>
                </div>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    <?= htmlspecialchars($vetVisit['visit_reason']) ?>
                </span>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php if (!empty($vetVisit['diagnosis'])): ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Diagnosis</h3>
                            <p class="text-sm text-gray-900 dark:text-white">
                                <?= nl2br(htmlspecialchars($vetVisit['diagnosis'])) ?>
                            </p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($vetVisit['treatment'])): ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Treatment</h3>
                            <p class="text-sm text-gray-900 dark:text-white">
                                <?= nl2br(htmlspecialchars($vetVisit['treatment'])) ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    <?php if (!empty($vetVisit['follow_up_date'])): ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Follow-up Date</h3>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                <?= date('M j, Y', strtotime($vetVisit['follow_up_date'])) ?>
                                <?php
                                $daysUntil = (strtotime($vetVisit['follow_up_date']) - time()) / (60 * 60 * 24);
                                $statusClass = '';
                                $statusText = '';
                                
                                if ($daysUntil < 0) {
                                    $statusClass = 'text-red-600 dark:text-red-400';
                                    $statusText = 'Overdue by ' . abs(ceil($daysUntil)) . ' day' . (abs(ceil($daysUntil)) !== 1 ? 's' : '');
                                } elseif ($daysUntil === 0) {
                                    $statusClass = 'text-yellow-600 dark:text-yellow-400';
                                    $statusText = 'Due today';
                                } elseif ($daysUntil <= 3) {
                                    $statusClass = 'text-orange-600 dark:text-orange-400';
                                    $statusText = 'Due in ' . ceil($daysUntil) . ' day' . (ceil($daysUntil) !== 1 ? 's' : '');
                                } else {
                                    $statusClass = 'text-green-600 dark:text-green-400';
                                    $statusText = 'Due in ' . ceil($daysUntil) . ' days';
                                }
                                ?>
                                <span class="block mt-1 text-xs <?= $statusClass ?>">
                                    <?= $statusText ?>
                                </span>
                            </p>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($vetVisit['cost'])): ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Cost</h3>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                Rs <?= number_format($vetVisit['cost'], 2) ?>
                            </p>
                        </div>
                    <?php endif; ?>

                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Record Information</h3>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            Created: <?= date('M j, Y', strtotime($vetVisit['created_at'])) ?><br>
                            Last Updated: <?= date('M j, Y', strtotime($vetVisit['updated_at'])) ?>
                        </p>
                    </div>
                </div>

                <?php if (!empty($vetVisit['notes'])): ?>
                    <div class="mt-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Notes</h3>
                        <p class="text-sm text-gray-900 dark:text-white">
                            <?= nl2br(htmlspecialchars($vetVisit['notes'])) ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
