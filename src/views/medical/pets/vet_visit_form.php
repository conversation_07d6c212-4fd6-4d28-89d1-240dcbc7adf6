<?php
/**
 * Pet Vet Visit Form View
 *
 * Form for recording pet vet visits
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-stethoscope text-primary-600 dark:text-primary-400 mr-2"></i> Record Vet Visit for <?= htmlspecialchars($pet['name']) ?>
            </h1>
            <a href="/momentum/medical/pets/view/<?= $pet['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Pet
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/medical/pets/vet-visit/save" method="post" class="p-6">
                <input type="hidden" name="pet_id" value="<?= $pet['id'] ?>">

                <!-- Visit Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Visit Information
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="visit_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Visit Date <span class="text-red-600">*</span>
                            </label>
                            <input type="date" name="visit_date" id="visit_date" required value="<?= date('Y-m-d') ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>

                        <div>
                            <label for="visit_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Visit Time
                            </label>
                            <input type="time" name="visit_time" id="visit_time"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>

                        <div>
                            <label for="vet_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Veterinarian Name
                            </label>
                            <input type="text" name="vet_name" id="vet_name"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Dr. Smith">
                        </div>

                        <div>
                            <label for="clinic_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Clinic Name
                            </label>
                            <input type="text" name="clinic_name" id="clinic_name"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Animal Care Clinic">
                        </div>
                    </div>
                </div>

                <!-- Visit Details -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-clipboard-list text-primary-600 dark:text-primary-400 mr-2"></i> Visit Details
                    </h2>

                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="visit_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Visit Type <span class="text-red-600">*</span>
                            </label>
                            <select name="visit_type" id="visit_type" required
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select visit type</option>
                                <option value="routine_checkup">Routine Checkup</option>
                                <option value="vaccination">Vaccination</option>
                                <option value="illness">Illness</option>
                                <option value="injury">Injury</option>
                                <option value="surgery">Surgery</option>
                                <option value="dental">Dental</option>
                                <option value="emergency">Emergency</option>
                                <option value="follow_up">Follow-up</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label for="reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Reason for Visit <span class="text-red-600">*</span>
                            </label>
                            <textarea name="reason" id="reason" rows="2" required
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Describe the reason for the visit"></textarea>
                        </div>

                        <div>
                            <label for="diagnosis" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Diagnosis
                            </label>
                            <textarea name="diagnosis" id="diagnosis" rows="2"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Veterinarian's diagnosis"></textarea>
                        </div>

                        <div>
                            <label for="treatment" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Treatment
                            </label>
                            <textarea name="treatment" id="treatment" rows="2"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Treatment provided"></textarea>
                        </div>


                    </div>
                </div>

                <!-- Follow-up Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-calendar-check text-primary-600 dark:text-primary-400 mr-2"></i> Follow-up Information
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="follow_up_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Follow-up Date
                            </label>
                            <input type="date" name="follow_up_date" id="follow_up_date"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>


                    </div>
                </div>

                <!-- Cost Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-money-bill-wave text-primary-600 dark:text-primary-400 mr-2"></i> Cost Information
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="cost" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Cost
                            </label>
                            <div class="flex">
                                <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 sm:text-sm">
                                    Rs
                                </span>
                                <input type="number" name="cost" id="cost" step="0.01" min="0"
                                    class="block w-full rounded-r-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            </div>
                        </div>


                    </div>
                </div>

                <!-- Additional Notes -->
                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Additional Notes
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                        class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        placeholder="Any additional notes about the vet visit"></textarea>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Save Vet Visit
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
