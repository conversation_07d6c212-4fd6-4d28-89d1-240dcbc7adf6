<?php
/**
 * Pet View
 *
 * Displays details of a specific pet
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back navigation -->
        <div class="mb-6">
            <a href="/momentum/medical/pets" class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Pets
            </a>
        </div>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0 high-contrast-text">
                <?php
                $speciesIcon = 'paw';
                $iconColor = 'text-primary-600 dark:text-primary-400';

                if (strtolower($pet['species']) === 'dog') {
                    $speciesIcon = 'dog';
                    $iconColor = 'text-yellow-600 dark:text-yellow-400';
                } elseif (strtolower($pet['species']) === 'cat') {
                    $speciesIcon = 'cat';
                    $iconColor = 'text-gray-600 dark:text-gray-400';
                } elseif (strtolower($pet['species']) === 'bird') {
                    $speciesIcon = 'dove';
                    $iconColor = 'text-blue-600 dark:text-blue-400';
                } elseif (strtolower($pet['species']) === 'fish') {
                    $speciesIcon = 'fish';
                    $iconColor = 'text-blue-600 dark:text-blue-400';
                } elseif (strtolower($pet['species']) === 'rabbit') {
                    $speciesIcon = 'rabbit';
                    $iconColor = 'text-gray-600 dark:text-gray-400';
                }
                ?>
                <i class="fas fa-<?= $speciesIcon ?> <?= $iconColor ?> mr-2"></i> <?= htmlspecialchars($pet['name']) ?>
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/medical/pets/health-dashboard/<?= $pet['id'] ?>"
                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow pulse-animation"
                   data-important="true"
                   data-keyboard-shortcut="H">
                    <i class="fas fa-heartbeat mr-1"></i> Health Dashboard
                </a>
                <a href="/momentum/medical/pets/edit/<?= $pet['id'] ?>"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow"
                   data-keyboard-shortcut="E">
                    <i class="fas fa-edit mr-1"></i> Edit
                </a>
                <a href="/momentum/medical/pets/delete/<?= $pet['id'] ?>"
                   onclick="return confirm('Are you sure you want to delete this pet?')"
                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200 adhd-focus-glow"
                   data-keyboard-shortcut="D">
                    <i class="fas fa-trash-alt mr-1"></i> Delete
                </a>
            </div>
        </div>

        <!-- Pet Information -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Pet Information
                </h2>
                <a href="/momentum/medical/pets/edit/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                    <i class="fas fa-edit mr-1"></i> Edit
                </a>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Species</h3>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($pet['species']) ?></p>
                    </div>
                    <?php if (!empty($pet['breed'])): ?>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Breed</h3>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($pet['breed']) ?></p>
                        </div>
                    <?php endif; ?>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Gender</h3>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= ucfirst(htmlspecialchars($pet['gender'])) ?></p>
                    </div>
                    <?php if (!empty($pet['birth_date'])): ?>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Birth Date</h3>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($pet['birth_date'])) ?></p>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($pet['adoption_date'])): ?>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Adoption Date</h3>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($pet['adoption_date'])) ?></p>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($pet['color'])): ?>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Color</h3>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($pet['color']) ?></p>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($pet['weight'])): ?>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Weight</h3>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($pet['weight']) ?> <?= htmlspecialchars($pet['weight_unit']) ?></p>
                        </div>
                    <?php endif; ?>
                    <?php if (!empty($pet['microchip_id'])): ?>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Microchip ID</h3>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($pet['microchip_id']) ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if (!empty($pet['notes'])): ?>
                    <div class="mt-4">
                        <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</h3>
                        <p class="mt-1 text-sm text-gray-900 dark:text-white"><?= nl2br(htmlspecialchars($pet['notes'])) ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Feature Categories -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <!-- Health & Medical -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-heartbeat text-red-600 dark:text-red-400 mr-2"></i> Health & Medical
                    </h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="/momentum/medical/pets/medication/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-pills mr-2"></i> Add Medication
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/treatment/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-syringe mr-2"></i> Add Treatment
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/vaccination-schedule/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-calendar-check mr-2"></i> Vaccination Schedule
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/growth/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-ruler mr-2"></i> Growth Tracking
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/vitals/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-weight mr-2"></i> Record Vitals
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/health-assessment/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-clipboard-check mr-2"></i> Health Assessment
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/vet-visit/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-user-md mr-2"></i> Record Vet Visit
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Nutrition & Diet -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-utensils text-green-600 dark:text-green-400 mr-2"></i> Nutrition & Diet
                    </h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="/momentum/medical/pets/diet-plan/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-clipboard-list mr-2"></i> Create Diet Plan
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/meal/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-drumstick-bite mr-2"></i> Log Meal
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/food-allergy/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-allergies mr-2"></i> Add Food Allergy
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Activity & Behavior -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-running text-blue-600 dark:text-blue-400 mr-2"></i> Activity & Behavior
                    </h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="/momentum/medical/pets/activity/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-walking mr-2"></i> Log Activity
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/behavior/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-brain mr-2"></i> Record Behavior
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/training/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-graduation-cap mr-2"></i> Add Training Log
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Management -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-cog text-gray-600 dark:text-gray-400 mr-2"></i> Management
                    </h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="/momentum/medical/pets/expense/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-money-bill-wave mr-2"></i> Add Expense
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/document/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-file-alt mr-2"></i> Upload Document
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/contact/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-address-book mr-2"></i> Add Contact
                            </a>
                        </li>
                        <li>
                            <a href="/momentum/medical/pets/reminder/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-bell mr-2"></i> Set Reminder
                            </a>
                        </li>
                        <?php if (strtolower($pet['gender']) === 'female'): ?>
                        <li>
                            <a href="/momentum/medical/pets/breeding/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-baby mr-2"></i> Breeding Record
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Vitals Summary -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-weight text-purple-600 dark:text-purple-400 mr-2"></i> Vitals Summary
                </h2>
                <div>
                    <a href="/momentum/medical/pets/vitals/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mr-4">
                        Record New <i class="fas fa-plus ml-1"></i>
                    </a>
                    <a href="/momentum/medical/pets/vitals/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View History <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (empty($recentVitals)): ?>
                    <div class="text-center">
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No vitals records found</p>
                        <a href="/momentum/medical/pets/vitals/new/<?= $pet['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Record First Vitals
                        </a>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Weight</h3>
                            <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                <?= !empty($pet['weight']) ? htmlspecialchars($pet['weight']) . ' ' . htmlspecialchars($pet['weight_unit']) : 'Not recorded' ?>
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <?= !empty($recentVitals[0]['record_date']) ? 'Last updated: ' . date('M j, Y', strtotime($recentVitals[0]['record_date'])) : '' ?>
                            </p>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Temperature</h3>
                            <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                <?= !empty($recentVitals[0]['temperature']) ? htmlspecialchars($recentVitals[0]['temperature']) . ' °C' : 'Not recorded' ?>
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <?= !empty($recentVitals[0]['record_date']) ? 'Recorded: ' . date('M j, Y', strtotime($recentVitals[0]['record_date'])) : '' ?>
                            </p>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Heart Rate</h3>
                            <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                <?= !empty($recentVitals[0]['heart_rate']) ? htmlspecialchars($recentVitals[0]['heart_rate']) . ' BPM' : 'Not recorded' ?>
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <?= !empty($recentVitals[0]['record_date']) ? 'Recorded: ' . date('M j, Y', strtotime($recentVitals[0]['record_date'])) : '' ?>
                            </p>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Respiratory Rate</h3>
                            <p class="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                                <?= !empty($recentVitals[0]['respiratory_rate']) ? htmlspecialchars($recentVitals[0]['respiratory_rate']) . ' breaths/min' : 'Not recorded' ?>
                            </p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                <?= !empty($recentVitals[0]['record_date']) ? 'Recorded: ' . date('M j, Y', strtotime($recentVitals[0]['record_date'])) : '' ?>
                            </p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Health Assessment Summary -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-clipboard-check text-green-600 dark:text-green-400 mr-2"></i> Health Assessment
                </h2>
                <div>
                    <a href="/momentum/medical/pets/health-assessment/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mr-4">
                        Add New <i class="fas fa-plus ml-1"></i>
                    </a>
                    <a href="/momentum/medical/pets/health-assessments/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View History <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (empty($recentHealthAssessment)): ?>
                    <div class="text-center">
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No health assessments found</p>
                        <a href="/momentum/medical/pets/health-assessment/new/<?= $pet['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Add First Assessment
                        </a>
                    </div>
                <?php else: ?>
                    <?php
                    $assessment = $recentHealthAssessment[0];
                    $healthClass = '';
                    $healthIcon = '';

                    switch ($assessment['overall_health']) {
                        case 'excellent':
                            $healthClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                            $healthIcon = 'fa-check-circle';
                            break;
                        case 'good':
                            $healthClass = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
                            $healthIcon = 'fa-thumbs-up';
                            break;
                        case 'fair':
                            $healthClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                            $healthIcon = 'fa-exclamation-circle';
                            break;
                        case 'poor':
                            $healthClass = 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
                            $healthIcon = 'fa-exclamation-triangle';
                            break;
                        case 'critical':
                            $healthClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                            $healthIcon = 'fa-exclamation-triangle';
                            break;
                        default:
                            $healthClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
                            $healthIcon = 'fa-question-circle';
                    }
                    ?>

                    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                        <div class="flex items-center mb-2 md:mb-0">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?= $healthClass ?> mr-3">
                                <i class="fas <?= $healthIcon ?> mr-1"></i> <?= ucfirst(htmlspecialchars($assessment['overall_health'])) ?>
                            </span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                Assessed on <?= date('M j, Y', strtotime($assessment['assessment_date'])) ?>
                            </span>
                        </div>
                        <a href="/momentum/medical/pets/health-assessments/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            View Details <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                        <?php if (!empty($assessment['coat_condition'])): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400">Coat</h3>
                                <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                                    <?= ucfirst(htmlspecialchars($assessment['coat_condition'])) ?>
                                </p>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($assessment['dental_health'])): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400">Dental</h3>
                                <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                                    <?= ucfirst(htmlspecialchars($assessment['dental_health'])) ?>
                                </p>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($assessment['energy_level'])): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400">Energy</h3>
                                <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                                    <?= ucfirst(str_replace('_', ' ', htmlspecialchars($assessment['energy_level']))) ?>
                                </p>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($assessment['appetite'])): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400">Appetite</h3>
                                <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">
                                    <?= ucfirst(htmlspecialchars($assessment['appetite'])) ?>
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Vet Visit Summary -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-stethoscope text-blue-600 dark:text-blue-400 mr-2"></i> Veterinary Care
                </h2>
                <div>
                    <a href="/momentum/medical/pets/vet-visit/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mr-4">
                        Record Visit <i class="fas fa-plus ml-1"></i>
                    </a>
                    <a href="/momentum/medical/pets/vet-visits/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View History <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (!empty($upcomingVetVisits)): ?>
                    <div class="mb-4">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Upcoming Follow-ups</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <?php foreach (array_slice($upcomingVetVisits, 0, 2) as $visit): ?>
                                <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                                    <div class="flex justify-between items-start">
                                        <div class="font-medium text-blue-800 dark:text-blue-200">
                                            <?= date('M j, Y', strtotime($visit['follow_up_date'])) ?>
                                        </div>
                                        <?php
                                        $daysUntil = (strtotime($visit['follow_up_date']) - time()) / (60 * 60 * 24);
                                        $badgeClass = $daysUntil <= 3 ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200';
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $badgeClass ?>">
                                            <?= ceil($daysUntil) ?> days
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                        <?= !empty($visit['visit_reason']) ? htmlspecialchars($visit['visit_reason']) : 'Visit' ?> follow-up
                                    </p>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php if (count($upcomingVetVisits) > 2): ?>
                            <div class="mt-2 text-right">
                                <a href="/momentum/medical/pets/vet-visits/<?= $pet['id'] ?>" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    View all <?= count($upcomingVetVisits) ?> upcoming follow-ups <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($recentVetVisit) && empty($upcomingVetVisits)): ?>
                    <div class="text-center">
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No vet visits recorded</p>
                        <a href="/momentum/medical/pets/vet-visit/new/<?= $pet['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Record First Visit
                        </a>
                    </div>
                <?php elseif (!empty($recentVetVisit)): ?>
                    <div class="mt-3">
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Most Recent Visit</h3>
                        <?php $visit = $recentVetVisit[0]; ?>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                                <div>
                                    <span class="font-medium text-gray-900 dark:text-white">
                                        <?= date('F j, Y', strtotime($visit['visit_date'])) ?>
                                    </span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">
                                        <?= !empty($visit['clinic_name']) ? htmlspecialchars($visit['clinic_name']) : '' ?>
                                    </span>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mt-1 md:mt-0">
                                    <?= !empty($visit['visit_reason']) ? htmlspecialchars($visit['visit_reason']) : 'Visit' ?>
                                </span>
                            </div>

                            <?php if (!empty($visit['diagnosis']) || !empty($visit['treatment'])): ?>
                                <div class="mt-2 text-sm">
                                    <?php if (!empty($visit['diagnosis'])): ?>
                                        <p class="text-gray-900 dark:text-white">
                                            <span class="font-medium">Diagnosis:</span> <?= htmlspecialchars($visit['diagnosis']) ?>
                                        </p>
                                    <?php endif; ?>

                                    <?php if (!empty($visit['treatment'])): ?>
                                        <p class="text-gray-900 dark:text-white mt-1">
                                            <span class="font-medium">Treatment:</span> <?= htmlspecialchars($visit['treatment']) ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <div class="mt-3 text-right">
                                <a href="/momentum/medical/pets/vet-visits/<?= $pet['id'] ?>" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    View full details <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Reminders -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-bell text-yellow-600 dark:text-yellow-400 mr-2"></i> Reminders
                </h2>
                <div>
                    <a href="/momentum/medical/pets/reminder/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mr-4">
                        Add New <i class="fas fa-plus ml-1"></i>
                    </a>
                    <a href="/momentum/medical/pets/reminders/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        View All <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (empty($upcomingReminders)): ?>
                    <div class="text-center">
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No upcoming reminders</p>
                        <a href="/momentum/medical/pets/reminder/new/<?= $pet['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-1"></i> Create First Reminder
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($upcomingReminders as $reminder): ?>
                            <?php
                            // Calculate days until due
                            $dueDate = new DateTime($reminder['due_date']);
                            $today = new DateTime();
                            $interval = $today->diff($dueDate);
                            $daysUntil = $interval->days;
                            if ($today > $dueDate) {
                                $daysUntil = -$daysUntil; // Overdue
                            }

                            // Set status and classes based on days until due
                            $statusClass = '';
                            $statusText = '';

                            if ($daysUntil < 0) {
                                $statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                                $statusText = 'Overdue by ' . abs($daysUntil) . ' day' . (abs($daysUntil) !== 1 ? 's' : '');
                            } elseif ($daysUntil === 0) {
                                $statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                                $statusText = 'Due today';
                            } elseif ($daysUntil <= 3) {
                                $statusClass = 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
                                $statusText = 'Due in ' . $daysUntil . ' day' . ($daysUntil !== 1 ? 's' : '');
                            } else {
                                $statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                                $statusText = 'Due in ' . $daysUntil . ' days';
                            }

                            // Set icon based on reminder type
                            $typeIcon = '';
                            switch ($reminder['reminder_type']) {
                                case 'medication':
                                    $typeIcon = 'fa-pills';
                                    break;
                                case 'vaccination':
                                    $typeIcon = 'fa-syringe';
                                    break;
                                case 'vet_visit':
                                    $typeIcon = 'fa-stethoscope';
                                    break;
                                case 'grooming':
                                    $typeIcon = 'fa-cut';
                                    break;
                                case 'training':
                                    $typeIcon = 'fa-graduation-cap';
                                    break;
                                case 'feeding':
                                    $typeIcon = 'fa-utensils';
                                    break;
                                case 'exercise':
                                    $typeIcon = 'fa-running';
                                    break;
                                default:
                                    $typeIcon = 'fa-bell';
                            }

                            // Set priority class
                            $priorityClass = '';
                            switch ($reminder['priority']) {
                                case 'high':
                                    $priorityClass = 'border-l-4 border-red-500';
                                    break;
                                case 'normal':
                                    $priorityClass = 'border-l-4 border-blue-500';
                                    break;
                                case 'low':
                                    $priorityClass = 'border-l-4 border-gray-500';
                                    break;
                            }
                            ?>
                            <div id="reminder-<?= $reminder['id'] ?>"
                                 class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 adhd-card adhd-focus-border <?= $priorityClass ?>"
                                 data-type="<?= $reminder['reminder_type'] ?>"
                                 data-priority="<?= $reminder['priority'] ?>"
                                 data-category="<?= $reminder['reminder_type'] ?>"
                                 data-status="<?= $daysUntil < 0 ? 'overdue' : ($daysUntil === 0 ? 'due-today' : 'upcoming') ?>"
                                 <?= $daysUntil <= 1 ? 'data-important="true"' : '' ?>>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                            <i class="fas <?= $typeIcon ?> icon-<?= $reminder['reminder_type'] ?>" data-icon-category="<?= $reminder['reminder_type'] ?>"></i>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-gray-900 dark:text-white high-contrast-text">
                                                <?= htmlspecialchars($reminder['title']) ?>
                                            </h3>
                                            <div class="flex items-center mt-1">
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= date('M j, Y', strtotime($reminder['due_date'])) ?>
                                                    <?= !empty($reminder['due_time']) ? ' at ' . date('g:i A', strtotime($reminder['due_time'])) : '' ?>
                                                </span>
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium status-<?= $daysUntil < 0 ? 'overdue' : ($daysUntil === 0 ? 'due-today' : 'upcoming') ?>">
                                                    <?= $statusText ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <a href="/momentum/medical/pets/reminder/complete/<?= $reminder['id'] ?>"
                                           class="inline-flex items-center p-1.5 border border-transparent rounded-full shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 adhd-focus-glow"
                                           aria-label="Complete reminder">
                                            <i class="fas fa-check text-xs"></i>
                                        </a>
                                    </div>
                                </div>

                                <?php if ($daysUntil >= 0 && $daysUntil <= 7): ?>
                                    <div class="mt-2 ml-10">
                                        <div class="progress-container" id="progress-<?= $reminder['id'] ?>" aria-label="Time remaining until due">
                                            <div class="progress-bar <?= $daysUntil <= 1 ? 'progress-bar-danger' : ($daysUntil <= 3 ? 'progress-bar-warning' : 'progress-bar-info') ?>"
                                                 style="width: <?= max(0, min(100, (7 - $daysUntil) / 7 * 100)) ?>%"
                                                 data-percentage="<?= max(0, min(100, (7 - $daysUntil) / 7 * 100)) ?>"
                                                 data-type="<?= $daysUntil <= 1 ? 'danger' : ($daysUntil <= 3 ? 'warning' : 'info') ?>">
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>

                        <?php if (count($upcomingReminders) > 3): ?>
                            <div class="text-center mt-3">
                                <a href="/momentum/medical/pets/reminders/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    View all reminders <i class="fas fa-arrow-right ml-1"></i>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Medications -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-pills text-green-600 dark:text-green-400 mr-2"></i> Medications
                    </h2>
                    <a href="/momentum/medical/pets/medication/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        Add New <i class="fas fa-plus ml-1"></i>
                    </a>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <?php if (empty($medications)): ?>
                        <div class="px-4 py-5 sm:p-6 text-center">
                            <p class="text-gray-500 dark:text-gray-400">No medications found</p>
                            <a href="/momentum/medical/pets/medication/new/<?= $pet['id'] ?>" class="mt-3 inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-plus mr-1"></i> Add Medication
                            </a>
                        </div>
                    <?php else: ?>
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($medications as $medication): ?>
                                <li class="px-4 py-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <div class="flex justify-between">
                                        <div>
                                            <h3 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($medication['name']) ?></h3>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= htmlspecialchars($medication['dosage']) ?> <?= htmlspecialchars($medication['dosage_unit']) ?>,
                                                <?= str_replace('_', ' ', ucfirst($medication['frequency'])) ?>
                                            </p>
                                        </div>
                                        <div>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                Active
                                            </span>
                                        </div>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Treatments -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                        <i class="fas fa-syringe text-blue-600 dark:text-blue-400 mr-2"></i> Treatments
                    </h2>
                    <a href="/momentum/medical/pets/treatment/new/<?= $pet['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                        Add New <i class="fas fa-plus ml-1"></i>
                    </a>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <?php if (empty($treatments)): ?>
                        <div class="px-4 py-5 sm:p-6 text-center">
                            <p class="text-gray-500 dark:text-gray-400">No treatments found</p>
                            <a href="/momentum/medical/pets/treatment/new/<?= $pet['id'] ?>" class="mt-3 inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-plus mr-1"></i> Add Treatment
                            </a>
                        </div>
                    <?php else: ?>
                        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($treatments as $treatment): ?>
                                <li class="px-4 py-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <div class="flex justify-between">
                                        <div>
                                            <h3 class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($treatment['treatment_name']) ?></h3>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                <?= ucfirst(str_replace('_', ' ', $treatment['treatment_type'])) ?>,
                                                <?= date('M j, Y', strtotime($treatment['treatment_date'])) ?>
                                            </p>
                                        </div>
                                        <?php if (!empty($treatment['next_due_date'])): ?>
                                            <div>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                    Next: <?= date('M j, Y', strtotime($treatment['next_due_date'])) ?>
                                                </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
