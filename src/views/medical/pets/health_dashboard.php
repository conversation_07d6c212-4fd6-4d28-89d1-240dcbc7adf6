<?php
/**
 * Pet Health Dashboard View
 *
 * ADHD-friendly comprehensive dashboard for pet health overview
 */
?>

<style>
/* Health Score Component Styles */
:root {
    --text-green-600: #059669;
    --text-green-400: #34D399;
    --text-blue-600: #2563EB;
    --text-blue-400: #60A5FA;
    --text-yellow-600: #D97706;
    --text-yellow-400: #FBBF24;
    --text-orange-600: #EA580C;
    --text-orange-400: #FB923C;
    --text-red-600: #DC2626;
    --text-red-400: #F87171;
}

.progress-container {
    width: 100%;
    height: 8px;
    background-color: rgba(209, 213, 219, 0.5);
    border-radius: 9999px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 9999px;
    transition: width 0.5s ease-in-out;
}

.progress-bar-success {
    background-color: var(--text-green-600);
}

.progress-bar-info {
    background-color: var(--text-blue-600);
}

.progress-bar-warning {
    background-color: var(--text-yellow-600);
}

.progress-bar-danger {
    background-color: var(--text-red-600);
}

.dark .progress-bar-success {
    background-color: var(--text-green-400);
}

.dark .progress-bar-info {
    background-color: var(--text-blue-400);
}

.dark .progress-bar-warning {
    background-color: var(--text-yellow-400);
}

.dark .progress-bar-danger {
    background-color: var(--text-red-400);
}

/* Status Classes */
.status-overdue {
    background-color: rgba(239, 68, 68, 0.1);
    color: #EF4444;
}

.status-due-today {
    background-color: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
}

.status-upcoming {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3B82F6;
}

.dark .status-overdue {
    background-color: rgba(248, 113, 113, 0.2);
    color: #F87171;
}

.dark .status-due-today {
    background-color: rgba(251, 191, 36, 0.2);
    color: #FBBF24;
}

.dark .status-upcoming {
    background-color: rgba(96, 165, 250, 0.2);
    color: #60A5FA;
}
</style>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back navigation -->
        <div class="mb-6">
            <a href="/momentum/medical/pets/view/<?= $pet['id'] ?>"
               class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 adhd-focus-glow"
               data-keyboard-shortcut="B">
                <i class="fas fa-arrow-left mr-1"></i> Back to <?= htmlspecialchars($pet['name']) ?>
            </a>
        </div>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0 high-contrast-text">
                <i class="fas fa-heartbeat text-red-500 dark:text-red-400 mr-2"></i> Health Dashboard for <?= htmlspecialchars($pet['name']) ?>
            </h1>
            <div class="flex flex-wrap gap-2">
                <button id="focus-mode-toggle-dashboard"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200 adhd-focus-glow">
                    <i class="fas fa-eye mr-1"></i> Enter Focus Mode
                </button>
                <a href="/momentum/medical/pets/health-assessment/new/<?= $pet['id'] ?>"
                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow">
                    <i class="fas fa-plus mr-1"></i> New Health Assessment
                </a>
            </div>
        </div>

        <!-- Health Status Overview -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6 adhd-card">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white high-contrast-text">
                    <i class="fas fa-chart-line text-blue-500 dark:text-blue-400 mr-2"></i> Health Status Overview
                </h2>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?= $healthStatus['class'] ?>">
                    <?= $healthStatus['label'] ?>
                </span>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Overall Health Score -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg adhd-card focus-mode-show" id="health-score-card">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2 high-contrast-text">
                            Overall Health Score
                        </h3>
                        <div class="flex items-center justify-between">
                            <div class="text-3xl font-bold <?= $healthScore['class'] ?>">
                                <?= $healthScore['value'] ?>%
                            </div>
                            <div class="w-16 h-16 relative">
                                <svg class="w-full h-full" viewBox="0 0 36 36">
                                    <path class="stroke-current text-gray-200 dark:text-gray-600"
                                          stroke-width="3"
                                          fill="none"
                                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <path class="stroke-current <?= $healthScore['class'] ?>"
                                          stroke-width="3"
                                          stroke-linecap="round"
                                          fill="none"
                                          stroke-dasharray="<?= $healthScore['value'] ?>, 100"
                                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    <text x="18" y="20.5" class="text-xs font-medium" text-anchor="middle" fill="currentColor">
                                        <?= $healthScore['value'] ?>%
                                    </text>
                                </svg>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button id="show-health-details" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                                <i class="fas fa-chart-pie mr-1"></i> View score breakdown
                            </button>
                        </div>

                        <!-- Health Score Components Modal -->
                        <div id="health-score-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
                                <div class="px-4 py-5 sm:px-6 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                                        Health Score Breakdown
                                    </h3>
                                    <button id="close-health-modal" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="px-4 py-5 sm:p-6">
                                    <div class="mb-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                Overall Score: <span class="<?= $healthScore['class'] ?>"><?= $healthScore['value'] ?>%</span>
                                            </h4>
                                        </div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                            This score is calculated based on the following health components, weighted by importance.
                                        </p>
                                    </div>

                                    <div class="space-y-4">
                                        <?php foreach ($healthScore['components'] as $key => $component): ?>
                                            <?php
                                                // Determine component color class
                                                $componentClass = '';
                                                if ($component['score'] >= 90) {
                                                    $componentClass = 'text-green-600 dark:text-green-400';
                                                } else if ($component['score'] >= 75) {
                                                    $componentClass = 'text-blue-600 dark:text-blue-400';
                                                } else if ($component['score'] >= 60) {
                                                    $componentClass = 'text-yellow-600 dark:text-yellow-400';
                                                } else if ($component['score'] >= 40) {
                                                    $componentClass = 'text-orange-600 dark:text-orange-400';
                                                } else {
                                                    $componentClass = 'text-red-600 dark:text-red-400';
                                                }

                                                // Format component name
                                                $componentName = ucwords(str_replace('_', ' ', $key));

                                                // Calculate weighted score
                                                $weightedScore = round($component['score'] * $component['weight']);
                                            ?>
                                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                                <div class="flex justify-between items-center mb-1">
                                                    <h5 class="text-sm font-medium text-gray-900 dark:text-white">
                                                        <?= $componentName ?>
                                                    </h5>
                                                    <div class="flex items-center">
                                                        <span class="text-xs font-medium <?= $componentClass ?> mr-2">
                                                            <?= $component['score'] ?>%
                                                        </span>
                                                        <span class="text-xs text-gray-500 dark:text-gray-400">
                                                            (<?= $component['weight'] * 100 ?>% weight)
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="progress-container" aria-label="<?= $componentName ?> score">
                                                    <div class="progress-bar"
                                                         style="width: <?= $component['score'] ?>%; background-color: var(--<?= $componentClass ?>);"
                                                         data-percentage="<?= $component['score'] ?>">
                                                    </div>
                                                </div>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    <?= $component['description'] ?>
                                                </p>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Last Assessment -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg adhd-card focus-mode-hide">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2 high-contrast-text">
                            Last Health Assessment
                        </h3>
                        <?php if ($recentHealthAssessment): ?>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        <?= date('M j, Y', strtotime($recentHealthAssessment['assessment_date'])) ?>
                                    </p>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white mt-1">
                                        <?= htmlspecialchars($recentHealthAssessment['overall_condition']) ?>
                                    </p>
                                </div>
                                <a href="/momentum/medical/pets/health-assessment/view/<?= $recentHealthAssessment['id'] ?>"
                                   class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 adhd-focus-glow">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        <?php else: ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                No health assessments recorded yet.
                            </p>
                            <a href="/momentum/medical/pets/health-assessment/new/<?= $pet['id'] ?>"
                               class="inline-flex items-center mt-2 text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 adhd-focus-glow">
                                <i class="fas fa-plus mr-1"></i> Add first assessment
                            </a>
                        <?php endif; ?>
                    </div>

                    <!-- Next Vet Visit -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg adhd-card focus-mode-hide">
                        <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2 high-contrast-text">
                            Next Vet Visit
                        </h3>
                        <?php if (!empty($upcomingVetVisits)): ?>
                            <?php
                                $nextVisit = $upcomingVetVisits[0];
                                $daysUntil = (new DateTime($nextVisit['follow_up_date']))->diff(new DateTime())->days;
                                $statusClass = $daysUntil <= 3 ? 'status-due-today' : 'status-upcoming';
                            ?>
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    <?= date('M j, Y', strtotime($nextVisit['follow_up_date'])) ?>
                                </p>
                                <div class="flex items-center mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                        <?= $daysUntil > 0 ? "In {$daysUntil} days" : "Today" ?>
                                    </span>
                                </div>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                    <?= htmlspecialchars($nextVisit['follow_up_reason']) ?>
                                </p>
                            </div>
                        <?php else: ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                No upcoming vet visits scheduled.
                            </p>
                            <a href="/momentum/medical/pets/vet-visit/new/<?= $pet['id'] ?>"
                               class="inline-flex items-center mt-2 text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 adhd-focus-glow">
                                <i class="fas fa-plus mr-1"></i> Schedule vet visit
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Treatments & Vaccinations -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6 adhd-card">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white high-contrast-text">
                    <i class="fas fa-syringe text-yellow-500 dark:text-yellow-400 mr-2"></i> Treatments & Vaccinations
                </h2>
                <a href="/momentum/medical/pets/vaccination-schedule/<?= $pet['id'] ?>"
                   class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 adhd-focus-glow">
                    <i class="fas fa-calendar-check mr-1"></i> Vaccination Schedule
                </a>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (empty($treatments)): ?>
                    <div class="text-center py-6">
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No treatments or vaccinations found</p>
                        <a href="/momentum/medical/pets/treatment/new/<?= $pet['id'] ?>"
                           class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow">
                            <i class="fas fa-plus mr-1"></i> Add Treatment or Vaccination
                        </a>
                    </div>
                <?php else: ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Vaccinations -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3 high-contrast-text">
                                Vaccinations
                            </h3>
                            <?php
                            $vaccinations = array_filter($treatments, function($treatment) {
                                return $treatment['treatment_type'] === 'vaccination';
                            });

                            if (empty($vaccinations)): ?>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">No vaccinations recorded</p>
                            <?php else: ?>
                                <div class="space-y-3">
                                    <?php foreach (array_slice($vaccinations, 0, 3) as $vaccination): ?>
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';

                                        if (!empty($vaccination['next_due_date'])) {
                                            $dueDate = new DateTime($vaccination['next_due_date']);
                                            $today = new DateTime();
                                            $interval = $today->diff($dueDate);
                                            $daysUntil = $interval->days;

                                            if ($today > $dueDate) {
                                                $daysUntil = -$daysUntil; // Overdue
                                                $statusClass = 'status-overdue';
                                                $statusText = 'Overdue by ' . abs($daysUntil) . ' day' . (abs($daysUntil) !== 1 ? 's' : '');
                                            } elseif ($daysUntil === 0) {
                                                $statusClass = 'status-due-today';
                                                $statusText = 'Due today';
                                            } elseif ($daysUntil <= 30) {
                                                $statusClass = 'status-upcoming';
                                                $statusText = 'Due in ' . $daysUntil . ' day' . ($daysUntil !== 1 ? 's' : '');
                                            } else {
                                                $statusText = 'Next due: ' . date('M j, Y', strtotime($vaccination['next_due_date']));
                                            }
                                        } else {
                                            $statusText = 'Given on ' . date('M j, Y', strtotime($vaccination['treatment_date']));
                                        }
                                        ?>
                                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                    <?= htmlspecialchars($vaccination['treatment_name']) ?>
                                                </h4>
                                                <?php if (!empty($statusClass)): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                                        <?= $statusText ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if (empty($statusClass)): ?>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    <?= $statusText ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>

                                    <?php if (count($vaccinations) > 3): ?>
                                        <div class="text-right mt-2">
                                            <a href="/momentum/medical/pets/vaccination-schedule/<?= $pet['id'] ?>" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                                View all <?= count($vaccinations) ?> vaccinations <i class="fas fa-arrow-right ml-1"></i>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Other Treatments -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3 high-contrast-text">
                                Other Treatments
                            </h3>
                            <?php
                            $otherTreatments = array_filter($treatments, function($treatment) {
                                return $treatment['treatment_type'] !== 'vaccination';
                            });

                            if (empty($otherTreatments)): ?>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">No other treatments recorded</p>
                            <?php else: ?>
                                <div class="space-y-3">
                                    <?php foreach (array_slice($otherTreatments, 0, 3) as $treatment): ?>
                                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                            <div class="flex justify-between items-center">
                                                <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                    <?= htmlspecialchars($treatment['treatment_name']) ?>
                                                </h4>
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= ucfirst($treatment['treatment_type']) ?>
                                                </span>
                                            </div>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                <?= date('M j, Y', strtotime($treatment['treatment_date'])) ?>
                                            </p>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Health Metrics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Weight Tracking -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden adhd-card">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white high-contrast-text">
                        <i class="fas fa-weight text-green-500 dark:text-green-400 mr-2"></i> Weight Tracking
                    </h2>
                    <div class="flex space-x-4">
                        <a href="/momentum/medical/pets/growth/<?= $pet['id'] ?>"
                           class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 adhd-focus-glow">
                            <i class="fas fa-ruler mr-1"></i> Growth Tracking
                        </a>
                        <a href="/momentum/medical/pets/vitals/new/<?= $pet['id'] ?>"
                           class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 adhd-focus-glow">
                            Add New <i class="fas fa-plus ml-1"></i>
                        </a>
                    </div>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                    <?php if (!empty($weightHistory)): ?>
                        <div class="h-64 weight-chart" id="weight-chart"></div>
                        <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                            <div>
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Current</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white">
                                    <?= $currentWeight['value'] ?> <?= $currentWeight['unit'] ?>
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Change</p>
                                <p class="text-lg font-semibold <?= $weightChange['class'] ?>">
                                    <?= $weightChange['value'] ?> <?= $currentWeight['unit'] ?>
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Target</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white">
                                    <?= $targetWeight['value'] ?> <?= $targetWeight['unit'] ?>
                                </p>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-6">
                            <p class="text-gray-500 dark:text-gray-400 mb-4">No weight records found</p>
                            <a href="/momentum/medical/pets/vitals/new/<?= $pet['id'] ?>"
                               class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow">
                                <i class="fas fa-plus mr-1"></i> Add First Weight Record
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Medication Compliance -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden adhd-card">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white high-contrast-text">
                        <i class="fas fa-pills text-purple-500 dark:text-purple-400 mr-2"></i> Medication Compliance
                    </h2>
                    <a href="/momentum/medical/pets/medications/<?= $pet['id'] ?>"
                       class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 adhd-focus-glow">
                        View All <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                    <?php if (!empty($medications)): ?>
                        <div class="space-y-4">
                            <?php foreach ($medications as $medication): ?>
                                <?php
                                    // Calculate compliance percentage
                                    $compliance = $medication['compliance'];
                                    $complianceClass = '';
                                    if ($compliance >= 90) {
                                        $complianceClass = 'progress-bar-success';
                                    } elseif ($compliance >= 70) {
                                        $complianceClass = 'progress-bar-info';
                                    } elseif ($compliance >= 50) {
                                        $complianceClass = 'progress-bar-warning';
                                    } else {
                                        $complianceClass = 'progress-bar-danger';
                                    }
                                ?>
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <div class="flex justify-between items-center mb-2">
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white high-contrast-text">
                                            <?= htmlspecialchars($medication['name']) ?>
                                        </h3>
                                        <span class="text-xs font-medium text-gray-500 dark:text-gray-400">
                                            <?= $compliance ?>% compliance
                                        </span>
                                    </div>
                                    <div class="progress-container" aria-label="Medication compliance">
                                        <div class="progress-bar <?= $complianceClass ?>"
                                             style="width: <?= $compliance ?>%"
                                             data-percentage="<?= $compliance ?>"
                                             data-type="<?= $compliance >= 90 ? 'success' : ($compliance >= 70 ? 'info' : ($compliance >= 50 ? 'warning' : 'danger')) ?>">
                                        </div>
                                    </div>
                                    <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                        <?= $medication['dosage'] ?> - <?= $medication['frequency'] ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-6">
                            <p class="text-gray-500 dark:text-gray-400 mb-4">No medications found</p>
                            <a href="/momentum/medical/pets/medication/new/<?= $pet['id'] ?>"
                               class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow">
                                <i class="fas fa-plus mr-1"></i> Add First Medication
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Upcoming Care -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6 adhd-card">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white high-contrast-text">
                    <i class="fas fa-calendar-alt text-orange-500 dark:text-orange-400 mr-2"></i> Upcoming Care
                </h2>
                <div class="flex space-x-2">
                    <button id="filter-upcoming-care"
                            class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 adhd-focus-glow">
                        <i class="fas fa-filter mr-1"></i> Filter
                    </button>
                </div>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <?php if (empty($upcomingCare)): ?>
                    <div class="px-4 py-5 sm:p-6 text-center">
                        <p class="text-gray-500 dark:text-gray-400">No upcoming care items</p>
                    </div>
                <?php else: ?>
                    <div class="divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($upcomingCare as $careItem): ?>
                            <?php
                                // Set icon and class based on care type
                                $careIcon = '';
                                $careClass = '';
                                switch ($careItem['type']) {
                                    case 'medication':
                                        $careIcon = 'fa-pills';
                                        $careClass = 'category-medication';
                                        break;
                                    case 'vaccination':
                                        $careIcon = 'fa-syringe';
                                        $careClass = 'category-vaccination';
                                        break;
                                    case 'vet_visit':
                                        $careIcon = 'fa-stethoscope';
                                        $careClass = 'category-vet-visit';
                                        break;
                                    case 'grooming':
                                        $careIcon = 'fa-cut';
                                        $careClass = 'category-grooming';
                                        break;
                                    default:
                                        $careIcon = 'fa-calendar-check';
                                        $careClass = 'category-other';
                                }

                                // Calculate days until
                                $dueDate = new DateTime($careItem['due_date']);
                                $today = new DateTime();
                                $interval = $today->diff($dueDate);
                                $daysUntil = $interval->days;
                                if ($today > $dueDate) {
                                    $daysUntil = -$daysUntil; // Overdue
                                }

                                // Set status class
                                $statusClass = '';
                                if ($daysUntil < 0) {
                                    $statusClass = 'status-overdue';
                                } elseif ($daysUntil === 0) {
                                    $statusClass = 'status-due-today';
                                } elseif ($daysUntil <= 3) {
                                    $statusClass = 'status-upcoming';
                                } else {
                                    $statusClass = '';
                                }
                            ?>
                            <div class="px-4 py-4 sm:px-6 <?= $careClass ?>" data-care-type="<?= $careItem['type'] ?>">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                                            <i class="fas <?= $careIcon ?> icon-<?= $careItem['type'] ?>"></i>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-gray-900 dark:text-white high-contrast-text">
                                                <?= htmlspecialchars($careItem['title']) ?>
                                            </h3>
                                            <div class="flex items-center mt-1">
                                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                                    <?= date('M j, Y', strtotime($careItem['due_date'])) ?>
                                                </span>
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                                    <?php if ($daysUntil < 0): ?>
                                                        Overdue by <?= abs($daysUntil) ?> day<?= abs($daysUntil) !== 1 ? 's' : '' ?>
                                                    <?php elseif ($daysUntil === 0): ?>
                                                        Due today
                                                    <?php else: ?>
                                                        Due in <?= $daysUntil ?> day<?= $daysUntil !== 1 ? 's' : '' ?>
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <a href="<?= $careItem['action_url'] ?>"
                                           class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 adhd-focus-glow">
                                            <?= $careItem['action_text'] ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for visualizations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Health Score Modal
    const showHealthDetailsBtn = document.getElementById('show-health-details');
    const healthScoreModal = document.getElementById('health-score-modal');
    const closeHealthModalBtn = document.getElementById('close-health-modal');

    if (showHealthDetailsBtn && healthScoreModal && closeHealthModalBtn) {
        showHealthDetailsBtn.addEventListener('click', function() {
            healthScoreModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent scrolling
        });

        closeHealthModalBtn.addEventListener('click', function() {
            healthScoreModal.classList.add('hidden');
            document.body.style.overflow = ''; // Restore scrolling
        });

        // Close modal when clicking outside
        healthScoreModal.addEventListener('click', function(e) {
            if (e.target === healthScoreModal) {
                healthScoreModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !healthScoreModal.classList.contains('hidden')) {
                healthScoreModal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        });
    }

    // Initialize weight chart if data exists
    if (document.getElementById('weight-chart')) {
        const weightData = <?= json_encode($weightChartData) ?>;
        if (weightData && weightData.labels && weightData.labels.length > 0) {
            const ctx = document.getElementById('weight-chart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: weightData.labels,
                    datasets: [{
                        label: 'Weight',
                        data: weightData.values,
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                        pointRadius: 4,
                        tension: 0.2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(17, 24, 39, 0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            padding: 12,
                            displayColors: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            grid: {
                                color: 'rgba(156, 163, 175, 0.1)'
                            },
                            ticks: {
                                color: '#9CA3AF'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                color: '#9CA3AF',
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    }
                }
            });
        }
    }

    // Focus mode toggle for dashboard
    const focusModeToggle = document.getElementById('focus-mode-toggle-dashboard');
    if (focusModeToggle) {
        focusModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('focus-mode-active');

            // Update toggle button text
            const isActive = document.body.classList.contains('focus-mode-active');
            focusModeToggle.innerHTML = isActive ?
                '<i class="fas fa-eye-slash mr-1"></i> Exit Focus Mode' :
                '<i class="fas fa-eye mr-1"></i> Enter Focus Mode';
        });
    }

    // Filter for upcoming care
    const filterButton = document.getElementById('filter-upcoming-care');
    if (filterButton) {
        filterButton.addEventListener('click', function() {
            // Create filter dropdown
            const filterDropdown = document.createElement('div');
            filterDropdown.className = 'absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 z-50';
            filterDropdown.innerHTML = `
                <div class="py-1" role="menu" aria-orientation="vertical">
                    <button class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-filter="all">All</button>
                    <button class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-filter="medication">Medications</button>
                    <button class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-filter="vaccination">Vaccinations</button>
                    <button class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-filter="vet_visit">Vet Visits</button>
                    <button class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 w-full text-left" data-filter="grooming">Grooming</button>
                </div>
            `;

            // Position and show dropdown
            filterButton.parentNode.style.position = 'relative';
            filterButton.parentNode.appendChild(filterDropdown);

            // Add click handlers for filter options
            filterDropdown.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function() {
                    const filterType = this.getAttribute('data-filter');
                    const careItems = document.querySelectorAll('[data-care-type]');

                    careItems.forEach(item => {
                        if (filterType === 'all' || item.getAttribute('data-care-type') === filterType) {
                            item.style.display = '';
                        } else {
                            item.style.display = 'none';
                        }
                    });

                    // Remove dropdown after selection
                    filterDropdown.remove();
                });
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                if (!filterDropdown.contains(e.target) && e.target !== filterButton) {
                    filterDropdown.remove();
                    document.removeEventListener('click', closeDropdown);
                }
            });
        });
    }
});
</script>
