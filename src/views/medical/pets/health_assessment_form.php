<?php
/**
 * Pet Health Assessment Form View
 *
 * Form for recording pet health assessment
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-clipboard-check text-primary-600 dark:text-primary-400 mr-2"></i> Health Assessment for <?= htmlspecialchars($pet['name']) ?>
            </h1>
            <a href="/momentum/medical/pets/view/<?= $pet['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Pet
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/medical/pets/health-assessment/save" method="post" class="p-6">
                <input type="hidden" name="pet_id" value="<?= $pet['id'] ?>">

                <!-- Basic Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Basic Information
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="assessment_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Assessment Date <span class="text-red-600">*</span>
                            </label>
                            <input type="date" name="assessment_date" id="assessment_date" required value="<?= date('Y-m-d') ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                        
                        <div>
                            <label for="overall_health" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Overall Health Rating <span class="text-red-600">*</span>
                            </label>
                            <select name="overall_health" id="overall_health" required
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select rating</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Physical Assessment -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-paw text-primary-600 dark:text-primary-400 mr-2"></i> Physical Assessment
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="coat_condition" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Coat Condition
                            </label>
                            <select name="coat_condition" id="coat_condition"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select condition</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                                <option value="n/a">Not Applicable</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="skin_condition" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Skin Condition
                            </label>
                            <select name="skin_condition" id="skin_condition"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select condition</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="eye_condition" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Eye Condition
                            </label>
                            <select name="eye_condition" id="eye_condition"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select condition</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="ear_condition" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Ear Condition
                            </label>
                            <select name="ear_condition" id="ear_condition"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select condition</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="dental_health" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Dental Health
                            </label>
                            <select name="dental_health" id="dental_health"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select condition</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="nail_condition" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Nail/Paw Condition
                            </label>
                            <select name="nail_condition" id="nail_condition"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select condition</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                                <option value="n/a">Not Applicable</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Behavioral Assessment -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-brain text-primary-600 dark:text-primary-400 mr-2"></i> Behavioral Assessment
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="energy_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Energy Level
                            </label>
                            <select name="energy_level" id="energy_level"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select level</option>
                                <option value="very_high">Very High</option>
                                <option value="high">High</option>
                                <option value="normal">Normal</option>
                                <option value="low">Low</option>
                                <option value="very_low">Very Low</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="appetite" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Appetite
                            </label>
                            <select name="appetite" id="appetite"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select appetite</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                                <option value="none">None</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="hydration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Hydration
                            </label>
                            <select name="hydration" id="hydration"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select hydration</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="mobility" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Mobility
                            </label>
                            <select name="mobility" id="mobility"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select mobility</option>
                                <option value="excellent">Excellent</option>
                                <option value="good">Good</option>
                                <option value="fair">Fair</option>
                                <option value="poor">Poor</option>
                                <option value="very_poor">Very Poor</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Notes & Observations
                    </label>
                    <textarea name="notes" id="notes" rows="4"
                        class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        placeholder="Enter any additional observations, concerns, or notes about your pet's health"></textarea>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Save Assessment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
