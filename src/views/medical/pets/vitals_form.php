<?php
/**
 * Pet Vitals Form View
 *
 * Form for recording pet vital signs
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-weight text-primary-600 dark:text-primary-400 mr-2"></i> Record Vitals for <?= htmlspecialchars($pet['name']) ?>
            </h1>
            <a href="/momentum/medical/pets/view/<?= $pet['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Pet
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/medical/pets/vitals/save" method="post" class="p-6">
                <input type="hidden" name="pet_id" value="<?= $pet['id'] ?>">

                <!-- Vitals Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Vital Signs
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="record_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Record Date <span class="text-red-600">*</span>
                            </label>
                            <input type="date" name="record_date" id="record_date" required value="<?= date('Y-m-d') ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                        
                        <div>
                            <label for="weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Weight
                            </label>
                            <div class="flex">
                                <input type="number" name="weight" id="weight" step="0.01" min="0"
                                    class="block w-full rounded-l-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <select name="weight_unit" id="weight_unit"
                                    class="rounded-r-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                    <option value="kg" <?= $pet['weight_unit'] === 'kg' ? 'selected' : '' ?>>kg</option>
                                    <option value="lb" <?= $pet['weight_unit'] === 'lb' ? 'selected' : '' ?>>lb</option>
                                </select>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                Current weight: <?= $pet['weight'] ? htmlspecialchars($pet['weight']) . ' ' . htmlspecialchars($pet['weight_unit']) : 'Not recorded' ?>
                            </p>
                        </div>
                        
                        <div>
                            <label for="temperature" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Temperature
                            </label>
                            <div class="flex">
                                <input type="number" name="temperature" id="temperature" step="0.1" min="30" max="45"
                                    class="block w-full rounded-l-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 sm:text-sm">
                                    °C
                                </span>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                Normal range depends on species
                            </p>
                        </div>
                        
                        <div>
                            <label for="heart_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Heart Rate
                            </label>
                            <div class="flex">
                                <input type="number" name="heart_rate" id="heart_rate" min="0" max="300"
                                    class="block w-full rounded-l-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 sm:text-sm">
                                    BPM
                                </span>
                            </div>
                        </div>
                        
                        <div>
                            <label for="respiratory_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Respiratory Rate
                            </label>
                            <div class="flex">
                                <input type="number" name="respiratory_rate" id="respiratory_rate" min="0" max="100"
                                    class="block w-full rounded-l-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 sm:text-sm">
                                    breaths/min
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mb-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Notes
                    </label>
                    <textarea name="notes" id="notes" rows="3"
                        class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        placeholder="Any additional observations or notes about your pet's condition"></textarea>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Save Vitals Record
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
