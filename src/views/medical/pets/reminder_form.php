<?php
/**
 * Pet Reminder Form View
 *
 * Form for creating pet reminders
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white high-contrast-text">
                <i class="fas fa-bell text-primary-600 dark:text-primary-400 mr-2"></i> Create Reminder for <?= htmlspecialchars($pet['name']) ?>
            </h1>
            <a href="/momentum/medical/pets/view/<?= $pet['id'] ?>"
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow"
               data-keyboard-shortcut="B">
                <i class="fas fa-arrow-left mr-1"></i> Back to Pet
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden adhd-card">
            <form action="/momentum/medical/pets/reminder/save" method="post" class="p-6">
                <input type="hidden" name="pet_id" value="<?= $pet['id'] ?>">

                <!-- Reminder Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4 high-contrast-text">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Reminder Information
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 high-contrast-text">
                                Reminder Title <span class="text-red-600">*</span>
                            </label>
                            <input type="text" name="title" id="title" required
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 adhd-focus-glow"
                                placeholder="e.g., Flea Treatment, Grooming Appointment, etc."
                                aria-required="true">
                        </div>

                        <div>
                            <label for="reminder_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 high-contrast-text">
                                Reminder Type <span class="text-red-600">*</span>
                            </label>
                            <select name="reminder_type" id="reminder_type" required
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 adhd-focus-glow"
                                aria-required="true">
                                <option value="">Select type</option>
                                <option value="medication" data-icon="fa-pills">Medication</option>
                                <option value="vaccination" data-icon="fa-syringe">Vaccination</option>
                                <option value="vet_visit" data-icon="fa-stethoscope">Vet Visit</option>
                                <option value="grooming" data-icon="fa-cut">Grooming</option>
                                <option value="training" data-icon="fa-graduation-cap">Training</option>
                                <option value="feeding" data-icon="fa-utensils">Feeding</option>
                                <option value="exercise" data-icon="fa-running">Exercise</option>
                                <option value="other" data-icon="fa-bell">Other</option>
                            </select>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                <i class="fas fa-info-circle mr-1"></i> Each type has a unique color and icon for easy identification
                            </div>
                        </div>

                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 high-contrast-text">
                                Priority
                            </label>
                            <select name="priority" id="priority"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 adhd-focus-glow">
                                <option value="normal" data-color="#3b82f6">Normal</option>
                                <option value="high" data-color="#ef4444">High</option>
                                <option value="low" data-color="#6b7280">Low</option>
                            </select>
                            <div class="flex items-center mt-2">
                                <div class="w-4 h-4 bg-red-500 rounded-full mr-1"></div>
                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">High</span>
                                <div class="w-4 h-4 bg-blue-500 rounded-full mr-1"></div>
                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-2">Normal</span>
                                <div class="w-4 h-4 bg-gray-500 rounded-full mr-1"></div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">Low</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scheduling -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-calendar-alt text-primary-600 dark:text-primary-400 mr-2"></i> Scheduling
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="due_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Due Date <span class="text-red-600">*</span>
                            </label>
                            <input type="date" name="due_date" id="due_date" required
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>

                        <div>
                            <label for="due_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Due Time
                            </label>
                            <input type="time" name="due_time" id="due_time"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>

                        <div class="md:col-span-2">
                            <label for="recurrence_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Recurrence
                            </label>
                            <select name="recurrence_type" id="recurrence_type"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="none">One-time only</option>
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                                <option value="custom">Custom</option>
                            </select>
                        </div>

                        <div id="recurrence_interval_container" class="hidden">
                            <label for="recurrence_interval" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Repeat Every
                            </label>
                            <div class="flex">
                                <input type="number" name="recurrence_interval" id="recurrence_interval" min="1" value="1"
                                    class="block w-full rounded-l-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <span id="interval_unit" class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400 sm:text-sm">
                                    days
                                </span>
                            </div>
                        </div>

                        <div id="recurrence_end_container" class="hidden">
                            <label for="recurrence_end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                End Recurrence
                            </label>
                            <input type="date" name="recurrence_end_date" id="recurrence_end_date"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                    </div>
                </div>

                <!-- Notification -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-bell text-primary-600 dark:text-primary-400 mr-2"></i> Notification
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="notification_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Notification Type
                            </label>
                            <select name="notification_type" id="notification_type"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="app">App Notification</option>
                                <option value="email">Email</option>
                                <option value="both">Both</option>
                                <option value="none">None</option>
                            </select>
                        </div>

                        <div>
                            <label for="notification_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Notify Before
                            </label>
                            <select name="notification_time" id="notification_time"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="0">At the time</option>
                                <option value="15">15 minutes before</option>
                                <option value="30">30 minutes before</option>
                                <option value="60">1 hour before</option>
                                <option value="120">2 hours before</option>
                                <option value="1440">1 day before</option>
                                <option value="2880">2 days before</option>
                                <option value="10080">1 week before</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Additional Details -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-align-left text-primary-600 dark:text-primary-400 mr-2"></i> Additional Details
                    </h2>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="3"
                            class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            placeholder="Add any additional details about this reminder"></textarea>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 adhd-focus-glow pulse-animation"
                            data-keyboard-shortcut="S"
                            data-important="true">
                        <i class="fas fa-save mr-2"></i> Save Reminder
                    </button>
                </div>

                <!-- Keyboard Shortcuts Help -->
                <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        <i class="fas fa-keyboard text-primary-600 dark:text-primary-400 mr-2"></i> Keyboard Shortcuts
                    </h3>
                    <div class="grid grid-cols-2 gap-2 text-xs text-gray-500 dark:text-gray-400">
                        <div class="flex items-center">
                            <span class="keyboard-shortcut mr-2">B</span> Back to Pet
                        </div>
                        <div class="flex items-center">
                            <span class="keyboard-shortcut mr-2">S</span> Save Reminder
                        </div>
                        <div class="flex items-center">
                            <span class="keyboard-shortcut mr-2">Esc</span> Exit Focus Mode
                        </div>
                        <div class="flex items-center">
                            <span class="keyboard-shortcut mr-2">Tab</span> Navigate Form Fields
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const recurrenceTypeSelect = document.getElementById('recurrence_type');
    const recurrenceIntervalContainer = document.getElementById('recurrence_interval_container');
    const recurrenceEndContainer = document.getElementById('recurrence_end_container');
    const intervalUnitSpan = document.getElementById('interval_unit');

    // Function to update the visibility of recurrence fields
    function updateRecurrenceFields() {
        const recurrenceType = recurrenceTypeSelect.value;

        if (recurrenceType === 'none') {
            recurrenceIntervalContainer.classList.add('hidden');
            recurrenceEndContainer.classList.add('hidden');
        } else {
            recurrenceIntervalContainer.classList.remove('hidden');
            recurrenceEndContainer.classList.remove('hidden');

            // Update interval unit text
            switch (recurrenceType) {
                case 'daily':
                    intervalUnitSpan.textContent = 'days';
                    break;
                case 'weekly':
                    intervalUnitSpan.textContent = 'weeks';
                    break;
                case 'monthly':
                    intervalUnitSpan.textContent = 'months';
                    break;
                case 'yearly':
                    intervalUnitSpan.textContent = 'years';
                    break;
                case 'custom':
                    intervalUnitSpan.textContent = 'days';
                    break;
            }
        }
    }

    // Initial update
    updateRecurrenceFields();

    // Listen for changes
    recurrenceTypeSelect.addEventListener('change', updateRecurrenceFields);
});
</script>
