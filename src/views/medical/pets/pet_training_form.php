<?php
/**
 * Pet Training Form View
 *
 * Form for adding a new training log for a pet
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-graduation-cap text-primary-600 dark:text-primary-400 mr-2"></i> Add Training Log
            </h1>
            <a href="/momentum/medical/pets/view/<?= $pet['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to <?= htmlspecialchars($pet['name']) ?>
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/medical/pets/training/save" method="post" class="p-6">
                <input type="hidden" name="pet_id" value="<?= $pet['id'] ?>">

                <!-- Training Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Training Information
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="training_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Training Date <span class="text-red-600">*</span>
                            </label>
                            <input type="date" name="training_date" id="training_date" required
                                   value="<?= date('Y-m-d') ?>"
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="skill_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Skill/Command Name <span class="text-red-600">*</span>
                            </label>
                            <input type="text" name="skill_name" id="skill_name" required
                                   placeholder="e.g., Sit, Stay, Heel"
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                    </div>
                    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Duration (minutes)
                            </label>
                            <input type="number" name="duration" id="duration" min="1" max="240"
                                   placeholder="15"
                                   class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                        </div>
                        <div>
                            <label for="progress_rating" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Progress Rating (1-10)
                            </label>
                            <input type="range" name="progress_rating" id="progress_rating" min="1" max="10" value="5"
                                   class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                                   oninput="document.getElementById('progress_value').textContent = this.value">
                            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                <span>Poor</span>
                                <span id="progress_value">5</span>
                                <span>Excellent</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-sticky-note text-primary-600 dark:text-primary-400 mr-2"></i> Notes
                    </h2>
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Training Notes
                        </label>
                        <textarea name="notes" id="notes" rows="4"
                                  placeholder="Enter any observations, challenges, or successes during this training session..."
                                  class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"></textarea>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Save Training Log
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default date to today
    document.getElementById('training_date').valueAsDate = new Date();
});
</script>
