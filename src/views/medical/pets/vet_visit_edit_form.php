<?php
/**
 * Pet Vet Visit Edit Form
 *
 * Form for editing a vet visit record
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back navigation -->
        <div class="mb-6">
            <a href="/momentum/medical/pets/vet-visits/<?= $pet['id'] ?>" class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Vet Visits
            </a>
        </div>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0 high-contrast-text">
                <i class="fas fa-edit text-primary-600 dark:text-primary-400 mr-2"></i> Edit Vet Visit for <?= htmlspecialchars($pet['name']) ?>
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/medical/pets/vet-visit/update" method="post" class="space-y-6">
                    <input type="hidden" name="id" value="<?= $vetVisit['id'] ?>">
                    <input type="hidden" name="pet_id" value="<?= $pet['id'] ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="visit_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Visit Date <span class="text-red-600">*</span>
                            </label>
                            <input type="date" name="visit_date" id="visit_date" required
                                value="<?= $vetVisit['visit_date'] ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>

                        <div>
                            <label for="visit_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Visit Time
                            </label>
                            <input type="time" name="visit_time" id="visit_time"
                                value="<?= $vetVisit['visit_time'] ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>

                        <div>
                            <label for="vet_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Veterinarian Name
                            </label>
                            <input type="text" name="vet_name" id="vet_name"
                                value="<?= htmlspecialchars($vetVisit['vet_name'] ?? '') ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Dr. Smith">
                        </div>

                        <div>
                            <label for="clinic_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Clinic Name
                            </label>
                            <input type="text" name="clinic_name" id="clinic_name"
                                value="<?= htmlspecialchars($vetVisit['clinic_name'] ?? '') ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Animal Care Clinic">
                        </div>

                        <div class="md:col-span-2">
                            <label for="visit_reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Reason for Visit <span class="text-red-600">*</span>
                            </label>
                            <textarea name="visit_reason" id="visit_reason" rows="2" required
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Describe the reason for the visit"><?= htmlspecialchars($vetVisit['visit_reason'] ?? '') ?></textarea>
                        </div>

                        <div class="md:col-span-2">
                            <label for="diagnosis" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Diagnosis
                            </label>
                            <textarea name="diagnosis" id="diagnosis" rows="2"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Diagnosis provided by the veterinarian"><?= htmlspecialchars($vetVisit['diagnosis'] ?? '') ?></textarea>
                        </div>

                        <div class="md:col-span-2">
                            <label for="treatment" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Treatment
                            </label>
                            <textarea name="treatment" id="treatment" rows="2"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Treatment prescribed"><?= htmlspecialchars($vetVisit['treatment'] ?? '') ?></textarea>
                        </div>

                        <div>
                            <label for="follow_up_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Follow-up Date
                            </label>
                            <input type="date" name="follow_up_date" id="follow_up_date"
                                value="<?= $vetVisit['follow_up_date'] ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>

                        <div>
                            <label for="cost" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Cost (Rs)
                            </label>
                            <input type="number" name="cost" id="cost" step="0.01"
                                value="<?= $vetVisit['cost'] ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="0.00">
                        </div>

                        <div class="md:col-span-2">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Notes
                            </label>
                            <textarea name="notes" id="notes" rows="3"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                                placeholder="Additional notes about the visit"><?= htmlspecialchars($vetVisit['notes'] ?? '') ?></textarea>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <a href="/momentum/medical/pets/vet-visits/<?= $pet['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-save mr-1"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
