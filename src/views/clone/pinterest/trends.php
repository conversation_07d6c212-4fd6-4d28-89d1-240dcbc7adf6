<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex items-center mb-2">
            <a href="/momentum/clone/pinterest" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Pinterest Trends</h1>
        </div>
        <p class="text-gray-600 dark:text-gray-400">
            Analyze trending content and engagement patterns across Pinterest categories.
        </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Category Trends Chart -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-chart-line mr-2"></i> Category Trends
                </h2>

                <div class="mb-4">
                    <label for="trend-period" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Time Period
                    </label>
                    <select id="trend-period" class="w-full sm:w-auto px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                        <option value="7">Last 7 Days</option>
                        <option value="30" selected>Last 30 Days</option>
                        <option value="90">Last 90 Days</option>
                        <option value="365">Last Year</option>
                    </select>
                </div>

                <div class="h-80">
                    <canvas id="categoryTrendsChart"></canvas>
                </div>
            </div>

            <!-- Engagement Metrics Chart -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-chart-bar mr-2"></i> Engagement Metrics
                </h2>

                <div class="mb-4 flex flex-wrap gap-2">
                    <button class="engagement-metric-btn px-3 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded text-sm font-medium border-2 border-red-500" data-metric="saves">
                        Saves
                    </button>
                    <button class="engagement-metric-btn px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded text-sm font-medium" data-metric="comments">
                        Comments
                    </button>
                    <button class="engagement-metric-btn px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded text-sm font-medium" data-metric="clicks">
                        Clicks
                    </button>
                </div>

                <div class="h-80">
                    <canvas id="engagementMetricsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Top Trending Categories -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-fire mr-2"></i> Top Trending Categories
                </h2>

                <div class="space-y-4">
                    <?php foreach ($trends['categories'] as $category => $data): ?>
                        <div>
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-gray-900 dark:text-white font-medium"><?= $category ?></span>
                                <?php
                                    // Calculate growth (last value compared to first value)
                                    $first = $data['data'][0];
                                    $last = end($data['data']);
                                    $growth = $first > 0 ? (($last - $first) / $first) * 100 : 0;
                                    $isPositive = $growth >= 0;
                                ?>
                                <div class="flex items-center">
                                    <span class="<?= $isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?> text-sm font-medium mr-1">
                                        <?= $isPositive ? '+' : '' ?><?= number_format($growth, 1) ?>%
                                    </span>
                                    <i class="fas fa-arrow-<?= $isPositive ? 'up' : 'down' ?> <?= $isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>"></i>
                                </div>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                <div class="bg-red-600 h-2.5 rounded-full" style="width: <?= min(100, max(5, ($last / max($data['data']) * 100))) ?>%"></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Trend Insights -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-lightbulb mr-2"></i> Trend Insights
                </h2>

                <div class="space-y-4 text-gray-600 dark:text-gray-400">
                    <div>
                        <h3 class="font-medium text-gray-900 dark:text-white mb-1">Seasonal Patterns</h3>
                        <p class="text-sm">Home decor and recipe categories show strong seasonal trends, with peaks during holiday seasons.</p>
                    </div>

                    <div>
                        <h3 class="font-medium text-gray-900 dark:text-white mb-1">Visual Elements</h3>
                        <p class="text-sm">Pins with bright colors and clear typography consistently receive higher engagement across all categories.</p>
                    </div>

                    <div>
                        <h3 class="font-medium text-gray-900 dark:text-white mb-1">Content Length</h3>
                        <p class="text-sm">Infographics and step-by-step guides with 5-7 points receive the highest save rates.</p>
                    </div>

                    <div>
                        <h3 class="font-medium text-gray-900 dark:text-white mb-1">Emerging Trends</h3>
                        <p class="text-sm">Sustainable living and minimalist design categories are showing the fastest growth rates.</p>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="/momentum/clone/pinterest/analysis" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium">
                        <i class="fas fa-chart-pie mr-1"></i> View detailed analysis
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Parse trend data from PHP
        const trendData = <?= json_encode($trends) ?>;

        // Set up category trends chart
        const categoryTrendsCtx = document.getElementById('categoryTrendsChart').getContext('2d');
        const categoryTrendsChart = new Chart(categoryTrendsCtx, {
            type: 'line',
            data: {
                labels: trendData.categories['Home Decor'].labels,
                datasets: Object.entries(trendData.categories).map(([category, data], index) => {
                    const colors = [
                        { line: 'rgb(239, 68, 68)', bg: 'rgba(239, 68, 68, 0.1)' },
                        { line: 'rgb(59, 130, 246)', bg: 'rgba(59, 130, 246, 0.1)' },
                        { line: 'rgb(16, 185, 129)', bg: 'rgba(16, 185, 129, 0.1)' }
                    ];
                    return {
                        label: category,
                        data: data.data,
                        borderColor: colors[index].line,
                        backgroundColor: colors[index].bg,
                        tension: 0.3,
                        borderWidth: 2,
                        pointBackgroundColor: colors[index].line,
                        pointRadius: 3,
                        fill: true
                    };
                })
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            boxWidth: 6
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Pin Count'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time Period'
                        }
                    }
                }
            }
        });

        // Set up engagement metrics chart
        const engagementMetricsCtx = document.getElementById('engagementMetricsChart').getContext('2d');
        const engagementMetricsChart = new Chart(engagementMetricsCtx, {
            type: 'bar',
            data: {
                labels: trendData.engagement.labels,
                datasets: [{
                    label: 'Saves',
                    data: trendData.engagement.saves,
                    backgroundColor: 'rgba(239, 68, 68, 0.7)',
                    borderColor: 'rgb(239, 68, 68)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Count'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time Period'
                        }
                    }
                }
            }
        });

        // Handle engagement metric buttons
        const engagementButtons = document.querySelectorAll('.engagement-metric-btn');
        engagementButtons.forEach(button => {
            button.addEventListener('click', function() {
                const metric = this.getAttribute('data-metric');

                // Update active button styling
                engagementButtons.forEach(btn => {
                    btn.classList.remove('bg-red-100', 'text-red-800', 'dark:bg-red-900', 'dark:text-red-200', 'border-2', 'border-red-500');
                    btn.classList.add('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-200');
                });
                this.classList.remove('bg-gray-100', 'text-gray-800', 'dark:bg-gray-700', 'dark:text-gray-200');
                this.classList.add('bg-red-100', 'text-red-800', 'dark:bg-red-900', 'dark:text-red-200', 'border-2', 'border-red-500');

                // Update chart data
                engagementMetricsChart.data.datasets[0].label = metric.charAt(0).toUpperCase() + metric.slice(1);
                engagementMetricsChart.data.datasets[0].data = trendData.engagement[metric];
                engagementMetricsChart.update();
            });
        });

        // Handle trend period change
        document.getElementById('trend-period').addEventListener('change', function() {
            // In a real implementation, this would fetch new data based on the selected period
            // For now, we'll just show a message
            alert('In a real implementation, this would load data for the selected period: ' + this.value + ' days');
        });
    });
</script>

<script src="/momentum/js/pinterest-clone-fix.js"></script>
