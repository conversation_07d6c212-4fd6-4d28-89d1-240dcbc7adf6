<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <div class="flex items-center mb-2">
            <a href="/momentum/clone/pinterest/view-scrape/<?= $scrape['id'] ?>" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Analysis: <?= View::escape($scrape['search_term']) ?></h1>
        </div>
        <p class="text-gray-600 dark:text-gray-400">
            Detailed analysis of <?= $analysis['total_pins'] ?> pins collected on <?= date('F j, Y', strtotime($scrape['created_at'])) ?>
        </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Overview Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-chart-pie mr-2"></i> Overview
            </h2>

            <div class="space-y-4">
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">Total Pins</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400"><?= number_format($analysis['total_pins']) ?></span>
                    </div>
                </div>

                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">Total Saves</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400"><?= number_format($analysis['total_saves']) ?></span>
                    </div>
                </div>

                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">Total Comments</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400"><?= number_format($analysis['total_comments']) ?></span>
                    </div>
                </div>

                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">Average Saves per Pin</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400"><?= number_format($analysis['avg_saves']) ?></span>
                    </div>
                </div>

                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-gray-900 dark:text-white">Average Comments per Pin</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400"><?= number_format($analysis['avg_comments']) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Boards Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-clipboard mr-2"></i> Top Boards
            </h2>

            <div class="space-y-4">
                <?php foreach ($analysis['top_boards'] as $board): ?>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($board['name']) ?></span>
                            <span class="text-xs text-gray-500 dark:text-gray-400"><?= $board['count'] ?> pins</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-red-600 h-2 rounded-full" style="width: <?= ($board['count'] / $analysis['total_pins'] * 100) ?>%"></div>
                        </div>
                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Avg. <?= number_format(round($board['total_saves'] / $board['count'])) ?> saves per pin
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Keyword Frequency Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-tags mr-2"></i> Top Keywords
            </h2>

            <div class="flex flex-wrap gap-2">
                <?php foreach ($analysis['keyword_frequency'] as $keyword => $count): ?>
                    <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-sm" style="font-size: <?= min(1.5, max(0.8, 0.8 + ($count / 10 * 0.7))) ?>rem">
                        <?= View::escape($keyword) ?> (<?= $count ?>)
                    </span>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Pin Analysis -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            <i class="fas fa-thumbtack mr-2"></i> Pin Analysis
        </h2>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                    <tr>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Pin</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Board</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Saves</th>
                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Comments</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($pins as $pin): ?>
                        <tr>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-md object-cover" src="<?= $pin['image_url'] ?>" alt="<?= View::escape($pin['title']) ?>">
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4">
                                <div class="text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($pin['title']) ?></div>
                                <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs"><?= View::escape($pin['description']) ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white"><?= View::escape($pin['board_name']) ?></div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white"><?= number_format($pin['save_count']) ?></div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php
                                        $saveRatio = $pin['save_count'] / $analysis['avg_saves'];
                                        if ($saveRatio > 1.5) {
                                            echo '<span class="text-green-500"><i class="fas fa-arrow-up"></i> ' . round(($saveRatio - 1) * 100) . '% above avg</span>';
                                        } elseif ($saveRatio < 0.5) {
                                            echo '<span class="text-red-500"><i class="fas fa-arrow-down"></i> ' . round((1 - $saveRatio) * 100) . '% below avg</span>';
                                        } else {
                                            echo '<span>Near average</span>';
                                        }
                                    ?>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900 dark:text-white"><?= number_format($pin['comment_count']) ?></div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    <?php
                                        $commentRatio = $pin['comment_count'] / max(1, $analysis['avg_comments']);
                                        if ($commentRatio > 1.5) {
                                            echo '<span class="text-green-500"><i class="fas fa-arrow-up"></i> ' . round(($commentRatio - 1) * 100) . '% above avg</span>';
                                        } elseif ($commentRatio < 0.5) {
                                            echo '<span class="text-red-500"><i class="fas fa-arrow-down"></i> ' . round((1 - $commentRatio) * 100) . '% below avg</span>';
                                        } else {
                                            echo '<span>Near average</span>';
                                        }
                                    ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recommendations -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            <i class="fas fa-lightbulb mr-2"></i> Recommendations
        </h2>

        <div class="space-y-4 text-gray-600 dark:text-gray-400">
            <div>
                <h3 class="font-medium text-gray-900 dark:text-white mb-1">Content Strategy</h3>
                <p class="text-sm">Based on the analysis, focus on creating content related to the top keywords and boards identified in this scrape. The most engaging content appears to be about <?= array_key_first($analysis['keyword_frequency']) ?> and related topics.</p>
            </div>

            <div>
                <h3 class="font-medium text-gray-900 dark:text-white mb-1">Engagement Optimization</h3>
                <p class="text-sm">Pins with higher save counts tend to have clear, descriptive titles and high-quality images. Consider optimizing your content with these characteristics to improve engagement.</p>
            </div>

            <div>
                <h3 class="font-medium text-gray-900 dark:text-white mb-1">Board Strategy</h3>
                <p class="text-sm">The most popular boards in this niche are focused on <?= $analysis['top_boards'][0]['name'] ?? 'specific topics' ?>. Consider creating similar board categories to align with user interests.</p>
            </div>
        </div>
    </div>
</div>

<script src="/momentum/js/pinterest-clone-fix.js"></script>
