<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Upload Pinterest Pin</h1>

        <?php if (Session::hasFlash('error')): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                <p><?= Session::getFlash('error') ?></p>
            </div>
        <?php endif; ?>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <form action="/momentum/clone/pinterest/upload-pin" method="POST" enctype="multipart/form-data">
                <div class="mb-4">
                    <label for="board_id" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">Board <span class="text-red-600">*</span></label>
                    <select id="board_id" name="board_id" required
                            class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="">Select a board</option>
                        <?php foreach ($boards as $board): ?>
                            <option value="<?= $board['id'] ?>" <?= isset($_GET['board_id']) && $_GET['board_id'] === $board['id'] ? 'selected' : '' ?>>
                                <?= View::escape($board['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="mb-4">
                    <label for="image" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">Image <span class="text-red-600">*</span></label>
                    <div class="border-dashed border-2 border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                        <input type="file" id="image" name="image" required accept="image/*" class="hidden">
                        <label for="image" class="cursor-pointer">
                            <div id="image-preview" class="flex flex-col items-center justify-center">
                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 dark:text-gray-500 mb-2"></i>
                                <p class="text-gray-700 dark:text-gray-300">Click to upload an image</p>
                                <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">PNG, JPG, GIF up to 10MB</p>
                            </div>
                        </label>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="title" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">Title <span class="text-red-600">*</span></label>
                    <input type="text" id="title" name="title" required
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                           placeholder="Enter a title for your pin">
                </div>

                <div class="mb-4">
                    <label for="description" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">Description</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                              placeholder="Describe your pin (optional)"></textarea>
                </div>

                <div class="mb-6">
                    <label for="link" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">Link (optional)</label>
                    <input type="url" id="link" name="link"
                           class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                           placeholder="https://example.com">
                </div>

                <div class="flex justify-between">
                    <a href="/momentum/clone/pinterest/boards" class="bg-gray-300 hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Upload Pin
                    </button>
                </div>
            </form>
        </div>

        <div class="mt-8 text-center">
            <a href="/momentum/clone/pinterest/boards" class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                <i class="fas fa-arrow-left mr-1"></i> Back to Boards
            </a>
        </div>
    </div>
</div>

<script>
    // Image preview functionality
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('image-preview');
                preview.innerHTML = `
                    <img src="${e.target.result}" alt="Preview" class="max-h-40 mb-2">
                    <p class="text-gray-700 dark:text-gray-300">${file.name}</p>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                `;
            }
            reader.readAsDataURL(file);
        }
    });
</script>


