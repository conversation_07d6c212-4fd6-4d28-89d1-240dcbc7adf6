<?php
/**
 * View AI Prompt Details
 */
?>

<div class="ai-prompt-container">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
            <a href="/momentum/ai-prompts/library" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($prompt['title']) ?></h1>
                <div class="flex items-center space-x-3 mt-1">
                    <?php if ($category): ?>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                              style="background-color: <?= $category['color'] ?? '#6366F1' ?>20; color: <?= $category['color'] ?? '#6366F1' ?>;">
                            <i class="<?= $category['icon'] ?? 'fa-folder' ?> mr-1"></i>
                            <?= htmlspecialchars($category['name']) ?>
                        </span>
                    <?php endif; ?>

                    <?php if ($prompt['is_template']): ?>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                            <i class="fas fa-bookmark mr-1"></i>
                            Template
                        </span>
                    <?php endif; ?>

                    <?php if ($prompt['is_favorite']): ?>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                            <i class="fas fa-star mr-1"></i>
                            Favorite
                        </span>
                    <?php endif; ?>

                    <span class="text-sm text-gray-500 dark:text-gray-400">
                        Created <?= date('M j, Y', strtotime($prompt['created_at'])) ?>
                    </span>
                </div>
            </div>
        </div>

        <div class="flex space-x-3">
            <button onclick="toggleFavorite(<?= $prompt['id'] ?>)" class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-lg transition-colors duration-200">
                <i class="fas fa-star mr-2"></i>
                <?= $prompt['is_favorite'] ? 'Remove from Favorites' : 'Add to Favorites' ?>
            </button>

            <a href="/momentum/ai-prompts/edit/<?= $prompt['id'] ?>" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                <i class="fas fa-edit mr-2"></i>
                Edit
            </a>

            <a href="/momentum/ai-prompts/execute/<?= $prompt['id'] ?>" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors duration-200">
                <i class="fas fa-play mr-2"></i>
                Execute
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Description -->
            <?php if ($prompt['description']): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Description</h2>
                <p class="text-gray-700 dark:text-gray-300"><?= nl2br(htmlspecialchars($prompt['description'])) ?></p>
            </div>
            <?php endif; ?>

            <!-- Prompt Content -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Prompt Content</h2>
                    <div class="flex space-x-2">
                        <button onclick="copyPrompt()" class="inline-flex items-center px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                            <i class="fas fa-copy mr-1"></i>
                            Copy
                        </button>

                        <button onclick="showPreview()" class="inline-flex items-center px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-sm font-medium rounded-md hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200">
                            <i class="fas fa-eye mr-1"></i>
                            Preview
                        </button>
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <pre id="promptContent" class="text-gray-900 dark:text-white font-mono text-sm whitespace-pre-wrap"><?= htmlspecialchars($prompt['prompt_text']) ?></pre>
                </div>
            </div>

            <!-- Variables -->
            <?php if (!empty($variables)): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Variables</h2>

                <div class="space-y-3">
                    <?php foreach ($variables as $variable): ?>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-medium text-gray-900 dark:text-white">
                                    {{<?= htmlspecialchars($variable['name']) ?>}}
                                </h3>
                                <span class="text-xs px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded">
                                    <?= ucfirst($variable['type']) ?>
                                </span>
                            </div>

                            <?php if ($variable['description']): ?>
                                <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">
                                    <?= htmlspecialchars($variable['description']) ?>
                                </p>
                            <?php endif; ?>

                            <?php if ($variable['default_value']): ?>
                                <p class="text-gray-500 dark:text-gray-500 text-xs">
                                    Default: <?= htmlspecialchars($variable['default_value']) ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Tags -->
            <?php if ($prompt['tags']): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Tags</h2>
                <div class="flex flex-wrap gap-2">
                    <?php
                    $tags = explode(',', $prompt['tags']);
                    foreach ($tags as $tag):
                        $tag = trim($tag);
                        if ($tag):
                    ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                            #<?= htmlspecialchars($tag) ?>
                        </span>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Statistics</h2>

                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Usage Count</span>
                        <span class="font-medium text-gray-900 dark:text-white"><?= $prompt['usage_count'] ?? 0 ?></span>
                    </div>

                    <?php if ($prompt['effectiveness_rating']): ?>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Rating</span>
                        <div class="flex items-center">
                            <span class="font-medium text-gray-900 dark:text-white mr-1"><?= number_format($prompt['effectiveness_rating'], 1) ?></span>
                            <div class="flex">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star text-xs <?= $i <= $prompt['effectiveness_rating'] ? 'text-yellow-500' : 'text-gray-300' ?>"></i>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Created</span>
                        <span class="font-medium text-gray-900 dark:text-white"><?= date('M j, Y', strtotime($prompt['created_at'])) ?></span>
                    </div>

                    <div class="flex items-center justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Last Updated</span>
                        <span class="font-medium text-gray-900 dark:text-white"><?= date('M j, Y', strtotime($prompt['updated_at'])) ?></span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h2>

                <div class="space-y-3">
                    <a href="/momentum/ai-prompts/execute/<?= $prompt['id'] ?>" class="w-full inline-flex justify-center items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-play mr-2"></i>
                        Execute Prompt
                    </a>

                    <a href="/momentum/ai-prompts/duplicate/<?= $prompt['id'] ?>" class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-copy mr-2"></i>
                        Duplicate
                    </a>

                    <a href="/momentum/ai-prompts/export/<?= $prompt['id'] ?>" class="w-full inline-flex justify-center items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </a>

                    <button onclick="sharePrompt()" class="w-full inline-flex justify-center items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-share mr-2"></i>
                        Share
                    </button>
                </div>
            </div>

            <!-- Related Prompts -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Related Prompts</h2>

                <div class="space-y-3">
                    <p class="text-gray-600 dark:text-gray-400 text-sm">
                        Related prompts will be shown here based on category and tags.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleFavorite(promptId) {
    fetch(`/momentum/ai-prompts/toggle-favorite/${promptId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the button text and reload the page to reflect changes
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error toggling favorite:', error);
    });
}

function copyPrompt() {
    const promptContent = document.getElementById('promptContent').textContent;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(promptContent).then(() => {
            showNotification('Prompt copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy: ', err);
            fallbackCopyTextToClipboard(promptContent);
        });
    } else {
        fallbackCopyTextToClipboard(promptContent);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showNotification('Prompt copied to clipboard!', 'success');
        } else {
            showNotification('Failed to copy prompt', 'error');
        }
    } catch (err) {
        console.error('Fallback: Oops, unable to copy', err);
        showNotification('Failed to copy prompt', 'error');
    }

    document.body.removeChild(textArea);
}

function showPreview() {
    const promptContent = document.getElementById('promptContent').textContent;

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Prompt Preview</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 font-mono text-sm text-gray-900 dark:text-white whitespace-pre-wrap max-h-96 overflow-y-auto">
                    ${promptContent.replace(/\{\{([^}]+)\}\}/g, '<span class="bg-purple-200 dark:bg-purple-800 px-1 rounded">{{$1}}</span>')}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function sharePrompt() {
    const url = window.location.href;

    if (navigator.share) {
        navigator.share({
            title: document.querySelector('h1').textContent,
            url: url
        }).then(() => {
            showNotification('Prompt shared successfully!', 'success');
        }).catch(err => {
            console.error('Error sharing:', err);
            copyToClipboard(url);
        });
    } else {
        copyToClipboard(url);
    }
}

function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('Link copied to clipboard!', 'success');
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
