<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex items-center mb-8">
        <a href="/momentum/help" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <i class="fas fa-arrow-left"></i>
            <span class="ml-1">Back to Help Center</span>
        </a>
        <h1 class="text-3xl font-bold">Feature Request</h1>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Submit a Feature Request</h2>
        <p class="mb-6">Have an idea for a new feature or an improvement to an existing one? We'd love to hear it! Please fill out the form below with as much detail as possible.</p>
        
        <form action="/momentum/help/submit-feature-request" method="post" class="space-y-6">
            <div>
                <label for="feature_title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Feature Title</label>
                <input type="text" name="feature_title" id="feature_title" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white" placeholder="A brief title for your feature idea" required>
            </div>
            
            <div>
                <label for="feature_category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Feature Category</label>
                <select name="feature_category" id="feature_category" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                    <option value="">Select a category</option>
                    <option value="mind-mapping">Mind Mapping</option>
                    <option value="adhd">ADHD Management</option>
                    <option value="productivity">Productivity</option>
                    <option value="wellness">Wellness</option>
                    <option value="learning">Learning</option>
                    <option value="social">Social</option>
                    <option value="financial">Financial</option>
                    <option value="environment">Environment</option>
                    <option value="ai">AI Assistance</option>
                    <option value="analytics">Analytics</option>
                    <option value="gamification">Gamification</option>
                    <option value="integration">Integration</option>
                    <option value="contact">Contact Management</option>
                    <option value="resource">Resource Management</option>
                    <option value="workspace">Workspace</option>
                    <option value="knowledge">Knowledge Management</option>
                    <option value="meeting">Meetings</option>
                    <option value="digital-asset">Digital Assets</option>
                    <option value="health">Health</option>
                    <option value="home">Home</option>
                    <option value="travel">Travel</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div>
                <label for="feature_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Feature Description</label>
                <textarea name="feature_description" id="feature_description" rows="5" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white" placeholder="Describe your feature idea in detail. What problem does it solve? How would it work?" required></textarea>
            </div>
            
            <div>
                <label for="use_case" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Use Case</label>
                <textarea name="use_case" id="use_case" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white" placeholder="Describe a specific scenario where this feature would be useful"></textarea>
            </div>
            
            <div>
                <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Priority Level</label>
                <select name="priority" id="priority" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white">
                    <option value="low">Low - Nice to have</option>
                    <option value="medium">Medium - Would improve my experience</option>
                    <option value="high">High - Would significantly improve my experience</option>
                    <option value="critical">Critical - Essential for my use case</option>
                </select>
            </div>
            
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Your Email (optional)</label>
                <input type="email" name="email" id="email" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:text-white" placeholder="If you'd like us to follow up with you">
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">We'll only use this to contact you about your feature request</p>
            </div>
            
            <div class="pt-4">
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Submit Feature Request
                </button>
            </div>
        </form>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">Feature Request Guidelines</h2>
        <div class="space-y-4">
            <div>
                <h3 class="font-medium text-lg">What makes a good feature request?</h3>
                <ul class="list-disc pl-5 mt-2 space-y-1 text-gray-600 dark:text-gray-400">
                    <li>Clear description of the problem you're trying to solve</li>
                    <li>Specific examples of how you would use the feature</li>
                    <li>Explanation of why existing features don't meet your needs</li>
                    <li>Ideas for how the feature might work (optional)</li>
                </ul>
            </div>
            
            <div>
                <h3 class="font-medium text-lg">What happens after you submit?</h3>
                <p class="text-gray-600 dark:text-gray-400 mt-2">
                    Our team reviews all feature requests regularly. We prioritize features based on user demand, alignment with our roadmap, and technical feasibility. While we can't implement every request, your feedback is invaluable in shaping the future of Momentum.
                </p>
            </div>
            
            <div>
                <h3 class="font-medium text-lg">Want to see what others have requested?</h3>
                <p class="text-gray-600 dark:text-gray-400 mt-2">
                    Check out our <a href="/momentum/help/feature-overview" class="text-primary-600 dark:text-primary-400 hover:underline">Feature Overview</a> page to see what features are already planned or in development.
                </p>
            </div>
        </div>
    </div>
</div>
