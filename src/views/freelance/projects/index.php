<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Projects</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your freelance projects</p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="/momentum/freelance/projects/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Project
                </a>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                        <i class="fas fa-project-diagram text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Projects</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $projectSummary['total'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-tasks text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Projects</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $projectSummary['active'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3">
                        <i class="fas fa-check-circle text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Completed Projects</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $projectSummary['completed'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-md p-3">
                        <i class="fas fa-money-bill-wave text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Value</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">Rs <?= number_format($projectSummary['total_value'] ?? 0, 0) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <form action="/momentum/freelance/projects" method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                        <input type="text" name="search" id="search" value="<?= isset($filters['search']) ? htmlspecialchars($filters['search']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Statuses</option>
                            <option value="not_started" <?= isset($filters['status']) && $filters['status'] === 'not_started' ? 'selected' : '' ?>>Not Started</option>
                            <option value="in_progress" <?= isset($filters['status']) && $filters['status'] === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="on_hold" <?= isset($filters['status']) && $filters['status'] === 'on_hold' ? 'selected' : '' ?>>On Hold</option>
                            <option value="completed" <?= isset($filters['status']) && $filters['status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="cancelled" <?= isset($filters['status']) && $filters['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                    <div>
                        <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Client</label>
                        <select name="client_id" id="client_id" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Clients</option>
                            <?php if (isset($clients) && is_array($clients)): ?>
                                <?php foreach ($clients as $client): ?>
                                    <option value="<?= $client['id'] ?>" <?= isset($filters['client_id']) && $filters['client_id'] == $client['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($client['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div>
                        <label for="sort" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sort By</label>
                        <select name="sort" id="sort" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="deadline" <?= isset($filters['sort']) && $filters['sort'] === 'deadline' ? 'selected' : '' ?>>Deadline</option>
                            <option value="value" <?= isset($filters['sort']) && $filters['sort'] === 'value' ? 'selected' : '' ?>>Value</option>
                            <option value="client" <?= isset($filters['sort']) && $filters['sort'] === 'client' ? 'selected' : '' ?>>Client</option>
                            <option value="status" <?= isset($filters['sort']) && $filters['sort'] === 'status' ? 'selected' : '' ?>>Status</option>
                            <option value="created_at" <?= isset($filters['sort']) && $filters['sort'] === 'created_at' ? 'selected' : 'selected' ?>>Date Created</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i> Apply Filters
                    </button>
                    <a href="/momentum/freelance/projects" class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i> Clear
                    </a>
                </div>
            </form>
        </div>

        <!-- Projects List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Project List</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    <?= count($projects ?? []) ?> projects found
                </p>
            </div>
            
            <?php if (empty($projects)): ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No projects found. Create your first project to get started!</p>
                    <a href="/momentum/freelance/projects/create" class="inline-flex items-center justify-center px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Create Project
                    </a>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Project</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Client</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Deadline</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Value</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($projects as $project): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($project['name']) ?>
                                        </div>
                                        <?php if (!empty($project['description'])): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                                                <?= htmlspecialchars(substr($project['description'], 0, 50)) ?><?= strlen($project['description']) > 50 ? '...' : '' ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white">
                                            <?= htmlspecialchars($project['client_name']) ?>
                                        </div>
                                        <?php if (!empty($project['client_company'])): ?>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <?= htmlspecialchars($project['client_company']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            <?php if ($project['status'] === 'completed'): ?>
                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                            <?php elseif ($project['status'] === 'in_progress'): ?>
                                                bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                                            <?php elseif ($project['status'] === 'on_hold'): ?>
                                                bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                            <?php elseif ($project['status'] === 'cancelled'): ?>
                                                bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200
                                            <?php else: ?>
                                                bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300
                                            <?php endif; ?>">
                                            <?= ucfirst(str_replace('_', ' ', htmlspecialchars($project['status']))) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?php if (!empty($project['deadline'])): ?>
                                            <?= date('M d, Y', strtotime($project['deadline'])) ?>
                                            <?php 
                                                $deadline = new DateTime($project['deadline']);
                                                $today = new DateTime();
                                                $diff = $today->diff($deadline);
                                                $daysRemaining = $deadline > $today ? $diff->days : -$diff->days;
                                                
                                                if ($project['status'] !== 'completed' && $project['status'] !== 'cancelled'):
                                                    if ($daysRemaining < 0): 
                                            ?>
                                                <span class="text-xs text-red-600 dark:text-red-400 ml-1">
                                                    <?= abs($daysRemaining) ?> days overdue
                                                </span>
                                            <?php elseif ($daysRemaining <= 7): ?>
                                                <span class="text-xs text-yellow-600 dark:text-yellow-400 ml-1">
                                                    <?= $daysRemaining ?> days left
                                                </span>
                                            <?php endif; endif; ?>
                                        <?php else: ?>
                                            No deadline
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?= !empty($project['value']) ? 'Rs ' . number_format($project['value'], 0) : 'N/A' ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="/momentum/freelance/projects/view/<?= $project['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3">View</a>
                                        <a href="/momentum/freelance/projects/edit/<?= $project['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-3">Edit</a>
                                        <a href="/momentum/freelance/invoices/create?project_id=<?= $project['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">Invoice</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
