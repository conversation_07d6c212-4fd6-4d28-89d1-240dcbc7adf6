<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/freelance/invoices/view/<?= isset($invoice['id']) ? $invoice['id'] : '0' ?>" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Invoice</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Edit Invoice: <?= isset($invoice['invoice_number']) ? htmlspecialchars($invoice['invoice_number']) : 'Invoice' ?>
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Invoice Information</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    Update the invoice details
                </p>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <form action="/momentum/freelance/invoices/update/<?= isset($invoice['id']) ? $invoice['id'] : '0' ?>" method="POST" id="invoiceForm">
                    <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                        <div class="sm:col-span-2">
                            <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Client *</label>
                            <div class="mt-1">
                                <select name="client_id" id="client_id" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required>
                                    <option value="">Select Client</option>
                                    <?php if (isset($clients) && is_array($clients)): ?>
                                        <?php foreach ($clients as $client): ?>
                                            <option value="<?= $client['id'] ?>" <?= (isset($invoice['client_id']) && $invoice['client_id'] == $client['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($client['name']) ?> <?= !empty($client['company']) ? '(' . htmlspecialchars($client['company']) . ')' : '' ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="project_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Project</label>
                            <div class="mt-1">
                                <select name="project_id" id="project_id" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="">Select Project (Optional)</option>
                                    <?php if (isset($projects) && is_array($projects)): ?>
                                        <?php foreach ($projects as $project): ?>
                                            <option value="<?= $project['id'] ?>" data-client="<?= $project['client_id'] ?>" <?= (isset($invoice['project_id']) && $invoice['project_id'] == $project['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($project['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="invoice_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Invoice Number *</label>
                            <div class="mt-1">
                                <input type="text" name="invoice_number" id="invoice_number" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required value="<?= isset($invoice['invoice_number']) ? htmlspecialchars($invoice['invoice_number']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="invoice_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Invoice Date *</label>
                            <div class="mt-1">
                                <input type="date" name="invoice_date" id="invoice_date" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required value="<?= isset($invoice['invoice_date']) ? htmlspecialchars($invoice['invoice_date']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="due_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Due Date *</label>
                            <div class="mt-1">
                                <input type="date" name="due_date" id="due_date" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" required value="<?= isset($invoice['due_date']) ? htmlspecialchars($invoice['due_date']) : '' ?>">
                            </div>
                        </div>

                        <div class="sm:col-span-2">
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                            <div class="mt-1">
                                <select name="status" id="status" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                    <option value="draft" <?= (isset($invoice['status']) && $invoice['status'] === 'draft') ? 'selected' : '' ?>>Draft</option>
                                    <option value="sent" <?= (isset($invoice['status']) && $invoice['status'] === 'sent') ? 'selected' : '' ?>>Sent</option>
                                    <option value="paid" <?= (isset($invoice['status']) && $invoice['status'] === 'paid') ? 'selected' : '' ?>>Paid</option>
                                    <option value="overdue" <?= (isset($invoice['status']) && $invoice['status'] === 'overdue') ? 'selected' : '' ?>>Overdue</option>
                                    <option value="cancelled" <?= (isset($invoice['status']) && $invoice['status'] === 'cancelled') ? 'selected' : '' ?>>Cancelled</option>
                                </select>
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                            <div class="mt-1">
                                <textarea name="notes" id="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= isset($invoice['notes']) ? htmlspecialchars($invoice['notes']) : '' ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Invoice Items</h4>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700" id="invoice-items-table">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Quantity</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Unit Price</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total</th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700" id="invoice-items-body">
                                    <?php if (isset($invoice['items']) && !empty($invoice['items'])): ?>
                                        <?php foreach ($invoice['items'] as $index => $item): ?>
                                            <tr class="invoice-item">
                                                <td class="px-6 py-4">
                                                    <input type="hidden" name="items[<?= $index ?>][id]" value="<?= $item['id'] ?>">
                                                    <input type="text" name="items[<?= $index ?>][description]" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Item description" required value="<?= isset($item['description']) ? htmlspecialchars($item['description']) : '' ?>">
                                                </td>
                                                <td class="px-6 py-4">
                                                    <input type="number" name="items[<?= $index ?>][quantity]" class="item-quantity shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Quantity" min="1" step="1" value="<?= isset($item['quantity']) ? htmlspecialchars($item['quantity']) : '1' ?>" required>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <input type="number" name="items[<?= $index ?>][unit_price]" class="item-price shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Price" min="0" step="0.01" value="<?= isset($item['unit_price']) ? htmlspecialchars($item['unit_price']) : '0' ?>" required>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <input type="number" name="items[<?= $index ?>][total]" class="item-total shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Total" value="<?= isset($item['total']) ? htmlspecialchars($item['total']) : '' ?>" readonly>
                                                </td>
                                                <td class="px-6 py-4 text-right">
                                                    <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 remove-item">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr class="invoice-item">
                                            <td class="px-6 py-4">
                                                <input type="text" name="items[0][description]" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Item description" required>
                                            </td>
                                            <td class="px-6 py-4">
                                                <input type="number" name="items[0][quantity]" class="item-quantity shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Quantity" min="1" step="1" value="1" required>
                                            </td>
                                            <td class="px-6 py-4">
                                                <input type="number" name="items[0][unit_price]" class="item-price shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Price" min="0" step="0.01" required>
                                            </td>
                                            <td class="px-6 py-4">
                                                <input type="number" name="items[0][total]" class="item-total shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Total" readonly>
                                            </td>
                                            <td class="px-6 py-4 text-right">
                                                <button type="button" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 remove-item">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="5" class="px-6 py-4">
                                            <button type="button" id="add-item" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                                <i class="fas fa-plus mr-2"></i> Add Item
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="px-6 py-4 text-right font-medium">Subtotal:</td>
                                        <td class="px-6 py-4">
                                            <input type="number" name="subtotal" id="subtotal" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($invoice['subtotal']) ? htmlspecialchars($invoice['subtotal']) : '0.00' ?>" readonly>
                                        </td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="px-6 py-4 text-right font-medium">Tax (%):</td>
                                        <td class="px-6 py-4">
                                            <input type="number" name="tax_rate" id="tax_rate" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" min="0" max="100" step="0.01" value="<?= isset($invoice['tax_rate']) ? htmlspecialchars($invoice['tax_rate']) : '0' ?>">
                                        </td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="px-6 py-4 text-right font-medium">Tax Amount:</td>
                                        <td class="px-6 py-4">
                                            <input type="number" name="tax_amount" id="tax_amount" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($invoice['tax_amount']) ? htmlspecialchars($invoice['tax_amount']) : '0.00' ?>" readonly>
                                        </td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="px-6 py-4 text-right font-medium">Total Amount:</td>
                                        <td class="px-6 py-4">
                                            <input type="number" name="total_amount" id="total_amount" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= isset($invoice['total_amount']) ? htmlspecialchars($invoice['total_amount']) : '0.00' ?>" readonly>
                                        </td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <a href="/momentum/freelance/invoices/view/<?= isset($invoice['id']) ? $invoice['id'] : '0' ?>" class="bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Cancel
                        </a>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Update Invoice
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize item row counter
        let itemCounter = <?= isset($invoice['items']) ? count($invoice['items']) : 1 ?>;

        // Add item button
        document.getElementById('add-item').addEventListener('click', function() {
            const tbody = document.getElementById('invoice-items-body');
            const template = document.querySelector('.invoice-item').cloneNode(true);

            // Update input names with new index
            template.querySelectorAll('input').forEach(input => {
                if (input.name) {
                    input.name = input.name.replace(/items\[\d+\]/, 'items[' + itemCounter + ']');
                    if (input.name.includes('[id]')) {
                        input.remove(); // Remove ID field for new items
                    } else {
                        input.value = input.name.includes('[quantity]') ? '1' : '';
                    }
                }
            });

            // Add event listeners to new row
            addItemEventListeners(template);

            tbody.appendChild(template);
            itemCounter++;
        });

        // Add event listeners to initial rows
        document.querySelectorAll('.invoice-item').forEach(row => {
            addItemEventListeners(row);
        });

        // Function to add event listeners to item row
        function addItemEventListeners(row) {
            // Remove item button
            row.querySelector('.remove-item').addEventListener('click', function() {
                if (document.querySelectorAll('.invoice-item').length > 1) {
                    row.remove();
                    calculateTotals();
                }
            });

            // Calculate item total when quantity or price changes
            row.querySelector('.item-quantity').addEventListener('input', function() {
                calculateItemTotal(row);
            });

            row.querySelector('.item-price').addEventListener('input', function() {
                calculateItemTotal(row);
            });
        }

        // Calculate item total
        function calculateItemTotal(row) {
            const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(row.querySelector('.item-price').value) || 0;
            const total = quantity * price;

            row.querySelector('.item-total').value = total.toFixed(2);
            calculateTotals();
        }

        // Calculate invoice totals
        function calculateTotals() {
            let subtotal = 0;

            document.querySelectorAll('.item-total').forEach(input => {
                subtotal += parseFloat(input.value) || 0;
            });

            const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
            const taxAmount = subtotal * (taxRate / 100);
            const totalAmount = subtotal + taxAmount;

            document.getElementById('subtotal').value = subtotal.toFixed(2);
            document.getElementById('tax_amount').value = taxAmount.toFixed(2);
            document.getElementById('total_amount').value = totalAmount.toFixed(2);
        }

        // Update tax amount when tax rate changes
        document.getElementById('tax_rate').addEventListener('input', calculateTotals);

        // Link project selection to client
        document.getElementById('project_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const clientId = selectedOption.getAttribute('data-client');
                if (clientId) {
                    document.getElementById('client_id').value = clientId;
                }
            }
        });

        // Link client selection to filter projects
        document.getElementById('client_id').addEventListener('change', function() {
            const clientId = this.value;
            const projectSelect = document.getElementById('project_id');

            // Reset project selection
            projectSelect.value = '';

            // Show/hide projects based on client
            Array.from(projectSelect.options).forEach(option => {
                if (option.value === '') return; // Skip the placeholder option

                const projectClientId = option.getAttribute('data-client');
                if (clientId === '' || projectClientId === clientId) {
                    option.style.display = '';
                } else {
                    option.style.display = 'none';
                }
            });
        });

        // Initialize calculations
        calculateTotals();
    });
</script>
