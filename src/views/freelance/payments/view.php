<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/freelance/payments" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Payments</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Payment #<?= isset($payment['payment_number']) ? htmlspecialchars($payment['payment_number']) : 'Details' ?>
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Payment Information</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                        Payment details and status
                    </p>
                </div>
                <div class="flex space-x-2">
                    <a href="/momentum/freelance/payments/edit/<?= isset($payment['id']) ? $payment['id'] : '0' ?>" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-edit mr-2"></i> Edit
                    </a>
                    <a href="/momentum/freelance/payments/delete/<?= isset($payment['id']) ? $payment['id'] : '0' ?>" class="inline-flex items-center px-3 py-2 border border-red-300 dark:border-red-700 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200" onclick="return confirm('Are you sure you want to delete this payment?');">
                        <i class="fas fa-trash-alt mr-2"></i> Delete
                    </a>
                </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <p class="text-gray-500 dark:text-gray-400 italic">Payment view template - This is a placeholder. The full implementation will be added later.</p>
            </div>
        </div>
    </div>
</div>
