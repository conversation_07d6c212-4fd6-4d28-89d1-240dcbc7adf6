<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/income-opportunities/view/<?= $opportunity['id'] ?>" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Opportunity</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Log Activity: <?= htmlspecialchars($opportunity['name']) ?></h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Activity Log Details</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    Track your progress, time invested, and earnings
                </p>
            </div>
            
            <form action="/momentum/income-opportunities/save-activity-log/<?= $opportunity['id'] ?>" method="post" class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="space-y-4">
                        <div>
                            <label for="log_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date <span class="text-red-500">*</span></label>
                            <input type="date" name="log_date" id="log_date" value="<?= isset($data['log_date']) ? htmlspecialchars($data['log_date']) : date('Y-m-d') ?>" required class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 <?= isset($errors['log_date']) ? 'border-red-500' : '' ?>">
                            <?php if (isset($errors['log_date'])): ?>
                                <p class="mt-1 text-sm text-red-500"><?= $errors['log_date'] ?></p>
                            <?php endif; ?>
                        </div>
                        
                        <div>
                            <label for="hours_spent" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Hours Spent</label>
                            <input type="number" name="hours_spent" id="hours_spent" min="0" step="0.25" value="<?= isset($data['hours_spent']) ? htmlspecialchars($data['hours_spent']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How many hours did you spend on this opportunity?</p>
                        </div>
                        
                        <div>
                            <label for="amount_earned" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Amount Earned (Rs)</label>
                            <input type="number" name="amount_earned" id="amount_earned" min="0" step="0.01" value="<?= isset($data['amount_earned']) ? htmlspecialchars($data['amount_earned']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How much did you earn from this activity?</p>
                        </div>
                    </div>
                    
                    <!-- Activity Details -->
                    <div class="space-y-4">
                        <div>
                            <label for="activities" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Activities Performed</label>
                            <textarea name="activities" id="activities" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"><?= isset($data['activities']) ? htmlspecialchars($data['activities']) : '' ?></textarea>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What tasks or activities did you complete?</p>
                        </div>
                        
                        <div>
                            <label for="challenges" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Challenges Faced</label>
                            <textarea name="challenges" id="challenges" rows="2" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"><?= isset($data['challenges']) ? htmlspecialchars($data['challenges']) : '' ?></textarea>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What obstacles or difficulties did you encounter?</p>
                        </div>
                        
                        <div>
                            <label for="wins" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Wins & Achievements</label>
                            <textarea name="wins" id="wins" rows="2" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"><?= isset($data['wins']) ? htmlspecialchars($data['wins']) : '' ?></textarea>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What successes or positive outcomes did you achieve?</p>
                        </div>
                    </div>
                </div>
                
                <!-- Next Steps -->
                <div class="mt-6">
                    <label for="next_steps" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Next Steps</label>
                    <textarea name="next_steps" id="next_steps" rows="3" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"><?= isset($data['next_steps']) ? htmlspecialchars($data['next_steps']) : '' ?></textarea>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">What are your planned next actions for this opportunity?</p>
                </div>
                
                <div class="mt-6 flex justify-end">
                    <a href="/momentum/income-opportunities/view/<?= $opportunity['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-3">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Save Activity Log
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
