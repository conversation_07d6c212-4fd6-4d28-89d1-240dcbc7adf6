<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Income Opportunity Explorer</h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Discover, evaluate, and track online income opportunities</p>
            </div>
            <div class="mt-4 md:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/income-opportunities/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> Add Opportunity
                </a>
                <a href="/momentum/income-evaluator" class="inline-flex items-center justify-center px-4 py-2 border border-purple-500 dark:border-purple-600 rounded-md shadow-sm text-sm font-medium text-purple-700 dark:text-purple-300 bg-purple-50 dark:bg-purple-900 hover:bg-purple-100 dark:hover:bg-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200">
                    <i class="fas fa-balance-scale mr-2"></i> Compare & Evaluate
                </a>
                <a href="/momentum/passive-income" class="inline-flex items-center justify-center px-4 py-2 border border-green-500 dark:border-green-600 rounded-md shadow-sm text-sm font-medium text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900 hover:bg-green-100 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                    <i class="fas fa-chart-pie mr-2"></i> Passive Income Portfolio
                </a>
                <a href="/momentum/help/online-income-strategies-guide" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-book mr-2"></i> Income Guide
                </a>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-md p-3">
                        <i class="fas fa-list text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Opportunities</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $opportunitySummary['total_opportunities'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Opportunities</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $opportunitySummary['active_opportunities'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3">
                        <i class="fas fa-tools text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Implementing</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $opportunitySummary['implementing_opportunities'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3">
                        <i class="fas fa-search text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">Researching</h2>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $opportunitySummary['researching_opportunities'] ?? 0 ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6">
            <form action="/momentum/income-opportunities" method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Search</label>
                        <input type="text" name="search" id="search" value="<?= isset($filters['search']) ? htmlspecialchars($filters['search']) : '' ?>" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                    </div>
                    <div>
                        <label for="income_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Income Type</label>
                        <select name="income_type" id="income_type" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Types</option>
                            <option value="active" <?= isset($filters['income_type']) && $filters['income_type'] === 'active' ? 'selected' : '' ?>>Active Income</option>
                            <option value="passive" <?= isset($filters['income_type']) && $filters['income_type'] === 'passive' ? 'selected' : '' ?>>Passive Income</option>
                            <option value="semi-passive" <?= isset($filters['income_type']) && $filters['income_type'] === 'semi-passive' ? 'selected' : '' ?>>Semi-Passive Income</option>
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Statuses</option>
                            <option value="considering" <?= isset($filters['status']) && $filters['status'] === 'considering' ? 'selected' : '' ?>>Considering</option>
                            <option value="researching" <?= isset($filters['status']) && $filters['status'] === 'researching' ? 'selected' : '' ?>>Researching</option>
                            <option value="implementing" <?= isset($filters['status']) && $filters['status'] === 'implementing' ? 'selected' : '' ?>>Implementing</option>
                            <option value="active" <?= isset($filters['status']) && $filters['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="paused" <?= isset($filters['status']) && $filters['status'] === 'paused' ? 'selected' : '' ?>>Paused</option>
                            <option value="abandoned" <?= isset($filters['status']) && $filters['status'] === 'abandoned' ? 'selected' : '' ?>>Abandoned</option>
                        </select>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="skill_level" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Skill Level</label>
                        <select name="skill_level" id="skill_level" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Skill Levels</option>
                            <option value="beginner" <?= isset($filters['skill_level']) && $filters['skill_level'] === 'beginner' ? 'selected' : '' ?>>Beginner</option>
                            <option value="intermediate" <?= isset($filters['skill_level']) && $filters['skill_level'] === 'intermediate' ? 'selected' : '' ?>>Intermediate</option>
                            <option value="advanced" <?= isset($filters['skill_level']) && $filters['skill_level'] === 'advanced' ? 'selected' : '' ?>>Advanced</option>
                            <option value="mixed" <?= isset($filters['skill_level']) && $filters['skill_level'] === 'mixed' ? 'selected' : '' ?>>Mixed</option>
                        </select>
                    </div>
                    <div>
                        <label for="startup_cost" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Startup Cost</label>
                        <select name="startup_cost" id="startup_cost" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="">All Cost Levels</option>
                            <option value="none" <?= isset($filters['startup_cost']) && $filters['startup_cost'] === 'none' ? 'selected' : '' ?>>None</option>
                            <option value="low" <?= isset($filters['startup_cost']) && $filters['startup_cost'] === 'low' ? 'selected' : '' ?>>Low</option>
                            <option value="medium" <?= isset($filters['startup_cost']) && $filters['startup_cost'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                            <option value="high" <?= isset($filters['startup_cost']) && $filters['startup_cost'] === 'high' ? 'selected' : '' ?>>High</option>
                        </select>
                    </div>
                    <div>
                        <label for="sort" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Sort By</label>
                        <select name="sort" id="sort" class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            <option value="priority" <?= isset($filters['sort']) && $filters['sort'] === 'priority' ? 'selected' : '' ?>>Priority</option>
                            <option value="name" <?= isset($filters['sort']) && $filters['sort'] === 'name' ? 'selected' : '' ?>>Name</option>
                            <option value="income_potential" <?= isset($filters['sort']) && $filters['sort'] === 'income_potential' ? 'selected' : '' ?>>Income Potential</option>
                            <option value="startup_cost" <?= isset($filters['sort']) && $filters['sort'] === 'startup_cost' ? 'selected' : '' ?>>Startup Cost</option>
                            <option value="time_commitment" <?= isset($filters['sort']) && $filters['sort'] === 'time_commitment' ? 'selected' : '' ?>>Time Commitment</option>
                            <option value="recently_updated" <?= isset($filters['sort']) && $filters['sort'] === 'recently_updated' ? 'selected' : '' ?>>Recently Updated</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-filter mr-2"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>

        <!-- Opportunities List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Income Opportunities</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                    <?= count($opportunities) ?> opportunities found
                </p>
            </div>

            <?php if (empty($opportunities)): ?>
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No income opportunities found. Add your first opportunity to get started!</p>
                    <a href="/momentum/income-opportunities/create" class="inline-flex items-center justify-center px-4 py-2 mt-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Add Opportunity
                    </a>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Income Potential</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Startup Cost</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time Commitment</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($opportunities as $opportunity): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                    <?= htmlspecialchars($opportunity['name']) ?>
                                                </div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                    <?= htmlspecialchars($opportunity['category']) ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php if ($opportunity['income_type'] === 'active'): ?>
                                                bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                                            <?php elseif ($opportunity['income_type'] === 'passive'): ?>
                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                            <?php else: ?>
                                                bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200
                                            <?php endif; ?>">
                                            <?= ucfirst(htmlspecialchars($opportunity['income_type'])) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php if ($opportunity['status'] === 'active'): ?>
                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                            <?php elseif ($opportunity['status'] === 'implementing'): ?>
                                                bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                                            <?php elseif ($opportunity['status'] === 'researching'): ?>
                                                bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200
                                            <?php elseif ($opportunity['status'] === 'considering'): ?>
                                                bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                            <?php elseif ($opportunity['status'] === 'paused'): ?>
                                                bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200
                                            <?php else: ?>
                                                bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200
                                            <?php endif; ?>">
                                            <?= ucfirst(htmlspecialchars($opportunity['status'])) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <?php if (!empty($opportunity['estimated_income_min']) || !empty($opportunity['estimated_income_max'])): ?>
                                            <?php if (!empty($opportunity['estimated_income_min']) && !empty($opportunity['estimated_income_max'])): ?>
                                                Rs <?= number_format($opportunity['estimated_income_min'], 0) ?> - <?= number_format($opportunity['estimated_income_max'], 0) ?>
                                            <?php elseif (!empty($opportunity['estimated_income_min'])): ?>
                                                Rs <?= number_format($opportunity['estimated_income_min'], 0) ?>+
                                            <?php else: ?>
                                                Up to Rs <?= number_format($opportunity['estimated_income_max'], 0) ?>
                                            <?php endif; ?>
                                            <span class="text-xs">/<?= htmlspecialchars($opportunity['income_frequency']) ?></span>
                                        <?php else: ?>
                                            Varies
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php if ($opportunity['startup_cost'] === 'none'): ?>
                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                            <?php elseif ($opportunity['startup_cost'] === 'low'): ?>
                                                bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                                            <?php elseif ($opportunity['startup_cost'] === 'medium'): ?>
                                                bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                            <?php else: ?>
                                                bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200
                                            <?php endif; ?>">
                                            <?= ucfirst(htmlspecialchars($opportunity['startup_cost'])) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php if ($opportunity['time_commitment'] === 'minimal'): ?>
                                                bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                                            <?php elseif ($opportunity['time_commitment'] === 'low'): ?>
                                                bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                                            <?php elseif ($opportunity['time_commitment'] === 'medium'): ?>
                                                bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200
                                            <?php else: ?>
                                                bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200
                                            <?php endif; ?>">
                                            <?= ucfirst(htmlspecialchars($opportunity['time_commitment'])) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="/momentum/income-opportunities/view/<?= $opportunity['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3">View</a>
                                        <a href="/momentum/income-opportunities/edit/<?= $opportunity['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-3">Edit</a>
                                        <a href="/momentum/income-opportunities/log-activity/<?= $opportunity['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300">Log</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
