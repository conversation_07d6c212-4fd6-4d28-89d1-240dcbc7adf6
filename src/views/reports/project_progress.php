<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Project Progress Report</h1>
                <p class="text-gray-500 dark:text-gray-400 mt-1"><?= View::escape($project['name']) ?></p>
            </div>
            <div>
                <a href="/momentum/reports" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- Project Overview Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Project Overview</h2>
                <div class="space-y-4">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Status</p>
                        <p class="text-lg font-medium text-gray-900 dark:text-white"><?= ucfirst($project['status']) ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Progress</p>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-1">
                            <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $project['progress'] ?>%"></div>
                        </div>
                        <p class="text-lg font-medium text-gray-900 dark:text-white"><?= $project['progress'] ?>% Complete</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Timeline</p>
                        <p class="text-lg font-medium text-gray-900 dark:text-white">
                            <?= !empty($project['start_date']) ? date('M j, Y', strtotime($project['start_date'])) : 'Not set' ?> -
                            <?= !empty($project['end_date']) ? date('M j, Y', strtotime($project['end_date'])) : 'Not set' ?>
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Total Tasks</p>
                        <p class="text-lg font-medium text-gray-900 dark:text-white"><?= count($tasks) ?></p>
                    </div>
                </div>
            </div>

            <!-- Task Status Distribution Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Task Status Distribution</h2>
                <div class="space-y-4">
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">To Do</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= $taskStatusCounts['todo'] ?> tasks</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-gray-500 h-2.5 rounded-full" style="width: <?= count($tasks) > 0 ? ($taskStatusCounts['todo'] / count($tasks) * 100) : 0 ?>%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">In Progress</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= $taskStatusCounts['in_progress'] ?> tasks</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-blue-500 h-2.5 rounded-full" style="width: <?= count($tasks) > 0 ? ($taskStatusCounts['in_progress'] / count($tasks) * 100) : 0 ?>%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Done</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= $taskStatusCounts['done'] ?> tasks</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-green-500 h-2.5 rounded-full" style="width: <?= count($tasks) > 0 ? ($taskStatusCounts['done'] / count($tasks) * 100) : 0 ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Task Priority Distribution Card -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Task Priority Distribution</h2>
                <div class="space-y-4">
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Low</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= $taskPriorityCounts['low'] ?> tasks</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-gray-500 h-2.5 rounded-full" style="width: <?= count($tasks) > 0 ? ($taskPriorityCounts['low'] / count($tasks) * 100) : 0 ?>%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Medium</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= $taskPriorityCounts['medium'] ?> tasks</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-blue-500 h-2.5 rounded-full" style="width: <?= count($tasks) > 0 ? ($taskPriorityCounts['medium'] / count($tasks) * 100) : 0 ?>%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">High</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= $taskPriorityCounts['high'] ?> tasks</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-orange-500 h-2.5 rounded-full" style="width: <?= count($tasks) > 0 ? ($taskPriorityCounts['high'] / count($tasks) * 100) : 0 ?>%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Urgent</span>
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= $taskPriorityCounts['urgent'] ?> tasks</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-red-500 h-2.5 rounded-full" style="width: <?= count($tasks) > 0 ? ($taskPriorityCounts['urgent'] / count($tasks) * 100) : 0 ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Over Time Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Progress Over Time</h2>
            <div id="progress-chart" style="height: 300px;"></div>
        </div>

        <!-- Task Category Distribution Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Task Category Distribution</h2>
            <?php if (array_sum($taskCategoryCounts) === 0): ?>
                <p class="text-gray-500 dark:text-gray-400">No categories assigned to tasks.</p>
            <?php else: ?>
                <div id="category-chart" style="height: 300px;"></div>
            <?php endif; ?>
        </div>

        <!-- Export Options -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Export Report</h2>
            <div class="flex space-x-4">
                <button id="export-pdf" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-file-pdf mr-2"></i> Export as PDF
                </button>
                <button id="export-csv" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-file-csv mr-2"></i> Export as CSV
                </button>
            </div>
        </div>

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Progress Over Time Chart
        const progressData = <?= json_encode(array_values($progressOverTime)) ?>;
        const progressDates = <?= json_encode(array_keys($progressOverTime)) ?>;

        const progressOptions = {
            series: [{
                name: 'Progress',
                data: progressData
            }],
            chart: {
                type: 'line',
                height: 300,
                toolbar: {
                    show: false
                },
                fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            },
            colors: ['#4f46e5'],
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            grid: {
                borderColor: '#e0e0e0',
                row: {
                    colors: ['#f3f3f3', 'transparent'],
                    opacity: 0.5
                }
            },
            xaxis: {
                categories: progressDates,
                labels: {
                    formatter: function(value) {
                        return new Date(value).toLocaleDateString();
                    }
                }
            },
            yaxis: {
                title: {
                    text: 'Progress (%)'
                },
                min: 0,
                max: 100
            },
            tooltip: {
                y: {
                    formatter: function(value) {
                        return value + '%';
                    }
                }
            }
        };

        const progressChart = new ApexCharts(document.querySelector("#progress-chart"), progressOptions);
        progressChart.render();

        // Category Distribution Chart
        <?php if (array_sum($taskCategoryCounts) > 0): ?>
        const categoryData = <?= json_encode(array_values($taskCategoryCounts)) ?>;
        const categoryLabels = <?= json_encode(array_keys($taskCategoryCounts)) ?>;

        const categoryOptions = {
            series: categoryData,
            chart: {
                type: 'pie',
                height: 300,
                fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            },
            labels: categoryLabels,
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }],
            colors: ['#4f46e5', '#10b981', '#f59e0b', '#ef4444', '#6366f1', '#8b5cf6', '#ec4899', '#14b8a6', '#f43f5e', '#84cc16']
        };

        const categoryChart = new ApexCharts(document.querySelector("#category-chart"), categoryOptions);
        categoryChart.render();
        <?php endif; ?>

        // Export buttons
        document.getElementById('export-pdf').addEventListener('click', function() {
            window.location.href = '/momentum/reports/export/project-progress/<?= $project['id'] ?>/pdf';
        });

        document.getElementById('export-csv').addEventListener('click', function() {
            window.location.href = '/momentum/reports/export/project-progress/<?= $project['id'] ?>/csv';
        });
    });
</script>

    </div>
</div>
