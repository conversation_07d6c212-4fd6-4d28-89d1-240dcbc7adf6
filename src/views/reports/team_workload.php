<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Team Workload Report</h1>
                <p class="text-gray-500 dark:text-gray-400 mt-1"><?= View::escape($project['name']) ?></p>
            </div>
            <div>
                <a href="/momentum/reports" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Reports
                </a>
            </div>
        </div>

        <!-- Team Overview -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Team Overview</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Team Member</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Role</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Tasks</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Completed</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Completion Rate</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($projectMembers as $member): ?>
                            <?php
                                $memberId = $member['user_id'];
                                $memberStats = $memberTaskCounts[$memberId] ?? ['name' => $member['name'], 'total' => 0, 'completed' => 0];
                                $completionRate = $memberStats['total'] > 0 ? round(($memberStats['completed'] / $memberStats['total']) * 100) : 0;
                            ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                            <span class="text-gray-500 dark:text-gray-400 font-medium">
                                                <?= strtoupper(substr($member['name'], 0, 1)) ?>
                                            </span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?= View::escape($member['name']) ?>
                                            </div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <?= View::escape($member['email']) ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        <?= $member['role'] === 'owner' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                                           ($member['role'] === 'admin' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                           'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200') ?>">
                                        <?= ucfirst($member['role']) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <?= $memberStats['total'] ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <?= $memberStats['completed'] ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2 w-32">
                                            <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $completionRate ?>%"></div>
                                        </div>
                                        <span class="text-sm text-gray-500 dark:text-gray-400"><?= $completionRate ?>%</span>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Task Distribution Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Task Distribution</h2>
            <div id="task-distribution-chart" style="height: 300px;"></div>
        </div>

        <!-- Task Status by Member Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Task Status by Member</h2>
            <div id="task-status-chart" style="height: 300px;"></div>
        </div>

        <!-- Export Options -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Export Report</h2>
            <div class="flex space-x-4">
                <button id="export-pdf" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-file-pdf mr-2"></i> Export as PDF
                </button>
                <button id="export-csv" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-file-csv mr-2"></i> Export as CSV
                </button>
            </div>
        </div>

<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Task Distribution Chart
        const memberNames = [];
        const totalTasks = [];
        const completedTasks = [];

        <?php foreach ($memberTaskCounts as $memberId => $stats): ?>
            memberNames.push('<?= addslashes($stats['name']) ?>');
            totalTasks.push(<?= $stats['total'] ?>);
            completedTasks.push(<?= $stats['completed'] ?>);
        <?php endforeach; ?>

        const taskDistributionOptions = {
            series: [{
                name: 'Total Tasks',
                data: totalTasks
            }, {
                name: 'Completed Tasks',
                data: completedTasks
            }],
            chart: {
                type: 'bar',
                height: 300,
                stacked: false,
                toolbar: {
                    show: false
                },
                fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: memberNames,
            },
            yaxis: {
                title: {
                    text: 'Number of Tasks'
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + " tasks"
                    }
                }
            },
            colors: ['#4f46e5', '#10b981']
        };

        const taskDistributionChart = new ApexCharts(document.querySelector("#task-distribution-chart"), taskDistributionOptions);
        taskDistributionChart.render();

        // Task Status by Member Chart
        const memberStatusNames = [];
        const todoTasks = [];
        const inProgressTasks = [];
        const doneTasks = [];

        <?php foreach ($memberTaskStatusCounts as $memberId => $stats): ?>
            memberStatusNames.push('<?= addslashes($stats['name']) ?>');
            todoTasks.push(<?= $stats['todo'] ?>);
            inProgressTasks.push(<?= $stats['in_progress'] ?>);
            doneTasks.push(<?= $stats['done'] ?>);
        <?php endforeach; ?>

        const taskStatusOptions = {
            series: [{
                name: 'To Do',
                data: todoTasks
            }, {
                name: 'In Progress',
                data: inProgressTasks
            }, {
                name: 'Done',
                data: doneTasks
            }],
            chart: {
                type: 'bar',
                height: 300,
                stacked: true,
                toolbar: {
                    show: false
                },
                fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                },
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: memberStatusNames,
            },
            yaxis: {
                title: {
                    text: 'Number of Tasks'
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + " tasks"
                    }
                }
            },
            colors: ['#9ca3af', '#3b82f6', '#10b981']
        };

        const taskStatusChart = new ApexCharts(document.querySelector("#task-status-chart"), taskStatusOptions);
        taskStatusChart.render();

        // Export buttons
        document.getElementById('export-pdf').addEventListener('click', function() {
            window.location.href = '/momentum/reports/export/team-workload/<?= $project['id'] ?>/pdf';
        });

        document.getElementById('export-csv').addEventListener('click', function() {
            window.location.href = '/momentum/reports/export/team-workload/<?= $project['id'] ?>/csv';
        });
    });
</script>

    </div>
</div>
