<?php if (empty($checklists)): ?>
    <div class="text-center py-8">
        <div class="text-gray-500 dark:text-gray-400 mb-4">
            <i class="fas fa-check-square text-4xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No checklists yet</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-4">
            Create a checklist to track your project progress.
        </p>
        <a href="/momentum/projects/<?= $project['id'] ?>/checklists/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-plus mr-2"></i> Create Checklist
        </a>
    </div>
<?php else: ?>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <?php foreach ($checklists as $checklist): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                        <?= htmlspecialchars($checklist['name']) ?>
                    </h3>
                    <span class="badge <?= getStatusBadgeClass($checklist['status']) ?>">
                        <?= ucfirst($checklist['status']) ?>
                    </span>
                </div>
                <div class="px-4 py-3">
                    <?php if (!empty($checklist['description'])): ?>
                        <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">
                            <?= htmlspecialchars($checklist['description']) ?>
                        </p>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                            <span>Progress: <?= $checklist['stats']['completion_percentage'] ?>%</span>
                            <span><?= $checklist['stats']['completed'] ?> / <?= $checklist['stats']['total'] ?> completed</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="bg-green-600 h-2.5 rounded-full" style="width: <?= $checklist['stats']['completion_percentage'] ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            Created: <?= date('M j, Y', strtotime($checklist['created_at'])) ?>
                        </div>
                        <a href="/momentum/checklists/view/<?= $checklist['id'] ?>" class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-900 hover:bg-primary-200 dark:hover:bg-primary-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-eye mr-1"></i> View
                        </a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<?php
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'active':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
        case 'completed':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
        case 'archived':
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
}
?>
