<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1><?= htmlspecialchars($checklist['name']) ?></h1>
            <?php if (!empty($checklist['description'])): ?>
                <p class="lead"><?= htmlspecialchars($checklist['description']) ?></p>
            <?php endif; ?>
            
            <?php if ($project): ?>
                <p>
                    <span class="badge bg-info">Project: 
                        <a href="/momentum/projects/view/<?= $project['id'] ?>" class="text-white">
                            <?= htmlspecialchars($project['name']) ?>
                        </a>
                    </span>
                </p>
            <?php endif; ?>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="/momentum/checklists" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Checklists
                </a>
                <a href="/momentum/checklists/edit/<?= $checklist['id'] ?>" class="btn btn-outline-primary">
                    <i class="fas fa-edit"></i> Edit
                </a>
                <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#addItemModal">
                    <i class="fas fa-plus"></i> Add Item
                </button>
                <a href="/momentum/checklists/create-template-from-checklist/<?= $checklist['id'] ?>" class="btn btn-outline-info">
                    <i class="fas fa-save"></i> Save as Template
                </a>
            </div>
        </div>
    </div>
    
    <!-- Progress Bar -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="card-title">Progress</h5>
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $stats['completion_percentage'] ?>%;" 
                             aria-valuenow="<?= $stats['completion_percentage'] ?>" aria-valuemin="0" aria-valuemax="100">
                            <?= $stats['completion_percentage'] ?>%
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="row text-center mt-3 mt-md-0">
                        <div class="col">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <h6 class="mb-0">Total</h6>
                                    <h4 class="mb-0"><?= $stats['total'] ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card bg-success text-white">
                                <div class="card-body py-2">
                                    <h6 class="mb-0">Completed</h6>
                                    <h4 class="mb-0"><?= $stats['completed'] ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card bg-warning">
                                <div class="card-body py-2">
                                    <h6 class="mb-0">In Progress</h6>
                                    <h4 class="mb-0"><?= $stats['in_progress'] ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card bg-secondary text-white">
                                <div class="card-body py-2">
                                    <h6 class="mb-0">Pending</h6>
                                    <h4 class="mb-0"><?= $stats['pending'] ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Checklist Items -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Checklist Items</h5>
        </div>
        <div class="card-body p-0">
            <div class="checklist-container">
                <?php if (empty($checklist['items'])): ?>
                    <div class="text-center p-4">
                        <p class="mb-0">No items in this checklist yet.</p>
                        <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus"></i> Add First Item
                        </button>
                    </div>
                <?php else: ?>
                    <ul class="list-group list-group-flush checklist-items">
                        <?php foreach ($checklist['items'] as $item): ?>
                            <?php echo renderChecklistItem($item); ?>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Item Modal -->
<div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="/momentum/checklists/add-item/<?= $checklist['id'] ?>" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title" id="addItemModalLabel">Add Checklist Item</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="text" class="form-label">Item Text</label>
                        <input type="text" class="form-control" id="text" name="text" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="parent_id" class="form-label">Parent Item (Optional)</label>
                        <select class="form-select" id="parent_id" name="parent_id">
                            <option value="">None (Top Level Item)</option>
                            <?php 
                            function renderParentOptions($items, $level = 0) {
                                $output = '';
                                foreach ($items as $item) {
                                    $indent = str_repeat('— ', $level);
                                    $output .= '<option value="' . $item['id'] . '">' . $indent . htmlspecialchars($item['text']) . '</option>';
                                    if (!empty($item['sub_items'])) {
                                        $output .= renderParentOptions($item['sub_items'], $level + 1);
                                    }
                                }
                                return $output;
                            }
                            echo renderParentOptions($checklist['items']);
                            ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="resource_link" class="form-label">Resource Link (Optional)</label>
                        <input type="text" class="form-control" id="resource_link" name="resource_link" placeholder="URL or resource identifier">
                        <small class="form-text text-muted">Link to documentation, agent, or other resource</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Item Details Modal -->
<div class="modal fade" id="itemDetailsModal" tabindex="-1" aria-labelledby="itemDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="itemDetailsModalLabel">Item Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h5 id="item-details-text"></h5>
                <p id="item-details-description" class="text-muted"></p>
                
                <div id="item-details-resource" class="mb-3 d-none">
                    <h6>Resource:</h6>
                    <a href="#" id="item-details-resource-link" target="_blank"></a>
                </div>
                
                <div class="mb-3">
                    <h6>Status:</h6>
                    <div class="btn-group w-100" role="group" aria-label="Item status">
                        <button type="button" class="btn btn-outline-secondary status-btn" data-status="pending">Pending</button>
                        <button type="button" class="btn btn-outline-warning status-btn" data-status="in_progress">In Progress</button>
                        <button type="button" class="btn btn-outline-success status-btn" data-status="completed">Completed</button>
                    </div>
                    <input type="hidden" id="item-details-id">
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="delete-item-link" class="btn btn-danger me-auto">Delete Item</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle item status changes
        document.querySelectorAll('.status-btn').forEach(button => {
            button.addEventListener('click', function() {
                const itemId = document.getElementById('item-details-id').value;
                const status = this.dataset.status;
                
                updateItemStatus(itemId, status);
            });
        });
        
        // Handle checkbox clicks
        document.querySelectorAll('.checklist-item-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const itemId = this.dataset.itemId;
                const status = this.checked ? 'completed' : 'pending';
                
                updateItemStatus(itemId, status);
            });
        });
        
        // Handle item clicks to show details
        document.querySelectorAll('.checklist-item-text').forEach(item => {
            item.addEventListener('click', function() {
                const itemId = this.dataset.itemId;
                const text = this.dataset.text;
                const description = this.dataset.description;
                const resourceLink = this.dataset.resourceLink;
                const status = this.dataset.status;
                
                document.getElementById('item-details-id').value = itemId;
                document.getElementById('item-details-text').textContent = text;
                document.getElementById('item-details-description').textContent = description || 'No description provided.';
                
                // Set resource link if available
                const resourceContainer = document.getElementById('item-details-resource');
                const resourceLinkElement = document.getElementById('item-details-resource-link');
                
                if (resourceLink) {
                    resourceContainer.classList.remove('d-none');
                    resourceLinkElement.href = resourceLink;
                    resourceLinkElement.textContent = resourceLink;
                } else {
                    resourceContainer.classList.add('d-none');
                }
                
                // Set delete link
                document.getElementById('delete-item-link').href = '/momentum/checklists/delete-item/' + itemId;
                
                // Set active status button
                document.querySelectorAll('.status-btn').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.status === status) {
                        btn.classList.add('active');
                    }
                });
                
                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('itemDetailsModal'));
                modal.show();
            });
        });
        
        // Function to update item status
        function updateItemStatus(itemId, status) {
            fetch('/momentum/checklists/update-item-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    item_id: itemId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the UI
                    const checkbox = document.querySelector(`.checklist-item-checkbox[data-item-id="${itemId}"]`);
                    const textElement = document.querySelector(`.checklist-item-text[data-item-id="${itemId}"]`);
                    const listItem = checkbox.closest('li');
                    
                    // Update checkbox
                    checkbox.checked = status === 'completed';
                    
                    // Update data attribute
                    textElement.dataset.status = status;
                    
                    // Update item appearance
                    listItem.classList.remove('list-group-item-success', 'list-group-item-warning');
                    if (status === 'completed') {
                        listItem.classList.add('list-group-item-success');
                        textElement.classList.add('text-decoration-line-through');
                    } else if (status === 'in_progress') {
                        listItem.classList.add('list-group-item-warning');
                        textElement.classList.remove('text-decoration-line-through');
                    } else {
                        textElement.classList.remove('text-decoration-line-through');
                    }
                    
                    // Update status buttons in modal
                    document.querySelectorAll('.status-btn').forEach(btn => {
                        btn.classList.remove('active');
                        if (btn.dataset.status === status) {
                            btn.classList.add('active');
                        }
                    });
                    
                    // Update progress stats
                    updateProgressStats(data.stats);
                    
                    // Show success message
                    const toast = new bootstrap.Toast(document.getElementById('statusToast'));
                    document.getElementById('statusToastBody').textContent = 'Item status updated successfully';
                    toast.show();
                } else {
                    console.error('Failed to update item status:', data.message);
                }
            })
            .catch(error => {
                console.error('Error updating item status:', error);
            });
        }
        
        // Function to update progress stats
        function updateProgressStats(stats) {
            // Update progress bar
            const progressBar = document.querySelector('.progress-bar');
            progressBar.style.width = stats.completion_percentage + '%';
            progressBar.setAttribute('aria-valuenow', stats.completion_percentage);
            progressBar.textContent = stats.completion_percentage + '%';
            
            // Update stat counters
            document.querySelector('h4:nth-of-type(1)').textContent = stats.total;
            document.querySelector('h4:nth-of-type(2)').textContent = stats.completed;
            document.querySelector('h4:nth-of-type(3)').textContent = stats.in_progress;
            document.querySelector('h4:nth-of-type(4)').textContent = stats.pending;
        }
    });
</script>

<!-- Toast for status updates -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="statusToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Checklist</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="statusToastBody">
            Item status updated successfully
        </div>
    </div>
</div>

<?php
// Function to render a checklist item and its sub-items
function renderChecklistItem($item) {
    $output = '<li class="list-group-item';
    
    // Add status-based styling
    if ($item['status'] === 'completed') {
        $output .= ' list-group-item-success';
    } elseif ($item['status'] === 'in_progress') {
        $output .= ' list-group-item-warning';
    }
    
    $output .= '">';
    $output .= '<div class="d-flex align-items-center">';
    
    // Checkbox
    $output .= '<div class="form-check me-3">';
    $output .= '<input class="form-check-input checklist-item-checkbox" type="checkbox" data-item-id="' . $item['id'] . '"';
    if ($item['status'] === 'completed') {
        $output .= ' checked';
    }
    $output .= '>';
    $output .= '</div>';
    
    // Item text
    $output .= '<div class="flex-grow-1">';
    $output .= '<span class="checklist-item-text';
    
    // Add strikethrough for completed items
    if ($item['status'] === 'completed') {
        $output .= ' text-decoration-line-through';
    }
    
    $output .= '" data-item-id="' . $item['id'] . '" data-text="' . htmlspecialchars($item['text']) . '" data-description="' . htmlspecialchars($item['description']) . '" data-resource-link="' . htmlspecialchars($item['resource_link']) . '" data-status="' . $item['status'] . '">';
    $output .= htmlspecialchars($item['text']);
    $output .= '</span>';
    
    // Show resource link indicator if available
    if (!empty($item['resource_link'])) {
        $output .= ' <i class="fas fa-link text-info" title="Has resource link"></i>';
    }
    
    $output .= '</div>';
    
    // Status badge
    $output .= '<div class="ms-auto">';
    if ($item['status'] === 'in_progress') {
        $output .= '<span class="badge bg-warning">In Progress</span>';
    } elseif ($item['status'] === 'completed') {
        $output .= '<span class="badge bg-success">Completed</span>';
    } else {
        $output .= '<span class="badge bg-secondary">Pending</span>';
    }
    $output .= '</div>';
    
    $output .= '</div>';
    
    // Render sub-items if any
    if (!empty($item['sub_items'])) {
        $output .= '<ul class="list-group list-group-flush mt-2 ms-4 border-start">';
        foreach ($item['sub_items'] as $subItem) {
            $output .= renderChecklistItem($subItem);
        }
        $output .= '</ul>';
    }
    
    $output .= '</li>';
    
    return $output;
}
?>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
