<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1><?= htmlspecialchars($template['name']) ?></h1>
            <?php if (!empty($template['description'])): ?>
                <p class="lead"><?= htmlspecialchars($template['description']) ?></p>
            <?php endif; ?>
            
            <div>
                <span class="badge bg-info"><?= ucfirst($template['category']) ?></span>
                <?php if ($template['is_system_template']): ?>
                    <span class="badge bg-secondary">System Template</span>
                <?php else: ?>
                    <span class="badge bg-primary">Your Template</span>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="/momentum/checklists/templates" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Templates
                </a>
                <a href="/momentum/checklists/create?template_id=<?= $template['id'] ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Use Template
                </a>
                <?php if (!$template['is_system_template']): ?>
                    <a href="/momentum/checklists/delete-template/<?= $template['id'] ?>" class="btn btn-outline-danger" 
                       onclick="return confirm('Are you sure you want to delete this template?')">
                        <i class="fas fa-trash"></i> Delete
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Template Items -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Template Items</h5>
        </div>
        <div class="card-body p-0">
            <div class="template-container">
                <?php if (empty($template['items'])): ?>
                    <div class="text-center p-4">
                        <p class="mb-0">No items in this template.</p>
                    </div>
                <?php else: ?>
                    <ul class="list-group list-group-flush template-items">
                        <?php foreach ($template['items'] as $item): ?>
                            <?php echo renderTemplateItem($item); ?>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Template Usage Instructions -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">How to Use This Template</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body text-center">
                            <div class="display-4 mb-2">1</div>
                            <h5>Create a New Checklist</h5>
                            <p>Click the "Use Template" button above to create a new checklist based on this template.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body text-center">
                            <div class="display-4 mb-2">2</div>
                            <h5>Customize as Needed</h5>
                            <p>Add, edit, or remove items to tailor the checklist to your specific needs.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-body text-center">
                            <div class="display-4 mb-2">3</div>
                            <h5>Track Your Progress</h5>
                            <p>Mark items as in progress or completed as you work through the checklist.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Function to render a template item and its sub-items
function renderTemplateItem($item) {
    $output = '<li class="list-group-item">';
    $output .= '<div class="d-flex align-items-center">';
    
    // Item text
    $output .= '<div class="flex-grow-1">';
    $output .= '<span class="template-item-text">';
    $output .= htmlspecialchars($item['text']);
    $output .= '</span>';
    
    // Show resource link indicator if available
    if (!empty($item['resource_link'])) {
        $output .= ' <i class="fas fa-link text-info" title="Has resource link"></i>';
    }
    
    // Show description if available
    if (!empty($item['description'])) {
        $output .= '<p class="text-muted small mb-0">' . htmlspecialchars($item['description']) . '</p>';
    }
    
    $output .= '</div>';
    
    $output .= '</div>';
    
    // Render sub-items if any
    if (!empty($item['sub_items'])) {
        $output .= '<ul class="list-group list-group-flush mt-2 ms-4 border-start">';
        foreach ($item['sub_items'] as $subItem) {
            $output .= renderTemplateItem($subItem);
        }
        $output .= '</ul>';
    }
    
    $output .= '</li>';
    
    return $output;
}
?>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
