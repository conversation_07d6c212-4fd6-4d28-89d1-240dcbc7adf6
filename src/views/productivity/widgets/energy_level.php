<div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-8" data-widget="energy-level">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Energy Level</h3>
            <a href="/momentum/productivity/energy-tracking" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                View details
            </a>
        </div>
        
        <?php if ($latestEnergyLevel): ?>
            <div class="flex items-center mb-4">
                <div class="w-16 h-16 rounded-full flex items-center justify-center text-xl font-bold mr-4
                    <?php
                    if ($latestEnergyLevel['level'] >= 8) {
                        echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                    } elseif ($latestEnergyLevel['level'] >= 5) {
                        echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                    } else {
                        echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                    }
                    ?>
                ">
                    <?= $latestEnergyLevel['level'] ?>/10
                </div>
                <div>
                    <p class="font-medium text-gray-900 dark:text-white">
                        <?php
                        if ($latestEnergyLevel['level'] >= 8) {
                            echo 'High Energy';
                        } elseif ($latestEnergyLevel['level'] >= 5) {
                            echo 'Medium Energy';
                        } else {
                            echo 'Low Energy';
                        }
                        ?>
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Recorded: <?= date('g:i A', strtotime($latestEnergyLevel['recorded_at'])) ?>
                    </p>
                </div>
            </div>
            
            <div class="space-y-3">
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Today's Energy</h4>
                    <?php if (!empty($todayEnergyLevels)): ?>
                        <div class="flex space-x-1">
                            <?php 
                            foreach ($todayEnergyLevels as $energyLevel): 
                                $color = '';
                                if ($energyLevel['level'] >= 8) {
                                    $color = 'bg-green-500';
                                } elseif ($energyLevel['level'] >= 5) {
                                    $color = 'bg-yellow-500';
                                } else {
                                    $color = 'bg-red-500';
                                }
                            ?>
                                <div class="w-6 h-10 rounded-sm <?= $color ?>" style="opacity: <?= $energyLevel['level'] / 10 ?>" title="<?= date('g:i A', strtotime($energyLevel['recorded_at'])) ?>: <?= $energyLevel['level'] ?>/10"></div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-sm text-gray-500 dark:text-gray-400">No other records today</p>
                    <?php endif; ?>
                </div>
                
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Optimal Task Scheduling</h4>
                    <?php if (!empty($recommendations)): ?>
                        <div class="grid grid-cols-2 gap-2">
                            <?php
                            $currentHour = (int)date('G');
                            $nextHours = [];
                            
                            // Get the next 4 hours
                            for ($i = 0; $i < 4; $i++) {
                                $hour = ($currentHour + $i) % 24;
                                $nextHours[$hour] = $recommendations[$hour] ?? 'unknown';
                            }
                            
                            foreach ($nextHours as $hour => $recommendation):
                                $bgColor = '';
                                $textColor = '';
                                $icon = '';
                                $label = '';
                                
                                switch ($recommendation) {
                                    case 'high':
                                        $bgColor = 'bg-green-100 dark:bg-green-900';
                                        $textColor = 'text-green-800 dark:text-green-200';
                                        $icon = 'fa-bolt';
                                        $label = 'High';
                                        break;
                                    case 'medium':
                                        $bgColor = 'bg-yellow-100 dark:bg-yellow-900';
                                        $textColor = 'text-yellow-800 dark:text-yellow-200';
                                        $icon = 'fa-check';
                                        $label = 'Medium';
                                        break;
                                    case 'low':
                                        $bgColor = 'bg-red-100 dark:bg-red-900';
                                        $textColor = 'text-red-800 dark:text-red-200';
                                        $icon = 'fa-battery-quarter';
                                        $label = 'Low';
                                        break;
                                    default:
                                        $bgColor = 'bg-gray-100 dark:bg-gray-700';
                                        $textColor = 'text-gray-800 dark:text-gray-200';
                                        $icon = 'fa-question';
                                        $label = 'Unknown';
                                }
                            ?>
                                <div class="flex items-center p-2 rounded-md <?= $bgColor ?> <?= $textColor ?>">
                                    <i class="fas <?= $icon ?> mr-2"></i>
                                    <div>
                                        <div class="text-xs font-medium"><?= date('g A', strtotime("$hour:00")) ?></div>
                                        <div class="text-xs"><?= $label ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Not enough data for recommendations</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="mt-4 flex justify-between">
                <button id="recordEnergyBtn" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-plus mr-1"></i> Record Now
                </button>
                <a href="/momentum/productivity/time-blocking" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-calendar-alt mr-1"></i> Schedule
                </a>
            </div>
        <?php else: ?>
            <div class="text-center py-4">
                <p class="text-gray-500 dark:text-gray-400">No energy levels recorded yet</p>
                <a href="/momentum/productivity/energy-tracking" class="mt-2 inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                    <i class="fas fa-plus mr-1"></i> Start tracking
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Energy Level Modal -->
<div id="energyLevelModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Record Energy Level</h3>
            <form id="energyLevelForm" action="/momentum/productivity/record-energy-level" method="POST">
                <div class="mb-4">
                    <label for="level" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Energy Level (1-10)</label>
                    <div class="flex items-center space-x-2">
                        <input type="range" id="level" name="level" min="1" max="10" value="5" class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        <span id="levelValue" class="text-lg font-medium text-gray-900 dark:text-white">5</span>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes</label>
                    <textarea id="notes" name="notes" rows="2" class="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" id="cancelBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const modal = document.getElementById('energyLevelModal');
    const form = document.getElementById('energyLevelForm');
    const recordBtn = document.getElementById('recordEnergyBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const levelInput = document.getElementById('level');
    const levelValue = document.getElementById('levelValue');
    
    // Show level value
    if (levelInput && levelValue) {
        levelInput.addEventListener('input', function() {
            levelValue.textContent = this.value;
        });
    }
    
    // Open modal for recording new energy level
    if (recordBtn && modal) {
        recordBtn.addEventListener('click', function() {
            // Reset form
            form.reset();
            
            // Show modal
            modal.classList.remove('hidden');
        });
    }
    
    // Close modal
    if (cancelBtn && modal) {
        cancelBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });
    }
});
</script>
