<div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-8" data-widget="task-batch">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Task Batches</h3>
            <a href="/momentum/productivity/task-batching" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                View all
            </a>
        </div>
        
        <?php if (!empty($recommendedBatches['batches'])): ?>
            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Recommended for Your Current Energy
                    <?php if ($latestEnergyLevel): ?>
                        <span class="ml-1 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                            <?php
                            if ($latestEnergyLevel['level'] >= 8) {
                                echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                            } elseif ($latestEnergyLevel['level'] >= 5) {
                                echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                            } else {
                                echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                            }
                            ?>
                        ">
                            <?= $latestEnergyLevel['level'] ?>/10
                        </span>
                    <?php endif; ?>
                </h4>
                
                <div class="space-y-2">
                    <?php 
                    // Show up to 3 recommended batches
                    $displayBatches = array_slice($recommendedBatches['batches'], 0, 3);
                    foreach ($displayBatches as $batch): 
                    ?>
                        <a href="/momentum/productivity/view-batch/<?= $batch['id'] ?>" class="block p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-2 h-8 rounded-sm mr-3
                                        <?php
                                        if ($batch['energy_level'] === 'high') {
                                            echo 'bg-green-500';
                                        } elseif ($batch['energy_level'] === 'medium') {
                                            echo 'bg-yellow-500';
                                        } else {
                                            echo 'bg-red-500';
                                        }
                                        ?>
                                    "></div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($batch['name']) ?></p>
                                        <div class="flex items-center mt-1">
                                            <div class="w-16 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                                <?php 
                                                $progress = $batch['task_count'] > 0 ? ($batch['completed_count'] / $batch['task_count']) * 100 : 0;
                                                ?>
                                                <div class="h-full bg-primary-500" style="width: <?= $progress ?>%"></div>
                                            </div>
                                            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                                                <?= $batch['completed_count'] ?>/<?= $batch['task_count'] ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-gray-400 dark:text-gray-500">
                                    <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-lightbulb text-yellow-500"></i>
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Task Batching Tip</h4>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Group similar tasks together based on your energy levels to reduce context switching and improve focus.
                        </p>
                        <div class="mt-2">
                            <a href="/momentum/productivity/task-batching" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                Create your first batch
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Batch Statistics -->
        <div class="grid grid-cols-3 gap-2">
            <div class="bg-green-50 dark:bg-green-900 rounded-lg p-2 text-center">
                <p class="text-xs text-green-800 dark:text-green-200">High Energy</p>
                <p class="text-lg font-semibold text-green-700 dark:text-green-300"><?= $statistics['high_energy_batches'] ?? 0 ?></p>
            </div>
            <div class="bg-yellow-50 dark:bg-yellow-900 rounded-lg p-2 text-center">
                <p class="text-xs text-yellow-800 dark:text-yellow-200">Medium Energy</p>
                <p class="text-lg font-semibold text-yellow-700 dark:text-yellow-300"><?= $statistics['medium_energy_batches'] ?? 0 ?></p>
            </div>
            <div class="bg-red-50 dark:bg-red-900 rounded-lg p-2 text-center">
                <p class="text-xs text-red-800 dark:text-red-200">Low Energy</p>
                <p class="text-lg font-semibold text-red-700 dark:text-red-300"><?= $statistics['low_energy_batches'] ?? 0 ?></p>
            </div>
        </div>
    </div>
</div>
