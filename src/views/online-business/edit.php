<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Business Venture</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Update details for <?= htmlspecialchars($businessVenture['name']) ?>
                </p>
            </div>

            <div class="flex flex-wrap items-center gap-3">
                <a href="/momentum/online-business/dashboard/<?= $businessVenture['id'] ?>" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1.5"></i> Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="px-5 py-5">
                <form action="/momentum/online-business/update/<?= $businessVenture['id'] ?>" method="POST" class="space-y-6">
                    <!-- Venture Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Venture Name</label>
                        <div class="mt-1">
                            <input type="text" name="name" id="name" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= htmlspecialchars($businessVenture['name']) ?>" required>
                        </div>
                    </div>

                    <!-- Venture Type -->
                    <div>
                        <label for="business_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Venture Type</label>
                        <div class="mt-1">
                            <select name="business_type" id="business_type" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="ecommerce" <?= $businessVenture['business_type'] == 'ecommerce' ? 'selected' : '' ?>>E-commerce Store</option>
                                <option value="content" <?= $businessVenture['business_type'] == 'content' ? 'selected' : '' ?>>Content Website</option>
                                <option value="saas" <?= $businessVenture['business_type'] == 'saas' ? 'selected' : '' ?>>SaaS Product</option>
                                <option value="digital_products" <?= $businessVenture['business_type'] == 'digital_products' ? 'selected' : '' ?>>Digital Products</option>
                                <option value="youtube" <?= $businessVenture['business_type'] == 'youtube' ? 'selected' : '' ?>>YouTube Channel</option>
                                <option value="podcast" <?= $businessVenture['business_type'] == 'podcast' ? 'selected' : '' ?>>Podcast</option>
                                <option value="membership" <?= $businessVenture['business_type'] == 'membership' ? 'selected' : '' ?>>Membership Site</option>
                                <option value="other" <?= $businessVenture['business_type'] == 'other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                        <div class="mt-1">
                            <textarea name="description" id="description" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= htmlspecialchars($businessVenture['description'] ?? '') ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Brief description of your business venture and its goals.</p>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
                        <div class="mt-1">
                            <select name="status" id="status" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                                <option value="planning" <?= $businessVenture['status'] == 'planning' ? 'selected' : '' ?>>Planning</option>
                                <option value="development" <?= $businessVenture['status'] == 'development' ? 'selected' : '' ?>>Development</option>
                                <option value="active" <?= $businessVenture['status'] == 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="paused" <?= $businessVenture['status'] == 'paused' ? 'selected' : '' ?>>Paused</option>
                                <option value="closed" <?= $businessVenture['status'] == 'closed' ? 'selected' : '' ?>>Closed</option>
                            </select>
                        </div>
                    </div>

                    <!-- Website URL -->
                    <div>
                        <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Website URL</label>
                        <div class="mt-1">
                            <input type="url" name="website" id="website" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= htmlspecialchars($businessVenture['website'] ?? '') ?>">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Optional. The URL of your business website.</p>
                    </div>

                    <!-- Start Date -->
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                        <div class="mt-1">
                            <input type="date" name="start_date" id="start_date" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" value="<?= $businessVenture['start_date'] ?? '' ?>">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Actual or planned start date.</p>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                        <div class="mt-1">
                            <textarea name="notes" id="notes" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"><?= htmlspecialchars($businessVenture['notes'] ?? '') ?></textarea>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Optional. Any additional notes or information.</p>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-between">
                        <a href="/momentum/online-business/delete/<?= $businessVenture['id'] ?>" onclick="return confirm('Are you sure you want to delete this business venture? This action cannot be undone.')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <i class="fas fa-trash-alt mr-1.5"></i> Delete Venture
                        </a>
                        
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-save mr-1.5"></i> Update Venture
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
