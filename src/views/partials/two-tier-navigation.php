<?php
/**
 * Two-Tier Navigation Header
 *
 * This partial contains a navigation header with a two-tier design for better organization
 * and improved ADHD-friendly UI.
 */

// Get the current path to determine active tab
$currentPath = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
$basePath = '/momentum';

// Function to check if a path is active
if (!function_exists('isActive')) {
    function isActive($path) {
        global $currentPath, $basePath;

    // Ensure $currentPath is a string to avoid deprecated warning with strpos()
    if (!is_string($currentPath)) {
        $currentPath = (string)$currentPath;
    }

    if ($path === '/dashboard' && ($currentPath === $basePath || $currentPath === $basePath . '/')) {
        return true;
    }

    // Make sure we have a valid string for both parameters
    $fullPath = $basePath . $path;
    return is_string($fullPath) && strpos($currentPath, $fullPath) === 0;
    }
}

// Function to generate active class for primary nav items
if (!function_exists('activePrimaryClass')) {
    function activePrimaryClass($path) {
        return isActive($path) ? 'active' : '';
    }
}

// Function to generate active class for secondary nav items
if (!function_exists('activeSecondaryClass')) {
    function activeSecondaryClass($path) {
        return isActive($path) ? 'active' : '';
    }
}
?>

<!-- Two-Tier Navigation Bar -->
<div class="two-tier-nav">
    <!-- Top Tier - Logo and Primary Navigation -->
    <div class="nav-tier-top">
        <!-- Logo Section -->
        <div class="nav-section nav-logo-section">
            <a href="/momentum/dashboard" class="nav-logo">
                <i class="fas fa-bolt"></i> Momentum
            </a>
        </div>

        <!-- Primary Navigation Section -->
        <div class="nav-section">
            <a href="/momentum/dashboard" class="nav-item-primary <?= activePrimaryClass('/dashboard') ?>">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <a href="/momentum/tasks" class="nav-item-primary <?= activePrimaryClass('/tasks') ?>">
                <i class="fas fa-tasks"></i> Tasks
            </a>
            <a href="/momentum/projects" class="nav-item-primary <?= activePrimaryClass('/projects') ?>">
                <i class="fas fa-project-diagram"></i> Projects
            </a>
            <a href="/momentum/reports" class="nav-item-primary <?= activePrimaryClass('/reports') ?>">
                <i class="fas fa-chart-line"></i> Reports
            </a>
            <a href="/momentum/finances" class="nav-item-primary <?= activePrimaryClass('/finances') ?>">
                <i class="fas fa-dollar-sign"></i> Finances
            </a>
            <a href="/momentum/ai-agents" class="nav-item-primary <?= activePrimaryClass('/ai-agents') ?>">
                <i class="fas fa-robot"></i> AI Agents
            </a>
        </div>

        <!-- User Section -->
        <div class="nav-section">
            <!-- Theme Toggle -->
            <a href="/momentum/dashboard/toggle-theme" class="nav-item-primary" title="Toggle Theme">
                <i class="fas fa-moon dark:hidden"></i>
                <i class="fas fa-sun hidden dark:block"></i>
                <span class="sr-only">Toggle Theme</span>
            </a>

            <!-- User Menu -->
            <div class="nav-dropdown">
                <button type="button" id="user-menu-button" class="nav-item-primary">
                    <i class="fas fa-user"></i>
                    <span class="sr-only">User Menu</span>
                </button>
                <div id="user-dropdown-menu" class="nav-dropdown-menu">
                    <a href="/momentum/profile" class="nav-dropdown-item">
                        <i class="fas fa-user-circle mr-2"></i> Profile
                    </a>
                    <a href="/momentum/settings" class="nav-dropdown-item">
                        <i class="fas fa-cog mr-2"></i> Settings
                    </a>
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    <a href="/momentum/logout" class="nav-dropdown-item">
                        <i class="fas fa-sign-out-alt mr-2"></i> Sign out
                    </a>
                </div>
            </div>

            <!-- Mobile Menu Button -->
            <button type="button" id="mobile-menu-button" class="mobile-menu-button">
                <i class="fas fa-bars"></i>
                <span class="sr-only">Menu</span>
            </button>
        </div>
    </div>

    <!-- Bottom Tier - Secondary Navigation -->
    <div class="nav-tier-bottom">
        <!-- Productivity Tools Section -->
        <div class="nav-section">
            <a href="/momentum/productivity/focus-timer" class="nav-item-secondary <?= activeSecondaryClass('/productivity/focus-timer') ?>">
                <i class="fas fa-stopwatch"></i> Focus
            </a>
            <a href="/momentum/productivity/tools" class="nav-item-secondary <?= activeSecondaryClass('/productivity/tools') ?>">
                <i class="fas fa-th-large"></i> Productivity
            </a>
            <a href="/momentum/adhd" class="nav-item-secondary <?= activeSecondaryClass('/adhd') ?>">
                <i class="fas fa-brain"></i> ADHD
            </a>
        </div>

        <div class="nav-divider"></div>

        <!-- Content Section -->
        <div class="nav-section">
            <a href="/momentum/ideas" class="nav-item-secondary <?= activeSecondaryClass('/ideas') ?>">
                <i class="fas fa-lightbulb"></i> Ideas
            </a>
            <a href="/momentum/notes" class="nav-item-secondary <?= activeSecondaryClass('/notes') ?>">
                <i class="fas fa-sticky-note"></i> Notes
            </a>
        </div>

        <div class="nav-divider"></div>

        <!-- Professional Section -->
        <div class="nav-section">
            <a href="/momentum/freelance" class="nav-item-secondary <?= activeSecondaryClass('/freelance') ?>">
                <i class="fas fa-briefcase"></i> Freelance
            </a>

            <!-- Income Dropdown -->
            <div class="nav-dropdown">
                <button id="income-dropdown-button" class="nav-item-secondary <?= isActive('/income-opportunities') || isActive('/passive-income') || isActive('/online-business') ? 'active' : '' ?>">
                    <i class="fas fa-money-bill-wave"></i> Income <i class="fas fa-chevron-down ml-1 text-xs"></i>
                </button>
                <div id="income-dropdown-menu" class="nav-dropdown-menu">
                    <a href="/momentum/income-opportunities" class="nav-dropdown-item">
                        <i class="fas fa-search mr-2"></i> Income Opportunities
                    </a>
                    <a href="/momentum/passive-income" class="nav-dropdown-item">
                        <i class="fas fa-chart-pie mr-2"></i> Passive Income
                    </a>
                    <a href="/momentum/online-business" class="nav-dropdown-item">
                        <i class="fas fa-chart-line mr-2"></i> Online Business
                    </a>
                </div>
            </div>
        </div>

        <div class="nav-divider"></div>

        <!-- Tools Section -->
        <div class="nav-section">
            <a href="/momentum/tools" class="nav-item-secondary <?= activeSecondaryClass('/tools') ?>">
                <i class="fas fa-tools"></i> Tools
            </a>
            <a href="/momentum/clone" class="nav-item-secondary <?= activeSecondaryClass('/clone') ?>">
                <i class="fas fa-clone"></i> Clone
            </a>
            <a href="/momentum/astrology" class="nav-item-secondary <?= activeSecondaryClass('/astrology') ?>">
                <i class="fas fa-star"></i> Astrology
            </a>
            <a href="/momentum/medical" class="nav-item-secondary <?= activeSecondaryClass('/medical') ?>">
                <i class="fas fa-heartbeat"></i> Medical
            </a>
            <a href="/momentum/help" class="nav-item-secondary <?= activeSecondaryClass('/help') ?>">
                <i class="fas fa-question-circle"></i> Help
            </a>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const navContainer = document.querySelector('.two-tier-nav');

        if (mobileMenuButton && navContainer) {
            mobileMenuButton.addEventListener('click', function() {
                navContainer.classList.toggle('mobile-menu-open');
            });
        }

        // User dropdown toggle
        const userMenuButton = document.getElementById('user-menu-button');
        const userDropdownMenu = document.getElementById('user-dropdown-menu');

        if (userMenuButton && userDropdownMenu) {
            userMenuButton.addEventListener('click', function(e) {
                e.stopPropagation();
                userDropdownMenu.classList.toggle('show');
            });

            // Close when clicking outside
            document.addEventListener('click', function(e) {
                if (!userMenuButton.contains(e.target) && !userDropdownMenu.contains(e.target)) {
                    userDropdownMenu.classList.remove('show');
                }
            });
        }

        // Income dropdown toggle
        const incomeDropdownButton = document.getElementById('income-dropdown-button');
        const incomeDropdownMenu = document.getElementById('income-dropdown-menu');

        if (incomeDropdownButton && incomeDropdownMenu) {
            incomeDropdownButton.addEventListener('click', function(e) {
                e.stopPropagation();
                incomeDropdownMenu.classList.toggle('show');
            });

            // Close when clicking outside
            document.addEventListener('click', function(e) {
                if (!incomeDropdownButton.contains(e.target) && !incomeDropdownMenu.contains(e.target)) {
                    incomeDropdownMenu.classList.remove('show');
                }
            });
        }
    });
</script>
