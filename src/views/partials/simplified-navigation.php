<?php
/**
 * Simplified Navigation Header
 *
 * This partial contains a simplified navigation header without dropdown menus.
 * All dropdown menu items have been moved to the dashboard as dedicated sections.
 */

// Get the current path to determine active tab
$currentPath = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
$basePath = '/momentum';

// Function to check if a path is active
if (!function_exists('isActive')) {
    function isActive($path) {
        global $currentPath, $basePath;

    // Ensure $currentPath is a string to avoid deprecated warning with strpos()
    if (!is_string($currentPath)) {
        $currentPath = (string)$currentPath;
    }

    if ($path === '/dashboard' && ($currentPath === $basePath || $currentPath === $basePath . '/')) {
        return true;
    }

    // Make sure we have a valid string for both parameters
    $fullPath = $basePath . $path;
    return is_string($fullPath) && strpos($currentPath, $fullPath) === 0;
    }
}

// Function to generate active class
if (!function_exists('activeClass')) {
    function activeClass($path) {
        return isActive($path)
            ? 'border-primary-500 text-primary-600 dark:text-primary-400'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600';
    }
}
?>

<div class="bg-white dark:bg-gray-800 shadow" style="position: relative; z-index: 50;">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <!-- Logo and brand -->
            <div class="flex">
                <div class="flex-shrink-0 flex items-center">
                    <a href="/momentum/dashboard" class="text-xl font-bold text-primary-600 dark:text-primary-400">
                        <i class="fas fa-bolt mr-2"></i>Momentum
                    </a>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="flex items-center -mr-2 md:hidden">
                <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                    <span class="sr-only">Open main menu</span>
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- Desktop navigation -->
            <div class="hidden md:flex md:items-center md:space-x-4" id="desktop-navigation" style="display: flex !important;">
                <nav class="flex space-x-4">
                    <a href="/momentum/dashboard" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/dashboard') ?>">
                        <i class="fas fa-home mr-1"></i> Dashboard
                    </a>
                    <a href="/momentum/tasks" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/tasks') ?>">
                        <i class="fas fa-tasks mr-1"></i> Tasks
                    </a>
                    <a href="/momentum/projects" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/projects') ?>">
                        <i class="fas fa-project-diagram mr-1"></i> Projects
                    </a>
                    <a href="/momentum/reports" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/reports') ?>">
                        <i class="fas fa-chart-line mr-1"></i> Reports
                    </a>
                    <a href="/momentum/ideas" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/ideas') ?>">
                        <i class="fas fa-lightbulb mr-1"></i> Ideas
                    </a>
                    <a href="/momentum/notes" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/notes') ?>">
                        <i class="fas fa-sticky-note mr-1"></i> Notes
                    </a>
                    <a href="/momentum/finances" class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium <?= activeClass('/finances') ?>">
                        <i class="fas fa-dollar-sign mr-1"></i> Finances
                    </a>
                </nav>

                <!-- Theme toggle -->
                <div class="ml-4 relative flex-shrink-0">
                    <a href="/momentum/dashboard/toggle-theme" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 p-2 hover:bg-gray-100 dark:hover:bg-gray-600" title="Toggle Theme">
                        <span class="sr-only">Toggle Theme</span>
                        <div class="h-5 w-5 flex items-center justify-center text-primary-600 dark:text-primary-300">
                            <i class="fas fa-moon dark:hidden"></i>
                            <i class="fas fa-sun hidden dark:block"></i>
                        </div>
                    </a>
                </div>

                <!-- User menu -->
                <div class="ml-4 relative flex-shrink-0">
                    <div class="relative">
                        <button type="button" id="user-menu-button" class="bg-white dark:bg-gray-700 rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <span class="sr-only">Open user menu</span>
                            <div class="h-8 w-8 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center text-primary-600 dark:text-primary-300">
                                <i class="fas fa-user"></i>
                            </div>
                        </button>

                        <!-- User dropdown menu (simplified with just logout) -->
                        <div id="user-dropdown-menu" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <a href="/momentum/logout" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-sign-out-alt mr-2"></i> Sign out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state -->
    <div id="mobile-menu" class="hidden md:hidden" style="position: relative; z-index: 50;">
        <div class="pt-2 pb-3 space-y-1">
            <a href="/momentum/dashboard" class="<?= isActive('/dashboard') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-home mr-2"></i> Dashboard
            </a>
            <a href="/momentum/tasks" class="<?= isActive('/tasks') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-tasks mr-2"></i> Tasks
            </a>
            <a href="/momentum/projects" class="<?= isActive('/projects') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-project-diagram mr-2"></i> Projects
            </a>
            <a href="/momentum/reports" class="<?= isActive('/reports') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-chart-line mr-2"></i> Reports
            </a>
            <a href="/momentum/ideas" class="<?= isActive('/ideas') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-lightbulb mr-2"></i> Ideas
            </a>
            <a href="/momentum/notes" class="<?= isActive('/notes') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-sticky-note mr-2"></i> Notes
            </a>
            <a href="/momentum/finances" class="<?= isActive('/finances') ? 'bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-700 dark:text-primary-300' : 'border-transparent text-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:text-gray-300' ?> block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
                <i class="fas fa-dollar-sign mr-2"></i> Finances
            </a>
        </div>
        <div class="pt-4 pb-3 border-t border-gray-200 dark:border-gray-600">
            <div class="flex items-center px-4">
                <div class="flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center text-primary-600 dark:text-primary-300">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div class="ml-3">
                    <div class="text-base font-medium text-gray-800 dark:text-white">
                        <?= isset($_SESSION['user']['name']) ? $_SESSION['user']['name'] : 'User' ?>
                    </div>
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        <?= isset($_SESSION['user']['email']) ? $_SESSION['user']['email'] : '' ?>
                    </div>
                </div>
            </div>
            <div class="mt-3 space-y-1">
                <a href="/momentum/dashboard/toggle-theme" class="block px-4 py-2 text-base font-medium text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-adjust mr-2"></i> Toggle Theme
                </a>
                <a href="/momentum/logout" class="block px-4 py-2 text-base font-medium text-gray-500 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-sign-out-alt mr-2"></i> Sign out
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure desktop navigation is always visible
        const desktopNavigation = document.getElementById('desktop-navigation');
        if (desktopNavigation) {
            // Force display to be flex for desktop navigation
            desktopNavigation.style.display = 'flex';
        }

        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // Simple user menu toggle
        const userMenuButton = document.getElementById('user-menu-button');
        const userDropdownMenu = document.getElementById('user-dropdown-menu');

        if (userMenuButton && userDropdownMenu) {
            userMenuButton.addEventListener('click', function() {
                userDropdownMenu.classList.toggle('hidden');
            });

            // Close when clicking outside
            document.addEventListener('click', function(e) {
                if (!userMenuButton.contains(e.target) && !userDropdownMenu.contains(e.target)) {
                    userDropdownMenu.classList.add('hidden');
                }
            });
        }
    });
</script>
