<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/income-evaluator" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-arrow-left"></i>
                <span class="ml-1">Back to Evaluator</span>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Evaluate: <?= htmlspecialchars($comparison['name']) ?></h1>
        </div>

        <?php if (empty($comparison['opportunities'])): ?>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <p class="text-gray-500 dark:text-gray-400 mb-4">No opportunities added to this comparison.</p>
                <a href="/momentum/income-evaluator" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Evaluator
                </a>
            </div>
        <?php else: ?>
            <form action="/momentum/income-evaluator/save-scores/<?= $comparison['id'] ?>" method="post">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">Evaluation Instructions</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-700 dark:text-gray-300 mb-4">
                            Rate each opportunity on a scale of 1-10 for each criterion, where:
                        </p>
                        <ul class="list-disc pl-5 mb-4 text-gray-700 dark:text-gray-300 space-y-1">
                            <li><strong>1-3:</strong> Poor - Does not meet your needs or has significant drawbacks</li>
                            <li><strong>4-6:</strong> Average - Meets basic requirements but has limitations</li>
                            <li><strong>7-8:</strong> Good - Strong performance with minor limitations</li>
                            <li><strong>9-10:</strong> Excellent - Exceptional performance with minimal drawbacks</li>
                        </ul>
                        <p class="text-gray-700 dark:text-gray-300">
                            Add notes to explain your ratings or record important considerations.
                        </p>
                    </div>
                </div>

                <?php foreach ($criteriaByType as $type => $typeCriteria): ?>
                    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                        <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                                <?= ucfirst($type) ?> Criteria
                            </h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Criteria</th>
                                        <?php foreach ($comparison['opportunities'] as $opportunity): ?>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                <?= htmlspecialchars($opportunity['name']) ?>
                                            </th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    <?php foreach ($typeCriteria as $criteria): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($criteria['name']) ?></div>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">Weight: <?= $criteria['weight'] ?></div>
                                                <?php if (!empty($criteria['description'])): ?>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1"><?= htmlspecialchars($criteria['description']) ?></div>
                                                <?php endif; ?>
                                            </td>
                                            
                                            <?php foreach ($comparison['opportunities'] as $opportunity): ?>
                                                <?php 
                                                    $opportunityId = $opportunity['opportunity_id'];
                                                    $criteriaId = $criteria['id'];
                                                    $score = isset($scores[$opportunityId]['criteria_scores'][$criteriaId]) 
                                                        ? $scores[$opportunityId]['criteria_scores'][$criteriaId]['score'] 
                                                        : '';
                                                    $note = isset($scores[$opportunityId]['criteria_scores'][$criteriaId]) 
                                                        ? $scores[$opportunityId]['criteria_scores'][$criteriaId]['notes'] 
                                                        : '';
                                                ?>
                                                <td class="px-6 py-4">
                                                    <div class="flex flex-col space-y-2">
                                                        <select name="score_<?= $opportunityId ?>_<?= $criteriaId ?>" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm">
                                                            <option value="">Select...</option>
                                                            <?php for ($i = 1; $i <= 10; $i++): ?>
                                                                <option value="<?= $i ?>" <?= $score === (string)$i ? 'selected' : '' ?>><?= $i ?></option>
                                                            <?php endfor; ?>
                                                        </select>
                                                        <textarea name="note_<?= $opportunityId ?>_<?= $criteriaId ?>" rows="2" class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm" placeholder="Notes..."><?= htmlspecialchars($note) ?></textarea>
                                                    </div>
                                                </td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endforeach; ?>

                <div class="flex justify-end space-x-3 mb-6">
                    <a href="/momentum/income-evaluator" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i> Save and View Results
                    </button>
                </div>
            </form>
        <?php endif; ?>
    </div>
</div>
