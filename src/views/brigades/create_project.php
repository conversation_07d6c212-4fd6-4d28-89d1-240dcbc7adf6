<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>Create Brigade Project</h1>
            <p class="lead">Create a new AI Agent Army brigade project.</p>
        </div>
        <div class="col-auto">
            <a href="/momentum/brigades/templates" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Templates
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">New Brigade Project</h5>
        </div>
        <div class="card-body">
            <form action="/momentum/brigades/create-project" method="POST">
                <div class="mb-3">
                    <label for="brigade_type" class="form-label">Brigade Type</label>
                    <select name="brigade_type" id="brigade_type" class="form-select" required>
                        <option value="">Select Brigade Type</option>
                        <?php foreach ($brigadeTypes as $type => $name): ?>
                            <option value="<?= $type ?>" <?= isset($_GET['type']) && $_GET['type'] === $type ? 'selected' : '' ?>>
                                <?= htmlspecialchars($name) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="project_name" class="form-label">Project Name</label>
                    <input type="text" name="project_name" id="project_name" class="form-control" required>
                </div>

                <div class="mb-3">
                    <label for="project_description" class="form-label">Project Description</label>
                    <textarea name="project_description" id="project_description" class="form-control" rows="3"></textarea>
                </div>

                <div class="mb-3">
                    <label for="deadline" class="form-label">Deadline</label>
                    <input type="date" name="deadline" id="deadline" class="form-control" value="<?= date('Y-m-d', strtotime('+1 week')) ?>" required>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> The Aegis Director agent will be automatically assigned as the Brigade Commander. After project creation, you can assign other agents to specific roles.
                </div>

                <div class="text-end">
                    <button type="submit" class="btn btn-primary">Create Brigade Project</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
