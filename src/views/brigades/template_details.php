<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1><?= htmlspecialchars($template['name']) ?></h1>
            <p class="lead"><?= htmlspecialchars($template['description']) ?></p>
        </div>
        <div class="col-auto">
            <a href="/momentum/brigades/templates" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Templates
            </a>
            <a href="/momentum/brigades/create-project?type=<?= $template['brigade_type'] ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create Project
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Agent Roles</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <?php foreach ($template['roles'] as $roleKey => $role): ?>
                            <div class="list-group-item">
                                <h5 class="mb-1"><?= htmlspecialchars($role['name']) ?></h5>
                                <p class="mb-1"><?= htmlspecialchars($role['description']) ?></p>
                                <small class="text-muted">
                                    <strong>Required Skills:</strong> 
                                    <?= implode(', ', array_map('ucfirst', $role['required_skills'])) ?>
                                </small>
                                <?php if ($role['default_agent']): ?>
                                    <div class="mt-2">
                                        <span class="badge bg-info">Default Agent: <?= htmlspecialchars($role['default_agent']) ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Implementation Process</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> This brigade template includes <?= count($template['tasks']) ?> predefined tasks with dependencies and role assignments.
                    </div>
                    
                    <p>When you create a project using this template:</p>
                    <ol>
                        <li>All tasks will be automatically created with proper dependencies</li>
                        <li>The Aegis Director agent will be assigned as Brigade Commander</li>
                        <li>Other agents will be assigned to roles based on their skills</li>
                        <li>You can customize agent assignments after project creation</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Task Workflow</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Task Name</th>
                            <th>Description</th>
                            <th>Assigned Role</th>
                            <th>Duration</th>
                            <th>Priority</th>
                            <th>Dependencies</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($template['tasks'] as $task): ?>
                            <tr>
                                <td><?= htmlspecialchars($task['name']) ?></td>
                                <td><?= htmlspecialchars($task['description']) ?></td>
                                <td>
                                    <span class="badge bg-primary">
                                        <?= htmlspecialchars($task['assigned_role']) ?>
                                    </span>
                                </td>
                                <td><?= $task['duration'] ?> min</td>
                                <td>
                                    <?php
                                    $priorityClass = 'bg-info';
                                    if ($task['priority'] === 'high') $priorityClass = 'bg-warning';
                                    if ($task['priority'] === 'critical') $priorityClass = 'bg-danger';
                                    ?>
                                    <span class="badge <?= $priorityClass ?>">
                                        <?= ucfirst($task['priority']) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (empty($task['dependencies'])): ?>
                                        <span class="text-muted">None</span>
                                    <?php else: ?>
                                        <?php foreach ($task['dependencies'] as $i => $dep): ?>
                                            <span class="badge bg-secondary"><?= htmlspecialchars($dep) ?></span>
                                            <?php if ($i < count($task['dependencies']) - 1): ?>, <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
