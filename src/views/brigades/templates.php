<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>AI Agent Army Brigade Templates</h1>
            <p class="lead">Select a brigade template to create a new AI Agent Army project.</p>
        </div>
        <div class="col-auto">
            <a href="/momentum/brigades/create-project" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create Brigade Project
            </a>
        </div>
    </div>

    <div class="row">
        <?php foreach ($templates as $type => $template): ?>
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0"><?= htmlspecialchars($template['name']) ?></h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text"><?= htmlspecialchars($template['description']) ?></p>
                        
                        <h6 class="mt-3">Agent Roles:</h6>
                        <ul class="list-group list-group-flush mb-3">
                            <?php foreach ($template['roles'] as $roleKey => $role): ?>
                                <li class="list-group-item">
                                    <strong><?= htmlspecialchars($role['name']) ?></strong>: 
                                    <?= htmlspecialchars($role['description']) ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <h6>Key Tasks:</h6>
                        <ul class="list-group list-group-flush">
                            <?php 
                            $taskCount = 0;
                            foreach ($template['tasks'] as $task): 
                                if ($taskCount >= 5) break; // Show only first 5 tasks
                                $taskCount++;
                            ?>
                                <li class="list-group-item">
                                    <strong><?= htmlspecialchars($task['name']) ?></strong>
                                </li>
                            <?php endforeach; ?>
                            <?php if (count($template['tasks']) > 5): ?>
                                <li class="list-group-item text-muted">
                                    ... and <?= count($template['tasks']) - 5 ?> more tasks
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <div class="card-footer">
                        <a href="/momentum/brigades/view/<?= $type ?>" class="btn btn-outline-primary">View Details</a>
                        <a href="/momentum/brigades/create-project?type=<?= $type ?>" class="btn btn-primary">Create Project</a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
