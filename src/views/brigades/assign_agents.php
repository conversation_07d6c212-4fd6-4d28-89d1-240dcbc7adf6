<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>Assign Agents to Brigade Roles</h1>
            <p class="lead">Project: <?= htmlspecialchars($project['name']) ?></p>
        </div>
        <div class="col-auto">
            <a href="/momentum/projects/view/<?= $project['id'] ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Project
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Agent Role Assignments</h5>
        </div>
        <div class="card-body">
            <form action="/momentum/brigades/process-assignments/<?= $project['id'] ?>" method="POST">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Assign your AI agents to specific roles in this brigade. Each role has specific responsibilities and required skills.
                </div>

                <?php foreach ($template['roles'] as $roleKey => $role): ?>
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5 class="mb-0"><?= htmlspecialchars($role['name']) ?></h5>
                        </div>
                        <div class="card-body">
                            <p><?= htmlspecialchars($role['description']) ?></p>
                            <p><strong>Required Skills:</strong> <?= implode(', ', array_map('ucfirst', $role['required_skills'])) ?></p>
                            
                            <div class="mb-3">
                                <label for="role_<?= $roleKey ?>" class="form-label">Assign Agent</label>
                                <select name="role_assignments[<?= $roleKey ?>]" id="role_<?= $roleKey ?>" class="form-select">
                                    <option value="">Select Agent</option>
                                    <?php foreach ($agents as $agent): ?>
                                        <?php 
                                        $isCurrentlyAssigned = false;
                                        foreach ($currentAssignments as $assignment) {
                                            if ($assignment['role'] === $role['name'] && $assignment['agent_id'] === $agent['id']) {
                                                $isCurrentlyAssigned = true;
                                                break;
                                            }
                                        }
                                        ?>
                                        <option value="<?= $agent['id'] ?>" <?= $isCurrentlyAssigned ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($agent['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <div class="text-end">
                    <button type="submit" class="btn btn-primary">Save Assignments</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
