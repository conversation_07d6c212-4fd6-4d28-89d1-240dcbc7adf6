/**
 * Dashboard JavaScript
 * 
 * Handles dashboard-specific functionality.
 */

import { getElement, getElements, debounce, ajax } from './modules/core';

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard JavaScript loaded');
    
    // Initialize dashboard components
    initDashboardWidgets();
    initDashboardLayout();
    initQuickActions();
    initDataRefresh();
});

/**
 * Initialize dashboard widgets
 */
function initDashboardWidgets() {
    // Initialize task widgets
    initTaskWidgets();
    
    // Initialize finance widgets
    initFinanceWidgets();
    
    // Initialize ADHD tracking widgets
    initADHDWidgets();
    
    // Initialize AI agent widgets
    initAIAgentWidgets();
}

/**
 * Initialize task widgets
 */
function initTaskWidgets() {
    // Add click handlers for task checkboxes
    const taskCheckboxes = getElements('.task-checkbox');
    taskCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', handleTaskCheckboxChange);
    });
    
    // Add click handlers for task expand buttons
    const expandButtons = getElements('.task-expand-button');
    expandButtons.forEach(button => {
        button.addEventListener('click', handleTaskExpand);
    });
}

/**
 * Handle task checkbox change
 */
function handleTaskCheckboxChange(event) {
    const checkbox = event.target;
    const taskId = checkbox.getAttribute('data-task-id');
    const isChecked = checkbox.checked;
    
    // Update task status via AJAX
    ajax(`/momentum/tasks/update-status/${taskId}`, {
        method: 'POST',
        body: JSON.stringify({
            status: isChecked ? 'done' : 'todo'
        })
    })
    .then(response => {
        // Update UI
        const taskItem = checkbox.closest('.task-item');
        if (taskItem) {
            if (isChecked) {
                taskItem.classList.add('completed');
            } else {
                taskItem.classList.remove('completed');
            }
        }
    })
    .catch(error => {
        console.error('Error updating task status:', error);
        // Revert checkbox state on error
        checkbox.checked = !isChecked;
    });
}

/**
 * Handle task expand button click
 */
function handleTaskExpand(event) {
    const button = event.target.closest('.task-expand-button');
    const taskItem = button.closest('.task-item');
    const taskDetails = taskItem.querySelector('.task-details');
    
    // Toggle expanded state
    const isExpanded = taskItem.classList.toggle('expanded');
    
    // Update button text
    button.textContent = isExpanded ? 'Collapse' : 'Expand';
    
    // Toggle details visibility
    if (taskDetails) {
        taskDetails.style.display = isExpanded ? 'block' : 'none';
    }
}

/**
 * Initialize finance widgets
 */
function initFinanceWidgets() {
    // Initialize finance charts if Chart.js is available
    if (window.Chart) {
        initFinanceCharts();
    }
}

/**
 * Initialize finance charts
 */
function initFinanceCharts() {
    // Get chart canvases
    const incomeExpenseCanvas = getElement('#income-expense-chart');
    const categoryCanvas = getElement('#expense-category-chart');
    
    // Initialize charts if canvases exist
    if (incomeExpenseCanvas) {
        initIncomeExpenseChart(incomeExpenseCanvas);
    }
    
    if (categoryCanvas) {
        initCategoryChart(categoryCanvas);
    }
}

/**
 * Initialize income/expense chart
 */
function initIncomeExpenseChart(canvas) {
    // Get chart data from data attributes
    const labels = JSON.parse(canvas.getAttribute('data-labels') || '[]');
    const incomeData = JSON.parse(canvas.getAttribute('data-income') || '[]');
    const expenseData = JSON.parse(canvas.getAttribute('data-expense') || '[]');
    
    // Create chart
    new Chart(canvas, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Income',
                    data: incomeData,
                    backgroundColor: 'rgba(46, 204, 113, 0.5)',
                    borderColor: 'rgba(46, 204, 113, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Expenses',
                    data: expenseData,
                    backgroundColor: 'rgba(231, 76, 60, 0.5)',
                    borderColor: 'rgba(231, 76, 60, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * Initialize category chart
 */
function initCategoryChart(canvas) {
    // Get chart data from data attributes
    const labels = JSON.parse(canvas.getAttribute('data-labels') || '[]');
    const data = JSON.parse(canvas.getAttribute('data-values') || '[]');
    const colors = JSON.parse(canvas.getAttribute('data-colors') || '[]');
    
    // Create chart
    new Chart(canvas, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [
                {
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

/**
 * Initialize ADHD widgets
 */
function initADHDWidgets() {
    // Initialize symptom tracking chart if available
    const symptomCanvas = getElement('#adhd-symptom-chart');
    if (symptomCanvas && window.Chart) {
        initSymptomChart(symptomCanvas);
    }
}

/**
 * Initialize symptom chart
 */
function initSymptomChart(canvas) {
    // Get chart data from data attributes
    const labels = JSON.parse(canvas.getAttribute('data-labels') || '[]');
    const data = JSON.parse(canvas.getAttribute('data-values') || '[]');
    
    // Create chart
    new Chart(canvas, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Symptom Severity',
                    data: data,
                    backgroundColor: 'rgba(52, 152, 219, 0.2)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 10
                }
            }
        }
    });
}

/**
 * Initialize AI agent widgets
 */
function initAIAgentWidgets() {
    // Add click handlers for agent cards
    const agentCards = getElements('.agent-card');
    agentCards.forEach(card => {
        card.addEventListener('click', handleAgentCardClick);
    });
}

/**
 * Handle agent card click
 */
function handleAgentCardClick(event) {
    const card = event.currentTarget;
    const agentId = card.getAttribute('data-agent-id');
    
    // Navigate to agent detail page
    window.location.href = `/momentum/ai-agents/${agentId}`;
}

/**
 * Initialize dashboard layout
 */
function initDashboardLayout() {
    // Get layout preference from localStorage
    const savedLayout = localStorage.getItem('dashboard_layout');
    
    // Apply saved layout if available
    if (savedLayout) {
        applyDashboardLayout(savedLayout);
    }
    
    // Add click handlers for layout buttons
    const layoutButtons = getElements('.layout-button');
    layoutButtons.forEach(button => {
        button.addEventListener('click', handleLayoutButtonClick);
    });
}

/**
 * Apply dashboard layout
 */
function applyDashboardLayout(layout) {
    // Remove all layout classes
    document.body.classList.remove('layout-standard', 'layout-adhd', 'layout-focus', 'layout-custom');
    
    // Add selected layout class
    document.body.classList.add(`layout-${layout}`);
    
    // Update active button
    const layoutButtons = getElements('.layout-button');
    layoutButtons.forEach(button => {
        const buttonLayout = button.getAttribute('data-layout');
        if (buttonLayout === layout) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
}

/**
 * Handle layout button click
 */
function handleLayoutButtonClick(event) {
    const button = event.currentTarget;
    const layout = button.getAttribute('data-layout');
    
    // Apply layout
    applyDashboardLayout(layout);
    
    // Save preference
    localStorage.setItem('dashboard_layout', layout);
}

/**
 * Initialize quick actions
 */
function initQuickActions() {
    // Add click handlers for quick action buttons
    const quickActionButtons = getElements('.quick-action-button');
    quickActionButtons.forEach(button => {
        button.addEventListener('click', handleQuickActionClick);
    });
}

/**
 * Handle quick action button click
 */
function handleQuickActionClick(event) {
    const button = event.currentTarget;
    const action = button.getAttribute('data-action');
    
    // Handle different actions
    switch (action) {
        case 'add-task':
            showQuickAddTaskModal();
            break;
        case 'add-idea':
            showQuickAddIdeaModal();
            break;
        case 'add-transaction':
            showQuickAddTransactionModal();
            break;
        case 'focus-mode':
            toggleFocusMode();
            break;
    }
}

/**
 * Show quick add task modal
 */
function showQuickAddTaskModal() {
    const modal = getElement('#quick-add-task-modal');
    if (modal) {
        modal.classList.add('active');
        
        // Focus on title input
        const titleInput = modal.querySelector('input[name="title"]');
        if (titleInput) {
            titleInput.focus();
        }
    }
}

/**
 * Initialize data refresh
 */
function initDataRefresh() {
    // Refresh dashboard data periodically
    setInterval(refreshDashboardData, 5 * 60 * 1000); // Every 5 minutes
}

/**
 * Refresh dashboard data
 */
const refreshDashboardData = debounce(function() {
    // Only refresh if user is active
    if (document.visibilityState === 'visible') {
        // Refresh task widgets
        refreshTaskWidgets();
        
        // Refresh finance widgets
        refreshFinanceWidgets();
    }
}, 1000);

/**
 * Refresh task widgets
 */
function refreshTaskWidgets() {
    // Get task widgets
    const todayTasksWidget = getElement('#today-tasks-widget');
    const overdueTasksWidget = getElement('#overdue-tasks-widget');
    
    // Refresh today tasks
    if (todayTasksWidget) {
        const widgetId = todayTasksWidget.getAttribute('data-widget-id');
        
        ajax(`/momentum/widgets/refresh/${widgetId}`)
            .then(response => {
                if (response.html) {
                    todayTasksWidget.innerHTML = response.html;
                    initTaskWidgets(); // Re-initialize event handlers
                }
            })
            .catch(error => {
                console.error('Error refreshing today tasks widget:', error);
            });
    }
    
    // Refresh overdue tasks
    if (overdueTasksWidget) {
        const widgetId = overdueTasksWidget.getAttribute('data-widget-id');
        
        ajax(`/momentum/widgets/refresh/${widgetId}`)
            .then(response => {
                if (response.html) {
                    overdueTasksWidget.innerHTML = response.html;
                    initTaskWidgets(); // Re-initialize event handlers
                }
            })
            .catch(error => {
                console.error('Error refreshing overdue tasks widget:', error);
            });
    }
}

/**
 * Refresh finance widgets
 */
function refreshFinanceWidgets() {
    // Get finance widgets
    const financeSummaryWidget = getElement('#finance-summary-widget');
    
    // Refresh finance summary
    if (financeSummaryWidget) {
        const widgetId = financeSummaryWidget.getAttribute('data-widget-id');
        
        ajax(`/momentum/widgets/refresh/${widgetId}`)
            .then(response => {
                if (response.html) {
                    financeSummaryWidget.innerHTML = response.html;
                    initFinanceWidgets(); // Re-initialize event handlers
                }
            })
            .catch(error => {
                console.error('Error refreshing finance summary widget:', error);
            });
    }
}
