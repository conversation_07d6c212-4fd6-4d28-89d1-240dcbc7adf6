/**
 * Main JavaScript Entry Point
 * 
 * This file serves as the main entry point for the application's JavaScript.
 * It loads all required modules and initializes them.
 */

// Import modules
import './modules/core';
import './modules/ui-enhancements';

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Main JavaScript loaded');
    
    // Initialize modules
    initializeModules();
    
    // Setup global event listeners
    setupGlobalEventListeners();
});

/**
 * Initialize all modules
 */
function initializeModules() {
    // Check if modules are available
    if (window.DashboardLayout) {
        console.log('Initializing DashboardLayout module');
        window.DashboardLayout.init();
    }
    
    if (window.TaskManager) {
        console.log('Initializing TaskManager module');
        window.TaskManager.init();
    }
}

/**
 * Setup global event listeners
 */
function setupGlobalEventListeners() {
    // Add global event listeners here
    document.addEventListener('keydown', handleGlobalKeydown);
    
    // Add click handler for notifications
    const notifications = document.querySelectorAll('.notification');
    notifications.forEach(notification => {
        notification.addEventListener('click', handleNotificationClick);
    });
}

/**
 * Handle global keydown events
 */
function handleGlobalKeydown(event) {
    // Handle keyboard shortcuts
    if (event.key === 'Escape') {
        // Close modals, dropdowns, etc.
        closeAllModals();
    }
}

/**
 * Handle notification clicks
 */
function handleNotificationClick(event) {
    // Mark notification as read
    event.currentTarget.classList.add('read');
    
    // Prevent default if it's a link
    if (event.target.tagName === 'A') {
        return;
    }
    
    event.preventDefault();
}

/**
 * Close all modals
 */
function closeAllModals() {
    const modals = document.querySelectorAll('.modal.active');
    modals.forEach(modal => {
        modal.classList.remove('active');
    });
}

// Export functions for use in other modules
export {
    initializeModules,
    setupGlobalEventListeners,
    handleGlobalKeydown,
    closeAllModals
};
