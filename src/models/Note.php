<?php
/**
 * Enhanced Note Model
 *
 * Handles note-related database operations with speed optimizations and ADHD-friendly features.
 */

require_once __DIR__ . '/BaseModel.php';

class Note extends BaseModel {
    protected $table = 'notes';

    // Cache for frequently accessed data
    private static $categoryCache = [];
    private static $tagCache = [];
    private static $statsCache = [];

    /**
     * Get notes for a specific user with optimized queries and ADHD-friendly sorting
     */
    public function getUserNotes($userId, $filters = [], $limit = null, $offset = 0) {
        // Build optimized query with indexes
        $sql = "SELECT id, title, content, category, tags, is_pinned, is_favorite,
                       priority_level, created_at, updated_at, last_accessed,
                       CASE
                           WHEN is_pinned = 1 THEN 1
                           WHEN is_favorite = 1 THEN 2
                           WHEN priority_level = 'high' THEN 3
                           WHEN priority_level = 'medium' THEN 4
                           ELSE 5
                       END as sort_priority
                FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        // Apply filters with optimized conditions
        if (!empty($filters)) {
            // Filter by category (exact match for speed)
            if (isset($filters['category']) && !empty($filters['category'])) {
                $sql .= " AND category = ?";
                $params[] = $filters['category'];
            }

            // Filter by tag (optimized LIKE with index)
            if (isset($filters['tag']) && !empty($filters['tag'])) {
                $sql .= " AND tags LIKE ?";
                $params[] = "%{$filters['tag']}%";
            }

            // Filter by priority (ADHD-friendly)
            if (isset($filters['priority']) && !empty($filters['priority'])) {
                $sql .= " AND priority_level = ?";
                $params[] = $filters['priority'];
            }

            // Filter by date range
            if (isset($filters['date_from']) && !empty($filters['date_from'])) {
                $sql .= " AND DATE(updated_at) >= ?";
                $params[] = $filters['date_from'];
            }

            if (isset($filters['date_to']) && !empty($filters['date_to'])) {
                $sql .= " AND DATE(updated_at) <= ?";
                $params[] = $filters['date_to'];
            }

            // Filter favorites only
            if (isset($filters['favorites_only']) && $filters['favorites_only']) {
                $sql .= " AND is_favorite = 1";
            }
        }

        // ADHD-friendly sorting: pinned first, then favorites, then by priority and recency
        $sql .= " ORDER BY sort_priority ASC, updated_at DESC";

        // Add pagination for performance
        if ($limit) {
            $sql .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
        }

        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get note categories for a specific user with caching
     */
    public function getUserNoteCategories($userId) {
        // Check cache first for speed
        $cacheKey = "categories_user_{$userId}";
        if (isset(self::$categoryCache[$cacheKey])) {
            return self::$categoryCache[$cacheKey];
        }

        $sql = "SELECT DISTINCT category, COUNT(*) as note_count
                FROM {$this->table}
                WHERE user_id = ? AND category IS NOT NULL AND category != ''
                GROUP BY category
                ORDER BY note_count DESC, category ASC";

        $result = $this->db->fetchAll($sql, [$userId]);

        // Cache the result
        self::$categoryCache[$cacheKey] = $result;

        return $result;
    }

    /**
     * Enhanced search with full-text capabilities and ADHD-friendly features
     */
    public function searchNotes($userId, $searchTerm, $filters = []) {
        $sql = "SELECT *,
                       CASE
                           WHEN title LIKE ? THEN 1
                           WHEN content LIKE ? THEN 2
                           WHEN tags LIKE ? THEN 3
                           ELSE 4
                       END as relevance_score,
                       CASE
                           WHEN is_pinned = 1 THEN 1
                           WHEN is_favorite = 1 THEN 2
                           WHEN priority_level = 'high' THEN 3
                           ELSE 4
                       END as priority_score
                FROM {$this->table}
                WHERE user_id = ? AND (
                    title LIKE ? OR
                    content LIKE ? OR
                    tags LIKE ? OR
                    category LIKE ?
                )";

        $searchParam = "%{$searchTerm}%";
        $params = [
            $searchParam, $searchParam, $searchParam, // For relevance scoring
            $userId, $searchParam, $searchParam, $searchParam, $searchParam
        ];

        // Apply additional filters
        if (!empty($filters['category'])) {
            $sql .= " AND category = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['priority'])) {
            $sql .= " AND priority_level = ?";
            $params[] = $filters['priority'];
        }

        // ADHD-friendly sorting: relevance + priority
        $sql .= " ORDER BY priority_score ASC, relevance_score ASC, updated_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get popular tags with usage count (ADHD-friendly autocomplete)
     */
    public function getPopularTags($userId, $limit = 20) {
        $cacheKey = "tags_user_{$userId}";
        if (isset(self::$tagCache[$cacheKey])) {
            return self::$tagCache[$cacheKey];
        }

        $sql = "SELECT tags FROM {$this->table}
                WHERE user_id = ? AND tags IS NOT NULL AND tags != ''";

        $notes = $this->db->fetchAll($sql, [$userId]);
        $tagCounts = [];

        foreach ($notes as $note) {
            $tags = explode(',', $note['tags']);
            foreach ($tags as $tag) {
                $tag = trim($tag);
                if (!empty($tag)) {
                    $tagCounts[$tag] = ($tagCounts[$tag] ?? 0) + 1;
                }
            }
        }

        // Sort by usage count
        arsort($tagCounts);
        $result = array_slice($tagCounts, 0, $limit, true);

        // Cache the result
        self::$tagCache[$cacheKey] = $result;

        return $result;
    }

    /**
     * Auto-save functionality for ADHD users
     */
    public function autoSave($userId, $noteId, $data) {
        $autoSaveData = [
            'title' => $data['title'] ?? '',
            'content' => $data['content'] ?? '',
            'category' => $data['category'] ?? null,
            'tags' => $data['tags'] ?? null,
            'updated_at' => date('Y-m-d H:i:s'),
            'auto_saved' => 1
        ];

        if ($noteId && $noteId !== 'new') {
            // Update existing note
            $note = $this->find($noteId);
            if ($note && $note['user_id'] == $userId) {
                return $this->update($noteId, $autoSaveData);
            }
        } else {
            // Create new auto-saved note
            $autoSaveData['user_id'] = $userId;
            $autoSaveData['created_at'] = date('Y-m-d H:i:s');
            return $this->create($autoSaveData);
        }

        return false;
    }

    /**
     * Update last accessed time for analytics
     */
    public function updateLastAccessed($noteId, $userId) {
        $note = $this->find($noteId);
        if ($note && $note['user_id'] == $userId) {
            return $this->update($noteId, ['last_accessed' => date('Y-m-d H:i:s')]);
        }
        return false;
    }

    /**
     * Get note statistics for ADHD insights with caching
     */
    public function getNoteStats($userId) {
        // Check cache first for speed
        $cacheKey = "stats_user_{$userId}";
        if (isset(self::$statsCache[$cacheKey])) {
            return self::$statsCache[$cacheKey];
        }

        $sql = "SELECT
                    COUNT(*) as total_notes,
                    COUNT(CASE WHEN is_pinned = 1 THEN 1 END) as pinned_notes,
                    COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_notes,
                    COUNT(CASE WHEN priority_level = 'high' THEN 1 END) as high_priority,
                    COUNT(CASE WHEN priority_level = 'medium' THEN 1 END) as medium_priority,
                    COUNT(CASE WHEN priority_level = 'low' THEN 1 END) as low_priority,
                    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_notes,
                    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_notes
                FROM {$this->table}
                WHERE user_id = ?";

        $result = $this->db->fetchOne($sql, [$userId]);

        // Cache the result
        if ($result) {
            self::$statsCache[$cacheKey] = $result;
        }

        return $result;
    }

    /**
     * Clear cache when data changes
     */
    public function clearCache($userId) {
        unset(self::$categoryCache["categories_user_{$userId}"]);
        unset(self::$tagCache["tags_user_{$userId}"]);
        unset(self::$statsCache["stats_user_{$userId}"]);
    }

    /**
     * Override create to clear cache
     */
    public function create($data) {
        $result = parent::create($data);
        if ($result && isset($data['user_id'])) {
            $this->clearCache($data['user_id']);
        }
        return $result;
    }

    /**
     * Override update to clear cache
     */
    public function update($id, $data) {
        $note = $this->find($id);
        $result = parent::update($id, $data);
        if ($result && $note) {
            $this->clearCache($note['user_id']);
        }
        return $result;
    }

    /**
     * Override delete to clear cache
     */
    public function delete($id) {
        $note = $this->find($id);
        $result = parent::delete($id);
        if ($result && $note) {
            $this->clearCache($note['user_id']);
        }
        return $result;
    }



    /**
     * Get all note templates for a user
     */
    public function getTemplates($userId, $includePublic = true) {
        $sql = "SELECT * FROM note_templates WHERE user_id = ?";
        $params = [$userId];

        if ($includePublic) {
            $sql .= " OR is_public = 1";
        }

        $sql .= " ORDER BY usage_count DESC, name ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Create a new note template
     */
    public function createTemplate($userId, $data) {
        $sql = "INSERT INTO note_templates (user_id, name, description, template_content, category, default_tags, is_public, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

        $params = [
            $userId,
            $data['name'],
            $data['description'] ?? '',
            $data['template_content'] ?? '',
            $data['category'] ?? 'General',
            $data['default_tags'] ?? '',
            $data['is_public'] ?? 0
        ];

        $stmt = $this->db->query($sql, $params);
        return $stmt ? $this->db->getConnection()->lastInsertId() : false;
    }

    /**
     * Get a specific template
     */
    public function getTemplate($templateId, $userId = null) {
        $sql = "SELECT * FROM note_templates WHERE id = ?";
        $params = [$templateId];

        if ($userId) {
            $sql .= " AND (user_id = ? OR is_public = 1)";
            $params[] = $userId;
        }

        return $this->db->fetchOne($sql, $params);
    }

    /**
     * Update template usage count
     */
    public function incrementTemplateUsage($templateId) {
        $sql = "UPDATE note_templates SET usage_count = usage_count + 1 WHERE id = ?";
        $stmt = $this->db->query($sql, [$templateId]);
        return $stmt ? $stmt->rowCount() > 0 : false;
    }

    /**
     * Create note from template
     */
    public function createFromTemplate($userId, $templateId, $customData = []) {
        $template = $this->getTemplate($templateId, $userId);
        if (!$template) {
            return false;
        }

        // Increment usage count
        $this->incrementTemplateUsage($templateId);

        // Create note with template data
        $noteData = [
            'title' => $customData['title'] ?? $template['name'],
            'content' => $customData['content'] ?? $template['template_content'],
            'category' => $customData['category'] ?? $template['category'],
            'tags' => $customData['tags'] ?? $template['default_tags'],
            'priority_level' => $customData['priority_level'] ?? 'medium'
        ];

        return $this->create($userId, $noteData);
    }

    /**
     * Advanced search with multiple criteria and analytics
     */
    public function advancedSearch($userId, $filters = []) {
        $sql = "SELECT *,
                       CASE
                           WHEN title LIKE ? THEN 1
                           WHEN content LIKE ? THEN 2
                           WHEN tags LIKE ? THEN 3
                           ELSE 4
                       END as relevance_score
                FROM {$this->table}
                WHERE user_id = ?";

        $params = [];
        $searchTerm = $filters['q'] ?? '';

        if (!empty($searchTerm)) {
            $searchParam = "%{$searchTerm}%";
            $params = [$searchParam, $searchParam, $searchParam];
            $sql .= " AND (title LIKE ? OR content LIKE ? OR tags LIKE ?)";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $params[] = $searchParam;
        }

        $params = array_merge([$searchTerm ? "%{$searchTerm}%" : '', $searchTerm ? "%{$searchTerm}%" : '', $searchTerm ? "%{$searchTerm}%" : '', $userId], $params);

        // Add filters
        if (!empty($filters['category'])) {
            $sql .= " AND category = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['priority'])) {
            $sql .= " AND priority_level = ?";
            $params[] = $filters['priority'];
        }

        if (!empty($filters['date_from'])) {
            $sql .= " AND created_at >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $sql .= " AND created_at <= ?";
            $params[] = $filters['date_to'] . ' 23:59:59';
        }

        if (isset($filters['is_favorite']) && $filters['is_favorite'] !== '') {
            $sql .= " AND is_favorite = ?";
            $params[] = $filters['is_favorite'];
        }

        if (isset($filters['is_pinned']) && $filters['is_pinned'] !== '') {
            $sql .= " AND is_pinned = ?";
            $params[] = $filters['is_pinned'];
        }

        $sql .= " ORDER BY relevance_score ASC, is_pinned DESC, is_favorite DESC, updated_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get search analytics
     */
    public function getSearchAnalytics($userId, $filters = []) {
        $analytics = [];

        // Total matching notes
        $sql = "SELECT COUNT(*) as total FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        if (!empty($filters['q'])) {
            $searchParam = "%{$filters['q']}%";
            $sql .= " AND (title LIKE ? OR content LIKE ? OR tags LIKE ?)";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $params[] = $searchParam;
        }

        $analytics['total_matches'] = $this->db->fetchOne($sql, $params)['total'];

        // Category breakdown
        $sql = "SELECT category, COUNT(*) as count FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        if (!empty($filters['q'])) {
            $searchParam = "%{$filters['q']}%";
            $sql .= " AND (title LIKE ? OR content LIKE ? OR tags LIKE ?)";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $params[] = $searchParam;
        }

        $sql .= " GROUP BY category ORDER BY count DESC";
        $analytics['by_category'] = $this->db->fetchAll($sql, $params);

        return $analytics;
    }

    /**
     * Bulk operations on notes
     */
    public function bulkAction($userId, $action, $noteIds) {
        if (empty($noteIds) || !is_array($noteIds)) {
            return false;
        }

        // Verify all notes belong to the user
        $placeholders = str_repeat('?,', count($noteIds) - 1) . '?';
        $sql = "SELECT id FROM {$this->table} WHERE user_id = ? AND id IN ($placeholders)";
        $params = array_merge([$userId], $noteIds);

        $validNotes = $this->db->fetchAll($sql, $params);
        $validNoteIds = array_column($validNotes, 'id');

        if (empty($validNoteIds)) {
            return false;
        }

        $placeholders = str_repeat('?,', count($validNoteIds) - 1) . '?';

        switch ($action) {
            case 'delete':
                $sql = "DELETE FROM {$this->table} WHERE id IN ($placeholders)";
                $stmt = $this->db->query($sql, $validNoteIds);
                return $stmt ? $stmt->rowCount() > 0 : false;

            case 'pin':
                $sql = "UPDATE {$this->table} SET is_pinned = 1 WHERE id IN ($placeholders)";
                $stmt = $this->db->query($sql, $validNoteIds);
                return $stmt ? $stmt->rowCount() > 0 : false;

            case 'unpin':
                $sql = "UPDATE {$this->table} SET is_pinned = 0 WHERE id IN ($placeholders)";
                $stmt = $this->db->query($sql, $validNoteIds);
                return $stmt ? $stmt->rowCount() > 0 : false;

            case 'favorite':
                $sql = "UPDATE {$this->table} SET is_favorite = 1 WHERE id IN ($placeholders)";
                $stmt = $this->db->query($sql, $validNoteIds);
                return $stmt ? $stmt->rowCount() > 0 : false;

            case 'unfavorite':
                $sql = "UPDATE {$this->table} SET is_favorite = 0 WHERE id IN ($placeholders)";
                $stmt = $this->db->query($sql, $validNoteIds);
                return $stmt ? $stmt->rowCount() > 0 : false;

            case 'archive':
                $sql = "UPDATE {$this->table} SET is_archived = 1 WHERE id IN ($placeholders)";
                $stmt = $this->db->query($sql, $validNoteIds);
                return $stmt ? $stmt->rowCount() > 0 : false;

            case 'unarchive':
                $sql = "UPDATE {$this->table} SET is_archived = 0 WHERE id IN ($placeholders)";
                $stmt = $this->db->query($sql, $validNoteIds);
                return $stmt ? $stmt->rowCount() > 0 : false;

            default:
                return false;
        }
    }
}
