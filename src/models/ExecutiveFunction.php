<?php
/**
 * Executive Function Model
 *
 * Handles executive function exercises and tracking
 */

require_once __DIR__ . '/BaseModel.php';

class ExecutiveFunction extends BaseModel {
    protected $table = 'executive_function_exercises';
    protected $resultsTable = 'exercise_results';
    protected $progressTable = 'executive_function_progress';

    /**
     * Get all exercises
     */
    public function getAllExercises() {
        $sql = "SELECT * FROM {$this->table} ORDER BY category, difficulty_level";
        return $this->db->fetchAll($sql);
    }

    /**
     * Get exercises by category
     */
    public function getExercisesByCategory($category) {
        $sql = "SELECT * FROM {$this->table}
                WHERE category = ?
                ORDER BY difficulty_level";

        return $this->db->fetchAll($sql, [$category]);
    }

    /**
     * Get exercises by difficulty level
     */
    public function getExercisesByDifficulty($difficultyLevel) {
        $sql = "SELECT * FROM {$this->table}
                WHERE difficulty_level = ?
                ORDER BY category";

        return $this->db->fetchAll($sql, [$difficultyLevel]);
    }

    /**
     * Get exercise by ID
     */
    public function getExercise($id) {
        return $this->find($id);
    }

    /**
     * Get recommended exercises for a user
     */
    public function getRecommendedExercises($userId, $limit = 3) {
        // Get user's executive function scores
        $sql = "SELECT
                AVG(CASE WHEN category = 'working_memory' THEN score END) as working_memory_score,
                AVG(CASE WHEN category = 'task_initiation' THEN score END) as task_initiation_score,
                AVG(CASE WHEN category = 'planning' THEN score END) as planning_score,
                AVG(CASE WHEN category = 'organization' THEN score END) as organization_score,
                AVG(CASE WHEN category = 'time_management' THEN score END) as time_management_score,
                AVG(CASE WHEN category = 'emotional_regulation' THEN score END) as emotional_regulation_score
                FROM {$this->progressTable}
                WHERE user_id = ?
                AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";

        $scores = $this->db->fetchOne($sql, [$userId]);

        if (!$scores) {
            // If no scores yet, return beginner exercises across categories
            $sql = "SELECT * FROM {$this->table}
                    WHERE difficulty_level = 1
                    ORDER BY RAND()
                    LIMIT ?";

            return $this->db->fetchAll($sql, [$limit]);
        }

        // Find the lowest scoring category
        $lowestCategory = 'working_memory';
        $lowestScore = $scores['working_memory_score'] ?? 10;

        foreach ($scores as $category => $score) {
            if ($score < $lowestScore && $score !== null) {
                $lowestCategory = str_replace('_score', '', $category);
                $lowestScore = $score;
            }
        }

        // Get appropriate difficulty level
        $difficultyLevel = 1; // Default to beginner

        if ($lowestScore >= 7) {
            $difficultyLevel = 3; // Advanced
        } else if ($lowestScore >= 4) {
            $difficultyLevel = 2; // Intermediate
        }

        // Get exercises for the lowest category at appropriate difficulty
        $sql = "SELECT * FROM {$this->table}
                WHERE category = ? AND difficulty_level = ?
                ORDER BY RAND()
                LIMIT ?";

        $exercises = $this->db->fetchAll($sql, [$lowestCategory, $difficultyLevel, $limit]);

        // If not enough exercises found, get more from other categories
        if (count($exercises) < $limit) {
            $remaining = $limit - count($exercises);

            $sql = "SELECT * FROM {$this->table}
                    WHERE category != ? AND difficulty_level = ?
                    ORDER BY RAND()
                    LIMIT ?";

            $additionalExercises = $this->db->fetchAll($sql, [$lowestCategory, $difficultyLevel, $remaining]);

            $exercises = array_merge($exercises, $additionalExercises);
        }

        return $exercises;
    }

    /**
     * Record exercise result
     */
    public function recordResult($data) {
        return $this->db->insert($this->resultsTable, $data);
    }

    /**
     * Get user's exercise results
     */
    public function getUserResults($userId, $limit = null) {
        $sql = "SELECT r.*, e.name, e.category, e.difficulty_level
                FROM {$this->resultsTable} r
                JOIN {$this->table} e ON r.exercise_id = e.id
                WHERE r.user_id = ?
                ORDER BY r.completion_date DESC, r.completion_time DESC";

        if ($limit) {
            $sql .= " LIMIT ?";
            return $this->db->fetchAll($sql, [$userId, $limit]);
        }

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get user's recent exercise results
     */
    public function getUserRecentResults($userId, $limit = 10) {
        return $this->getUserResults($userId, $limit);
    }

    /**
     * Get user's results for a specific exercise
     */
    public function getUserResultsByExercise($userId, $exerciseId) {
        $sql = "SELECT *
                FROM {$this->resultsTable}
                WHERE user_id = ? AND exercise_id = ?
                ORDER BY completion_date DESC, completion_time DESC";

        return $this->db->fetchAll($sql, [$userId, $exerciseId]);
    }

    /**
     * Update user's executive function progress
     */
    public function updateProgress($data) {
        // Check if entry exists for this date and category
        $sql = "SELECT id FROM {$this->progressTable}
                WHERE user_id = ? AND category = ? AND date = ?";

        $existing = $this->db->fetchOne($sql, [
            $data['user_id'],
            $data['category'],
            $data['date']
        ]);

        if ($existing) {
            // Update existing entry
            return $this->db->update(
                $this->progressTable,
                ['score' => $data['score'], 'updated_at' => date('Y-m-d H:i:s')],
                "id = ?",
                [$existing['id']]
            );
        } else {
            // Create new entry
            return $this->db->insert($this->progressTable, $data);
        }
    }

    /**
     * Get user's progress over time
     */
    public function getUserProgress($userId, $days = 90) {
        $sql = "SELECT
                date,
                category,
                score
                FROM {$this->progressTable}
                WHERE user_id = ?
                AND date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY date, category";

        return $this->db->fetchAll($sql, [$userId, $days]);
    }

    /**
     * Get user's current scores
     */
    public function getUserCurrentScores($userId) {
        $sql = "SELECT
                category,
                score
                FROM {$this->progressTable} p1
                WHERE user_id = ?
                AND date = (
                    SELECT MAX(date)
                    FROM {$this->progressTable} p2
                    WHERE p2.user_id = p1.user_id
                    AND p2.category = p1.category
                )
                ORDER BY category";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get user's progress by category
     */
    public function getUserProgressByCategory($userId) {
        $sql = "SELECT
                category,
                AVG(score) as avg_score,
                MAX(score) as max_score,
                MIN(score) as min_score,
                COUNT(*) as entry_count
                FROM {$this->progressTable}
                WHERE user_id = ?
                GROUP BY category
                ORDER BY category";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get exercise categories
     */
    public function getCategories() {
        $sql = "SELECT DISTINCT category FROM {$this->table} ORDER BY category";
        return $this->db->fetchAll($sql);
    }

    /**
     * Get difficulty levels
     */
    public function getDifficultyLevels() {
        $sql = "SELECT DISTINCT difficulty_level FROM {$this->table} ORDER BY difficulty_level";
        return $this->db->fetchAll($sql);
    }

    /**
     * Get user's progress over time
     */
    public function getUserProgressOverTime($userId, $days = 90) {
        $sql = "SELECT
                date,
                category,
                score
                FROM {$this->progressTable}
                WHERE user_id = ?
                AND date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY date, category";

        return $this->db->fetchAll($sql, [$userId, $days]);
    }

    /**
     * Get user's exercise completion counts
     */
    public function getUserExerciseCounts($userId) {
        $sql = "SELECT
                e.category,
                COUNT(DISTINCT r.exercise_id) as completed_exercises,
                (SELECT COUNT(*) FROM {$this->table} WHERE category = e.category) as total_exercises
                FROM {$this->resultsTable} r
                JOIN {$this->table} e ON r.exercise_id = e.id
                WHERE r.user_id = ?
                GROUP BY e.category";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Update progress for a user in a specific category
     */
    public function updateUserProgress($userId, $category, $score) {
        $data = [
            'user_id' => $userId,
            'category' => $category,
            'date' => date('Y-m-d'),
            'score' => $score,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->updateProgress($data);
    }

    /**
     * Save exercise result
     */
    public function saveResult($data) {
        return $this->db->insert($this->resultsTable, $data);
    }
}
