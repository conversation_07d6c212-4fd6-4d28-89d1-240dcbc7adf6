<?php
/**
 * Checklist Model
 *
 * Handles the creation, retrieval, and management of checklists for the AI Agent Army
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/ChecklistItem.php';
require_once __DIR__ . '/ChecklistTemplate.php';

class Checklist extends BaseModel {
    /**
     * Create a new checklist
     *
     * @param array $data Checklist data
     * @return int|false The ID of the created checklist or false on failure
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO checklists (
                user_id,
                project_id,
                template_id,
                name,
                description,
                checklist_type,
                status,
                created_at,
                updated_at
            ) VALUES (
                :user_id,
                :project_id,
                :template_id,
                :name,
                :description,
                :checklist_type,
                :status,
                :created_at,
                :updated_at
            )";

            $params = [
                ':user_id' => $data['user_id'],
                ':project_id' => $data['project_id'] ?? null,
                ':template_id' => $data['template_id'] ?? null,
                ':name' => $data['name'],
                ':description' => $data['description'] ?? '',
                ':checklist_type' => $data['checklist_type'] ?? 'standard',
                ':status' => $data['status'] ?? 'active',
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ];

            $checklistId = $this->db->insert($sql, $params);

            if ($checklistId && isset($data['items']) && is_array($data['items'])) {
                $this->addItemsToChecklist($checklistId, $data['items']);
            } elseif ($checklistId && isset($data['template_id']) && $data['template_id']) {
                $this->populateFromTemplate($checklistId, $data['template_id']);
            }

            return $checklistId;
        } catch (Exception $e) {
            error_log("Error creating checklist: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Add items to a checklist
     *
     * @param int $checklistId Checklist ID
     * @param array $items Array of checklist items
     * @return bool Success or failure
     */
    public function addItemsToChecklist($checklistId, $items) {
        try {
            $checklistItemModel = new ChecklistItem();

            foreach ($items as $index => $item) {
                $itemData = [
                    'checklist_id' => $checklistId,
                    'parent_id' => $item['parent_id'] ?? null,
                    'text' => $item['text'],
                    'description' => $item['description'] ?? '',
                    'status' => $item['status'] ?? 'pending',
                    'sort_order' => $item['sort_order'] ?? $index,
                    'due_date' => $item['due_date'] ?? null,
                    'assigned_to' => $item['assigned_to'] ?? null,
                    'resource_link' => $item['resource_link'] ?? null
                ];

                $itemId = $checklistItemModel->create($itemData);

                if ($itemId && isset($item['sub_items']) && is_array($item['sub_items'])) {
                    foreach ($item['sub_items'] as $subIndex => $subItem) {
                        $subItemData = [
                            'checklist_id' => $checklistId,
                            'parent_id' => $itemId,
                            'text' => $subItem['text'],
                            'description' => $subItem['description'] ?? '',
                            'status' => $subItem['status'] ?? 'pending',
                            'sort_order' => $subItem['sort_order'] ?? $subIndex,
                            'due_date' => $subItem['due_date'] ?? null,
                            'assigned_to' => $subItem['assigned_to'] ?? null,
                            'resource_link' => $subItem['resource_link'] ?? null
                        ];

                        $checklistItemModel->create($subItemData);
                    }
                }
            }

            return true;
        } catch (Exception $e) {
            error_log("Error adding items to checklist: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Populate a checklist from a template
     *
     * @param int $checklistId Checklist ID
     * @param int $templateId Template ID
     * @return bool Success or failure
     */
    public function populateFromTemplate($checklistId, $templateId) {
        try {
            $templateModel = new ChecklistTemplate();
            $template = $templateModel->getTemplateWithItems($templateId);

            if (!$template || !isset($template['items']) || !is_array($template['items'])) {
                return false;
            }

            return $this->addItemsToChecklist($checklistId, $template['items']);
        } catch (Exception $e) {
            error_log("Error populating checklist from template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a checklist by ID
     *
     * @param int $checklistId Checklist ID
     * @param int $userId User ID
     * @return array|false Checklist data or false if not found
     */
    public function getChecklist($checklistId, $userId) {
        try {
            $sql = "SELECT * FROM checklists WHERE id = :id AND user_id = :user_id";
            $checklist = $this->db->fetchOne($sql, [':id' => $checklistId, ':user_id' => $userId]);

            if (!$checklist) {
                return false;
            }

            $checklist['items'] = $this->getChecklistItems($checklistId);
            return $checklist;
        } catch (Exception $e) {
            error_log("Error getting checklist: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all items for a checklist
     *
     * @param int $checklistId Checklist ID
     * @return array Array of checklist items
     */
    public function getChecklistItems($checklistId) {
        try {
            $checklistItemModel = new ChecklistItem();
            return $checklistItemModel->getItemsByChecklist($checklistId);
        } catch (Exception $e) {
            error_log("Error getting checklist items: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all checklists for a user
     *
     * @param int $userId User ID
     * @param string $status Optional status filter
     * @return array Array of checklists
     */
    public function getUserChecklists($userId, $status = null) {
        try {
            $sql = "SELECT * FROM checklists WHERE user_id = :user_id";
            $params = [':user_id' => $userId];

            if ($status) {
                $sql .= " AND status = :status";
                $params[':status'] = $status;
            }

            $sql .= " ORDER BY created_at DESC";

            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Error getting user checklists: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all checklists for a project
     *
     * @param int $projectId Project ID
     * @param int $userId User ID
     * @return array Array of checklists
     */
    public function getProjectChecklists($projectId, $userId) {
        try {
            $sql = "SELECT * FROM checklists WHERE project_id = :project_id AND user_id = :user_id ORDER BY created_at DESC";
            return $this->db->fetchAll($sql, [':project_id' => $projectId, ':user_id' => $userId]);
        } catch (Exception $e) {
            error_log("Error getting project checklists: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update a checklist
     *
     * @param int $checklistId Checklist ID
     * @param array $data Checklist data to update
     * @return bool Success or failure
     */
    public function update($checklistId, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $checklistId];

            $allowedFields = ['name', 'description', 'status', 'checklist_type', 'project_id'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }

            if (empty($updateFields)) {
                return false;
            }

            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');

            $sql = "UPDATE checklists SET " . implode(', ', $updateFields) . " WHERE id = :id";

            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating checklist: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a checklist if it belongs to the user
     *
     * @param int $checklistId Checklist ID
     * @param int $userId User ID
     * @return bool Success or failure
     */
    public function deleteIfOwner($checklistId, $userId) {
        try {
            // First, delete all checklist items
            $checklistItemModel = new ChecklistItem();
            $checklistItemModel->deleteAllItems($checklistId);

            // Then delete the checklist
            $sql = "DELETE FROM checklists WHERE id = :id AND user_id = :user_id";
            return $this->db->execute($sql, [':id' => $checklistId, ':user_id' => $userId]);
        } catch (Exception $e) {
            error_log("Error deleting checklist: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a checklist
     *
     * @param int $id Checklist ID
     * @return bool Success or failure
     */
    public function delete($id) {
        try {
            // First, delete all checklist items
            $checklistItemModel = new ChecklistItem();
            $checklistItemModel->deleteAllItems($id);

            // Then delete the checklist
            $sql = "DELETE FROM checklists WHERE id = :id";
            return $this->db->execute($sql, [':id' => $id]);
        } catch (Exception $e) {
            error_log("Error deleting checklist: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get checklist completion statistics
     *
     * @param int $checklistId Checklist ID
     * @return array Completion statistics
     */
    public function getCompletionStats($checklistId) {
        try {
            $items = $this->getChecklistItems($checklistId);

            $total = count($items);
            $completed = 0;
            $inProgress = 0;
            $pending = 0;

            foreach ($items as $item) {
                switch ($item['status']) {
                    case 'completed':
                        $completed++;
                        break;
                    case 'in_progress':
                        $inProgress++;
                        break;
                    case 'pending':
                    default:
                        $pending++;
                        break;
                }
            }

            return [
                'total' => $total,
                'completed' => $completed,
                'in_progress' => $inProgress,
                'pending' => $pending,
                'completion_percentage' => $total > 0 ? round(($completed / $total) * 100) : 0
            ];
        } catch (Exception $e) {
            error_log("Error getting checklist completion stats: " . $e->getMessage());
            return [
                'total' => 0,
                'completed' => 0,
                'in_progress' => 0,
                'pending' => 0,
                'completion_percentage' => 0
            ];
        }
    }
}
