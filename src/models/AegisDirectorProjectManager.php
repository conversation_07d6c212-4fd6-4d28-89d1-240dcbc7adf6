<?php
/**
 * Aegis Director Project Manager
 *
 * Handles the integration between the Aegis Director agent and the project management system
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/Project.php';
require_once __DIR__ . '/Task.php';
require_once __DIR__ . '/AIAgent.php';
require_once __DIR__ . '/AIAgentTask.php';
require_once __DIR__ . '/AIAgentInteraction.php';
require_once __DIR__ . '/ProjectAgentAssignment.php';
require_once __DIR__ . '/BrigadeTemplateManager.php';

class AegisDirectorProjectManager extends BaseModel {
    protected $table = null; // This is a service class, not directly tied to a table

    protected $projectModel;
    protected $taskModel;
    protected $agentModel;
    protected $agentTaskModel;
    protected $interactionModel;
    protected $assignmentModel;
    protected $brigadeTemplateManager;

    public function __construct() {
        parent::__construct();
        $this->projectModel = new Project();
        $this->taskModel = new Task();
        $this->agentModel = new AIAgent();
        $this->agentTaskModel = new AIAgentTask();
        $this->interactionModel = new AIAgentInteraction();
        $this->assignmentModel = new ProjectAgentAssignment();
        $this->brigadeTemplateManager = new BrigadeTemplateManager();
    }

    /**
     * Get the Aegis Director agent for a user
     *
     * @param int $userId User ID
     * @return array|false Agent data or false if not found
     */
    public function getAegisDirectorAgent($userId) {
        // First try to find the agent for the specified user
        $agents = $this->agentModel->getUserAgents($userId);

        foreach ($agents as $agent) {
            if ($agent['name'] === 'Aegis Director') {
                return $agent;
            }
        }

        // If not found for the specified user, try to find it for user ID 1 (admin/default user)
        if ($userId != 1) {
            $adminAgents = $this->agentModel->getUserAgents(1);

            foreach ($adminAgents as $agent) {
                if ($agent['name'] === 'Aegis Director') {
                    // Log that we're using the admin's agent
                    error_log("Using admin's Aegis Director agent for user {$userId}");
                    return $agent;
                }
            }
        }

        // If still not found, try to get it directly by name without user restriction
        $sql = "SELECT a.*, c.name as category_name, c.color as category_color, c.icon as category_icon
                FROM ai_agents a
                LEFT JOIN ai_agent_categories c ON a.category_id = c.id
                WHERE a.name = 'Aegis Director'
                LIMIT 1";

        $agent = $this->db->fetchOne($sql, []);

        if ($agent) {
            error_log("Found Aegis Director agent without user restriction");
            return $agent;
        }

        error_log("Aegis Director agent not found for user {$userId}");
        return false;
    }

    /**
     * Ensure the Aegis Director agent exists for a user, creating it if it doesn't
     *
     * @param int $userId User ID
     * @return array|false Agent data or false on failure
     */
    public function ensureAegisDirectorAgent($userId) {
        // Try to get the existing agent
        $agent = $this->getAegisDirectorAgent($userId);

        if ($agent) {
            return $agent;
        }

        // Agent doesn't exist, so create it
        error_log("Creating Aegis Director agent for user {$userId}");

        // Get the Executive Function category ID, or create it if it doesn't exist
        $sql = "SELECT id FROM ai_agent_categories WHERE name = 'Executive Function' LIMIT 1";
        $category = $this->db->fetchOne($sql, []);

        $categoryId = null;
        if ($category) {
            $categoryId = $category['id'];
        } else {
            // Create the category
            $categoryData = [
                'name' => 'Executive Function',
                'description' => 'Agents that help with planning, organization, and task management',
                'color' => '#4A6FDC',
                'icon' => 'brain',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $categoryId = $this->db->insert('ai_agent_categories', $categoryData);

            if (!$categoryId) {
                error_log("Failed to create Executive Function category");
                return false;
            }
        }

        // Create the Aegis Director agent
        $agentData = [
            'user_id' => $userId,
            'name' => 'Aegis Director',
            'description' => 'The Aegis Director is an AI agent designed to help with ADHD executive functioning challenges. It provides unyielding focus, proactive planning, constructive intervention, accountability, time management, and motivational rigor.',
            'category_id' => $categoryId,
            'model' => 'gpt-4',
            'temperature' => 0.7,
            'max_tokens' => 2000,
            'system_prompt' => "You are the Aegis Director, an AI agent specifically designed to help users with ADHD executive functioning challenges. Your primary role is to provide structure, discipline, and accountability. You have the following capabilities:\n\n1. Unyielding Focus: You help the user maintain focus on their priorities and prevent distractions.\n2. Proactive Planning: You assist with breaking down complex tasks into manageable steps.\n3. Constructive Intervention: You step in when you detect the user is getting off track.\n4. Accountability: You hold the user accountable for their commitments and deadlines.\n5. Time Management: You help the user manage their time effectively and avoid procrastination.\n6. Motivational Rigor: You provide firm but supportive motivation to keep the user moving forward.\n\nYour tone is direct, clear, and authoritative but not condescending. You are a supportive ally who understands the challenges of ADHD but doesn't accept excuses. You balance empathy with high expectations.",
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $agentId = $this->agentModel->create($agentData);

        if (!$agentId) {
            error_log("Failed to create Aegis Director agent");
            return false;
        }

        // Get the newly created agent
        return $this->getAegisDirectorAgent($userId);
    }

    /**
     * Create a rapid implementation project
     *
     * @param int $userId User ID
     * @param string $projectName Project name
     * @param string $projectDescription Project description
     * @param string $deadline Project deadline (YYYY-MM-DD)
     * @return int|false Project ID or false on failure
     */
    public function createRapidImplementationProject($userId, $projectName, $projectDescription, $deadline) {
        // Create the project
        $projectData = [
            'user_id' => $userId,
            'name' => $projectName,
            'description' => $projectDescription,
            'start_date' => date('Y-m-d'),
            'end_date' => $deadline,
            'status' => 'in_progress',
            'progress' => 0,
            'is_template' => false,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $projectId = $this->projectModel->create($projectData);

        if (!$projectId) {
            return false;
        }

        // Get or create the Aegis Director agent
        $aegisDirector = $this->ensureAegisDirectorAgent($userId);

        if (!$aegisDirector) {
            error_log("Failed to get or create Aegis Director agent for user {$userId}");
            return $projectId; // Return project ID even if agent not found
        }

        error_log("Using Aegis Director agent ID: {$aegisDirector['id']} for user {$userId}");

        // Assign Aegis Director to the project
        $this->assignmentModel->assignAgentToProject($projectId, $aegisDirector['id'], 'Project Manager');

        // Create a system interaction to record the project creation
        $this->interactionModel->createInteraction([
            'agent_id' => $aegisDirector['id'],
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => "Created rapid implementation project: {$projectName}",
            'response' => "I will help you complete this project by {$deadline}. Let's break it down into actionable tasks and get started immediately.",
            'success' => true,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        return $projectId;
    }

    /**
     * Create the 24-hour implementation plan
     *
     * @param int $projectId Project ID
     * @param int $userId User ID
     * @return bool Success or failure
     */
    public function create24HourImplementationPlan($projectId, $userId) {
        // Get the project
        $project = $this->projectModel->getProjectDetails($projectId, $userId);

        if (!$project) {
            return false;
        }

        // Get or create the Aegis Director agent
        $aegisDirector = $this->ensureAegisDirectorAgent($userId);

        if (!$aegisDirector) {
            error_log("Failed to get or create Aegis Director agent for user {$userId}");
            return false;
        }

        error_log("Using Aegis Director agent ID: {$aegisDirector['id']} for user {$userId}");

        // Create tasks for the 24-hour implementation plan
        $tasks = [
            [
                'title' => '1. Define clear project objectives and success metrics',
                'description' => 'Clearly articulate what you want to accomplish in 24 hours and how you will measure success.',
                'priority' => 'high',
                'estimated_time' => 30, // minutes
                'status' => 'todo'
            ],
            [
                'title' => '2. Identify minimum viable deliverables',
                'description' => 'Determine the absolute minimum features or components needed for a functional implementation.',
                'priority' => 'high',
                'estimated_time' => 30,
                'status' => 'todo'
            ],
            [
                'title' => '3. Break down implementation into 1-hour blocks',
                'description' => 'Create a detailed hour-by-hour schedule for the entire 24-hour period.',
                'priority' => 'high',
                'estimated_time' => 60,
                'status' => 'todo'
            ],
            [
                'title' => '4. Prepare development environment and tools',
                'description' => 'Set up all necessary software, accounts, and resources needed for implementation.',
                'priority' => 'high',
                'estimated_time' => 60,
                'status' => 'todo'
            ],
            [
                'title' => '5. Implement core functionality (Phase 1)',
                'description' => 'Focus on the most critical features first.',
                'priority' => 'high',
                'estimated_time' => 240,
                'status' => 'todo'
            ],
            [
                'title' => '6. Test core functionality',
                'description' => 'Verify that the core features work as expected.',
                'priority' => 'high',
                'estimated_time' => 60,
                'status' => 'todo'
            ],
            [
                'title' => '7. Implement secondary features (Phase 2)',
                'description' => 'Add important but non-critical features.',
                'priority' => 'medium',
                'estimated_time' => 240,
                'status' => 'todo'
            ],
            [
                'title' => '8. Integrate with existing systems',
                'description' => 'Connect with any necessary external services or APIs.',
                'priority' => 'medium',
                'estimated_time' => 120,
                'status' => 'todo'
            ],
            [
                'title' => '9. Comprehensive testing',
                'description' => 'Test all features and fix critical bugs.',
                'priority' => 'high',
                'estimated_time' => 120,
                'status' => 'todo'
            ],
            [
                'title' => '10. Documentation and deployment preparation',
                'description' => 'Create basic documentation and prepare for deployment.',
                'priority' => 'medium',
                'estimated_time' => 60,
                'status' => 'todo'
            ],
            [
                'title' => '11. Final review and deployment',
                'description' => 'Conduct a final review and deploy the implementation.',
                'priority' => 'high',
                'estimated_time' => 60,
                'status' => 'todo'
            ],
            [
                'title' => '12. Post-implementation review',
                'description' => 'Evaluate what was accomplished and identify next steps.',
                'priority' => 'medium',
                'estimated_time' => 30,
                'status' => 'todo'
            ]
        ];

        // Create the tasks
        $taskIds = [];
        foreach ($tasks as $taskData) {
            $taskData['user_id'] = $userId;
            $taskData['project_id'] = $projectId;
            $taskData['created_at'] = date('Y-m-d H:i:s');
            $taskData['updated_at'] = date('Y-m-d H:i:s');

            $taskId = $this->taskModel->create($taskData);
            if ($taskId) {
                $taskIds[] = $taskId;

                // Create agent task for Aegis Director
                $this->agentTaskModel->createTask([
                    'agent_id' => $aegisDirector['id'],
                    'user_id' => $userId,
                    'title' => "Monitor: " . $taskData['title'],
                    'description' => "Ensure completion of task: " . $taskData['description'],
                    'priority' => $taskData['priority'],
                    'status' => 'pending',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }

        // Create dependencies between tasks (sequential)
        for ($i = 0; $i < count($taskIds) - 1; $i++) {
            $this->taskModel->addDependency($taskIds[$i + 1], $taskIds[$i]);
        }

        // Create a system interaction to record the plan creation
        $this->interactionModel->createInteraction([
            'agent_id' => $aegisDirector['id'],
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => "Created 24-hour implementation plan for project: {$project['name']}",
            'response' => "I've created a detailed 24-hour implementation plan with 12 key tasks. Each task has been assigned a priority and estimated time. I'll monitor your progress and help keep you on track to complete this project within 24 hours.",
            'success' => true,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        return true;
    }

    /**
     * Create an AI Agent Army implementation project
     *
     * @param int $userId User ID
     * @param string $brigadeType Brigade type (content_creation, lead_generation, customer_support, data_analysis)
     * @param string $projectName Project name
     * @param string $projectDescription Project description
     * @param string $deadline Project deadline (YYYY-MM-DD)
     * @return int|false Project ID or false on failure
     */
    public function createAgentArmyProject($userId, $brigadeType, $projectName, $projectDescription, $deadline) {
        try {
            error_log("Creating AI Agent Army project for user {$userId} with brigade type {$brigadeType}");

            // Step 1: Create the project using the brigade template manager
            $projectId = $this->brigadeTemplateManager->createProjectFromTemplate(
                $userId,
                $brigadeType,
                $projectName,
                $projectDescription,
                $deadline
            );

            if (!$projectId) {
                error_log("Failed to create project from template");
                return false;
            }

            error_log("Created project with ID: {$projectId}");

            // Step 2: Get or create the Aegis Director agent
            $aegisDirector = $this->ensureAegisDirectorAgent($userId);

            if (!$aegisDirector) {
                error_log("Failed to get or create Aegis Director agent for user {$userId}");
                return $projectId; // Return project ID even if agent not found
            }

            error_log("Using Aegis Director agent ID: {$aegisDirector['id']} for user {$userId}");

            // Step 3: Assign Aegis Director to the project as Brigade Commander
            $assignmentResult = $this->assignmentModel->assignAgentToProject($projectId, $aegisDirector['id'], 'Brigade Commander');

            if (!$assignmentResult) {
                error_log("Failed to assign Aegis Director to the project");
            } else {
                error_log("Assigned Aegis Director to the project successfully");
            }

            // Step 4: Create a system interaction to record the project creation
            $interactionData = [
                'agent_id' => $aegisDirector['id'],
                'user_id' => $userId,
                'interaction_type' => 'system',
                'content' => "Created AI Agent Army project: {$projectName} (Brigade Type: " . ucfirst(str_replace('_', ' ', $brigadeType)) . ")",
                'response' => "I've created your {$brigadeType} brigade project. I'll help you assemble the right agents for each role and ensure this project is completed by {$deadline}.",
                'success' => 1, // Use integer instead of boolean
                'created_at' => date('Y-m-d H:i:s')
            ];

            $interactionResult = $this->interactionModel->createInteraction($interactionData);

            if (!$interactionResult) {
                error_log("Failed to create system interaction for project creation");
            } else {
                error_log("Created system interaction for project creation with ID: {$interactionResult}");
            }

            // Step 5: Assign agents to brigade roles
            $roleAssignments = [
                'brigade_commander' => $aegisDirector['id'] // Assign Aegis Director as Brigade Commander
            ];

            $assignmentResult = $this->brigadeTemplateManager->assignAgentsToBrigadeRoles(
                $projectId,
                $brigadeType,
                $userId,
                $roleAssignments
            );

            if (!$assignmentResult) {
                error_log("Failed to assign agents to brigade roles for project {$projectId}");
            } else {
                error_log("Successfully assigned agents to brigade roles for project {$projectId}");
            }

            return $projectId;

        } catch (Exception $e) {
            echo "Exception in createAgentArmyProject: " . $e->getMessage() . "\n";
            error_log("Exception in createAgentArmyProject: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Get projects managed by Aegis Director
     *
     * @param int $userId User ID
     * @return array Array of projects
     */
    public function getAegisDirectorProjects($userId) {
        // Get or create the Aegis Director agent
        $aegisDirector = $this->ensureAegisDirectorAgent($userId);

        if (!$aegisDirector) {
            error_log("Failed to get or create Aegis Director agent for user {$userId}");
            return [];
        }

        error_log("Using Aegis Director agent ID: {$aegisDirector['id']} for user {$userId}");

        // Get projects where Aegis Director is assigned
        return $this->assignmentModel->getAgentProjects($aegisDirector['id']);
    }

    /**
     * Get available brigade types
     *
     * @return array Array of brigade types
     */
    public function getAvailableBrigadeTypes() {
        return [
            'content_creation' => 'Content Creation Brigade',
            'lead_generation' => 'Lead Generation Brigade',
            'customer_support' => 'Customer Support Brigade',
            'data_analysis' => 'Data Analysis Brigade'
        ];
    }

    /**
     * Get brigade template details
     *
     * @param string $brigadeType Brigade type
     * @return array|null Brigade template details or null if not found
     */
    public function getBrigadeTemplateDetails($brigadeType) {
        return $this->brigadeTemplateManager->getBrigadeTemplate($brigadeType);
    }

    /**
     * Create default tasks for an AI Agent Army brigade
     *
     * @param int $projectId Project ID
     * @param string $brigadeType Brigade type (content_creation, lead_generation, customer_support, data_analysis)
     * @param int $userId User ID
     * @return bool Success or failure
     */
    public function createBrigadeTasks($projectId, $brigadeType, $userId) {
        try {
            // Get the project
            $project = $this->projectModel->getProjectDetails($projectId, $userId);

            if (!$project) {
                error_log("Project not found: {$projectId} for user {$userId}");
                return false;
            }

            // Get or create the Aegis Director agent
            $aegisDirector = $this->ensureAegisDirectorAgent($userId);

            if (!$aegisDirector) {
                error_log("Failed to get or create Aegis Director agent for user {$userId}");
                return false;
            }

            error_log("Using Aegis Director agent ID: {$aegisDirector['id']} for user {$userId}");
            error_log("Creating tasks for {$brigadeType} brigade in project {$projectId}");

            // Define tasks based on brigade type
            $tasks = [];

            switch ($brigadeType) {
            case 'content_creation':
                $tasks = [
                    [
                        'title' => '1. Define content strategy and goals',
                        'description' => 'Establish clear objectives, target audience, and key performance indicators for the content.',
                        'priority' => 'high',
                        'estimated_time' => 120, // minutes
                        'status' => 'todo'
                    ],
                    [
                        'title' => '2. Conduct content audit and gap analysis',
                        'description' => 'Analyze existing content and identify opportunities for new content.',
                        'priority' => 'high',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '3. Develop content calendar',
                        'description' => 'Create a schedule for content creation and publication.',
                        'priority' => 'high',
                        'estimated_time' => 120,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '4. Set up content creation workflow',
                        'description' => 'Establish processes for ideation, creation, review, and publication.',
                        'priority' => 'medium',
                        'estimated_time' => 90,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '5. Create content templates and guidelines',
                        'description' => 'Develop standardized formats and style guides for consistent content.',
                        'priority' => 'medium',
                        'estimated_time' => 120,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '6. Implement AI content generation tools',
                        'description' => 'Set up and configure AI tools for content creation and optimization.',
                        'priority' => 'high',
                        'estimated_time' => 240,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '7. Develop content distribution strategy',
                        'description' => 'Plan how content will be shared across different channels.',
                        'priority' => 'medium',
                        'estimated_time' => 120,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '8. Set up content performance tracking',
                        'description' => 'Implement analytics to measure content effectiveness.',
                        'priority' => 'medium',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ]
                ];
                break;

            case 'lead_generation':
                $tasks = [
                    [
                        'title' => '1. Define ideal customer profile and buyer personas',
                        'description' => 'Create detailed profiles of target customers and decision-makers.',
                        'priority' => 'high',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '2. Set up lead generation infrastructure',
                        'description' => 'Implement tools and systems for capturing and managing leads.',
                        'priority' => 'high',
                        'estimated_time' => 240,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '3. Develop lead magnet strategy',
                        'description' => 'Create valuable resources to attract potential leads.',
                        'priority' => 'high',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '4. Implement AI-powered lead scoring',
                        'description' => 'Set up systems to evaluate and prioritize leads based on likelihood to convert.',
                        'priority' => 'medium',
                        'estimated_time' => 210,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '5. Create automated lead nurturing sequences',
                        'description' => 'Develop email and content sequences to move leads through the funnel.',
                        'priority' => 'high',
                        'estimated_time' => 240,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '6. Set up lead analytics and reporting',
                        'description' => 'Implement systems to track lead sources, conversions, and ROI.',
                        'priority' => 'medium',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '7. Develop AI-assisted outreach campaigns',
                        'description' => 'Create personalized outreach strategies using AI tools.',
                        'priority' => 'high',
                        'estimated_time' => 210,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '8. Implement lead handoff process',
                        'description' => 'Establish protocols for transferring qualified leads to sales.',
                        'priority' => 'medium',
                        'estimated_time' => 120,
                        'status' => 'todo'
                    ]
                ];
                break;

            case 'customer_support':
                $tasks = [
                    [
                        'title' => '1. Map customer support journey and touchpoints',
                        'description' => 'Identify all stages where customers need support and assistance.',
                        'priority' => 'high',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '2. Set up knowledge base and documentation',
                        'description' => 'Create comprehensive self-service resources for customers.',
                        'priority' => 'high',
                        'estimated_time' => 240,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '3. Implement AI chatbot for first-line support',
                        'description' => 'Deploy and train an AI assistant to handle common customer inquiries.',
                        'priority' => 'high',
                        'estimated_time' => 300,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '4. Develop automated ticket routing system',
                        'description' => 'Create intelligent workflows to direct complex issues to the right specialists.',
                        'priority' => 'medium',
                        'estimated_time' => 210,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '5. Set up sentiment analysis for customer interactions',
                        'description' => 'Implement tools to monitor customer satisfaction in real-time.',
                        'priority' => 'medium',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '6. Create proactive support protocols',
                        'description' => 'Develop systems to identify and address potential issues before customers report them.',
                        'priority' => 'high',
                        'estimated_time' => 210,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '7. Implement support analytics dashboard',
                        'description' => 'Set up reporting to track key support metrics and identify improvement opportunities.',
                        'priority' => 'medium',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '8. Develop continuous improvement framework',
                        'description' => 'Create processes for regularly enhancing support based on data and feedback.',
                        'priority' => 'medium',
                        'estimated_time' => 150,
                        'status' => 'todo'
                    ]
                ];
                break;

            case 'data_analysis':
                $tasks = [
                    [
                        'title' => '1. Define data analysis objectives and KPIs',
                        'description' => 'Establish clear goals and metrics for the data analysis brigade.',
                        'priority' => 'high',
                        'estimated_time' => 120,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '2. Set up data collection infrastructure',
                        'description' => 'Implement systems to gather and store relevant data from various sources.',
                        'priority' => 'high',
                        'estimated_time' => 240,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '3. Develop data cleaning and preparation protocols',
                        'description' => 'Create automated processes for ensuring data quality and consistency.',
                        'priority' => 'high',
                        'estimated_time' => 210,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '4. Implement AI-powered data analysis tools',
                        'description' => 'Set up machine learning and statistical analysis capabilities.',
                        'priority' => 'high',
                        'estimated_time' => 300,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '5. Create automated reporting systems',
                        'description' => 'Develop dashboards and regular reports for key stakeholders.',
                        'priority' => 'medium',
                        'estimated_time' => 240,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '6. Set up predictive analytics capabilities',
                        'description' => 'Implement forecasting and trend analysis functionality.',
                        'priority' => 'high',
                        'estimated_time' => 270,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '7. Develop data visualization library',
                        'description' => 'Create templates and tools for effectively communicating insights visually.',
                        'priority' => 'medium',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '8. Implement insight-to-action framework',
                        'description' => 'Establish processes for converting data insights into actionable business decisions.',
                        'priority' => 'high',
                        'estimated_time' => 210,
                        'status' => 'todo'
                    ]
                ];
                break;

            default:
                // Generic tasks for any brigade type
                $tasks = [
                    [
                        'title' => '1. Define brigade objectives and strategy',
                        'description' => 'Establish clear goals and approach for the brigade.',
                        'priority' => 'high',
                        'estimated_time' => 120,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '2. Set up brigade infrastructure',
                        'description' => 'Implement necessary tools and systems.',
                        'priority' => 'high',
                        'estimated_time' => 180,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '3. Develop brigade workflows',
                        'description' => 'Create processes for efficient operation.',
                        'priority' => 'high',
                        'estimated_time' => 150,
                        'status' => 'todo'
                    ],
                    [
                        'title' => '4. Implement performance tracking',
                        'description' => 'Set up systems to measure brigade effectiveness.',
                        'priority' => 'medium',
                        'estimated_time' => 120,
                        'status' => 'todo'
                    ]
                ];
        }

        // Create the tasks
        $taskIds = [];
        foreach ($tasks as $taskData) {
            $taskData['user_id'] = $userId;
            $taskData['project_id'] = $projectId;
            $taskData['created_at'] = date('Y-m-d H:i:s');
            $taskData['updated_at'] = date('Y-m-d H:i:s');

            $taskId = $this->taskModel->create($taskData);
            if ($taskId) {
                $taskIds[] = $taskId;

                // Create agent task for Aegis Director to monitor this task
                $this->agentTaskModel->createTask([
                    'agent_id' => $aegisDirector['id'],
                    'user_id' => $userId,
                    'title' => "Monitor: " . $taskData['title'],
                    'description' => "Ensure completion of task: " . $taskData['description'],
                    'priority' => $taskData['priority'],
                    'status' => 'pending',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }

        // Create dependencies between tasks (sequential)
        for ($i = 0; $i < count($taskIds) - 1; $i++) {
            $this->taskModel->addDependency($taskIds[$i + 1], $taskIds[$i]);
        }

        // Create a system interaction to record the task creation
        $interactionResult = $this->interactionModel->createInteraction([
            'agent_id' => $aegisDirector['id'],
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => "Created tasks for " . ucfirst(str_replace('_', ' ', $brigadeType)) . " Brigade project: {$project['name']}",
            'response' => "I've created " . count($tasks) . " tasks for your " . ucfirst(str_replace('_', ' ', $brigadeType)) . " Brigade. Each task has been assigned a priority and estimated time. I'll monitor your progress and help keep you on track to complete this project by the deadline.",
            'success' => true,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        if (!$interactionResult) {
            error_log("Failed to create system interaction for brigade tasks");
        } else {
            error_log("Created system interaction for brigade tasks with ID: {$interactionResult}");
        }

        error_log("Successfully created " . count($taskIds) . " tasks for {$brigadeType} brigade in project {$projectId}");
        return count($taskIds) > 0;

        } catch (Exception $e) {
            error_log("Exception in createBrigadeTasks: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Generate a progress report for a project
     *
     * @param int $projectId Project ID
     * @param int $userId User ID
     * @return string Progress report
     */
    public function generateProjectProgressReport($projectId, $userId) {
        // Get the project
        try {
            // Try to get the project directly from the database
            $sql = "SELECT * FROM projects WHERE id = ? AND user_id = ?";
            $project = $this->db->fetchOne($sql, [$projectId, $userId]);

            if (!$project) {
                // Try to get the project without user restriction
                $sql = "SELECT * FROM projects WHERE id = ?";
                $project = $this->db->fetchOne($sql, [$projectId]);

                if (!$project) {
                    return "Project not found with ID: {$projectId}.";
                }
            }
        } catch (Exception $e) {
            error_log("Exception in generateProjectProgressReport: " . $e->getMessage());
            return "Error retrieving project: " . $e->getMessage();
        }

        // Get project tasks
        $tasks = $this->taskModel->getProjectTasks($projectId);

        // Calculate statistics
        $totalTasks = count($tasks);
        $completedTasks = 0;
        $inProgressTasks = 0;
        $todoTasks = 0;
        $overdueTasks = 0;

        foreach ($tasks as $task) {
            if ($task['status'] === 'done') {
                $completedTasks++;
            } elseif ($task['status'] === 'in_progress') {
                $inProgressTasks++;
            } elseif ($task['status'] === 'todo') {
                $todoTasks++;

                // Check if task is overdue
                if (!empty($task['due_date']) && strtotime($task['due_date']) < time()) {
                    $overdueTasks++;
                }
            }
        }

        // Generate report
        $report = "# Project Progress Report: {$project['name']}\n\n";
        $report .= "## Overview\n";
        $report .= "- **Progress**: {$project['progress']}%\n";
        $report .= "- **Status**: " . ucfirst($project['status']) . "\n";
        $report .= "- **Start Date**: " . date('Y-m-d', strtotime($project['start_date'])) . "\n";
        $report .= "- **End Date**: " . ($project['end_date'] ? date('Y-m-d', strtotime($project['end_date'])) : 'Not set') . "\n\n";

        $report .= "## Task Status\n";
        $report .= "- **Total Tasks**: {$totalTasks}\n";
        $report .= "- **Completed**: {$completedTasks} (" . ($totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100) : 0) . "%)\n";
        $report .= "- **In Progress**: {$inProgressTasks} (" . ($totalTasks > 0 ? round(($inProgressTasks / $totalTasks) * 100) : 0) . "%)\n";
        $report .= "- **To Do**: {$todoTasks} (" . ($totalTasks > 0 ? round(($todoTasks / $totalTasks) * 100) : 0) . "%)\n";
        $report .= "- **Overdue**: {$overdueTasks}\n\n";

        $report .= "## Next Steps\n";

        // Identify next steps (tasks that are ready to be worked on)
        $nextSteps = [];
        foreach ($tasks as $task) {
            if ($task['status'] === 'todo') {
                // Check if all dependencies are completed
                $dependencies = $this->taskModel->getTaskDependencies($task['id']);
                $allDependenciesCompleted = true;

                foreach ($dependencies as $dependency) {
                    $dependencyTask = $this->taskModel->getTask($dependency['dependency_id'], $userId);
                    if ($dependencyTask && $dependencyTask['status'] !== 'done') {
                        $allDependenciesCompleted = false;
                        break;
                    }
                }

                if ($allDependenciesCompleted) {
                    $nextSteps[] = $task;
                }
            }
        }

        if (empty($nextSteps)) {
            $report .= "No tasks are currently ready to be worked on. All tasks are either completed, in progress, or blocked by dependencies.\n";
        } else {
            foreach ($nextSteps as $index => $task) {
                $report .= ($index + 1) . ". **" . $task['title'] . "** (Priority: " . ucfirst($task['priority']) . ")\n";
            }
        }

        return $report;
    }
}
