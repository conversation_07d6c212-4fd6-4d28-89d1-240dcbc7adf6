<?php
/**
 * AI Agent Model
 *
 * Handles AI agent data and operations
 */

require_once __DIR__ . '/BaseModel.php';

class AIAgent extends BaseModel {
    protected $table = 'ai_agents';

    /**
     * Get all agents for a user
     */
    public function getUserAgents($userId) {
        $sql = "SELECT a.*, c.name as category_name, c.color as category_color, c.icon as category_icon
                FROM {$this->table} a
                LEFT JOIN ai_agent_categories c ON a.category_id = c.id
                WHERE a.user_id = ?
                ORDER BY a.name ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get agents by category
     */
    public function getAgentsByCategory($userId, $categoryId) {
        $sql = "SELECT a.*, c.name as category_name, c.color as category_color, c.icon as category_icon
                FROM {$this->table} a
                LEFT JOIN ai_agent_categories c ON a.category_id = c.id
                WHERE a.user_id = ? AND a.category_id = ?
                ORDER BY a.name ASC";

        return $this->db->fetchAll($sql, [$userId, $categoryId]);
    }

    /**
     * Check if category has any agents
     */
    public function getCategoryAgents($categoryId) {
        $sql = "SELECT id, name FROM {$this->table} WHERE category_id = ?";
        return $this->db->fetchAll($sql, [$categoryId]);
    }

    /**
     * Get active agents for a user
     */
    public function getActiveAgents($userId, $limit = null) {
        $sql = "SELECT a.*, c.name as category_name, c.color as category_color, c.icon as category_icon
                FROM {$this->table} a
                LEFT JOIN ai_agent_categories c ON a.category_id = c.id
                WHERE a.user_id = ? AND a.status = 'active'
                ORDER BY a.last_active DESC";

        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get a single agent by ID
     *
     * @param int $id Agent ID
     * @param int $userId User ID
     * @param bool $strictUserCheck Whether to strictly check the user ID (default: true)
     * @return array|false Agent data or false if not found
     */
    public function getAgent($id, $userId, $strictUserCheck = true) {
        if ($strictUserCheck) {
            $sql = "SELECT a.*, c.name as category_name, c.color as category_color, c.icon as category_icon
                    FROM {$this->table} a
                    LEFT JOIN ai_agent_categories c ON a.category_id = c.id
                    WHERE a.id = ? AND a.user_id = ?";

            $result = $this->db->fetchOne($sql, [$id, $userId]);

            // If not found with strict user check, try to find the agent without user check
            if (!$result) {
                return $this->getAgent($id, $userId, false);
            }

            return $result;
        } else {
            // Get agent without checking user ID
            $sql = "SELECT a.*, c.name as category_name, c.color as category_color, c.icon as category_icon
                    FROM {$this->table} a
                    LEFT JOIN ai_agent_categories c ON a.category_id = c.id
                    WHERE a.id = ?";

            return $this->db->fetchOne($sql, [$id]);
        }
    }

    /**
     * Create a new agent
     */
    public function createAgent($data) {
        // Ensure created_at and updated_at are set
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->create($data);
    }

    /**
     * Update an agent
     */
    public function updateAgent($id, $data) {
        // Ensure updated_at is set
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->update($id, $data);
    }

    /**
     * Delete an agent
     */
    public function deleteAgent($id, $userId) {
        $sql = "DELETE FROM {$this->table} WHERE id = ? AND user_id = ?";

        return $this->db->query($sql, [$id, $userId]);
    }

    /**
     * Get agent statistics
     */
    public function getAgentStats($userId) {
        $sql = "SELECT
                    COUNT(*) as total_agents,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_agents,
                    SUM(CASE WHEN status = 'training' THEN 1 ELSE 0 END) as training_agents,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_agents,
                    SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as error_agents,
                    AVG(intelligence_level) as avg_intelligence,
                    AVG(efficiency_rating) as avg_efficiency,
                    AVG(reliability_score) as avg_reliability
                FROM {$this->table}
                WHERE user_id = ?";

        // Debug log
        error_log("Getting agent stats for user ID: {$userId}");
        error_log("SQL: {$sql}");

        // Get all agents for this user to debug
        $allAgents = $this->getUserAgents($userId);
        error_log("All agents for user {$userId}: " . print_r($allAgents, true));

        $result = $this->db->fetchOne($sql, [$userId]);
        error_log("Agent stats result: " . print_r($result, true));

        return $result;
    }

    /**
     * Get agent skills
     */
    public function getAgentSkills($agentId) {
        $sql = "SELECT s.*, m.proficiency_level
                FROM ai_agent_skill_mappings m
                JOIN ai_agent_skills s ON m.skill_id = s.id
                WHERE m.agent_id = ?
                ORDER BY m.proficiency_level DESC";

        return $this->db->fetchAll($sql, [$agentId]);
    }

    /**
     * Add skill to agent
     */
    public function addSkill($agentId, $skillId, $proficiencyLevel = 5) {
        $sql = "INSERT INTO ai_agent_skill_mappings
                (agent_id, skill_id, proficiency_level, created_at, updated_at)
                VALUES (?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                proficiency_level = ?, updated_at = NOW()";

        return $this->db->query($sql, [$agentId, $skillId, $proficiencyLevel, $proficiencyLevel]);
    }

    /**
     * Remove skill from agent
     */
    public function removeSkill($agentId, $skillId) {
        $sql = "DELETE FROM ai_agent_skill_mappings
                WHERE agent_id = ? AND skill_id = ?";

        return $this->db->query($sql, [$agentId, $skillId]);
    }

    /**
     * Update agent last active time
     */
    public function updateLastActive($id) {
        $sql = "UPDATE {$this->table}
                SET last_active = NOW(), updated_at = NOW()
                WHERE id = ?";

        return $this->db->query($sql, [$id]);
    }
}
