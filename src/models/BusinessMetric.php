<?php
/**
 * Business Metric Model
 *
 * This model handles operations related to business metrics and KPIs.
 */

class BusinessMetric
{
    private $db;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Get metrics for a specific business venture
     *
     * @param int $ventureId The venture ID
     * @param string $period The period to get metrics for (daily, weekly, monthly, yearly)
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @param int $maxDataPoints Maximum number of data points to return (for performance)
     * @return array The metrics
     */
    public function getVentureMetrics($ventureId, $period = 'monthly', $startDate = null, $endDate = null, $maxDataPoints = 100)
    {
        // Set default date range if not provided
        if (!$startDate) {
            $startDate = date('Y-m-d', strtotime('-1 year'));
        }

        if (!$endDate) {
            $endDate = date('Y-m-d');
        }

        // Adjust query based on period to reduce data points
        if ($period === 'daily') {
            // For daily data, we might need to sample if the date range is large
            $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24);

            if ($daysDiff > $maxDataPoints) {
                // Sample data to reduce points
                $interval = ceil($daysDiff / $maxDataPoints);
                $sql = "SELECT * FROM business_metrics
                        WHERE venture_id = ? AND metric_date BETWEEN ? AND ?
                        AND MOD(DATEDIFF(metric_date, ?), ?) = 0
                        ORDER BY metric_date ASC";
                $params = [$ventureId, $startDate, $endDate, $startDate, $interval];
            } else {
                // Use all daily data
                $sql = "SELECT * FROM business_metrics
                        WHERE venture_id = ? AND metric_date BETWEEN ? AND ?
                        ORDER BY metric_date ASC";
                $params = [$ventureId, $startDate, $endDate];
            }
        } else if ($period === 'weekly') {
            // Group by week
            $sql = "SELECT
                        DATE(DATE_ADD(?, INTERVAL FLOOR(DATEDIFF(metric_date, ?) / 7) * 7 DAY)) as metric_date,
                        SUM(revenue) as revenue,
                        SUM(expenses) as expenses,
                        SUM(profit) as profit,
                        SUM(sales_count) as sales_count,
                        SUM(new_customers) as new_customers,
                        AVG(website_visits) as website_visits,
                        AVG(conversion_rate) as conversion_rate,
                        AVG(average_order_value) as average_order_value,
                        GROUP_CONCAT(DISTINCT notes SEPARATOR '; ') as notes,
                        MIN(created_at) as created_at,
                        MAX(updated_at) as updated_at
                    FROM business_metrics
                    WHERE venture_id = ? AND metric_date BETWEEN ? AND ?
                    GROUP BY FLOOR(DATEDIFF(metric_date, ?) / 7)
                    ORDER BY metric_date ASC";
            $params = [$startDate, $startDate, $ventureId, $startDate, $endDate, $startDate];
        } else if ($period === 'monthly') {
            // Group by month
            $sql = "SELECT
                        DATE_FORMAT(metric_date, '%Y-%m-01') as metric_date,
                        SUM(revenue) as revenue,
                        SUM(expenses) as expenses,
                        SUM(profit) as profit,
                        SUM(sales_count) as sales_count,
                        SUM(new_customers) as new_customers,
                        AVG(website_visits) as website_visits,
                        AVG(conversion_rate) as conversion_rate,
                        AVG(average_order_value) as average_order_value,
                        GROUP_CONCAT(DISTINCT notes SEPARATOR '; ') as notes,
                        MIN(created_at) as created_at,
                        MAX(updated_at) as updated_at
                    FROM business_metrics
                    WHERE venture_id = ? AND metric_date BETWEEN ? AND ?
                    GROUP BY DATE_FORMAT(metric_date, '%Y-%m')
                    ORDER BY metric_date ASC";
            $params = [$ventureId, $startDate, $endDate];
        } else {
            // Default to all data
            $sql = "SELECT * FROM business_metrics
                    WHERE venture_id = ? AND metric_date BETWEEN ? AND ?
                    ORDER BY metric_date ASC";
            $params = [$ventureId, $startDate, $endDate];
        }

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get the latest metrics for a business venture
     *
     * @param int $ventureId The venture ID
     * @return array|false The latest metrics or false if none found
     */
    public function getLatestMetrics($ventureId)
    {
        $sql = "SELECT * FROM business_metrics
                WHERE venture_id = ?
                ORDER BY metric_date DESC
                LIMIT 1";

        return $this->db->fetchOne($sql, [$ventureId]);
    }

    /**
     * Create or update metrics for a specific date
     *
     * @param array $data The metric data
     * @return int|bool The new metric ID or true on update success, false on failure
     */
    public function saveMetrics($data)
    {
        // Check if metrics already exist for this date and venture
        $sql = "SELECT id FROM business_metrics
                WHERE venture_id = ? AND metric_date = ?";

        $existing = $this->db->fetchOne($sql, [$data['venture_id'], $data['metric_date']]);

        $now = date('Y-m-d H:i:s');

        if ($existing) {
            // Update existing metrics
            $sql = "UPDATE business_metrics SET
                        revenue = ?,
                        expenses = ?,
                        profit = ?,
                        sales_count = ?,
                        new_customers = ?,
                        website_visits = ?,
                        conversion_rate = ?,
                        average_order_value = ?,
                        notes = ?,
                        updated_at = ?
                    WHERE id = ?";

            $params = [
                $data['revenue'] ?? 0,
                $data['expenses'] ?? 0,
                $data['profit'] ?? 0,
                $data['sales_count'] ?? 0,
                $data['new_customers'] ?? 0,
                $data['website_visits'] ?? null,
                $data['conversion_rate'] ?? null,
                $data['average_order_value'] ?? null,
                $data['notes'] ?? null,
                $now,
                $existing['id']
            ];

            $stmt = $this->db->query($sql, $params);
            return $stmt ? $stmt->rowCount() > 0 : false;
        } else {
            // Insert new metrics
            $sql = "INSERT INTO business_metrics (
                        venture_id, metric_date, revenue, expenses, profit,
                        sales_count, new_customers, website_visits,
                        conversion_rate, average_order_value, notes,
                        created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $params = [
                $data['venture_id'],
                $data['metric_date'],
                $data['revenue'] ?? 0,
                $data['expenses'] ?? 0,
                $data['profit'] ?? 0,
                $data['sales_count'] ?? 0,
                $data['new_customers'] ?? 0,
                $data['website_visits'] ?? null,
                $data['conversion_rate'] ?? null,
                $data['average_order_value'] ?? null,
                $data['notes'] ?? null,
                $now,
                $now
            ];

            $stmt = $this->db->query($sql, $params);
            return $stmt ? $this->db->getConnection()->lastInsertId() : false;
        }
    }

    /**
     * Calculate metrics from sales and expenses data
     *
     * @param int $ventureId The venture ID
     * @param string $date The date to calculate metrics for (Y-m-d format)
     * @return bool Success or failure
     */
    public function calculateMetricsFromData($ventureId, $date)
    {
        // Get sales data for the date
        $salesSql = "SELECT
                        SUM(total_amount) as revenue,
                        COUNT(*) as sales_count,
                        COUNT(DISTINCT customer_id) as customer_count
                    FROM business_sales
                    WHERE venture_id = ? AND sale_date = ?";

        $salesData = $this->db->fetchOne($salesSql, [$ventureId, $date]);

        // Get expense data for the date
        $expenseSql = "SELECT SUM(amount) as expenses
                      FROM business_expenses
                      WHERE venture_id = ? AND expense_date = ?";

        $expenseData = $this->db->fetchOne($expenseSql, [$ventureId, $date]);

        // Get new customers for the date
        $customerSql = "SELECT COUNT(*) as new_customers
                       FROM business_customers
                       WHERE venture_id = ? AND customer_since = ?";

        $customerData = $this->db->fetchOne($customerSql, [$ventureId, $date]);

        // Calculate metrics
        $revenue = $salesData['revenue'] ?? 0;
        $expenses = $expenseData['expenses'] ?? 0;
        $profit = $revenue - $expenses;
        $salesCount = $salesData['sales_count'] ?? 0;
        $newCustomers = $customerData['new_customers'] ?? 0;

        // Calculate average order value if there were sales
        $averageOrderValue = $salesCount > 0 ? $revenue / $salesCount : 0;

        // Save the calculated metrics
        $metricData = [
            'venture_id' => $ventureId,
            'metric_date' => $date,
            'revenue' => $revenue,
            'expenses' => $expenses,
            'profit' => $profit,
            'sales_count' => $salesCount,
            'new_customers' => $newCustomers,
            'average_order_value' => $averageOrderValue
        ];

        return $this->saveMetrics($metricData);
    }

    /**
     * Get metrics summary for a date range
     *
     * @param int $ventureId The venture ID
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @return array The metrics summary
     */
    public function getMetricsSummary($ventureId, $startDate, $endDate)
    {
        $sql = "SELECT
                    SUM(revenue) as total_revenue,
                    SUM(expenses) as total_expenses,
                    SUM(profit) as total_profit,
                    SUM(sales_count) as total_sales,
                    SUM(new_customers) as total_new_customers,
                    AVG(average_order_value) as avg_order_value
                FROM business_metrics
                WHERE venture_id = ? AND metric_date BETWEEN ? AND ?";

        $summary = $this->db->fetchOne($sql, [$ventureId, $startDate, $endDate]);

        // If no data, return zeros
        if (!$summary) {
            return [
                'total_revenue' => 0,
                'total_expenses' => 0,
                'total_profit' => 0,
                'total_sales' => 0,
                'total_new_customers' => 0,
                'avg_order_value' => 0
            ];
        }

        return $summary;
    }
}
