<?php
/**
 * Income Evaluator Model
 *
 * Handles income opportunity evaluation and comparison operations.
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/IncomeOpportunity.php';

class IncomeEvaluator extends BaseModel {
    protected $criteriaTable = 'evaluation_criteria';
    protected $comparisonsTable = 'opportunity_comparisons';
    protected $comparisonOpportunitiesTable = 'comparison_opportunities';
    protected $criteriaScoresTable = 'comparison_criteria_scores';
    protected $userSkillsTable = 'user_skills';
    protected $opportunitySkillsTable = 'opportunity_skill_requirements';
    protected $opportunitiesTable = 'income_opportunities';

    private $opportunityModel;

    public function __construct() {
        parent::__construct();
        $this->opportunityModel = new IncomeOpportunity();
    }

    /**
     * Get evaluation criteria for a user
     *
     * @param int $userId User ID
     * @param string|null $criteriaType Optional filter by criteria type
     * @return array Evaluation criteria
     */
    public function getCriteria($userId, $criteriaType = null) {
        $sql = "SELECT * FROM {$this->criteriaTable}
                WHERE is_default = TRUE OR user_id = ?";
        $params = [$userId];

        if ($criteriaType) {
            $sql .= " AND criteria_type = ?";
            $params[] = $criteriaType;
        }

        $sql .= " ORDER BY criteria_type, weight DESC, name";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Create a new evaluation criteria
     *
     * @param array $data Criteria data
     * @return int|bool The ID of the created criteria or false on failure
     */
    public function createCriteria($data) {
        return $this->db->insert($this->criteriaTable, $data);
    }

    /**
     * Update an evaluation criteria
     *
     * @param int $id Criteria ID
     * @param array $data Updated criteria data
     * @return bool Success or failure
     */
    public function updateCriteria($id, $data) {
        return $this->db->update($this->criteriaTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete an evaluation criteria
     *
     * @param int $id Criteria ID
     * @param int $userId User ID (for security check)
     * @return bool Success or failure
     */
    public function deleteCriteria($id, $userId) {
        // Only allow deletion of user-created criteria (not default ones)
        return $this->db->delete(
            $this->criteriaTable,
            "id = ? AND user_id = ? AND is_default = FALSE",
            [$id, $userId]
        );
    }

    /**
     * Create a new opportunity comparison
     *
     * @param array $data Comparison data
     * @return int|bool The ID of the created comparison or false on failure
     */
    public function createComparison($data) {
        return $this->db->insert($this->comparisonsTable, $data);
    }

    /**
     * Get user's opportunity comparisons
     *
     * @param int $userId User ID
     * @return array Comparisons
     */
    public function getUserComparisons($userId) {
        $sql = "SELECT c.*,
                    COUNT(DISTINCT co.opportunity_id) as opportunity_count,
                    GROUP_CONCAT(DISTINCT o.name SEPARATOR ', ') as opportunity_names
                FROM {$this->comparisonsTable} c
                LEFT JOIN {$this->comparisonOpportunitiesTable} co ON c.id = co.comparison_id
                LEFT JOIN {$this->opportunitiesTable} o ON co.opportunity_id = o.id
                WHERE c.user_id = ?
                GROUP BY c.id
                ORDER BY c.last_updated DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get a specific comparison with its opportunities
     *
     * @param int $comparisonId Comparison ID
     * @param int $userId User ID (for security check)
     * @return array|bool Comparison data or false if not found
     */
    public function getComparison($comparisonId, $userId) {
        $sql = "SELECT * FROM {$this->comparisonsTable}
                WHERE id = ? AND user_id = ?";

        $comparison = $this->db->fetchOne($sql, [$comparisonId, $userId]);

        if (!$comparison) {
            return false;
        }

        // Get opportunities in this comparison
        $sql = "SELECT co.*, o.name, o.category, o.income_type, o.status,
                    o.estimated_income_min, o.estimated_income_max, o.time_commitment
                FROM {$this->comparisonOpportunitiesTable} co
                JOIN {$this->opportunitiesTable} o ON co.opportunity_id = o.id
                WHERE co.comparison_id = ?
                ORDER BY co.ranking, o.name";

        $comparison['opportunities'] = $this->db->fetchAll($sql, [$comparisonId]);

        return $comparison;
    }

    /**
     * Add an opportunity to a comparison
     *
     * @param int $comparisonId Comparison ID
     * @param int $opportunityId Opportunity ID
     * @param array $data Additional data (ranking, notes)
     * @return int|bool The ID of the created record or false on failure
     */
    public function addOpportunityToComparison($comparisonId, $opportunityId, $data = []) {
        $data['comparison_id'] = $comparisonId;
        $data['opportunity_id'] = $opportunityId;

        return $this->db->insert($this->comparisonOpportunitiesTable, $data);
    }

    /**
     * Remove an opportunity from a comparison
     *
     * @param int $comparisonId Comparison ID
     * @param int $opportunityId Opportunity ID
     * @return bool Success or failure
     */
    public function removeOpportunityFromComparison($comparisonId, $opportunityId) {
        return $this->db->delete(
            $this->comparisonOpportunitiesTable,
            "comparison_id = ? AND opportunity_id = ?",
            [$comparisonId, $opportunityId]
        );
    }

    /**
     * Save criteria scores for an opportunity in a comparison
     *
     * @param int $comparisonId Comparison ID
     * @param int $opportunityId Opportunity ID
     * @param array $scores Array of criteria scores [criteria_id => score]
     * @param array $notes Array of notes [criteria_id => note]
     * @return bool Success or failure
     */
    public function saveCriteriaScores($comparisonId, $opportunityId, $scores, $notes = []) {
        $success = true;

        foreach ($scores as $criteriaId => $score) {
            // Check if score already exists
            $sql = "SELECT id FROM {$this->criteriaScoresTable}
                    WHERE comparison_id = ? AND opportunity_id = ? AND criteria_id = ?";

            $existingScore = $this->db->fetchOne($sql, [$comparisonId, $opportunityId, $criteriaId]);

            $data = [
                'score' => $score,
                'notes' => $notes[$criteriaId] ?? null
            ];

            if ($existingScore) {
                // Update existing score
                $result = $this->db->update(
                    $this->criteriaScoresTable,
                    $data,
                    "id = ?",
                    [$existingScore['id']]
                );
            } else {
                // Insert new score
                $data['comparison_id'] = $comparisonId;
                $data['opportunity_id'] = $opportunityId;
                $data['criteria_id'] = $criteriaId;

                $result = $this->db->insert($this->criteriaScoresTable, $data);
            }

            if (!$result) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Get criteria scores for opportunities in a comparison
     *
     * @param int $comparisonId Comparison ID
     * @return array Scores grouped by opportunity and criteria
     */
    public function getComparisonScores($comparisonId) {
        $sql = "SELECT cs.*, c.name as criteria_name, c.weight as criteria_weight,
                    c.criteria_type, o.name as opportunity_name
                FROM {$this->criteriaScoresTable} cs
                JOIN {$this->criteriaTable} c ON cs.criteria_id = c.id
                JOIN {$this->opportunitiesTable} o ON cs.opportunity_id = o.id
                WHERE cs.comparison_id = ?
                ORDER BY o.name, c.criteria_type, c.name";

        $rawScores = $this->db->fetchAll($sql, [$comparisonId]);

        // Organize scores by opportunity and criteria
        $scores = [];
        foreach ($rawScores as $score) {
            if (!isset($scores[$score['opportunity_id']])) {
                $scores[$score['opportunity_id']] = [
                    'opportunity_name' => $score['opportunity_name'],
                    'criteria_scores' => [],
                    'total_score' => 0,
                    'weighted_score' => 0
                ];
            }

            $scores[$score['opportunity_id']]['criteria_scores'][$score['criteria_id']] = [
                'score' => $score['score'],
                'notes' => $score['notes'],
                'criteria_name' => $score['criteria_name'],
                'criteria_weight' => $score['criteria_weight'],
                'criteria_type' => $score['criteria_type']
            ];

            // Add to total and weighted scores
            $scores[$score['opportunity_id']]['total_score'] += $score['score'];
            $scores[$score['opportunity_id']]['weighted_score'] += $score['score'] * $score['criteria_weight'];
        }

        return $scores;
    }

    /**
     * Calculate and update rankings for opportunities in a comparison
     *
     * @param int $comparisonId Comparison ID
     * @return bool Success or failure
     */
    public function updateRankings($comparisonId) {
        // Get scores
        $scores = $this->getComparisonScores($comparisonId);

        // Sort by weighted score (descending)
        uasort($scores, function($a, $b) {
            return $b['weighted_score'] <=> $a['weighted_score'];
        });

        // Update ranks
        $rank = 1;
        $success = true;

        foreach ($scores as $opportunityId => $data) {
            $result = $this->db->update(
                $this->comparisonOpportunitiesTable,
                ['ranking' => $rank],
                "comparison_id = ? AND opportunity_id = ?",
                [$comparisonId, $opportunityId]
            );

            if (!$result) {
                $success = false;
            }

            $rank++;
        }

        return $success;
    }

    /**
     * Update a comparison
     *
     * @param int $comparisonId Comparison ID
     * @param array $data Updated comparison data
     * @return bool Success or failure
     */
    public function updateComparison($comparisonId, $data) {
        return $this->db->update($this->comparisonsTable, $data, "id = ?", [$comparisonId]);
    }

    /**
     * Delete a comparison
     *
     * @param int $comparisonId Comparison ID
     * @param int $userId User ID (for security check)
     * @return bool Success or failure
     */
    public function deleteComparison($comparisonId, $userId) {
        return $this->db->delete(
            $this->comparisonsTable,
            "id = ? AND user_id = ?",
            [$comparisonId, $userId]
        );
    }
}
