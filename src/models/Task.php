<?php
/**
 * Task Model
 *
 * Handles task-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class Task extends BaseModel {
    protected $table = 'tasks';

    /**
     * Get tasks for a specific user
     */
    public function getUserTasks($userId, $filters = []) {
        $sql = "SELECT t.*, c.name as category_name, c.color as category_color
                FROM {$this->table} t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ?";

        $params = [$userId];

        // Log the filters for debugging
        error_log("Task filters: " . print_r($filters, true));

        // Apply filters
        if (!empty($filters)) {
            // Filter by status
            if (isset($filters['status']) && $filters['status'] !== '') {
                // Check if multiple statuses are provided (comma-separated)
                if (strpos($filters['status'], ',') !== false) {
                    $statuses = explode(',', $filters['status']);
                    $statusPlaceholders = implode(',', array_fill(0, count($statuses), '?'));
                    $sql .= " AND t.status IN ($statusPlaceholders)";
                    foreach ($statuses as $status) {
                        $params[] = trim($status);
                    }
                } else {
                    $sql .= " AND t.status = ?";
                    $params[] = $filters['status'];
                }
            }

            // Filter by priority
            if (isset($filters['priority']) && $filters['priority'] !== '') {
                // Check if multiple priorities are provided (comma-separated)
                if (strpos($filters['priority'], ',') !== false) {
                    $priorities = explode(',', $filters['priority']);
                    $priorityPlaceholders = implode(',', array_fill(0, count($priorities), '?'));
                    $sql .= " AND t.priority IN ($priorityPlaceholders)";
                    foreach ($priorities as $priority) {
                        $params[] = trim($priority);
                    }
                } else {
                    $sql .= " AND t.priority = ?";
                    $params[] = $filters['priority'];
                }
            }

            // Filter by due date
            if (isset($filters['due_date']) && $filters['due_date'] !== '') {
                $sql .= " AND DATE(t.due_date) = ?";
                $params[] = $filters['due_date'];
            }

            // Filter by due date range
            if (isset($filters['due_date_start']) && isset($filters['due_date_end'])) {
                $sql .= " AND t.due_date BETWEEN ? AND ?";
                $params[] = $filters['due_date_start'];
                $params[] = $filters['due_date_end'];
            }

            // Filter for overdue tasks
            if (isset($filters['overdue']) && $filters['overdue']) {
                $today = date('Y-m-d');
                $sql .= " AND t.due_date < ? AND t.status != 'done'";
                $params[] = $today;
            }

            // Filter by category
            if (isset($filters['category_id']) && $filters['category_id'] !== '') {
                // Convert to integer to ensure proper comparison
                $categoryId = intval($filters['category_id']);
                $sql .= " AND t.category_id = ?";
                $params[] = $categoryId;

                // Log the category filter for debugging
                error_log("Filtering by category ID: " . $categoryId);
            }
        }

        // Order by due date and priority by default
        $sql .= " ORDER BY t.due_date ASC, t.priority DESC";

        // Log the final SQL query for debugging
        error_log("Task query SQL: " . $sql);
        error_log("Task query params: " . print_r($params, true));

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get tasks due today for a specific user
     */
    public function getTodayTasks($userId) {
        $today = date('Y-m-d');
        return $this->getUserTasks($userId, ['due_date' => $today]);
    }

    /**
     * Get completed tasks for a specific user
     */
    public function getCompletedTasks($userId, $limit = 10) {
        $sql = "SELECT t.*, c.name as category_name, c.color as category_color
                FROM {$this->table} t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ? AND t.status = 'done'
                ORDER BY t.completed_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Get upcoming tasks for a specific user
     */
    public function getUpcomingTasks($userId, $limit = 5) {
        $today = date('Y-m-d');
        $sql = "SELECT t.*, c.name as category_name, c.color as category_color
                FROM {$this->table} t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ? AND t.status != 'done' AND t.due_date >= ?
                ORDER BY t.due_date ASC, t.priority DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $today, $limit]);
    }

    /**
     * Get overdue tasks for a specific user
     */
    public function getOverdueTasks($userId) {
        $today = date('Y-m-d');
        $sql = "SELECT t.*, c.name as category_name, c.color as category_color
                FROM {$this->table} t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ? AND t.status != 'done' AND t.due_date < ?
                ORDER BY t.due_date ASC, t.priority DESC";

        return $this->db->fetchAll($sql, [$userId, $today]);
    }

    /**
     * Get active (non-completed) tasks for a specific user
     */
    public function getActiveTasks($userId) {
        $sql = "SELECT t.*, c.name as category_name, c.color as category_color
                FROM {$this->table} t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ? AND t.status != 'done'
                ORDER BY t.due_date ASC, t.priority DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Mark a task as complete
     */
    public function markAsComplete($taskId, $userId) {
        // First verify the task belongs to the user
        $task = $this->find($taskId);
        if (!$task || $task['user_id'] != $userId) {
            error_log("Task {$taskId} does not belong to user {$userId}");
            return false;
        }

        return $this->update($taskId, [
            'status' => 'done',
            'completed_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get subtasks for a specific task
     */
    public function getSubtasks($taskId) {
        return $this->findBy('parent_id', $taskId);
    }

    /**
     * Get tasks for a specific date range
     */
    public function getTasksByDateRange($userId, $startDate, $endDate) {
        $sql = "SELECT t.*, c.name as category_name, c.color as category_color
                FROM {$this->table} t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.user_id = ? AND t.due_date BETWEEN ? AND ?
                ORDER BY t.due_date ASC, t.priority DESC";

        return $this->db->fetchAll($sql, [$userId, $startDate, $endDate]);
    }

    /**
     * Get tasks for a specific project
     */
    public function getProjectTasks($projectId, $filters = []) {
        $sql = "SELECT t.*, c.name as category_name, c.color as category_color
                FROM {$this->table} t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.project_id = ?";

        $params = [$projectId];

        // Apply filters
        if (!empty($filters)) {
            // Filter by status
            if (isset($filters['status']) && $filters['status'] !== '') {
                $sql .= " AND t.status = ?";
                $params[] = $filters['status'];
            }

            // Filter by priority
            if (isset($filters['priority']) && $filters['priority'] !== '') {
                $sql .= " AND t.priority = ?";
                $params[] = $filters['priority'];
            }

            // Filter by assignee
            if (isset($filters['user_id']) && $filters['user_id'] !== '') {
                $sql .= " AND t.user_id = ?";
                $params[] = $filters['user_id'];
            }

            // Filter by category
            if (isset($filters['category_id']) && $filters['category_id'] !== '') {
                $sql .= " AND t.category_id = ?";
                $params[] = $filters['category_id'];
            }
        }

        // Order by due date and priority by default
        $sql .= " ORDER BY t.due_date ASC, t.priority DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get task with dependencies
     */
    public function getTaskWithDependencies($taskId) {
        // Get task details
        $task = $this->find($taskId);

        if (!$task) {
            return null;
        }

        // Get task dependencies
        require_once __DIR__ . '/TaskDependency.php';
        $dependencyModel = new TaskDependency();

        $task['dependencies'] = $dependencyModel->getTaskDependencies($taskId);
        $task['dependent_tasks'] = $dependencyModel->getDependentTasks($taskId);

        return $task;
    }

    /**
     * Check if a task can be started based on dependencies
     */
    public function canTaskBeStarted($taskId) {
        // Get task dependencies
        require_once __DIR__ . '/TaskDependency.php';
        $dependencyModel = new TaskDependency();
        $dependencies = $dependencyModel->getTaskDependencies($taskId);

        foreach ($dependencies as $dependency) {
            // For Finish-to-Start dependencies, the dependent task must be completed
            if ($dependency['dependency_type'] === 'finish_to_start' && $dependency['depends_on_status'] !== 'done') {
                return false;
            }

            // For Start-to-Start dependencies, the dependent task must be at least started
            if ($dependency['dependency_type'] === 'start_to_start' && $dependency['depends_on_status'] === 'todo') {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if a task can be completed based on dependencies
     */
    public function canTaskBeCompleted($taskId) {
        // Get task dependencies
        require_once __DIR__ . '/TaskDependency.php';
        $dependencyModel = new TaskDependency();
        $dependencies = $dependencyModel->getTaskDependencies($taskId);

        foreach ($dependencies as $dependency) {
            // For Finish-to-Finish dependencies, the dependent task must be completed
            if ($dependency['dependency_type'] === 'finish_to_finish' && $dependency['depends_on_status'] !== 'done') {
                return false;
            }

            // For Start-to-Finish dependencies, the dependent task must be at least started
            if ($dependency['dependency_type'] === 'start_to_finish' && $dependency['depends_on_status'] === 'todo') {
                return false;
            }
        }

        return true;
    }

    /**
     * Get task completion history for a project
     */
    public function getTaskCompletionHistory($projectId) {
        $sql = "SELECT id, title, completed_at, user_id
                FROM {$this->table}
                WHERE project_id = ? AND status = 'done' AND completed_at IS NOT NULL
                ORDER BY completed_at ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Get project tasks with due dates
     */
    public function getProjectTasksWithDueDates($projectId) {
        $sql = "SELECT t.*, c.name as category_name, c.color as category_color
                FROM {$this->table} t
                LEFT JOIN categories c ON t.category_id = c.id
                WHERE t.project_id = ? AND t.due_date IS NOT NULL
                ORDER BY t.due_date ASC";

        return $this->db->fetchAll($sql, [$projectId]);
    }

    /**
     * Add a dependency between tasks
     *
     * @param int $taskId The task that depends on another task
     * @param int $dependsOnTaskId The task that must be completed first
     * @param string $dependencyType The type of dependency (finish_to_start, start_to_start, finish_to_finish, start_to_finish)
     * @param int $lagTime The lag time in minutes
     * @return int|false The ID of the new dependency or false on failure
     */
    public function addDependency($taskId, $dependsOnTaskId, $dependencyType = 'finish_to_start', $lagTime = 0) {
        // Verify both tasks exist
        $task = $this->find($taskId);
        $dependsOnTask = $this->find($dependsOnTaskId);

        if (!$task || !$dependsOnTask) {
            return false;
        }

        // Check for circular dependencies
        require_once __DIR__ . '/TaskDependency.php';
        $dependencyModel = new TaskDependency();

        if ($dependencyModel->wouldCreateCircularDependency($taskId, $dependsOnTaskId)) {
            return false;
        }

        // Create the dependency
        $dependencyData = [
            'task_id' => $taskId,
            'depends_on_task_id' => $dependsOnTaskId,
            'dependency_type' => $dependencyType,
            'lag_time' => $lagTime,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $dependencyModel->create($dependencyData);
    }

    /**
     * Get task completion rate by day for a project
     */
    public function getTaskCompletionRateByDay($projectId, $startDate, $endDate) {
        $sql = "SELECT DATE(completed_at) as completion_date, COUNT(*) as completed_count
                FROM {$this->table}
                WHERE project_id = ? AND status = 'done' AND completed_at IS NOT NULL
                AND completed_at BETWEEN ? AND ?
                GROUP BY DATE(completed_at)
                ORDER BY completion_date ASC";

        return $this->db->fetchAll($sql, [$projectId, $startDate, $endDate]);
    }

    /**
     * Get task creation rate by day for a project
     */
    public function getTaskCreationRateByDay($projectId, $startDate, $endDate) {
        $sql = "SELECT DATE(created_at) as creation_date, COUNT(*) as created_count
                FROM {$this->table}
                WHERE project_id = ? AND created_at BETWEEN ? AND ?
                GROUP BY DATE(created_at)
                ORDER BY creation_date ASC";

        return $this->db->fetchAll($sql, [$projectId, $startDate, $endDate]);
    }
}
