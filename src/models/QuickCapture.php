<?php
/**
 * Quick Capture Model
 *
 * Handles screenshot, note, and voice capture management
 */

require_once __DIR__ . '/BaseModel.php';

class QuickCapture extends BaseModel {
    protected $table = 'quick_captures';

    /**
     * Get captures for a user with optional filters
     */
    public function getUserCaptures($userId, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        // Apply filters
        if (!empty($filters['type'])) {
            $sql .= " AND type = ?";
            $params[] = $filters['type'];
        }

        if (!empty($filters['category'])) {
            $sql .= " AND category = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['is_pinned'])) {
            $sql .= " AND is_pinned = ?";
            $params[] = $filters['is_pinned'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (title LIKE ? OR content LIKE ? OR tags LIKE ? OR ocr_text LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($filters['date_from'])) {
            $sql .= " AND created_at >= ?";
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $sql .= " AND created_at <= ?";
            $params[] = $filters['date_to'];
        }

        // Order by pinned first, then by creation date
        $sql .= " ORDER BY is_pinned DESC, created_at DESC";

        if (!empty($filters['limit'])) {
            $sql .= " LIMIT ?";
            $params[] = (int)$filters['limit'];
        }

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get recent captures
     */
    public function getRecentCaptures($userId, $limit = 10) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Get pinned captures
     */
    public function getPinnedCaptures($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND is_pinned = 1
                ORDER BY created_at DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Create a new capture
     */
    public function createCapture($data) {
        // Validate required fields
        if (empty($data['user_id']) || empty($data['type'])) {
            return false;
        }

        $captureData = [
            'user_id' => $data['user_id'],
            'type' => $data['type'],
            'title' => $data['title'] ?? null,
            'content' => $data['content'] ?? null,
            'file_path' => $data['file_path'] ?? null,
            'file_type' => $data['file_type'] ?? null,
            'file_size' => $data['file_size'] ?? null,
            'thumbnail_path' => $data['thumbnail_path'] ?? null,
            'ocr_text' => $data['ocr_text'] ?? null,
            'tags' => $data['tags'] ?? null,
            'category' => $data['category'] ?? null,
            'is_pinned' => $data['is_pinned'] ?? 0,
            'linked_prompt_id' => $data['linked_prompt_id'] ?? null,
            'linked_task_id' => $data['linked_task_id'] ?? null,
            'linked_project_id' => $data['linked_project_id'] ?? null,
            'metadata' => !empty($data['metadata']) ? json_encode($data['metadata']) : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->create($captureData);
    }

    /**
     * Update capture
     */
    public function updateCapture($id, $data) {
        $updateData = [
            'title' => $data['title'] ?? null,
            'content' => $data['content'] ?? null,
            'tags' => $data['tags'] ?? null,
            'category' => $data['category'] ?? null,
            'is_pinned' => $data['is_pinned'] ?? 0,
            'linked_prompt_id' => $data['linked_prompt_id'] ?? null,
            'linked_task_id' => $data['linked_task_id'] ?? null,
            'linked_project_id' => $data['linked_project_id'] ?? null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Only update OCR text if provided
        if (isset($data['ocr_text'])) {
            $updateData['ocr_text'] = $data['ocr_text'];
        }

        return $this->update($id, $updateData);
    }

    /**
     * Toggle pin status
     */
    public function togglePin($captureId, $userId) {
        $capture = $this->find($captureId);
        if (!$capture || $capture['user_id'] != $userId) {
            return false;
        }

        $newStatus = $capture['is_pinned'] ? 0 : 1;
        $sql = "UPDATE {$this->table} SET is_pinned = ?, updated_at = ? WHERE id = ?";
        return $this->execute($sql, [$newStatus, date('Y-m-d H:i:s'), $captureId]);
    }

    /**
     * Delete capture and associated files
     */
    public function deleteCapture($id, $userId) {
        $capture = $this->find($id);
        if (!$capture || $capture['user_id'] != $userId) {
            return false;
        }

        // Delete associated files
        if ($capture['file_path'] && file_exists($capture['file_path'])) {
            unlink($capture['file_path']);
        }

        if ($capture['thumbnail_path'] && file_exists($capture['thumbnail_path'])) {
            unlink($capture['thumbnail_path']);
        }

        // Delete annotations
        $this->execute("DELETE FROM capture_annotations WHERE capture_id = ?", [$id]);

        return $this->delete($id);
    }

    /**
     * Get capture statistics
     */
    public function getCaptureStats($userId) {
        $sql = "SELECT
                    COUNT(*) as total_captures,
                    COUNT(CASE WHEN type = 'screenshot' THEN 1 END) as screenshots,
                    COUNT(CASE WHEN type = 'note' THEN 1 END) as notes,
                    COUNT(CASE WHEN type = 'voice' THEN 1 END) as voice_notes,
                    COUNT(CASE WHEN is_pinned = 1 THEN 1 END) as pinned_captures,
                    SUM(file_size) as total_file_size
                FROM {$this->table}
                WHERE user_id = ?";

        $result = $this->db->fetchOne($sql, [$userId]);

        // Get category distribution
        $categorySql = "SELECT category, COUNT(*) as count
                       FROM {$this->table}
                       WHERE user_id = ? AND category IS NOT NULL
                       GROUP BY category
                       ORDER BY count DESC";

        $categories = $this->db->fetchAll($categorySql, [$userId]);

        // Get recent activity (last 7 days)
        $activitySql = "SELECT DATE(created_at) as date, COUNT(*) as count
                       FROM {$this->table}
                       WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                       GROUP BY DATE(created_at)
                       ORDER BY date DESC";

        $activity = $this->db->fetchAll($activitySql, [$userId]);

        return [
            'overview' => $result,
            'categories' => $categories,
            'recent_activity' => $activity
        ];
    }

    /**
     * Search captures with OCR text
     */
    public function searchCaptures($userId, $query, $filters = []) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND (
                    title LIKE ? OR
                    content LIKE ? OR
                    tags LIKE ? OR
                    ocr_text LIKE ?
                )";

        $searchTerm = '%' . $query . '%';
        $params = [$userId, $searchTerm, $searchTerm, $searchTerm, $searchTerm];

        // Apply additional filters
        if (!empty($filters['type'])) {
            $sql .= " AND type = ?";
            $params[] = $filters['type'];
        }

        if (!empty($filters['category'])) {
            $sql .= " AND category = ?";
            $params[] = $filters['category'];
        }

        $sql .= " ORDER BY created_at DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get captures linked to a specific prompt
     */
    public function getCapturesLinkedToPrompt($promptId, $userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND linked_prompt_id = ?
                ORDER BY created_at DESC";

        return $this->db->fetchAll($sql, [$userId, $promptId]);
    }

    /**
     * Get captures linked to a specific task
     */
    public function getCapturesLinkedToTask($taskId, $userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND linked_task_id = ?
                ORDER BY created_at DESC";

        return $this->db->fetchAll($sql, [$userId, $taskId]);
    }

    /**
     * Get available categories for user
     */
    public function getAvailableCategories($userId) {
        $sql = "SELECT DISTINCT category
                FROM {$this->table}
                WHERE user_id = ? AND category IS NOT NULL
                ORDER BY category ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Bulk update captures
     */
    public function bulkUpdateCaptures($captureIds, $userId, $updateData) {
        if (empty($captureIds) || !is_array($captureIds)) {
            return false;
        }

        // Verify all captures belong to user
        $placeholders = str_repeat('?,', count($captureIds) - 1) . '?';
        $checkSql = "SELECT COUNT(*) as count FROM {$this->table}
                     WHERE id IN ($placeholders) AND user_id = ?";

        $checkParams = array_merge($captureIds, [$userId]);
        $result = $this->db->fetchOne($checkSql, $checkParams);

        if ($result['count'] != count($captureIds)) {
            return false; // Some captures don't belong to user
        }

        // Build update query
        $setParts = [];
        $params = [];

        foreach ($updateData as $field => $value) {
            if (in_array($field, ['category', 'tags', 'is_pinned'])) {
                $setParts[] = "$field = ?";
                $params[] = $value;
            }
        }

        if (empty($setParts)) {
            return false;
        }

        $setParts[] = "updated_at = ?";
        $params[] = date('Y-m-d H:i:s');

        $sql = "UPDATE {$this->table} SET " . implode(', ', $setParts) .
               " WHERE id IN ($placeholders)";

        $params = array_merge($params, $captureIds);

        return $this->execute($sql, $params);
    }
}
