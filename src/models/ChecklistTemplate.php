<?php
/**
 * Checklist Template Model
 *
 * Handles the creation, retrieval, and management of checklist templates
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/ChecklistTemplateItem.php';

class ChecklistTemplate extends BaseModel {
    /**
     * Create a new checklist template
     *
     * @param array $data Template data
     * @return int|false The ID of the created template or false on failure
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO checklist_templates (
                name,
                description,
                template_type,
                category,
                is_system_template,
                created_by,
                created_at,
                updated_at
            ) VALUES (
                :name,
                :description,
                :template_type,
                :category,
                :is_system_template,
                :created_by,
                :created_at,
                :updated_at
            )";

            $params = [
                ':name' => $data['name'],
                ':description' => $data['description'] ?? '',
                ':template_type' => $data['template_type'] ?? 'standard',
                ':category' => $data['category'] ?? 'general',
                ':is_system_template' => $data['is_system_template'] ?? 0,
                ':created_by' => $data['created_by'] ?? null,
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ];

            $templateId = $this->db->insert($sql, $params);

            if ($templateId && isset($data['items']) && is_array($data['items'])) {
                $this->addItemsToTemplate($templateId, $data['items']);
            }

            return $templateId;
        } catch (Exception $e) {
            error_log("Error creating checklist template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Add items to a template
     *
     * @param int $templateId Template ID
     * @param array $items Array of template items
     * @return bool Success or failure
     */
    public function addItemsToTemplate($templateId, $items) {
        try {
            $templateItemModel = new ChecklistTemplateItem();

            foreach ($items as $index => $item) {
                $itemData = [
                    'template_id' => $templateId,
                    'parent_id' => $item['parent_id'] ?? null,
                    'text' => $item['text'],
                    'description' => $item['description'] ?? '',
                    'sort_order' => $item['sort_order'] ?? $index,
                    'resource_link' => $item['resource_link'] ?? null
                ];

                $itemId = $templateItemModel->create($itemData);

                if ($itemId && isset($item['sub_items']) && is_array($item['sub_items'])) {
                    foreach ($item['sub_items'] as $subIndex => $subItem) {
                        $subItemData = [
                            'template_id' => $templateId,
                            'parent_id' => $itemId,
                            'text' => $subItem['text'],
                            'description' => $subItem['description'] ?? '',
                            'sort_order' => $subItem['sort_order'] ?? $subIndex,
                            'resource_link' => $subItem['resource_link'] ?? null
                        ];

                        $templateItemModel->create($subItemData);
                    }
                }
            }

            return true;
        } catch (Exception $e) {
            error_log("Error adding items to template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a template by ID
     *
     * @param int $templateId Template ID
     * @return array|false Template data or false if not found
     */
    public function getTemplate($templateId) {
        try {
            $sql = "SELECT * FROM checklist_templates WHERE id = :id";
            return $this->db->fetchOne($sql, [':id' => $templateId]);
        } catch (Exception $e) {
            error_log("Error getting checklist template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a template with all its items
     *
     * @param int $templateId Template ID
     * @return array|false Template data with items or false if not found
     */
    public function getTemplateWithItems($templateId) {
        try {
            $template = $this->getTemplate($templateId);

            if (!$template) {
                return false;
            }

            $template['items'] = $this->getTemplateItems($templateId);
            return $template;
        } catch (Exception $e) {
            error_log("Error getting template with items: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all items for a template
     *
     * @param int $templateId Template ID
     * @return array Array of template items organized in a hierarchy
     */
    public function getTemplateItems($templateId) {
        try {
            $templateItemModel = new ChecklistTemplateItem();
            return $templateItemModel->getItemsByTemplate($templateId);
        } catch (Exception $e) {
            error_log("Error getting template items: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all templates
     *
     * @param string $category Optional category filter
     * @param string $type Optional template type filter
     * @return array Array of templates
     */
    public function getAllTemplates($category = null, $type = null) {
        try {
            $sql = "SELECT * FROM checklist_templates WHERE 1=1";
            $params = [];

            if ($category) {
                $sql .= " AND category = :category";
                $params[':category'] = $category;
            }

            if ($type) {
                $sql .= " AND template_type = :type";
                $params[':type'] = $type;
            }

            $sql .= " ORDER BY name ASC";

            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Error getting all templates: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all system templates
     *
     * @param string $category Optional category filter
     * @return array Array of system templates
     */
    public function getSystemTemplates($category = null) {
        try {
            $sql = "SELECT * FROM checklist_templates WHERE is_system_template = 1";
            $params = [];

            if ($category) {
                $sql .= " AND category = :category";
                $params[':category'] = $category;
            }

            $sql .= " ORDER BY name ASC";

            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Error getting system templates: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all user templates
     *
     * @param int $userId User ID
     * @param string $category Optional category filter
     * @return array Array of user templates
     */
    public function getUserTemplates($userId, $category = null) {
        try {
            $sql = "SELECT * FROM checklist_templates WHERE created_by = :user_id";
            $params = [':user_id' => $userId];

            if ($category) {
                $sql .= " AND category = :category";
                $params[':category'] = $category;
            }

            $sql .= " ORDER BY name ASC";

            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Error getting user templates: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update a template
     *
     * @param int $templateId Template ID
     * @param array $data Template data to update
     * @return bool Success or failure
     */
    public function update($templateId, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $templateId];

            $allowedFields = ['name', 'description', 'template_type', 'category'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }

            if (empty($updateFields)) {
                return false;
            }

            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');

            $sql = "UPDATE checklist_templates SET " . implode(', ', $updateFields) . " WHERE id = :id";

            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating checklist template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a template if it belongs to the user
     *
     * @param int $templateId Template ID
     * @param int $userId User ID (to ensure only user's own templates can be deleted)
     * @return bool Success or failure
     */
    public function deleteIfOwner($templateId, $userId) {
        try {
            // Check if this is a system template or belongs to the user
            $template = $this->getTemplate($templateId);

            if (!$template || ($template['is_system_template'] == 1) || ($template['created_by'] != $userId)) {
                return false;
            }

            // First, delete all template items
            $templateItemModel = new ChecklistTemplateItem();
            $templateItemModel->deleteAllItems($templateId);

            // Then delete the template
            $sql = "DELETE FROM checklist_templates WHERE id = :id";
            return $this->db->execute($sql, [':id' => $templateId]);
        } catch (Exception $e) {
            error_log("Error deleting checklist template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a template
     *
     * @param int $id Template ID
     * @return bool Success or failure
     */
    public function delete($id) {
        try {
            // First, delete all template items
            $templateItemModel = new ChecklistTemplateItem();
            $templateItemModel->deleteAllItems($id);

            // Then delete the template
            $sql = "DELETE FROM checklist_templates WHERE id = :id";
            return $this->db->execute($sql, [':id' => $id]);
        } catch (Exception $e) {
            error_log("Error deleting checklist template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create a template from an existing checklist
     *
     * @param int $checklistId Checklist ID
     * @param array $templateData Template data
     * @param int $userId User ID
     * @return int|false The ID of the created template or false on failure
     */
    public function createFromChecklist($checklistId, $templateData, $userId) {
        try {
            // Get the checklist and its items
            $checklistModel = new Checklist();
            $checklist = $checklistModel->getChecklist($checklistId, $userId);

            if (!$checklist) {
                return false;
            }

            // Create the template
            $data = [
                'name' => $templateData['name'],
                'description' => $templateData['description'] ?? $checklist['description'],
                'template_type' => $templateData['template_type'] ?? 'standard',
                'category' => $templateData['category'] ?? 'general',
                'is_system_template' => 0,
                'created_by' => $userId
            ];

            $templateId = $this->create($data);

            if (!$templateId) {
                return false;
            }

            // Convert checklist items to template items
            $templateItems = [];

            foreach ($checklist['items'] as $item) {
                $templateItem = [
                    'text' => $item['text'],
                    'description' => $item['description'],
                    'sort_order' => $item['sort_order'],
                    'resource_link' => $item['resource_link']
                ];

                if (!empty($item['sub_items'])) {
                    $templateItem['sub_items'] = [];

                    foreach ($item['sub_items'] as $subItem) {
                        $templateItem['sub_items'][] = [
                            'text' => $subItem['text'],
                            'description' => $subItem['description'],
                            'sort_order' => $subItem['sort_order'],
                            'resource_link' => $subItem['resource_link']
                        ];
                    }
                }

                $templateItems[] = $templateItem;
            }

            // Add items to the template
            $this->addItemsToTemplate($templateId, $templateItems);

            return $templateId;
        } catch (Exception $e) {
            error_log("Error creating template from checklist: " . $e->getMessage());
            return false;
        }
    }
}
