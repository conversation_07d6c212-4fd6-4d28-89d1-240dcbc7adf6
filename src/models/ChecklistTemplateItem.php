<?php
/**
 * Checklist Template Item Model
 * 
 * Handles the creation, retrieval, and management of checklist template items
 */

require_once __DIR__ . '/BaseModel.php';

class ChecklistTemplateItem extends BaseModel {
    /**
     * Create a new template item
     * 
     * @param array $data Template item data
     * @return int|false The ID of the created item or false on failure
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO checklist_template_items (
                template_id, 
                parent_id, 
                text, 
                description, 
                sort_order, 
                resource_link,
                created_at, 
                updated_at
            ) VALUES (
                :template_id, 
                :parent_id, 
                :text, 
                :description, 
                :sort_order, 
                :resource_link,
                :created_at, 
                :updated_at
            )";
            
            $params = [
                ':template_id' => $data['template_id'],
                ':parent_id' => $data['parent_id'] ?? null,
                ':text' => $data['text'],
                ':description' => $data['description'] ?? '',
                ':sort_order' => $data['sort_order'] ?? 0,
                ':resource_link' => $data['resource_link'] ?? null,
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->db->insert($sql, $params);
        } catch (Exception $e) {
            error_log("Error creating template item: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a template item by ID
     * 
     * @param int $itemId Item ID
     * @return array|false Item data or false if not found
     */
    public function getItem($itemId) {
        try {
            $sql = "SELECT * FROM checklist_template_items WHERE id = :id";
            return $this->db->fetchOne($sql, [':id' => $itemId]);
        } catch (Exception $e) {
            error_log("Error getting template item: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all items for a template
     * 
     * @param int $templateId Template ID
     * @return array Array of template items organized in a hierarchy
     */
    public function getItemsByTemplate($templateId) {
        try {
            $sql = "SELECT * FROM checklist_template_items WHERE template_id = :template_id ORDER BY sort_order ASC";
            $items = $this->db->fetchAll($sql, [':template_id' => $templateId]);
            
            // Organize items into a hierarchy
            $itemMap = [];
            $rootItems = [];
            
            // First, map all items by their ID
            foreach ($items as $item) {
                $item['sub_items'] = [];
                $itemMap[$item['id']] = $item;
            }
            
            // Then, build the hierarchy
            foreach ($items as $item) {
                if ($item['parent_id'] === null) {
                    $rootItems[] = &$itemMap[$item['id']];
                } else {
                    if (isset($itemMap[$item['parent_id']])) {
                        $itemMap[$item['parent_id']]['sub_items'][] = &$itemMap[$item['id']];
                    }
                }
            }
            
            return $rootItems;
        } catch (Exception $e) {
            error_log("Error getting template items: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update a template item
     * 
     * @param int $itemId Item ID
     * @param array $data Item data to update
     * @return bool Success or failure
     */
    public function update($itemId, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $itemId];
            
            $allowedFields = ['text', 'description', 'sort_order', 'resource_link'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return false;
            }
            
            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "UPDATE checklist_template_items SET " . implode(', ', $updateFields) . " WHERE id = :id";
            
            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating template item: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a template item
     * 
     * @param int $itemId Item ID
     * @return bool Success or failure
     */
    public function delete($itemId) {
        try {
            // First, delete all child items
            $sql = "DELETE FROM checklist_template_items WHERE parent_id = :parent_id";
            $this->db->execute($sql, [':parent_id' => $itemId]);
            
            // Then delete the item itself
            $sql = "DELETE FROM checklist_template_items WHERE id = :id";
            return $this->db->execute($sql, [':id' => $itemId]);
        } catch (Exception $e) {
            error_log("Error deleting template item: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete all items for a template
     * 
     * @param int $templateId Template ID
     * @return bool Success or failure
     */
    public function deleteAllItems($templateId) {
        try {
            $sql = "DELETE FROM checklist_template_items WHERE template_id = :template_id";
            return $this->db->execute($sql, [':template_id' => $templateId]);
        } catch (Exception $e) {
            error_log("Error deleting all template items: " . $e->getMessage());
            return false;
        }
    }
}
