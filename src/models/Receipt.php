<?php
/**
 * Receipt Model
 *
 * Handles receipt-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/../utils/FileUploadUtil.php';

class Receipt extends BaseModel {
    protected $table = 'transaction_receipts';

    /**
     * Get receipts for a specific transaction
     *
     * @param int $transactionId Transaction ID
     * @return array Receipts
     */
    public function getTransactionReceipts($transactionId) {
        $sql = "SELECT * FROM {$this->table} WHERE transaction_id = ? ORDER BY created_at DESC";
        return $this->db->fetchAll($sql, [$transactionId]);
    }

    /**
     * Get receipts for a specific user
     *
     * @param int $userId User ID
     * @param array $filters Optional filters
     * @return array Receipts
     */
    public function getUserReceipts($userId, $filters = []) {
        $sql = "SELECT r.*, f.type as transaction_type, f.category as transaction_category, 
                f.date as transaction_date, f.amount as transaction_amount 
                FROM {$this->table} r
                JOIN finances f ON r.transaction_id = f.id
                WHERE r.user_id = ?";
        
        $params = [$userId];
        
        // Add filters if provided
        if (!empty($filters['transaction_type'])) {
            $sql .= " AND f.type = ?";
            $params[] = $filters['transaction_type'];
        }
        
        if (!empty($filters['category'])) {
            $sql .= " AND f.category = ?";
            $params[] = $filters['category'];
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND f.date >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND f.date <= ?";
            $params[] = $filters['date_to'];
        }
        
        $sql .= " ORDER BY f.date DESC, r.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Add a receipt to a transaction
     *
     * @param array $data Receipt data
     * @return int|bool Receipt ID on success, false on failure
     */
    public function addReceipt($data) {
        return $this->create($data);
    }

    /**
     * Delete a receipt
     *
     * @param int $receiptId Receipt ID
     * @param int $userId User ID (for verification)
     * @return bool True on success, false on failure
     */
    public function deleteReceipt($receiptId, $userId) {
        // Get receipt to verify ownership and get file path
        $receipt = $this->find($receiptId);
        
        if (!$receipt || $receipt['user_id'] != $userId) {
            return false;
        }
        
        // Delete the file
        FileUploadUtil::deleteFile($receipt['file_path']);
        
        // Delete the database record
        return $this->delete($receiptId);
    }
}
