<?php
/**
 * YouTube Saved Video Model
 *
 * Handles the creation, retrieval, and management of saved YouTube videos
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/YouTubeVideo.php';

class YouTubeSavedVideo extends BaseModel {
    /**
     * Save a video for a user
     *
     * @param array $data Saved video data
     * @return int|false The ID of the saved video or false on failure
     */
    public function saveVideo($data) {
        try {
            // Check if video is already saved
            $existingSaved = $this->getSavedVideo($data['user_id'], $data['video_id']);

            if ($existingSaved) {
                // Update existing saved video
                $updateFields = [];
                $params = [':id' => $existingSaved['id']];

                if (isset($data['folder'])) {
                    $updateFields[] = "folder = :folder";
                    $params[':folder'] = $data['folder'];
                }

                if (isset($data['notes'])) {
                    $updateFields[] = "notes = :notes";
                    $params[':notes'] = $data['notes'];
                }

                if (empty($updateFields)) {
                    return $existingSaved['id'];
                }

                $updateFields[] = "updated_at = :updated_at";
                $params[':updated_at'] = date('Y-m-d H:i:s');

                $sql = "UPDATE youtube_saved_videos SET " . implode(', ', $updateFields) . " WHERE id = :id";

                $this->db->execute($sql, $params);
                return $existingSaved['id'];
            } else {
                // Create new saved video
                $sql = "INSERT INTO youtube_saved_videos (
                    user_id,
                    video_id,
                    folder,
                    notes,
                    created_at,
                    updated_at
                ) VALUES (
                    :user_id,
                    :video_id,
                    :folder,
                    :notes,
                    :created_at,
                    :updated_at
                )";

                $params = [
                    ':user_id' => $data['user_id'],
                    ':video_id' => $data['video_id'],
                    ':folder' => $data['folder'] ?? null,
                    ':notes' => $data['notes'] ?? null,
                    ':created_at' => date('Y-m-d H:i:s'),
                    ':updated_at' => date('Y-m-d H:i:s')
                ];

                return $this->db->insert($sql, $params);
            }
        } catch (Exception $e) {
            error_log("Error saving YouTube video: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a saved video
     *
     * @param int $userId User ID
     * @param int $videoId Video ID
     * @return array|false Saved video data or false if not found
     */
    public function getSavedVideo($userId, $videoId) {
        try {
            $sql = "SELECT * FROM youtube_saved_videos WHERE user_id = :user_id AND video_id = :video_id";
            return $this->db->fetchOne($sql, [':user_id' => $userId, ':video_id' => $videoId]);
        } catch (Exception $e) {
            error_log("Error getting saved YouTube video: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get a saved video by ID
     *
     * @param int $savedId Saved video ID
     * @param int $userId User ID
     * @return array|false Saved video data or false if not found
     */
    public function getSavedById($savedId, $userId) {
        try {
            $sql = "SELECT s.*, v.title, v.youtube_id, v.thumbnail_url, v.channel_title
                    FROM youtube_saved_videos s
                    JOIN youtube_videos v ON s.video_id = v.id
                    WHERE s.id = :id AND s.user_id = :user_id";

            return $this->db->fetchOne($sql, [':id' => $savedId, ':user_id' => $userId]);
        } catch (Exception $e) {
            error_log("Error getting saved YouTube video by ID: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all saved videos for a user
     *
     * @param int $userId User ID
     * @param string $folder Optional folder filter
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array Array of saved videos
     */
    public function getUserSavedVideos($userId, $folder = null, $limit = null, $offset = null) {
        try {
            $sql = "SELECT s.*, v.title, v.youtube_id, v.thumbnail_url, v.channel_title, v.view_count, v.published_at
                    FROM youtube_saved_videos s
                    JOIN youtube_videos v ON s.video_id = v.id
                    WHERE s.user_id = :user_id";

            $params = [':user_id' => $userId];

            if ($folder !== null) {
                $sql .= " AND s.folder = :folder";
                $params[':folder'] = $folder;
            }

            $sql .= " ORDER BY s.created_at DESC";

            if ($limit !== null) {
                $sql .= " LIMIT :limit";
                $params[':limit'] = $limit;

                if ($offset !== null) {
                    $sql .= " OFFSET :offset";
                    $params[':offset'] = $offset;
                }
            }

            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Error getting user saved YouTube videos: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all folders for a user
     *
     * @param int $userId User ID
     * @return array Array of folders
     */
    public function getUserFolders($userId) {
        try {
            $sql = "SELECT DISTINCT folder FROM youtube_saved_videos WHERE user_id = :user_id AND folder IS NOT NULL ORDER BY folder ASC";
            $folders = $this->db->fetchAll($sql, [':user_id' => $userId]);

            return array_column($folders, 'folder');
        } catch (Exception $e) {
            error_log("Error getting user YouTube folders: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update a saved video if it belongs to the user
     *
     * @param int $savedId Saved video ID
     * @param array $data Saved video data to update
     * @param int $userId User ID
     * @return bool Success or failure
     */
    public function updateIfOwner($savedId, $data, $userId) {
        try {
            $updateFields = [];
            $params = [':id' => $savedId, ':user_id' => $userId];

            $allowedFields = ['folder', 'notes'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }

            if (empty($updateFields)) {
                return false;
            }

            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');

            $sql = "UPDATE youtube_saved_videos SET " . implode(', ', $updateFields) . " WHERE id = :id AND user_id = :user_id";

            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating saved YouTube video: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update a saved video
     *
     * @param int $id Saved video ID
     * @param array $data Saved video data to update
     * @return bool Success or failure
     */
    public function update($id, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $id];

            $allowedFields = ['folder', 'notes'];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }

            if (empty($updateFields)) {
                return false;
            }

            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');

            $sql = "UPDATE youtube_saved_videos SET " . implode(', ', $updateFields) . " WHERE id = :id";

            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating saved YouTube video: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a saved video
     *
     * @param int $id Saved video ID
     * @return bool Success or failure
     */
    public function delete($id) {
        try {
            $sql = "DELETE FROM youtube_saved_videos WHERE id = :id";
            return $this->db->execute($sql, [':id' => $id]);
        } catch (Exception $e) {
            error_log("Error deleting saved YouTube video: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a saved video if it belongs to the user
     *
     * @param int $savedId Saved video ID
     * @param int $userId User ID
     * @return bool Success or failure
     */
    public function deleteIfOwner($savedId, $userId) {
        try {
            $sql = "DELETE FROM youtube_saved_videos WHERE id = :id AND user_id = :user_id";
            return $this->db->execute($sql, [':id' => $savedId, ':user_id' => $userId]);
        } catch (Exception $e) {
            error_log("Error deleting saved YouTube video: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if a video is saved by a user
     *
     * @param int $userId User ID
     * @param int $videoId Video ID
     * @return bool True if saved, false otherwise
     */
    public function isVideoSaved($userId, $videoId) {
        try {
            $sql = "SELECT COUNT(*) as count FROM youtube_saved_videos WHERE user_id = :user_id AND video_id = :video_id";
            $result = $this->db->fetchOne($sql, [':user_id' => $userId, ':video_id' => $videoId]);

            return $result && $result['count'] > 0;
        } catch (Exception $e) {
            error_log("Error checking if YouTube video is saved: " . $e->getMessage());
            return false;
        }
    }
}
