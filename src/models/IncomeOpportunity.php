<?php
/**
 * Income Opportunity Model
 *
 * Handles income opportunity-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class IncomeOpportunity extends BaseModel {
    protected $table = 'income_opportunities';
    protected $logsTable = 'income_opportunity_logs';
    protected $milestonesTable = 'income_opportunity_milestones';

    /**
     * Get income opportunities for a specific user
     */
    public function getUserOpportunities($userId, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        // Add debug logging
        error_log("Getting opportunities for user ID: $userId");

        // Apply filters
        if (!empty($filters['income_type'])) {
            $sql .= " AND income_type = ?";
            $params[] = $filters['income_type'];
        }

        if (!empty($filters['status'])) {
            $sql .= " AND status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['skill_level'])) {
            $sql .= " AND skill_level = ?";
            $params[] = $filters['skill_level'];
        }

        if (!empty($filters['startup_cost'])) {
            $sql .= " AND startup_cost = ?";
            $params[] = $filters['startup_cost'];
        }

        if (!empty($filters['time_commitment'])) {
            $sql .= " AND time_commitment = ?";
            $params[] = $filters['time_commitment'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (name LIKE ? OR description LIKE ? OR category LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // Add ordering
        $sql .= " ORDER BY ";
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'name':
                    $sql .= "name ASC";
                    break;
                case 'income_potential':
                    $sql .= "estimated_income_max DESC";
                    break;
                case 'startup_cost':
                    $sql .= "FIELD(startup_cost, 'none', 'low', 'medium', 'high')";
                    break;
                case 'time_commitment':
                    $sql .= "FIELD(time_commitment, 'minimal', 'low', 'medium', 'high')";
                    break;
                case 'recently_updated':
                    $sql .= "updated_at DESC";
                    break;
                default:
                    $sql .= "priority DESC, name ASC";
            }
        } else {
            $sql .= "priority DESC, name ASC";
        }

        // Execute query and get results
        $results = $this->db->fetchAll($sql, $params);

        // Log the results
        error_log("Found " . ($results ? count($results) : 0) . " opportunities for user ID: $userId");

        return $results;
    }

    /**
     * Get active income opportunities for a specific user
     */
    public function getActiveOpportunities($userId, $limit = null) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND status = 'active'
                ORDER BY priority DESC, name ASC";

        if ($limit) {
            $sql .= " LIMIT ?";
            return $this->db->fetchAll($sql, [$userId, $limit]);
        }

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get opportunity logs
     */
    public function getOpportunityLogs($opportunityId, $limit = null) {
        $sql = "SELECT * FROM {$this->logsTable}
                WHERE opportunity_id = ?
                ORDER BY log_date DESC";

        if ($limit) {
            $sql .= " LIMIT ?";
            return $this->db->fetchAll($sql, [$opportunityId, $limit]);
        }

        return $this->db->fetchAll($sql, [$opportunityId]);
    }

    /**
     * Add a log entry for an opportunity
     */
    public function addLog($logData) {
        return $this->db->insert($this->logsTable, $logData);
    }

    /**
     * Get opportunity milestones
     */
    public function getOpportunityMilestones($opportunityId) {
        $sql = "SELECT * FROM {$this->milestonesTable}
                WHERE opportunity_id = ?
                ORDER BY target_date ASC, id ASC";

        return $this->db->fetchAll($sql, [$opportunityId]);
    }

    /**
     * Add a milestone for an opportunity
     */
    public function addMilestone($milestoneData) {
        return $this->db->insert($this->milestonesTable, $milestoneData);
    }

    /**
     * Update a milestone
     */
    public function updateMilestone($id, $data) {
        return $this->db->update(
            $this->milestonesTable,
            $data,
            "{$this->primaryKey} = ?",
            [$id]
        );
    }

    /**
     * Delete a milestone
     */
    public function deleteMilestone($id) {
        return $this->db->delete(
            $this->milestonesTable,
            "{$this->primaryKey} = ?",
            [$id]
        );
    }

    /**
     * Get total earnings for an opportunity
     */
    public function getTotalEarnings($opportunityId) {
        $sql = "SELECT SUM(amount_earned) as total_earnings
                FROM {$this->logsTable}
                WHERE opportunity_id = ?";

        $result = $this->db->fetchOne($sql, [$opportunityId]);
        return $result ? (float)$result['total_earnings'] : 0;
    }

    /**
     * Get total time invested in an opportunity
     */
    public function getTotalTimeInvested($opportunityId) {
        $sql = "SELECT SUM(hours_spent) as total_hours
                FROM {$this->logsTable}
                WHERE opportunity_id = ?";

        $result = $this->db->fetchOne($sql, [$opportunityId]);
        return $result ? (float)$result['total_hours'] : 0;
    }

    /**
     * Calculate ROI for an opportunity
     */
    public function calculateROI($opportunityId) {
        $opportunity = $this->find($opportunityId);
        if (!$opportunity) {
            return 0;
        }

        $totalEarnings = $this->getTotalEarnings($opportunityId);
        $totalHours = $this->getTotalTimeInvested($opportunityId);

        if ($totalHours <= 0) {
            return 0;
        }

        return $totalEarnings / $totalHours;
    }

    /**
     * Get income opportunity categories
     */
    public function getCategories($userId) {
        $sql = "SELECT DISTINCT category FROM {$this->table} WHERE user_id = ? ORDER BY category";
        $results = $this->db->fetchAll($sql, [$userId]);

        $categories = [];
        foreach ($results as $result) {
            $categories[] = $result['category'];
        }

        return $categories;
    }

    /**
     * Get income opportunity summary for a user
     */
    public function getOpportunitySummary($userId) {
        $sql = "SELECT
                    COUNT(*) as total_opportunities,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_opportunities,
                    SUM(CASE WHEN status = 'implementing' THEN 1 ELSE 0 END) as implementing_opportunities,
                    SUM(CASE WHEN status = 'researching' THEN 1 ELSE 0 END) as researching_opportunities,
                    SUM(CASE WHEN status = 'considering' THEN 1 ELSE 0 END) as considering_opportunities,
                    COUNT(DISTINCT category) as total_categories
                FROM {$this->table}
                WHERE user_id = ?";

        return $this->db->fetchOne($sql, [$userId]);
    }
}
