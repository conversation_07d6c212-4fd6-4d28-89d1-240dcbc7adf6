<?php
/**
 * YouTube Search Model
 * 
 * Handles the creation, retrieval, and management of YouTube searches
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/YouTubeVideo.php';
require_once __DIR__ . '/YouTubeAPIQuota.php';
require_once __DIR__ . '/../utils/YouTubeAPI.php';

class YouTubeSearch extends BaseModel {
    /**
     * Create a new YouTube search
     * 
     * @param array $data Search data
     * @return int|false The ID of the created search or false on failure
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO youtube_searches (
                user_id, 
                search_query, 
                search_type, 
                status, 
                created_at, 
                updated_at
            ) VALUES (
                :user_id, 
                :search_query, 
                :search_type, 
                :status, 
                :created_at, 
                :updated_at
            )";
            
            $params = [
                ':user_id' => $data['user_id'],
                ':search_query' => $data['search_query'],
                ':search_type' => $data['search_type'] ?? 'keyword',
                ':status' => $data['status'] ?? 'pending',
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ];
            
            return $this->db->insert($sql, $params);
        } catch (Exception $e) {
            error_log("Error creating YouTube search: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get a search by ID
     * 
     * @param int $searchId Search ID
     * @param int $userId User ID
     * @return array|false Search data or false if not found
     */
    public function getSearch($searchId, $userId) {
        try {
            $sql = "SELECT * FROM youtube_searches WHERE id = :id AND user_id = :user_id";
            return $this->db->fetchOne($sql, [':id' => $searchId, ':user_id' => $userId]);
        } catch (Exception $e) {
            error_log("Error getting YouTube search: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all searches for a user
     * 
     * @param int $userId User ID
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array Array of searches
     */
    public function getUserSearches($userId, $limit = null, $offset = null) {
        try {
            $sql = "SELECT * FROM youtube_searches WHERE user_id = :user_id ORDER BY created_at DESC";
            $params = [':user_id' => $userId];
            
            if ($limit !== null) {
                $sql .= " LIMIT :limit";
                $params[':limit'] = $limit;
                
                if ($offset !== null) {
                    $sql .= " OFFSET :offset";
                    $params[':offset'] = $offset;
                }
            }
            
            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Error getting user YouTube searches: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Update a search
     * 
     * @param int $searchId Search ID
     * @param array $data Search data to update
     * @return bool Success or failure
     */
    public function update($searchId, $data) {
        try {
            $updateFields = [];
            $params = [':id' => $searchId];
            
            $allowedFields = ['status', 'results_count'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "{$field} = :{$field}";
                    $params[":{$field}"] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return false;
            }
            
            $updateFields[] = "updated_at = :updated_at";
            $params[':updated_at'] = date('Y-m-d H:i:s');
            
            $sql = "UPDATE youtube_searches SET " . implode(', ', $updateFields) . " WHERE id = :id";
            
            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("Error updating YouTube search: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a search
     * 
     * @param int $id Search ID
     * @return bool Success or failure
     */
    public function delete($id) {
        try {
            $sql = "DELETE FROM youtube_searches WHERE id = :id";
            return $this->db->execute($sql, [':id' => $id]);
        } catch (Exception $e) {
            error_log("Error deleting YouTube search: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a search if it belongs to the user
     * 
     * @param int $searchId Search ID
     * @param int $userId User ID
     * @return bool Success or failure
     */
    public function deleteIfOwner($searchId, $userId) {
        try {
            $sql = "DELETE FROM youtube_searches WHERE id = :id AND user_id = :user_id";
            return $this->db->execute($sql, [':id' => $searchId, ':user_id' => $userId]);
        } catch (Exception $e) {
            error_log("Error deleting YouTube search: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Execute a YouTube search
     * 
     * @param int $searchId Search ID
     * @param int $userId User ID
     * @param string $apiKey YouTube API key
     * @return bool Success or failure
     */
    public function executeSearch($searchId, $userId, $apiKey) {
        try {
            // Get the search
            $search = $this->getSearch($searchId, $userId);
            
            if (!$search) {
                return false;
            }
            
            // Update search status to in_progress
            $this->update($searchId, ['status' => 'in_progress']);
            
            // Check API quota
            $quotaModel = new YouTubeAPIQuota();
            if (!$quotaModel->checkQuotaAvailable($userId)) {
                $this->update($searchId, ['status' => 'failed']);
                return false;
            }
            
            // Initialize YouTube API
            $youtubeAPI = new YouTubeAPI($apiKey);
            
            // Execute search based on search type
            $results = [];
            switch ($search['search_type']) {
                case 'keyword':
                    $results = $youtubeAPI->searchByKeyword($search['search_query']);
                    break;
                case 'channel':
                    $results = $youtubeAPI->searchByChannel($search['search_query']);
                    break;
                case 'trending':
                    $results = $youtubeAPI->getTrending();
                    break;
                default:
                    $results = $youtubeAPI->searchByKeyword($search['search_query']);
                    break;
            }
            
            // Update quota usage
            $quotaModel->updateQuotaUsage($userId, $youtubeAPI->getLastQueryCost());
            
            if (empty($results)) {
                $this->update($searchId, ['status' => 'completed', 'results_count' => 0]);
                return true;
            }
            
            // Save videos
            $videoModel = new YouTubeVideo();
            $savedCount = 0;
            
            foreach ($results as $result) {
                $videoData = [
                    'youtube_id' => $result['id'],
                    'search_id' => $searchId,
                    'title' => $result['title'],
                    'description' => $result['description'],
                    'channel_id' => $result['channel_id'],
                    'channel_title' => $result['channel_title'],
                    'published_at' => $result['published_at'],
                    'view_count' => $result['view_count'],
                    'like_count' => $result['like_count'],
                    'comment_count' => $result['comment_count'],
                    'duration' => $result['duration'],
                    'thumbnail_url' => $result['thumbnail_url']
                ];
                
                $videoId = $videoModel->createOrUpdate($videoData);
                
                if ($videoId) {
                    $savedCount++;
                    
                    // Save tags
                    if (!empty($result['tags'])) {
                        $videoModel->saveTags($videoId, $result['tags']);
                    }
                }
            }
            
            // Update search status
            $this->update($searchId, [
                'status' => 'completed',
                'results_count' => $savedCount
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Error executing YouTube search: " . $e->getMessage());
            $this->update($searchId, ['status' => 'failed']);
            return false;
        }
    }
    
    /**
     * Get videos for a search
     * 
     * @param int $searchId Search ID
     * @param int $userId User ID
     * @param int $limit Optional limit
     * @param int $offset Optional offset
     * @return array Array of videos
     */
    public function getSearchVideos($searchId, $userId, $limit = null, $offset = null) {
        try {
            // Verify the search belongs to the user
            $search = $this->getSearch($searchId, $userId);
            
            if (!$search) {
                return [];
            }
            
            $videoModel = new YouTubeVideo();
            return $videoModel->getVideosBySearch($searchId, $limit, $offset);
        } catch (Exception $e) {
            error_log("Error getting search videos: " . $e->getMessage());
            return [];
        }
    }
}
