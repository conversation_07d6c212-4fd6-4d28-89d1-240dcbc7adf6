<?php
/**
 * Productivity Strategy Model
 *
 * Handles productivity strategies and user tracking
 */

require_once __DIR__ . '/BaseModel.php';

class ProductivityStrategy extends BaseModel {
    protected $table = 'productivity_strategies';
    protected $userStrategiesTable = 'user_strategies';

    /**
     * Get all productivity strategies
     */
    public function getAllStrategies() {
        // Get all strategies
        $sql = "SELECT * FROM {$this->table} ORDER BY name ASC";
        $result = $this->db->fetchAll($sql);

        if (!$result) {
            return [];
        }

        // Use PHP to filter out duplicates by name
        $uniqueStrategies = [];
        $seenNames = [];

        foreach ($result as $strategy) {
            if (!in_array($strategy['name'], $seenNames)) {
                $seenNames[] = $strategy['name'];
                $uniqueStrategies[] = $strategy;
            }
        }

        return $uniqueStrategies;
    }

    /**
     * Get strategies by difficulty
     */
    public function getStrategiesByDifficulty($difficulty) {
        $sql = "SELECT * FROM {$this->table}
                WHERE difficulty = ?
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, [$difficulty]);
    }

    /**
     * Get strategies by ADHD challenge
     */
    public function getStrategiesByChallenge($challenge) {
        $sql = "SELECT * FROM {$this->table}
                WHERE adhd_challenge LIKE ?
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, ["%$challenge%"]);
    }

    /**
     * Get strategy by ID
     */
    public function getStrategy($id) {
        return $this->find($id);
    }

    /**
     * Get user's active strategies
     */
    public function getUserActiveStrategies($userId) {
        $sql = "SELECT
                    us.*,
                    ps.name,
                    ps.description,
                    ps.implementation_steps,
                    ps.adhd_challenge,
                    ps.difficulty,
                    ps.time_required
                FROM {$this->userStrategiesTable} us
                JOIN {$this->table} ps ON us.strategy_id = ps.id
                WHERE us.user_id = ? AND us.is_active = 1
                ORDER BY us.start_date DESC";

        $result = $this->db->fetchAll($sql, [$userId]);

        if (!$result) {
            return [];
        }

        // Use PHP to filter out duplicates by strategy_id
        $uniqueStrategies = [];
        $seenStrategyIds = [];

        foreach ($result as $strategy) {
            if (!in_array($strategy['strategy_id'], $seenStrategyIds)) {
                $seenStrategyIds[] = $strategy['strategy_id'];
                $uniqueStrategies[] = $strategy;
            }
        }

        return $uniqueStrategies;
    }

    /**
     * Get user's strategy history
     */
    public function getUserStrategyHistory($userId) {
        $sql = "SELECT
                    us.*,
                    ps.name,
                    ps.description,
                    ps.implementation_steps,
                    ps.adhd_challenge,
                    ps.difficulty,
                    ps.time_required
                FROM {$this->userStrategiesTable} us
                JOIN {$this->table} ps ON us.strategy_id = ps.id
                WHERE us.user_id = ? AND us.is_active = 0
                ORDER BY us.start_date DESC";

        $result = $this->db->fetchAll($sql, [$userId]);

        if (!$result) {
            return [];
        }

        // Use PHP to filter out duplicates by strategy_id
        $uniqueStrategies = [];
        $seenStrategyIds = [];

        foreach ($result as $strategy) {
            if (!in_array($strategy['strategy_id'], $seenStrategyIds)) {
                $seenStrategyIds[] = $strategy['strategy_id'];
                $uniqueStrategies[] = $strategy;
            }
        }

        return $uniqueStrategies;
    }

    /**
     * Add strategy to user's active strategies
     */
    public function addUserStrategy($data) {
        return $this->db->insert($this->userStrategiesTable, $data);
    }

    /**
     * Update user strategy
     */
    public function updateUserStrategy($id, $data) {
        return $this->db->update(
            $this->userStrategiesTable,
            $data,
            "id = ?",
            [$id]
        );
    }

    /**
     * Deactivate user strategy
     */
    public function deactivateUserStrategy($id, $userId) {
        return $this->db->update(
            $this->userStrategiesTable,
            [
                'is_active' => 0,
                'updated_at' => date('Y-m-d H:i:s')
            ],
            "id = ? AND user_id = ?",
            [$id, $userId]
        );
    }

    /**
     * Get user strategy by strategy ID
     */
    public function getUserStrategyByStrategyId($userId, $strategyId) {
        $sql = "SELECT *
                FROM {$this->userStrategiesTable}
                WHERE user_id = ? AND strategy_id = ?
                ORDER BY is_active DESC, updated_at DESC
                LIMIT 1";

        return $this->db->fetchOne($sql, [$userId, $strategyId]);
    }

    /**
     * Get most effective strategies for a user
     */
    public function getMostEffectiveStrategies($userId) {
        $sql = "SELECT
                    us.effectiveness_rating,
                    ps.name,
                    ps.adhd_challenge
                FROM {$this->userStrategiesTable} us
                JOIN {$this->table} ps ON us.strategy_id = ps.id
                WHERE us.user_id = ? AND us.effectiveness_rating IS NOT NULL
                ORDER BY us.effectiveness_rating DESC
                LIMIT 3";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get recommended strategies based on symptom scores
     */
    public function getRecommendedStrategies($userId) {
        // Get user's lowest symptom scores from the last 7 days
        require_once __DIR__ . '/ADHDSymptom.php';
        $symptomModel = new ADHDSymptom();
        $averages = $symptomModel->getSymptomAverages($userId, 7);

        // If no data, return general recommendations
        if (!$averages) {
            return $this->getStrategiesByDifficulty('easy');
        }

        // Find the lowest scoring areas
        $scores = [
            'focus' => $averages['avg_focus'],
            'productivity' => $averages['avg_productivity'],
            'consistency' => $averages['avg_consistency'],
            'organization' => $averages['avg_organization'],
            'impulsivity' => $averages['avg_impulsivity'],
            'emotional' => $averages['avg_emotional']
        ];

        // Sort by score (ascending)
        asort($scores);

        // Get the two lowest scoring areas
        $lowestAreas = array_slice(array_keys($scores), 0, 2);

        // Map symptom areas to challenges
        $challengeMap = [
            'focus' => 'Focus',
            'productivity' => 'Productivity',
            'consistency' => 'Consistency',
            'organization' => 'Organization',
            'impulsivity' => 'Impulsivity',
            'emotional' => 'Emotional Regulation'
        ];

        // Get strategies for the lowest areas
        $recommendations = [];
        foreach ($lowestAreas as $area) {
            $challenge = $challengeMap[$area];
            $strategies = $this->getStrategiesByChallenge($challenge);
            $recommendations = array_merge($recommendations, $strategies);
        }

        // Remove duplicates
        $uniqueRecommendations = [];
        $ids = [];

        foreach ($recommendations as $strategy) {
            if (!in_array($strategy['id'], $ids)) {
                $ids[] = $strategy['id'];
                $uniqueRecommendations[] = $strategy;
            }
        }

        return $uniqueRecommendations;
    }
}
