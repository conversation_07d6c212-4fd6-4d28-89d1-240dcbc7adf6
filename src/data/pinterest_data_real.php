<?php
/**
 * Pinterest Data (Real API)
 *
 * This file contains functions to fetch real Pinterest data using the Pinterest API.
 */

require_once __DIR__ . '/../utils/Environment.php';
require_once __DIR__ . '/../api/PinterestAPIFactory.php';

// Load environment variables
Environment::load();

/**
 * Initialize the Pinterest API
 *
 * @return mixed PinterestAPI, PinterestOfficialAPI, or null
 */
function initPinterestAPI() {
    // Get Pinterest API credentials from environment variables
    $email = Environment::get('PINTEREST_EMAIL', '<EMAIL>');
    $password = Environment::get('PINTEREST_PASSWORD', 'Asa@4894');
    $username = Environment::get('PINTEREST_USERNAME', 'maxwelldoe7788');
    $chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');

    // Use the factory to get the appropriate API
    try {
        $api = PinterestAPI::getInstance($email, $password, $username, null, null, $chromeProfile);
        return $api;
    } catch (Exception $e) {
        error_log('Pinterest API initialization error: ' . $e->getMessage());
        return null;
    }
}

/**
 * Generate a Pinterest URL for a real pin
 *
 * @param string $title The pin title
 * @param string $searchTerm The search term
 * @return array An array containing the pin URL and pin ID
 */
function generatePinterestPinUrl($title, $searchTerm) {
    // Search for pins using the factory
    $pins = PinterestAPIFactory::search($searchTerm, 10);

    // If no pins found, return a placeholder
    if (empty($pins)) {
        return [
            'url' => 'https://www.pinterest.com/pin/573786808744734357/',
            'pin_id' => '573786808744734357',
            'direct_url' => 'https://www.pinterest.com/pin/573786808744734357/'
        ];
    }

    // Get a random pin
    $pin = $pins[array_rand($pins)];

    // Create a direct pin URL
    $url = "https://www.pinterest.com/pin/{$pin['pin_id']}/";

    return [
        'url' => $url,
        'pin_id' => $pin['pin_id'],
        'direct_url' => $url,
        'image_url' => $pin['image_url'] ?? ''
    ];
}

/**
 * Generate Pinterest data based on a search term using the real API
 *
 * @param string $searchTerm The search term to generate data for
 * @param int $count The number of pins to generate
 * @return array An array of pin data
 */
function getPinterestData($searchTerm, $count = 20) {
    // Normalize search term
    $searchTerm = strtolower(trim($searchTerm));

    // Check if we should use the API or fallback directly
    $useApi = true;

    // Check if API is disabled via environment variable
    if (Environment::get('DISABLE_PINTEREST_API') === 'true') {
        $useApi = false;
        error_log('Pinterest API disabled via environment variable. Using fallback data.');
    }

    // Check if we're on a shared hosting environment (Hostinger)
    if (strpos($_SERVER['SERVER_SOFTWARE'] ?? '', 'LiteSpeed') !== false) {
        // Hostinger uses LiteSpeed - we should use fallback data on production
        $useApi = false;
        error_log('Detected LiteSpeed server (likely Hostinger). Using fallback data.');
    }

    // If API is enabled, try to use it
    if ($useApi) {
        try {
            // Search for pins using the factory
            $pins = PinterestAPIFactory::search($searchTerm, $count);

            // If no pins found, use the fallback method
            if (empty($pins)) {
                return getPinterestDataFallback($searchTerm, $count);
            }

            // Process the pins
            $processedPins = [];
            foreach ($pins as $index => $pin) {
                // Create a unique image filename
                $imageFilename = 'pinterest_' . $pin['pin_id'] . '.jpg';

                // Determine the correct path based on environment
                $uploadDir = '/momentum/uploads/pinterest/';

                // Check if we're in Laragon
                if (strpos($_SERVER['SERVER_SOFTWARE'] ?? '', 'Laragon') !== false) {
                    $uploadDir = '/momentum/uploads/pinterest/';
                } else if (strpos($_SERVER['SERVER_SOFTWARE'] ?? '', 'LiteSpeed') !== false) {
                    // Hostinger path
                    $uploadDir = '/uploads/pinterest/';
                }

                $imagePath = $uploadDir . $imageFilename;
                $fullImagePath = $_SERVER['DOCUMENT_ROOT'] . $imagePath;

                // Create the directory if it doesn't exist
                $dir = dirname($fullImagePath);
                if (!file_exists($dir)) {
                    mkdir($dir, 0755, true);
                }

                // Check if image already exists
                $imageDownloaded = file_exists($fullImagePath);

                // Try to download the image if it doesn't exist
                if (!$imageDownloaded && !empty($pin['image_url'])) {
                    $imageDownloaded = PinterestAPIFactory::downloadImage($pin['image_url'], $fullImagePath);
                }

                // Use a placeholder if download failed
                if (!$imageDownloaded) {
                    $imagePath = "https://via.placeholder.com/600x800/f8f9fa/dc3545?text=" . urlencode("Pinterest Image " . ($index + 1));
                }

                // Create the pin
                $processedPins[] = [
                    'id' => $index + 1,
                    'pin_url' => $pin['pin_url'],
                    'pin_id' => $pin['pin_id'],
                    'image_url' => $imagePath,
                    'title' => $pin['title'] ?? 'Pinterest Pin',
                    'description' => $pin['description'] ?? '',
                    'board_name' => $pin['board_name'] ?? 'Pinterest Board',
                    'save_count' => $pin['save_count'] ?? 0,
                    'comment_count' => $pin['comment_count'] ?? 0,
                    'created_at' => $pin['created_at'] ?? date('Y-m-d H:i:s'),
                    'search_term' => $searchTerm
                ];
            }

            return $processedPins;
        } catch (Exception $e) {
            error_log('Error using Pinterest API: ' . $e->getMessage());
            return getPinterestDataFallback($searchTerm, $count);
        }
    }

    // If we get here, we should use the fallback method
    return getPinterestDataFallback($searchTerm, $count);
}

/**
 * Fallback method to generate Pinterest data if the API fails
 *
 * @param string $searchTerm The search term to generate data for
 * @param int $count The number of pins to generate
 * @return array An array of pin data
 */
function getPinterestDataFallback($searchTerm, $count = 20) {
    // Normalize search term
    $searchTerm = strtolower(trim($searchTerm));

    // Seed the random number generator with the search term to get consistent results
    srand(crc32($searchTerm));

    // Define categories based on search term
    $categories = [
        'home decor' => [
            'boards' => ['Modern Home', 'Minimalist Living', 'Cozy Spaces', 'DIY Home Projects', 'Interior Design', 'Home Organization'],
            'titles' => ['Beautiful %s Ideas for Your Home', 'Creative %s Inspiration', 'Modern %s Designs', 'DIY %s Projects', 'Stunning %s for Small Spaces'],
            'descriptions' => [
                'Transform your home with these beautiful %s ideas that will inspire you.',
                'Check out these amazing %s designs that will elevate your home decor.',
                'Simple yet elegant %s ideas for any home style.',
                'Creative and budget-friendly %s projects you can do yourself.',
                'Discover the latest trends in %s for your home.'
            ],
            'keywords' => ['minimalist', 'modern', 'rustic', 'scandinavian', 'industrial', 'bohemian', 'farmhouse', 'contemporary', 'traditional', 'coastal']
        ],
        'digital marketing' => [
            'boards' => ['Marketing Strategy', 'Social Media Tips', 'Content Marketing', 'SEO Techniques', 'Email Marketing', 'Digital Advertising'],
            'titles' => ['Effective %s Strategies', 'Ultimate Guide to %s', '%s Tips for Business Growth', 'How to Master %s', 'Boost Your Business with %s'],
            'descriptions' => [
                'Learn how to improve your %s strategy with these proven techniques.',
                'Discover the latest %s trends that will help your business stand out.',
                'Practical %s tips that you can implement today for better results.',
                'A comprehensive guide to %s for beginners and experts alike.',
                'Optimize your %s approach with these industry-leading practices.'
            ],
            'keywords' => ['social media', 'content', 'SEO', 'email', 'analytics', 'conversion', 'branding', 'strategy', 'advertising', 'engagement']
        ],
        'fitness' => [
            'boards' => ['Workout Routines', 'Healthy Recipes', 'Fitness Motivation', 'Weight Loss', 'Strength Training', 'Yoga & Stretching'],
            'titles' => ['Effective %s Workouts', '%s Routine for Beginners', 'Advanced %s Techniques', '30-Day %s Challenge', 'Quick %s Exercises'],
            'descriptions' => [
                'Transform your body with these effective %s workouts.',
                'Follow this %s routine to achieve your fitness goals faster.',
                'Learn proper %s techniques to maximize your results and prevent injury.',
                'Challenge yourself with this 30-day %s program designed for all fitness levels.',
                'Quick and effective %s exercises you can do anywhere, anytime.'
            ],
            'keywords' => ['cardio', 'strength', 'HIIT', 'yoga', 'pilates', 'weightlifting', 'bodyweight', 'running', 'flexibility', 'nutrition']
        ],
        'default' => [
            'boards' => ['Inspiration', 'Ideas', 'Tips & Tricks', 'How-To Guides', 'Resources', 'Favorites'],
            'titles' => ['Amazing %s Ideas', 'Creative %s Inspiration', 'Ultimate Guide to %s', 'Best %s Tips', 'How to Master %s'],
            'descriptions' => [
                'Discover amazing %s ideas that will inspire your next project.',
                'Creative %s inspiration for everyone, from beginners to experts.',
                'The ultimate guide to %s with step-by-step instructions.',
                'Learn the best %s tips and tricks from industry experts.',
                'Master %s with these comprehensive resources and guides.'
            ],
            'keywords' => ['creative', 'innovative', 'practical', 'simple', 'advanced', 'trending', 'popular', 'essential', 'unique', 'effective']
        ]
    ];

    // Determine which category to use
    $category = 'default';
    foreach (array_keys($categories) as $key) {
        if (strpos($searchTerm, $key) !== false) {
            $category = $key;
            break;
        }
    }

    // If no specific category matches, use the default
    $categoryData = $categories[$category];

    // Generate pins
    $pins = [];
    for ($i = 1; $i <= $count; $i++) {
        // Generate random data
        $boardName = $categoryData['boards'][array_rand($categoryData['boards'])];
        $keyword = $categoryData['keywords'][array_rand($categoryData['keywords'])];
        $titleTemplate = $categoryData['titles'][array_rand($categoryData['titles'])];
        $descriptionTemplate = $categoryData['descriptions'][array_rand($categoryData['descriptions'])];

        // Format title and description with keyword
        $title = sprintf($titleTemplate, ucfirst($keyword));
        $description = sprintf($descriptionTemplate, $keyword);

        // Generate random engagement metrics
        $saveCount = rand(50, 5000);
        $commentCount = rand(0, round($saveCount * 0.1)); // Comments are typically 0-10% of saves

        // Generate a random creation date within the last 30 days
        $createdAt = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));

        // Generate a real Pinterest pin URL and ID
        $pinData = generatePinterestPinUrl($title, $searchTerm);

        // Create a placeholder image URL that won't cause Access Denied errors
        $placeholderImageUrl = "https://via.placeholder.com/600x800/f8f9fa/dc3545?text=" . urlencode("Pinterest Image " . $i);

        // Create the pin
        $pins[] = [
            'id' => $i,
            'pin_url' => $pinData['url'],
            'pin_id' => $pinData['pin_id'],
            'image_url' => $placeholderImageUrl, // Use placeholder instead of Pinterest CDN
            'title' => $title,
            'description' => $description,
            'board_name' => $boardName,
            'save_count' => $saveCount,
            'comment_count' => $commentCount,
            'created_at' => $createdAt,
            'search_term' => $searchTerm
        ];
    }

    return $pins;
}
