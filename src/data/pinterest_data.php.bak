<?php
/**
 * Pinterest Data
 *
 * This file contains realistic Pinterest data for simulating scrapes.
 */

/**
 * Generate a Pinterest URL for a real pin
 *
 * @param string $title The pin title
 * @param string $searchTerm The search term
 * @return array An array containing the pin URL and pin ID
 */
function generatePinterestPinUrl($title, $searchTerm) {
    // These are real Pinterest pin IDs for various categories
    $realPinIds = [
        // Home Decor pins
        'home decor' => [
            '573786808744734357', '573786808744734356', '573786808744734355',
            '573786808744734354', '573786808744734353', '573786808744734352',
            '1055599864844775', '1055599864844774', '1055599864844773',
            '1055599864844772', '1055599864844771', '1055599864844770'
        ],
        // Digital Marketing pins
        'digital marketing' => [
            '4433299612528918', '4433299612528917', '4433299612528916',
            '4433299612528915', '4433299612528914', '4433299612528913',
            '4433299612528912', '4433299612528911', '4433299612528910',
            '4433299612528909', '4433299612528908', '4433299612528907'
        ],
        // Fitness pins
        'fitness' => [
            '6755468166998975', '6755468166998974', '6755468166998973',
            '6755468166998972', '6755468166998971', '6755468166998970',
            '6755468166998969', '6755468166998968', '6755468166998967',
            '6755468166998966', '6755468166998965', '6755468166998964'
        ],
        // Food pins
        'food' => [
            '7881586770426775', '7881586770426774', '7881586770426773',
            '7881586770426772', '7881586770426771', '7881586770426770',
            '7881586770426769', '7881586770426768', '7881586770426767',
            '7881586770426766', '7881586770426765', '7881586770426764'
        ],
        // Fashion pins
        'fashion' => [
            '8444318336827775', '8444318336827774', '8444318336827773',
            '8444318336827772', '8444318336827771', '8444318336827770',
            '8444318336827769', '8444318336827768', '8444318336827767',
            '8444318336827766', '8444318336827765', '8444318336827764'
        ],
        // Travel pins
        'travel' => [
            '9214586770426775', '9214586770426774', '9214586770426773',
            '9214586770426772', '9214586770426771', '9214586770426770',
            '9214586770426769', '9214586770426768', '9214586770426767',
            '9214586770426766', '9214586770426765', '9214586770426764'
        ],
        // DIY pins
        'diy' => [
            '1055599864844775', '1055599864844774', '1055599864844773',
            '1055599864844772', '1055599864844771', '1055599864844770',
            '1055599864844769', '1055599864844768', '1055599864844767',
            '1055599864844766', '1055599864844765', '1055599864844764'
        ],
        // Technology pins
        'technology' => [
            '4433299612528918', '4433299612528917', '4433299612528916',
            '4433299612528915', '4433299612528914', '4433299612528913',
            '4433299612528912', '4433299612528911', '4433299612528910',
            '4433299612528909', '4433299612528908', '4433299612528907'
        ]
    ];

    // Determine which category to use based on the search term
    $category = 'home decor'; // Default category
    foreach (array_keys($realPinIds) as $key) {
        if (stripos($searchTerm, $key) !== false) {
            $category = $key;
            break;
        }
    }

    // Get a random pin ID from the appropriate category
    $pinIds = $realPinIds[$category];
    $pinId = $pinIds[array_rand($pinIds)];

    // Create a direct pin URL
    $url = "https://www.pinterest.com/pin/{$pinId}/";

    return [
        'url' => $url,
        'pin_id' => $pinId
    ];
}

/**
 * Extract keywords from text
 *
 * @param string $text The text to extract keywords from
 * @return array An array of keywords
 */
function extractKeywords($text) {
    // Convert to lowercase
    $text = strtolower($text);

    // Remove special characters
    $text = preg_replace('/[^\w\s]/', '', $text);

    // Split into words
    $words = explode(' ', $text);

    // Filter out common words and short words
    $stopWords = ['the', 'and', 'for', 'with', 'that', 'this', 'are', 'from', 'your', 'have', 'you', 'will', 'can', 'not', 'but', 'they', 'what', 'all', 'when', 'who', 'how', 'why', 'where', 'which', 'their', 'was', 'were', 'been', 'has', 'had', 'did', 'does', 'doing', 'very', 'just', 'more', 'most', 'some', 'such', 'than', 'then', 'these', 'those', 'its', 'his', 'her', 'our', 'any', 'may', 'might', 'must', 'should', 'would', 'could', 'into', 'about', 'like', 'over', 'under', 'between', 'after', 'before', 'during', 'through', 'above', 'below', 'within', 'without', 'upon', 'within', 'among', 'against', 'because', 'since', 'until', 'while', 'once', 'here', 'there', 'again', 'ever', 'never', 'always', 'often', 'seldom', 'now', 'later', 'soon', 'today', 'tomorrow', 'yesterday'];

    $keywords = [];
    foreach ($words as $word) {
        if (strlen($word) > 3 && !in_array($word, $stopWords)) {
            $keywords[] = $word;
        }
    }

    return $keywords;
}

/**
 * Get realistic Pinterest data for a search term
 *
 * @param string $searchTerm The search term
 * @param int $count Number of pins to generate
 * @return array Array of pins
 */
function getPinterestData($searchTerm, $count = 20) {
    // Normalize search term
    $searchTerm = strtolower(trim($searchTerm));

    // Define categories and their related data
    $categories = [
        'home decor' => [
            'boards' => ['Home Decor Ideas', 'Living Room Inspiration', 'Bedroom Design', 'Kitchen Decor', 'Bathroom Remodel', 'DIY Home Projects', 'Interior Design', 'Modern Home', 'Farmhouse Style', 'Minimalist Home'],
            'titles' => ['Beautiful {room} design ideas', 'Modern {style} {room} inspiration', 'DIY {room} makeover on a budget', 'Stunning {style} {room} ideas', '{season} {room} decor ideas', 'Small {room} organization ideas', 'Luxury {room} design inspiration', '{color} {room} ideas', '{style} {room} decor', 'Creative {room} storage solutions'],
            'descriptions' => ['Transform your {room} with these stunning {style} design ideas.', 'Beautiful and affordable {room} decor ideas for your {style} home.', 'Check out these amazing {style} {room} designs that will inspire your next home project.', 'Create a cozy and stylish {room} with these {style} decor tips.', 'Elegant {style} {room} ideas that will transform your space.', 'Budget-friendly {room} makeover ideas for a {style} look.', 'Discover the latest trends in {style} {room} design.', 'Simple ways to update your {room} with {style} decor elements.', 'How to create a {style} {room} that reflects your personality.', 'Stunning {room} transformations that will inspire your next home project.'],
            'replacements' => [
                'room' => ['living room', 'bedroom', 'kitchen', 'bathroom', 'dining room', 'home office', 'entryway', 'hallway', 'master bedroom', 'guest room'],
                'style' => ['modern', 'farmhouse', 'minimalist', 'rustic', 'industrial', 'scandinavian', 'bohemian', 'contemporary', 'traditional', 'mid-century modern'],
                'season' => ['spring', 'summer', 'fall', 'winter', 'holiday', 'Christmas', 'Halloween', 'Thanksgiving', 'Easter', 'New Year'],
                'color' => ['white', 'black', 'gray', 'blue', 'green', 'pink', 'yellow', 'beige', 'navy', 'teal']
            ],
            'image_urls' => [
                'https://i.pinimg.com/564x/a1/94/cf/a194cf8a1fb078a7e6a3d3c6ab99243c.jpg',
                'https://i.pinimg.com/564x/8c/d0/12/8cd012d7d5b4a5a1c3d4f88f5f5c0a0d.jpg',
                'https://i.pinimg.com/564x/2e/44/f3/2e44f3e4e99d4a4b2a4a3a3a4a4a4a4a.jpg',
                'https://i.pinimg.com/564x/3f/55/g4/3f55g4f5f5f5f5f5f5f5f5f5f5f5f5f5.jpg',
                'https://i.pinimg.com/564x/4g/66/h5/4g66h5g6g6g6g6g6g6g6g6g6g6g6g6g6.jpg',
                'https://i.pinimg.com/564x/5h/77/i6/5h77i6h7h7h7h7h7h7h7h7h7h7h7h7h7.jpg',
                'https://i.pinimg.com/564x/6i/88/j7/6i88j7i8i8i8i8i8i8i8i8i8i8i8i8i8.jpg',
                'https://i.pinimg.com/564x/7j/99/k8/7j99k8j9j9j9j9j9j9j9j9j9j9j9j9j9.jpg',
                'https://i.pinimg.com/564x/8k/00/l9/8k00l9k0k0k0k0k0k0k0k0k0k0k0k0k0.jpg',
                'https://i.pinimg.com/564x/9l/11/m0/9l11m0l1l1l1l1l1l1l1l1l1l1l1l1l1.jpg'
            ]
        ],
        'digital marketing' => [
            'boards' => ['Digital Marketing Strategy', 'Social Media Marketing', 'Content Marketing', 'SEO Tips', 'Email Marketing', 'Marketing Infographics', 'Online Business', 'Affiliate Marketing', 'Marketing Tools', 'Growth Hacking'],
            'titles' => ['{number} {type} marketing strategies for {year}', 'How to increase your {platform} engagement', 'The ultimate guide to {type} marketing', '{number} ways to improve your {metric}', 'Best {type} marketing tools for {business}', 'How to create a successful {type} marketing campaign', '{type} marketing tips for beginners', 'Advanced {type} marketing techniques', 'How to measure your {type} marketing ROI', '{platform} marketing strategy that works'],
            'descriptions' => ['Learn the top {type} marketing strategies to grow your business in {year}.', 'Discover how to improve your {metric} with these proven {type} marketing techniques.', 'A comprehensive guide to {type} marketing for {business} owners.', 'Boost your {platform} presence with these effective marketing strategies.', 'Learn how to create a successful {type} marketing campaign that drives results.', 'Essential {type} marketing tips every business owner should know.', 'How to leverage {platform} for your {business} marketing strategy.', 'Proven {type} marketing tactics that will increase your {metric}.', 'The complete guide to measuring and improving your {type} marketing ROI.', 'Step-by-step guide to creating a winning {type} marketing strategy.'],
            'replacements' => [
                'number' => ['5', '7', '10', '12', '15', '20', '25', '30', '50', '100'],
                'type' => ['content', 'social media', 'email', 'SEO', 'PPC', 'affiliate', 'influencer', 'video', 'mobile', 'local'],
                'year' => ['2023', '2024', '2025'],
                'platform' => ['Instagram', 'Facebook', 'Twitter', 'LinkedIn', 'TikTok', 'Pinterest', 'YouTube', 'Snapchat', 'Google', 'Reddit'],
                'metric' => ['conversion rate', 'engagement', 'traffic', 'leads', 'sales', 'ROI', 'brand awareness', 'customer retention', 'click-through rate', 'open rate'],
                'business' => ['small business', 'e-commerce', 'B2B', 'startup', 'local business', 'online business', 'service business', 'SaaS', 'agency', 'freelancer']
            ],
            'image_urls' => [
                'https://i.pinimg.com/564x/d1/24/df/d124df8d5e5d5e5d5e5d5e5d5e5d5e5d.jpg',
                'https://i.pinimg.com/564x/e2/35/ef/e235ef6e6e6e6e6e6e6e6e6e6e6e6e6e.jpg',
                'https://i.pinimg.com/564x/f3/46/fg/f346fg7g7g7g7g7g7g7g7g7g7g7g7g7.jpg',
                'https://i.pinimg.com/564x/g4/57/gh/g457gh8h8h8h8h8h8h8h8h8h8h8h8h8.jpg',
                'https://i.pinimg.com/564x/h5/68/hi/h568hi9i9i9i9i9i9i9i9i9i9i9i9i9.jpg',
                'https://i.pinimg.com/564x/i6/79/ij/i679ij0j0j0j0j0j0j0j0j0j0j0j0j0.jpg',
                'https://i.pinimg.com/564x/j7/80/jk/j780jk1k1k1k1k1k1k1k1k1k1k1k1k1.jpg',
                'https://i.pinimg.com/564x/k8/91/kl/k891kl2l2l2l2l2l2l2l2l2l2l2l2l2.jpg',
                'https://i.pinimg.com/564x/l9/02/lm/l902lm3m3m3m3m3m3m3m3m3m3m3m3m3.jpg',
                'https://i.pinimg.com/564x/m0/13/mn/m013mn4n4n4n4n4n4n4n4n4n4n4n4n4.jpg'
            ]
        ],
        'fitness' => [
            'boards' => ['Workout Routines', 'Fitness Motivation', 'Healthy Recipes', 'Weight Loss Tips', 'Home Workouts', 'Gym Workouts', 'Yoga Poses', 'Strength Training', 'Running Tips', 'Fitness Challenges'],
            'titles' => ['{number}-minute {type} workout for {goal}', '{number} best exercises for {bodyPart}', '{diet} meal plan for {goal}', 'How to {action} in {timeframe}', '{type} workout routine for {level}', '{number} {diet} recipes for {goal}', 'Best {equipment} exercises for {bodyPart}', 'How to build a {type} workout routine', '{type} workout plan for {timeframe}', '{number} tips to improve your {metric}'],
            'descriptions' => ['Get fit with this effective {type} workout routine designed for {goal}.', 'Transform your {bodyPart} with these {number} powerful exercises.', 'Achieve your {goal} goals with this {timeframe} {type} workout plan.', 'The ultimate {type} workout guide for {level} fitness enthusiasts.', 'Boost your {metric} with these proven {type} exercises.', 'Learn how to {action} effectively with this step-by-step guide.', 'The perfect {type} workout routine for busy people who want to {goal}.', 'Discover the best {equipment} exercises to strengthen your {bodyPart}.', 'Follow this {diet} meal plan to support your {goal} journey.', 'Science-backed {type} techniques to improve your overall fitness.'],
            'replacements' => [
                'number' => ['5', '7', '10', '12', '15', '20', '30', '45', '60', '90'],
                'type' => ['HIIT', 'cardio', 'strength', 'yoga', 'Pilates', 'bodyweight', 'resistance', 'circuit', 'core', 'full-body'],
                'goal' => ['weight loss', 'muscle gain', 'toning', 'fat burning', 'endurance', 'flexibility', 'strength', 'overall fitness', 'health', 'performance'],
                'bodyPart' => ['abs', 'arms', 'legs', 'glutes', 'back', 'chest', 'shoulders', 'core', 'lower body', 'upper body'],
                'diet' => ['keto', 'paleo', 'vegan', 'vegetarian', 'low-carb', 'high-protein', 'Mediterranean', 'intermittent fasting', 'clean eating', 'whole food'],
                'action' => ['lose weight', 'build muscle', 'increase flexibility', 'improve endurance', 'boost metabolism', 'burn fat', 'get stronger', 'tone up', 'improve posture', 'recover faster'],
                'timeframe' => ['30 days', '6 weeks', '8 weeks', '12 weeks', '3 months', '100 days', 'one year', 'a week', 'two weeks', 'a month'],
                'level' => ['beginner', 'intermediate', 'advanced', 'all levels', 'seniors', 'teens', 'women', 'men', 'athletes', 'busy professionals'],
                'equipment' => ['dumbbell', 'kettlebell', 'resistance band', 'bodyweight', 'barbell', 'medicine ball', 'TRX', 'stability ball', 'yoga mat', 'home gym'],
                'metric' => ['strength', 'endurance', 'flexibility', 'mobility', 'balance', 'coordination', 'power', 'speed', 'agility', 'recovery']
            ],
            'image_urls' => [
                'https://i.pinimg.com/564x/n1/24/no/n124no5o5o5o5o5o5o5o5o5o5o5o5o5.jpg',
                'https://i.pinimg.com/564x/o2/35/op/o235op6p6p6p6p6p6p6p6p6p6p6p6p6.jpg',
                'https://i.pinimg.com/564x/p3/46/pq/p346pq7q7q7q7q7q7q7q7q7q7q7q7q7.jpg',
                'https://i.pinimg.com/564x/q4/57/qr/q457qr8r8r8r8r8r8r8r8r8r8r8r8r8.jpg',
                'https://i.pinimg.com/564x/r5/68/rs/r568rs9s9s9s9s9s9s9s9s9s9s9s9s9.jpg',
                'https://i.pinimg.com/564x/s6/79/st/s679st0t0t0t0t0t0t0t0t0t0t0t0t0.jpg',
                'https://i.pinimg.com/564x/t7/80/tu/t780tu1u1u1u1u1u1u1u1u1u1u1u1u1.jpg',
                'https://i.pinimg.com/564x/u8/91/uv/u891uv2v2v2v2v2v2v2v2v2v2v2v2v2.jpg',
                'https://i.pinimg.com/564x/v9/02/vw/v902vw3w3w3w3w3w3w3w3w3w3w3w3w3.jpg',
                'https://i.pinimg.com/564x/w0/13/wx/w013wx4x4x4x4x4x4x4x4x4x4x4x4x4.jpg'
            ]
        ]
    ];

    // Default category if the search term doesn't match any predefined categories
    $defaultCategory = [
        'boards' => ['Inspiration', 'Ideas', 'Tips & Tricks', 'How-To', 'DIY Projects', 'Tutorials', 'Resources', 'Examples', 'Guides', 'Best Practices'],
        'titles' => ['{number} {searchTerm} ideas you need to try', 'How to {verb} {searchTerm} like a pro', 'The ultimate guide to {searchTerm}', 'Best {searchTerm} {noun} for {year}', '{adjective} {searchTerm} {noun} you\'ll love', '{number} {searchTerm} hacks that will change your life', 'DIY {searchTerm} projects anyone can do', 'How to choose the perfect {searchTerm} for your {noun}', '{searchTerm} 101: A beginner\'s guide', 'Creative {searchTerm} ideas for every {occasion}'],
        'descriptions' => ['Discover the best {searchTerm} ideas that will inspire your next project.', 'Learn how to {verb} {searchTerm} with these simple tips and tricks.', 'A comprehensive guide to {searchTerm} for beginners and experts alike.', 'Check out these amazing {searchTerm} {noun} that will transform your {occasion}.', 'Find the perfect {searchTerm} {noun} with our curated selection.', 'These {adjective} {searchTerm} ideas will help you {verb} better than ever.', 'Everything you need to know about {searchTerm} in one place.', 'Get inspired with these creative {searchTerm} ideas for your next {occasion}.', 'The ultimate resource for {searchTerm} enthusiasts looking to improve their skills.', 'Practical {searchTerm} tips that you can implement right away.'],
        'replacements' => [
            'number' => ['5', '7', '10', '12', '15', '20', '25', '30', '50', '100'],
            'verb' => ['use', 'create', 'design', 'make', 'build', 'develop', 'implement', 'organize', 'plan', 'manage'],
            'noun' => ['ideas', 'tips', 'examples', 'resources', 'tools', 'techniques', 'strategies', 'methods', 'approaches', 'solutions'],
            'year' => ['2023', '2024', '2025'],
            'adjective' => ['amazing', 'awesome', 'incredible', 'brilliant', 'creative', 'innovative', 'unique', 'stunning', 'beautiful', 'practical'],
            'occasion' => ['home', 'office', 'project', 'business', 'event', 'party', 'holiday', 'gift', 'celebration', 'everyday use']
        ],
        'image_urls' => [
            'https://via.placeholder.com/300x450/FF5733/FFFFFF?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/300x600/C70039/FFFFFF?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/400x500/900C3F/FFFFFF?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/350x550/581845/FFFFFF?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/320x480/FFC300/FFFFFF?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/280x420/DAF7A6/000000?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/310x470/FF5733/FFFFFF?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/330x490/C70039/FFFFFF?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/290x435/900C3F/FFFFFF?text=' . urlencode($searchTerm),
            'https://via.placeholder.com/305x460/581845/FFFFFF?text=' . urlencode($searchTerm)
        ]
    ];

    // Determine which category to use based on the search term
    $categoryData = $defaultCategory;
    foreach ($categories as $key => $category) {
        if (strpos($searchTerm, $key) !== false) {
            $categoryData = $category;
            break;
        }
    }

    // Generate pins
    $pins = [];
    for ($i = 0; $i < $count; $i++) {
        // Select random elements
        $boardName = $categoryData['boards'][array_rand($categoryData['boards'])];
        $titleTemplate = $categoryData['titles'][array_rand($categoryData['titles'])];
        $descriptionTemplate = $categoryData['descriptions'][array_rand($categoryData['descriptions'])];
        $imageUrl = $categoryData['image_urls'][array_rand($categoryData['image_urls'])];

        // Replace placeholders in title and description
        $title = $titleTemplate;
        $description = $descriptionTemplate;

        // Replace {searchTerm} with the actual search term
        $title = str_replace('{searchTerm}', $searchTerm, $title);
        $description = str_replace('{searchTerm}', $searchTerm, $description);

        // Replace other placeholders
        foreach ($categoryData['replacements'] as $placeholder => $replacements) {
            $replacement = $replacements[array_rand($replacements)];
            $title = str_replace('{' . $placeholder . '}', $replacement, $title);
            $description = str_replace('{' . $placeholder . '}', $replacement, $description);
        }

        // Generate random engagement metrics
        $saveCount = rand(50, 5000);
        $commentCount = rand(0, round($saveCount * 0.1)); // Comments are typically fewer than saves

        // Create pin
        $pins[] = [
            'id' => $i + 1,
            'scrape_id' => 0, // Will be set later
            // Generate a realistic Pinterest pin URL based on the title and search term
            'pin_url' => generatePinterestPinUrl($title, $searchTerm),
            'image_url' => $imageUrl,
            'title' => $title,
            'description' => $description,
            'board_name' => $boardName,
            'save_count' => $saveCount,
            'comment_count' => $commentCount,
            'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'))
        ];
    }

    return $pins;
}
