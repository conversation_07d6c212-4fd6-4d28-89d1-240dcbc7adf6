<?php
/**
 * Seed script for checklist templates
 *
 * This script creates the default AI Agent Army checklist templates
 */

require_once __DIR__ . '/../../models/ChecklistTemplate.php';
require_once __DIR__ . '/../../utils/Database.php';

// Initialize the database connection
$db = Database::getInstance();
$templateModel = new ChecklistTemplate();

// Check if templates already exist
$existingTemplates = $templateModel->getSystemTemplates();
if (!empty($existingTemplates)) {
    echo "System templates already exist. Skipping seed.\n";
    exit;
}

// Create the Implementation Master Checklist template
$masterTemplate = [
    'name' => 'AI Agent Army: Implementation Master Checklist',
    'description' => 'A comprehensive checklist for implementing your AI Agent Army brigades. Use this to track your overall implementation progress.',
    'template_type' => 'master',
    'category' => 'implementation',
    'is_system_template' => 1,
    'created_by' => null
];

$masterTemplateId = $templateModel->create($masterTemplate);

if ($masterTemplateId) {
    echo "Created Implementation Master Checklist template with ID: {$masterTemplateId}\n";

    // Add items to the master template
    $masterItems = [
        [
            'text' => 'Initial Setup',
            'description' => 'Prepare for AI Agent Army implementation',
            'sub_items' => [
                [
                    'text' => 'Review the AI Agent Army Implementation Guide',
                    'description' => 'Familiarize yourself with the implementation process',
                    'resource_link' => '/momentum/docs/ai_agent_army/implementation_guide.md'
                ],
                [
                    'text' => 'Identify your primary business objectives for automation',
                    'description' => 'Define what you want to achieve with your AI Agent Army'
                ],
                [
                    'text' => 'Determine which brigade type will have the most immediate impact',
                    'description' => 'Choose between Content Creation, Lead Generation, Customer Support, or Data Analysis'
                ],
                [
                    'text' => 'Ensure you have access to the Aegis Director dashboard',
                    'description' => 'Verify you can access the dashboard and have necessary permissions'
                ],
                [
                    'text' => 'Verify you have the necessary data and resources for your selected brigade',
                    'description' => 'Ensure you have all required information and access'
                ]
            ]
        ],
        [
            'text' => 'Brigade Selection & Project Creation',
            'description' => 'Select and set up your first brigade',
            'sub_items' => [
                [
                    'text' => 'Evaluate all brigade types against your business needs',
                    'description' => 'Compare the different brigade types to find the best fit'
                ],
                [
                    'text' => 'Select your initial brigade type',
                    'description' => 'Choose the brigade that will provide the most immediate value'
                ],
                [
                    'text' => 'Create a new brigade project in the Aegis Director dashboard',
                    'description' => 'Set up the project structure for your selected brigade'
                ],
                [
                    'text' => 'Set clear, measurable objectives for the brigade',
                    'description' => 'Define specific goals and success metrics'
                ],
                [
                    'text' => 'Establish realistic deadlines and milestones',
                    'description' => 'Create a timeline for implementation and deployment'
                ]
            ]
        ],
        [
            'text' => 'Agent Assignment & Configuration',
            'description' => 'Set up and configure your AI agents',
            'sub_items' => [
                [
                    'text' => 'Review available agents and their capabilities',
                    'description' => 'Understand the strengths and limitations of your agents'
                ],
                [
                    'text' => 'Identify any skill gaps that need to be addressed',
                    'description' => 'Determine if you need to enhance agent capabilities'
                ],
                [
                    'text' => 'Assign agents to appropriate roles within the brigade',
                    'description' => 'Match agent capabilities to role requirements'
                ],
                [
                    'text' => 'Provide comprehensive briefing to all agents',
                    'description' => 'Ensure agents understand their roles and objectives'
                ],
                [
                    'text' => 'Configure agent-specific settings and parameters',
                    'description' => 'Optimize agent configuration for your specific needs'
                ],
                [
                    'text' => 'Establish communication protocols between agents',
                    'description' => 'Define how agents will share information and coordinate'
                ],
                [
                    'text' => 'Test agent interactions in a controlled environment',
                    'description' => 'Verify agents can work together effectively'
                ]
            ]
        ],
        [
            'text' => 'Workflow Implementation',
            'description' => 'Set up and optimize your brigade workflows',
            'sub_items' => [
                [
                    'text' => 'Review the predefined task workflow for your brigade',
                    'description' => 'Understand the default workflow structure'
                ],
                [
                    'text' => 'Adjust task dependencies as needed for your specific needs',
                    'description' => 'Customize the workflow to match your requirements'
                ],
                [
                    'text' => 'Set up necessary integrations with external tools and platforms',
                    'description' => 'Connect your brigade to other systems and data sources'
                ],
                [
                    'text' => 'Establish data flow processes between agents',
                    'description' => 'Define how information moves through the workflow'
                ],
                [
                    'text' => 'Create templates and frameworks for common tasks',
                    'description' => 'Standardize repetitive processes for efficiency'
                ],
                [
                    'text' => 'Implement quality control checkpoints',
                    'description' => 'Ensure output quality meets your standards'
                ],
                [
                    'text' => 'Set up approval workflows where needed',
                    'description' => 'Define review and approval processes'
                ]
            ]
        ],
        [
            'text' => 'Launch & Initial Operation',
            'description' => 'Deploy your brigade and begin operations',
            'sub_items' => [
                [
                    'text' => 'Conduct a final pre-launch review',
                    'description' => 'Verify all components are ready for deployment'
                ],
                [
                    'text' => 'Start with a limited scope or pilot project',
                    'description' => 'Begin with a controlled implementation'
                ],
                [
                    'text' => 'Monitor initial operations closely',
                    'description' => 'Watch for any issues or unexpected behavior'
                ],
                [
                    'text' => 'Provide immediate feedback to agents',
                    'description' => 'Help agents adjust to real-world conditions'
                ],
                [
                    'text' => 'Address any issues or bottlenecks promptly',
                    'description' => 'Quickly resolve problems as they arise'
                ],
                [
                    'text' => 'Document initial performance metrics',
                    'description' => 'Establish baseline performance data'
                ],
                [
                    'text' => 'Make necessary adjustments based on early results',
                    'description' => 'Refine the implementation based on initial feedback'
                ]
            ]
        ],
        [
            'text' => 'Performance Tracking & Optimization',
            'description' => 'Monitor and improve brigade performance',
            'sub_items' => [
                [
                    'text' => 'Set up dashboards for key performance indicators',
                    'description' => 'Create visualizations of important metrics'
                ],
                [
                    'text' => 'Establish regular performance review schedule',
                    'description' => 'Set up recurring reviews of brigade performance'
                ],
                [
                    'text' => 'Implement A/B testing for optimization opportunities',
                    'description' => 'Test different approaches to improve results'
                ],
                [
                    'text' => 'Collect and analyze user/customer feedback',
                    'description' => 'Incorporate external perspectives on performance'
                ],
                [
                    'text' => 'Identify bottlenecks and inefficiencies',
                    'description' => 'Find areas where performance can be improved'
                ],
                [
                    'text' => 'Implement incremental improvements',
                    'description' => 'Make small, continuous enhancements'
                ],
                [
                    'text' => 'Document best practices and lessons learned',
                    'description' => 'Create knowledge base for future reference'
                ]
            ]
        ],
        [
            'text' => 'Scaling & Expansion',
            'description' => 'Grow your AI Agent Army',
            'sub_items' => [
                [
                    'text' => 'Evaluate initial brigade performance against objectives',
                    'description' => 'Determine if the brigade is meeting its goals'
                ],
                [
                    'text' => 'Increase operational scope of successful brigades',
                    'description' => 'Expand the responsibilities of effective brigades'
                ],
                [
                    'text' => 'Identify next brigade type for implementation',
                    'description' => 'Choose the next brigade to deploy'
                ],
                [
                    'text' => 'Plan resource allocation for expansion',
                    'description' => 'Determine how to distribute resources across brigades'
                ],
                [
                    'text' => 'Implement cross-brigade coordination where applicable',
                    'description' => 'Enable brigades to work together effectively'
                ],
                [
                    'text' => 'Apply lessons learned to new implementations',
                    'description' => 'Use experience to improve future deployments'
                ],
                [
                    'text' => 'Update documentation and processes based on experience',
                    'description' => 'Refine your approach based on what you\'ve learned'
                ]
            ]
        ],
        [
            'text' => 'Integration with Business Operations',
            'description' => 'Fully integrate your AI Agent Army with your business',
            'sub_items' => [
                [
                    'text' => 'Connect brigade outputs to relevant business processes',
                    'description' => 'Ensure brigade results feed into business operations'
                ],
                [
                    'text' => 'Train team members on working with AI brigades',
                    'description' => 'Help your team collaborate effectively with AI agents'
                ],
                [
                    'text' => 'Establish clear handoff procedures between AI and human teams',
                    'description' => 'Define when and how work transitions between AI and humans'
                ],
                [
                    'text' => 'Create documentation for business users',
                    'description' => 'Provide guides for working with the AI Agent Army'
                ],
                [
                    'text' => 'Implement feedback mechanisms for business stakeholders',
                    'description' => 'Enable stakeholders to provide input on brigade performance'
                ],
                [
                    'text' => 'Measure business impact of brigade activities',
                    'description' => 'Quantify the value created by your AI Agent Army'
                ],
                [
                    'text' => 'Align brigade objectives with evolving business goals',
                    'description' => 'Ensure your AI Agent Army continues to support business priorities'
                ]
            ]
        ]
    ];

    $templateModel->addItemsToTemplate($masterTemplateId, $masterItems);
    echo "Added items to Implementation Master Checklist template\n";
}

// Create the Daily Operation Checklist template
$dailyTemplate = [
    'name' => 'AI Agent Army: Daily Operation Checklist',
    'description' => 'A daily checklist for monitoring and managing your AI Agent Army brigades. Use this to ensure smooth day-to-day operations.',
    'template_type' => 'operational',
    'category' => 'operations',
    'is_system_template' => 1,
    'created_by' => null
];

$dailyTemplateId = $templateModel->create($dailyTemplate);

if ($dailyTemplateId) {
    echo "Created Daily Operation Checklist template with ID: {$dailyTemplateId}\n";

    // Add items to the daily template
    $dailyItems = [
        [
            'text' => 'Morning Check-in (Start of Day)',
            'description' => 'Begin the day with a comprehensive system check',
            'sub_items' => [
                [
                    'text' => 'Review overnight performance metrics',
                    'description' => 'Check how the system performed while you were away'
                ],
                [
                    'text' => 'Check for any system alerts or errors',
                    'description' => 'Address any issues that occurred overnight'
                ],
                [
                    'text' => 'Verify all integrations are functioning properly',
                    'description' => 'Ensure connections to external systems are working'
                ],
                [
                    'text' => 'Review any pending tasks from previous day',
                    'description' => 'Follow up on unfinished business'
                ],
                [
                    'text' => 'Check for any urgent requests or escalations',
                    'description' => 'Address high-priority items first'
                ],
                [
                    'text' => 'Verify agent availability and capacity',
                    'description' => 'Ensure all agents are operational and ready'
                ],
                [
                    'text' => 'Review scheduled activities for the day',
                    'description' => 'Plan your day based on scheduled tasks'
                ],
                [
                    'text' => 'Brief team on day\'s priorities and focus areas',
                    'description' => 'Ensure everyone knows what to focus on'
                ]
            ]
        ],
        [
            'text' => 'Brigade-Specific Daily Checks',
            'description' => 'Check each active brigade',
            'sub_items' => [
                [
                    'text' => 'Content Creation Brigade: Review content production pipeline',
                    'description' => 'Check status of content in development'
                ],
                [
                    'text' => 'Content Creation Brigade: Check content quality metrics',
                    'description' => 'Review quality scores from previous day'
                ],
                [
                    'text' => 'Lead Generation Brigade: Review prospect identification progress',
                    'description' => 'Check how many new prospects were identified'
                ],
                [
                    'text' => 'Lead Generation Brigade: Check outreach campaign performance',
                    'description' => 'Review response rates and engagement'
                ],
                [
                    'text' => 'Customer Support Brigade: Review support queue status',
                    'description' => 'Check for any backlog or unresolved issues'
                ],
                [
                    'text' => 'Customer Support Brigade: Check customer satisfaction scores',
                    'description' => 'Review feedback from recent support interactions'
                ],
                [
                    'text' => 'Data Analysis Brigade: Review data collection status',
                    'description' => 'Ensure data is being collected properly'
                ],
                [
                    'text' => 'Data Analysis Brigade: Check for completed analyses',
                    'description' => 'Review any insights generated overnight'
                ]
            ]
        ],
        [
            'text' => 'Operational Monitoring',
            'description' => 'Monitor overall system performance',
            'sub_items' => [
                [
                    'text' => 'Review task completion rates',
                    'description' => 'Check how many tasks are being completed on time'
                ],
                [
                    'text' => 'Check workflow efficiency metrics',
                    'description' => 'Look for any slowdowns or bottlenecks'
                ],
                [
                    'text' => 'Verify resource utilization levels',
                    'description' => 'Ensure resources are being used efficiently'
                ],
                [
                    'text' => 'Review quality control results',
                    'description' => 'Check that output quality meets standards'
                ],
                [
                    'text' => 'Verify SLA compliance',
                    'description' => 'Ensure service level agreements are being met'
                ],
                [
                    'text' => 'Check for any unusual patterns or anomalies',
                    'description' => 'Look for unexpected behavior that might indicate issues'
                ]
            ]
        ],
        [
            'text' => 'Issue Management',
            'description' => 'Address any problems that arise',
            'sub_items' => [
                [
                    'text' => 'Address any critical issues immediately',
                    'description' => 'Resolve high-priority problems first'
                ],
                [
                    'text' => 'Prioritize non-critical issues',
                    'description' => 'Organize remaining issues by importance'
                ],
                [
                    'text' => 'Assign resources to resolve identified issues',
                    'description' => 'Ensure someone is responsible for each issue'
                ],
                [
                    'text' => 'Document all issues and resolution steps',
                    'description' => 'Keep records of problems and solutions'
                ],
                [
                    'text' => 'Verify fixes for previously identified issues',
                    'description' => 'Confirm that earlier problems have been resolved'
                ],
                [
                    'text' => 'Identify any recurring issues requiring deeper analysis',
                    'description' => 'Note patterns that might indicate systemic problems'
                ]
            ]
        ],
        [
            'text' => 'End of Day Review',
            'description' => 'Wrap up the day and prepare for tomorrow',
            'sub_items' => [
                [
                    'text' => 'Review day\'s performance metrics',
                    'description' => 'Check how the system performed today'
                ],
                [
                    'text' => 'Check task completion status',
                    'description' => 'Verify which tasks were completed and which remain'
                ],
                [
                    'text' => 'Verify all critical issues were addressed',
                    'description' => 'Ensure no high-priority problems remain unresolved'
                ],
                [
                    'text' => 'Document any outstanding items for next day',
                    'description' => 'Create a to-do list for tomorrow'
                ],
                [
                    'text' => 'Prepare summary of key achievements',
                    'description' => 'Document what was accomplished today'
                ],
                [
                    'text' => 'Identify focus areas for next day',
                    'description' => 'Plan priorities for tomorrow'
                ],
                [
                    'text' => 'Ensure all necessary handoffs are completed',
                    'description' => 'Make sure work transitions smoothly to next shift if applicable'
                ],
                [
                    'text' => 'Update operation log with day\'s activities',
                    'description' => 'Document the day\'s events and outcomes'
                ]
            ]
        ]
    ];

    $templateModel->addItemsToTemplate($dailyTemplateId, $dailyItems);
    echo "Added items to Daily Operation Checklist template\n";
}

// Create templates for each brigade type
$brigadeTypes = [
    'content_creation' => 'Content Creation Brigade',
    'lead_generation' => 'Lead Generation Brigade',
    'customer_support' => 'Customer Support Brigade',
    'data_analysis' => 'Data Analysis Brigade'
];

foreach ($brigadeTypes as $type => $name) {
    $brigadeTemplate = [
        'name' => "AI Agent Army: {$name} Setup Checklist",
        'description' => "A comprehensive checklist for setting up your {$name}. Use this to ensure all necessary components are properly configured.",
        'template_type' => 'setup',
        'category' => 'implementation',
        'is_system_template' => 1,
        'created_by' => null
    ];

    $brigadeTemplateId = $templateModel->create($brigadeTemplate);

    if ($brigadeTemplateId) {
        echo "Created {$name} Setup Checklist template with ID: {$brigadeTemplateId}\n";

        // Add common setup items
        $setupItems = [
            [
                'text' => 'Business Preparation',
                'description' => 'Define business requirements and objectives',
                'sub_items' => [
                    [
                        'text' => 'Define specific goals and objectives',
                        'description' => 'Clearly articulate what you want to achieve'
                    ],
                    [
                        'text' => 'Identify target audience and stakeholders',
                        'description' => 'Determine who will benefit from this brigade'
                    ],
                    [
                        'text' => 'Establish quality standards and guidelines',
                        'description' => 'Define what good output looks like'
                    ],
                    [
                        'text' => 'Define key performance indicators',
                        'description' => 'Determine how success will be measured'
                    ]
                ]
            ],
            [
                'text' => 'Resource Preparation',
                'description' => 'Gather necessary resources and tools',
                'sub_items' => [
                    [
                        'text' => 'Prepare access to required tools and platforms',
                        'description' => 'Ensure you have all necessary credentials'
                    ],
                    [
                        'text' => 'Gather reference materials and examples',
                        'description' => 'Collect materials that can guide implementation'
                    ],
                    [
                        'text' => 'Create templates and frameworks',
                        'description' => 'Develop standardized formats for efficiency'
                    ],
                    [
                        'text' => 'Establish approval workflows',
                        'description' => 'Define review and approval processes'
                    ]
                ]
            ],
            [
                'text' => 'Agent Configuration',
                'description' => 'Set up and configure AI agents',
                'sub_items' => [
                    [
                        'text' => 'Assign Brigade Commander (Aegis Director)',
                        'description' => 'Configure the Aegis Director as brigade leader'
                    ],
                    [
                        'text' => 'Identify and assign role-specific agents',
                        'description' => 'Match agents to appropriate roles based on capabilities'
                    ],
                    [
                        'text' => 'Configure agent parameters and settings',
                        'description' => 'Optimize agent configuration for your specific needs'
                    ],
                    [
                        'text' => 'Establish inter-agent communication protocols',
                        'description' => 'Define how agents will share information'
                    ],
                    [
                        'text' => 'Test agent interactions',
                        'description' => 'Verify agents can work together effectively'
                    ]
                ]
            ],
            [
                'text' => 'Workflow Implementation',
                'description' => 'Set up operational workflows',
                'sub_items' => [
                    [
                        'text' => 'Review and customize predefined workflow',
                        'description' => 'Adapt the standard workflow to your needs'
                    ],
                    [
                        'text' => 'Set up task dependencies and sequences',
                        'description' => 'Define the order of operations'
                    ],
                    [
                        'text' => 'Implement quality control checkpoints',
                        'description' => 'Add verification steps at critical points'
                    ],
                    [
                        'text' => 'Configure performance monitoring',
                        'description' => 'Set up tracking for key metrics'
                    ],
                    [
                        'text' => 'Test end-to-end workflow',
                        'description' => 'Verify the entire process works correctly'
                    ]
                ]
            ],
            [
                'text' => 'Launch Preparation',
                'description' => 'Final steps before going live',
                'sub_items' => [
                    [
                        'text' => 'Conduct final pre-launch review',
                        'description' => 'Verify all components are ready'
                    ],
                    [
                        'text' => 'Prepare stakeholder communication',
                        'description' => 'Create announcements and documentation'
                    ],
                    [
                        'text' => 'Set up initial monitoring',
                        'description' => 'Prepare to closely watch early performance'
                    ],
                    [
                        'text' => 'Create contingency plans',
                        'description' => 'Prepare for potential issues'
                    ],
                    [
                        'text' => 'Schedule post-launch review',
                        'description' => 'Plan when to evaluate initial performance'
                    ]
                ]
            ]
        ];

        // Add brigade-specific items
        switch ($type) {
            case 'content_creation':
                $setupItems[] = [
                    'text' => 'Content Strategy Development',
                    'description' => 'Create your content strategy',
                    'sub_items' => [
                        [
                            'text' => 'Define content types and formats',
                            'description' => 'Determine what kinds of content you\'ll create'
                        ],
                        [
                            'text' => 'Establish content calendar',
                            'description' => 'Plan content production schedule'
                        ],
                        [
                            'text' => 'Define SEO strategy',
                            'description' => 'Plan how content will be optimized for search'
                        ],
                        [
                            'text' => 'Create brand voice guidelines',
                            'description' => 'Define tone, style, and personality'
                        ]
                    ]
                ];
                break;

            case 'lead_generation':
                $setupItems[] = [
                    'text' => 'Lead Generation Strategy Development',
                    'description' => 'Create your lead generation strategy',
                    'sub_items' => [
                        [
                            'text' => 'Define ideal customer profile',
                            'description' => 'Create detailed description of target prospects'
                        ],
                        [
                            'text' => 'Establish lead qualification criteria',
                            'description' => 'Define what makes a good lead'
                        ],
                        [
                            'text' => 'Create outreach message templates',
                            'description' => 'Develop standardized messaging'
                        ],
                        [
                            'text' => 'Define follow-up sequences',
                            'description' => 'Plan how to nurture prospects'
                        ]
                    ]
                ];
                break;

            case 'customer_support':
                $setupItems[] = [
                    'text' => 'Support System Development',
                    'description' => 'Create your customer support system',
                    'sub_items' => [
                        [
                            'text' => 'Define support channels',
                            'description' => 'Determine which channels you\'ll support'
                        ],
                        [
                            'text' => 'Create knowledge base',
                            'description' => 'Develop repository of support information'
                        ],
                        [
                            'text' => 'Establish escalation criteria',
                            'description' => 'Define when issues should be escalated'
                        ],
                        [
                            'text' => 'Create response templates',
                            'description' => 'Develop standardized responses for common issues'
                        ]
                    ]
                ];
                break;

            case 'data_analysis':
                $setupItems[] = [
                    'text' => 'Data Analysis Framework Development',
                    'description' => 'Create your data analysis framework',
                    'sub_items' => [
                        [
                            'text' => 'Identify key data sources',
                            'description' => 'Determine where data will come from'
                        ],
                        [
                            'text' => 'Define data processing pipeline',
                            'description' => 'Plan how data will be cleaned and prepared'
                        ],
                        [
                            'text' => 'Establish analysis methodologies',
                            'description' => 'Define analytical approaches'
                        ],
                        [
                            'text' => 'Create visualization templates',
                            'description' => 'Develop standardized ways to present insights'
                        ]
                    ]
                ];
                break;
        }

        $templateModel->addItemsToTemplate($brigadeTemplateId, $setupItems);
        echo "Added items to {$name} Setup Checklist template\n";
    }
}

echo "Checklist templates seeding completed successfully!\n";
