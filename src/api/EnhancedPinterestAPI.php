<?php
/**
 * Enhanced Pinterest API
 *
 * This class provides a PHP wrapper for the enhanced_pinterest_scraper.py Python script.
 * It allows for seamless integration with the Pinterest API using Selenium, Requests, and BeautifulSoup.
 */

require_once __DIR__ . '/../utils/Environment.php';

// Load environment variables
Environment::load();

class EnhancedPinterestAPI {
    private static $instance = null;
    private $pythonPath;
    private $scriptDir;
    private $email;
    private $password;
    private $username;
    private $chromeProfile;
    private $isLoggedIn = false;

    /**
     * Constructor
     *
     * @param string $email Pinterest account email
     * @param string $password Pinterest account password
     * @param string $username Pinterest username
     * @param string $pythonPath Path to Python executable
     * @param string $chromeProfile Path to Chrome user profile directory
     */
    private function __construct($email, $password, $username, $pythonPath = null, $chromeProfile = null) {
        $this->email = $email;
        $this->password = $password;
        $this->username = $username;
        $this->chromeProfile = $chromeProfile;

        // Check if Python path is set in environment variables
        $envPythonPath = Environment::get('PYTHON_PATH');
        if ($pythonPath) {
            $this->pythonPath = $pythonPath;
        } elseif ($envPythonPath) {
            $this->pythonPath = $envPythonPath;
        } else {
            // Try to find Python in the system
            $this->pythonPath = $this->findPythonPath();
        }

        // Set script directory
        $this->scriptDir = __DIR__ . '/../../scripts/pinterest';

        // Check if the enhanced scraper script exists
        $this->checkScripts();
    }

    /**
     * Find Python path in the system
     *
     * @return string Python path
     */
    private function findPythonPath() {
        // Check if we're on Windows
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Try common Python paths on Windows
            $possiblePaths = [
                'python',
                'python3',
                'C:\\Python39\\python.exe',
                'C:\\Python310\\python.exe',
                'C:\\Python311\\python.exe',
                'C:\\Python312\\python.exe',
                'C:\\Python313\\python.exe'
            ];

            foreach ($possiblePaths as $path) {
                $output = [];
                $returnVar = 0;
                exec("$path --version 2>&1", $output, $returnVar);
                if ($returnVar === 0 && !empty($output[0]) && strpos($output[0], 'Python') !== false) {
                    return $path;
                }
            }
        } else {
            // Try common Python paths on Unix-like systems
            $possiblePaths = [
                'python3',
                'python'
            ];

            foreach ($possiblePaths as $path) {
                $output = [];
                $returnVar = 0;
                exec("$path --version 2>&1", $output, $returnVar);
                if ($returnVar === 0 && !empty($output[0]) && strpos($output[0], 'Python') !== false) {
                    return $path;
                }
            }
        }

        // Default to 'python' if not found
        return 'python';
    }

    /**
     * Check if the required scripts exist
     */
    private function checkScripts() {
        $enhancedScraperPath = $this->scriptDir . '/enhanced_pinterest_scraper.py';
        $setupScriptPath = $this->scriptDir . '/setup_enhanced_scraper.py';

        if (!file_exists($enhancedScraperPath) || !file_exists($setupScriptPath)) {
            error_log('Enhanced Pinterest Scraper scripts not found. Using fallback data.');
        }
    }

    /**
     * Get singleton instance
     *
     * @param string $email Pinterest account email
     * @param string $password Pinterest account password
     * @param string $username Pinterest username
     * @param string $pythonPath Path to Python executable
     * @param string $chromeProfile Path to Chrome user profile directory
     * @return EnhancedPinterestAPI Instance
     */
    public static function getInstance($email = null, $password = null, $username = null, $pythonPath = null, $chromeProfile = null) {
        if (self::$instance === null) {
            self::$instance = new self($email, $password, $username, $pythonPath, $chromeProfile);
        }
        return self::$instance;
    }

    /**
     * Execute a Python script
     *
     * @param string $script Script name
     * @param array $args Command line arguments
     * @param bool $humanLike Whether to use human-like behavior
     * @return array|null Result of the script execution
     */
    private function executeScript($script, $args = [], $humanLike = true) {
        $scriptPath = $this->scriptDir . '/' . $script;

        if (!file_exists($scriptPath)) {
            error_log("Script not found: $scriptPath");
            return null;
        }

        // Build the command
        $command = escapeshellcmd($this->pythonPath) . ' ' . escapeshellarg($scriptPath);

        // Add arguments
        foreach ($args as $key => $value) {
            if (is_bool($value)) {
                if ($value) {
                    $command .= ' ' . escapeshellarg((string)$key);
                }
            } else if ($value === null) {
                $command .= ' ' . escapeshellarg((string)$key);
            } else {
                $command .= ' ' . escapeshellarg((string)$key) . ' ' . escapeshellarg((string)$value);
            }
        }

        // Add human-like behavior flag if needed
        if (!$humanLike) {
            $command .= ' --no-human';
        }

        // Execute the command
        $output = [];
        $returnVar = 0;
        exec($command . ' 2>&1', $output, $returnVar);

        // Check if the command was successful
        if ($returnVar !== 0) {
            error_log("Error executing script: $command");
            error_log("Return code: $returnVar");
            error_log("Output: " . implode("\n", $output));
            return null;
        }

        // Parse the output as JSON
        $jsonOutput = implode("\n", $output);
        $result = json_decode($jsonOutput, true);

        if ($result === null) {
            error_log("Error parsing JSON output: $jsonOutput");
            return null;
        }

        return $result;
    }

    /**
     * Log in to Pinterest
     *
     * @param bool $humanLike Whether to use human-like behavior
     * @return bool True if login successful, false otherwise
     */
    public function login($humanLike = true) {
        // Check if the script exists
        $scriptPath = $this->scriptDir . '/enhanced_pinterest_scraper.py';
        if (!file_exists($scriptPath)) {
            error_log('Enhanced Pinterest Scraper script not found.');
            return false;
        }

        // Build arguments
        $args = [
            'login' => null,
            '--email' => $this->email,
            '--password' => $this->password
        ];

        if ($this->chromeProfile) {
            $args['--chrome_profile'] = $this->chromeProfile;
        }

        // Execute the script
        $result = $this->executeScript('enhanced_pinterest_scraper.py', $args, $humanLike);

        if ($result && isset($result['success']) && $result['success']) {
            $this->isLoggedIn = true;
            return true;
        }

        return false;
    }

    /**
     * Search Pinterest
     *
     * @param string $query Search query
     * @param string $scope Search scope (pins, boards, users)
     * @param int $limit Maximum number of results
     * @param bool $humanLike Whether to use human-like behavior
     * @return array|null Search results
     */
    public function searchPins($query, $scope = 'pins', $limit = 20, $humanLike = true) {
        // Check if the script exists
        $scriptPath = $this->scriptDir . '/enhanced_pinterest_scraper.py';
        if (!file_exists($scriptPath)) {
            error_log('Enhanced Pinterest Scraper script not found.');
            return null;
        }

        // Build arguments
        $args = [
            'search' => null,
            '--query' => $query,
            '--scope' => $scope,
            '--limit' => $limit
        ];

        if ($this->chromeProfile) {
            $args['--chrome_profile'] = $this->chromeProfile;
        }

        // Execute the script
        $result = $this->executeScript('enhanced_pinterest_scraper.py', $args, $humanLike);

        if ($result && is_array($result)) {
            return $result;
        }

        return null;
    }

    /**
     * Get pin details
     *
     * @param string $pinId Pinterest pin ID
     * @param bool $humanLike Whether to use human-like behavior
     * @return array|null Pin details
     */
    public function getPinDetails($pinId, $humanLike = true) {
        // Check if the script exists
        $scriptPath = $this->scriptDir . '/enhanced_pinterest_scraper.py';
        if (!file_exists($scriptPath)) {
            error_log('Enhanced Pinterest Scraper script not found.');
            return null;
        }

        // Build arguments
        $args = [
            'pin_details' => null,
            '--pin_id' => $pinId
        ];

        if ($this->chromeProfile) {
            $args['--chrome_profile'] = $this->chromeProfile;
        }

        // Execute the script
        $result = $this->executeScript('enhanced_pinterest_scraper.py', $args, $humanLike);

        if ($result && is_array($result)) {
            return $result;
        }

        return null;
    }

    /**
     * Download image from Pinterest
     *
     * @param string $imageUrl Image URL
     * @param string $outputPath Output file path
     * @param bool $humanLike Whether to use human-like behavior
     * @return bool True if download successful, false otherwise
     */
    public function downloadImage($imageUrl, $outputPath, $humanLike = true) {
        // Check if the script exists
        $scriptPath = $this->scriptDir . '/enhanced_pinterest_scraper.py';
        if (!file_exists($scriptPath)) {
            error_log('Enhanced Pinterest Scraper script not found.');
            return false;
        }

        // Build arguments
        $args = [
            'download_image' => null,
            '--image_url' => $imageUrl,
            '--output_path' => $outputPath
        ];

        if ($this->chromeProfile) {
            $args['--chrome_profile'] = $this->chromeProfile;
        }

        // Execute the script
        $result = $this->executeScript('enhanced_pinterest_scraper.py', $args, $humanLike);

        if ($result && isset($result['success']) && $result['success']) {
            return true;
        }

        return false;
    }

    /**
     * Run a comprehensive test of the Enhanced Pinterest Scraper
     *
     * @param string $query Search query for testing
     * @param int $limit Maximum number of results for testing
     * @param bool $humanLike Whether to use human-like behavior
     * @return array|null Test results
     */
    public function runTest($query = 'home decor', $limit = 3, $humanLike = true) {
        // Check if the script exists
        $scriptPath = $this->scriptDir . '/enhanced_pinterest_scraper.py';
        if (!file_exists($scriptPath)) {
            error_log('Enhanced Pinterest Scraper script not found.');
            return null;
        }

        // Build arguments
        $args = [
            'test' => null,
            '--email' => $this->email,
            '--password' => $this->password,
            '--query' => $query,
            '--limit' => $limit
        ];

        if ($this->chromeProfile) {
            $args['--chrome_profile'] = $this->chromeProfile;
        }

        // Execute the script
        return $this->executeScript('enhanced_pinterest_scraper.py', $args, $humanLike);
    }

    /**
     * Setup the Enhanced Pinterest Scraper
     *
     * @return bool True if setup successful, false otherwise
     */
    public function setupScraper() {
        // Check if the setup script exists
        $setupScriptPath = $this->scriptDir . '/setup_enhanced_scraper.py';
        if (!file_exists($setupScriptPath)) {
            error_log('Enhanced Pinterest Scraper setup script not found.');
            return false;
        }

        // Execute the setup script
        $command = escapeshellcmd($this->pythonPath) . ' ' . escapeshellarg($setupScriptPath);
        $output = [];
        $returnVar = 0;
        exec($command . ' 2>&1', $output, $returnVar);

        // Check if the command was successful
        if ($returnVar !== 0) {
            error_log("Error executing setup script: $command");
            error_log("Return code: $returnVar");
            error_log("Output: " . implode("\n", $output));
            return false;
        }

        return true;
    }
}
