<?php
/**
 * Pinterest API Factory
 *
 * This class provides a factory for creating Pinterest API instances.
 * It selects the appropriate API based on the configuration.
 */

require_once __DIR__ . '/../utils/Environment.php';
require_once __DIR__ . '/PinterestAPI.php';
require_once __DIR__ . '/PinterestOfficialAPI.php';

// Load environment variables
Environment::load();

class PinterestAPIFactory {
    /**
     * Get a Pinterest API instance based on the configuration
     *
     * @return PinterestAPI|PinterestOfficialAPI|null
     */
    public static function getAPI() {
        // Get the API type from the environment
        $apiType = Environment::get('PINTEREST_API_TYPE', 'unofficial');

        // Check if API is disabled
        if (Environment::get('DISABLE_PINTEREST_API') === 'true') {
            error_log('Pinterest API disabled via environment variable. Using fallback data.');
            return null;
        }

        // Check if we're on a shared hosting environment (Hostinger)
        if (strpos($_SERVER['SERVER_SOFTWARE'] ?? '', 'LiteSpeed') !== false) {
            // <PERSON>inger uses LiteSpeed - we should use fallback data on production
            error_log('Detected LiteSpeed server (likely Hostinger). Using fallback data.');
            return null;
        }

        // Create the appropriate API instance
        switch ($apiType) {
            case 'official':
                // Check if API key or access token is available
                $apiKey = Environment::get('PINTEREST_API_KEY');
                $accessToken = Environment::get('PINTEREST_ACCESS_TOKEN');

                if (!$apiKey && !$accessToken) {
                    error_log('Pinterest API key or access token is required for official API. Using fallback data.');
                    return null;
                }

                return PinterestOfficialAPI::getInstance($apiKey, $accessToken);

            case 'unofficial':
                // Check if credentials are available
                $email = Environment::get('PINTEREST_EMAIL');
                $password = Environment::get('PINTEREST_PASSWORD');
                $username = Environment::get('PINTEREST_USERNAME');

                if (!$email || !$password || !$username) {
                    error_log('Pinterest credentials are required for unofficial API. Using fallback data.');
                    return null;
                }

                // Get Chrome profile path
                $chromeProfile = Environment::get('CHROME_PROFILE_PATH');

                // Initialize the API with Chrome profile if available
                $api = PinterestAPI::getInstance($email, $password, $username, null, null, $chromeProfile);

                // TEMPORARY FIX: Skip login check and return API instance directly
                error_log('TEMPORARY FIX: Bypassing Pinterest login check and returning API instance directly');
                return $api;

                /* Original code commented out for debugging
                // Try to login
                try {
                    $loginResult = $api->login();

                    // Check if login was successful
                    if (!$loginResult || (is_array($loginResult) && isset($loginResult['success']) && !$loginResult['success'])) {
                        error_log('Pinterest login failed. Attempting to fix Chrome profile...');

                        // Try to fix Chrome profile
                        $fixResult = $api->fixChromeProfile();
                    } else {
                        // Login successful
                        return $api;
                    }
                } catch (Exception $e) {
                    error_log('Pinterest login exception: ' . $e->getMessage());

                    // Try to fix Chrome profile
                    $fixResult = $api->fixChromeProfile();
                }
                */

                    if ($fixResult) {
                        error_log('Chrome profile fixed. Retrying login...');

                        // Update environment variable
                        Environment::set('CHROME_PROFILE_PATH', $fixResult['profile_dir']);

                        // Retry login
                        $loginResult = $api->login();

                        if (!$loginResult) {
                            error_log('Pinterest login failed again after fixing Chrome profile. Using fallback data.');
                            return null;
                        }
                    } else {
                        error_log('Failed to fix Chrome profile. Using fallback data.');
                        return null;
                    }
                }

                return $api;

            case 'fallback':
                // Use fallback data
                return null;

            default:
                error_log('Unknown Pinterest API type: ' . $apiType . '. Using fallback data.');
                return null;
        }
    }

    /**
     * Search for pins
     *
     * @param string $query Search query
     * @param int $limit Maximum number of results
     * @return array Search results
     */
    public static function search($query, $limit = 20) {
        $api = self::getAPI();

        if ($api === null) {
            return self::getFallbackSearchResults($query, $limit);
        }

        if ($api instanceof PinterestOfficialAPI) {
            return $api->searchPins($query, $limit);
        } else {
            return $api->search($query, 'pins', $limit);
        }
    }

    /**
     * Get pin details
     *
     * @param string $pinId Pinterest pin ID
     * @return array|null Pin details or null if not found
     */
    public static function getPinDetails($pinId) {
        $api = self::getAPI();

        if ($api === null) {
            return null;
        }

        if ($api instanceof PinterestOfficialAPI) {
            return $api->getPinDetails($pinId);
        } else {
            return $api->getPinDetails($pinId);
        }
    }

    /**
     * Download image from Pinterest
     *
     * @param string $imageUrl Image URL
     * @param string $outputPath Output file path
     * @return bool True if download successful, false otherwise
     */
    public static function downloadImage($imageUrl, $outputPath) {
        $api = self::getAPI();

        if ($api === null) {
            return self::downloadImageFallback($imageUrl, $outputPath);
        }

        if ($api instanceof PinterestOfficialAPI) {
            return $api->downloadImage($imageUrl, $outputPath);
        } else {
            return $api->downloadImage($imageUrl, $outputPath);
        }
    }

    /**
     * Download image fallback
     *
     * @param string $imageUrl Image URL
     * @param string $outputPath Output file path
     * @return bool True if download successful, false otherwise
     */
    private static function downloadImageFallback($imageUrl, $outputPath) {
        // If the URL is already a placeholder, just return true (no need to download)
        if (strpos($imageUrl, 'placeholder.com') !== false) {
            return true;
        }

        try {
            // Create directory if it doesn't exist
            $dir = dirname($outputPath);
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }

            // Try to download the image using file_get_contents
            $imageData = @file_get_contents($imageUrl);

            if ($imageData !== false) {
                // Save the image
                if (file_put_contents($outputPath, $imageData) !== false) {
                    return true;
                }
            }

            // If file_get_contents fails, try cURL
            if (function_exists('curl_init')) {
                $ch = curl_init($imageUrl);
                $fp = fopen($outputPath, 'wb');

                curl_setopt($ch, CURLOPT_FILE, $fp);
                curl_setopt($ch, CURLOPT_HEADER, 0);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

                $success = curl_exec($ch);

                curl_close($ch);
                fclose($fp);

                if ($success) {
                    return true;
                }
            }
        } catch (Exception $e) {
            error_log('Image download failed: ' . $e->getMessage());
        }

        // If all methods fail, create a placeholder image
        return self::createPlaceholderImage($outputPath);
    }

    /**
     * Create a placeholder image
     *
     * @param string $outputPath Output file path
     * @return bool True if successful, false otherwise
     */
    private static function createPlaceholderImage($outputPath) {
        // Create directory if it doesn't exist
        $dir = dirname($outputPath);
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }

        // Try to get a placeholder image
        $placeholderUrl = "https://via.placeholder.com/600x800/f8f9fa/dc3545?text=" . urlencode("Pinterest Image");

        try {
            $imageData = @file_get_contents($placeholderUrl);

            if ($imageData !== false) {
                // Save the image
                if (file_put_contents($outputPath, $imageData) !== false) {
                    return true;
                }
            }
        } catch (Exception $e) {
            error_log('Placeholder image creation failed: ' . $e->getMessage());
        }

        // If all else fails, create a simple 1x1 transparent GIF
        $transparentGif = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');
        file_put_contents($outputPath, $transparentGif);

        return true;
    }

    /**
     * Get fallback search results
     *
     * @param string $query Search query
     * @param int $limit Maximum number of results
     * @return array Fallback search results
     */
    private static function getFallbackSearchResults($query, $limit = 20) {
        // Define some fallback pin IDs that are known to work
        $fallbackPinIds = [
            '573786808744734357',
            '1055599864844775',
            '637470522321759130',
            '660551470361350911',
            '422775483766435552',
            '422775483766435542'
        ];

        $results = [];
        $count = min($limit, count($fallbackPinIds));

        for ($i = 0; $i < $count; $i++) {
            $pinId = $fallbackPinIds[$i];

            // Create a fallback pin result
            $results[] = [
                'id' => $pinId,
                'pin_id' => $pinId,
                'pin_url' => "https://www.pinterest.com/pin/{$pinId}/",
                'title' => 'Pinterest Pin for "' . $query . '"',
                'description' => 'This is a fallback pin for the search query: ' . $query,
                'image_url' => "https://via.placeholder.com/600x800/f8f9fa/dc3545?text=" . urlencode("Pinterest Image " . ($i + 1)),
                'board_name' => 'Pinterest Board',
                'save_count' => rand(50, 5000),
                'comment_count' => rand(0, 50),
                'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'))
            ];
        }

        return $results;
    }
}
