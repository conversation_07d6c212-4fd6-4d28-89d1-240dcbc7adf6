<?php
/**
 * Pinterest API Wrapper
 *
 * This class provides a PHP wrapper for the py3-pinterest Python library.
 * It allows for seamless integration with the Pinterest API.
 */

require_once __DIR__ . '/../utils/Environment.php';

// Load environment variables
Environment::load();

class PinterestAPI {
    private static $instance = null;
    private $pythonPath;
    private $scriptDir;
    private $email;
    private $password;
    private $username;
    private $credRoot;
    private $chromeProfile;
    private $isLoggedIn = false;

    /**
     * Constructor
     *
     * @param string $email Pinterest account email
     * @param string $password Pinterest account password
     * @param string $username Pinterest username
     * @param string $credRoot Directory to store credentials
     * @param string $pythonPath Path to Python executable
     * @param string $chromeProfile Path to Chrome user profile directory
     */
    private function __construct($email, $password, $username, $credRoot = null, $pythonPath = null, $chromeProfile = null) {
        $this->email = $email;
        $this->password = $password;
        $this->username = $username;
        $this->chromeProfile = $chromeProfile;

        // Check if Python path is set in environment variables
        $envPythonPath = Environment::get('PYTHON_PATH');

        if ($envPythonPath) {
            $this->pythonPath = $envPythonPath;
        } else if ($pythonPath !== null) {
            // Use provided Python path
            $this->pythonPath = $pythonPath;
        } else {
            // Auto-detect Python path
            if (file_exists('C:/Python313/python.exe')) {
                $this->pythonPath = 'C:/Python313/python.exe';
            } elseif (file_exists('C:/Python312/python.exe')) {
                $this->pythonPath = 'C:/Python312/python.exe';
            } elseif (file_exists('C:/Python311/python.exe')) {
                $this->pythonPath = 'C:/Python311/python.exe';
            } elseif (file_exists('C:/Python310/python.exe')) {
                $this->pythonPath = 'C:/Python310/python.exe';
            } elseif (file_exists('C:/laragon/bin/python/python.exe')) {
                $this->pythonPath = 'C:/laragon/bin/python/python.exe';
            } else {
                // Default to 'python' and hope it's in the PATH
                $this->pythonPath = 'python';
            }
        }

        // Set credential root directory
        if ($credRoot !== null) {
            $this->credRoot = $credRoot;
        } else {
            // Default to data directory in the project
            $this->credRoot = dirname(__DIR__, 2) . '/data/pinterest_credentials';
        }

        // Create the credential directory if it doesn't exist
        if (!file_exists($this->credRoot)) {
            mkdir($this->credRoot, 0755, true);
        }

        // Set script directory
        $this->scriptDir = __DIR__ . '/../../scripts/pinterest';

        // Create the script directory if it doesn't exist
        if (!file_exists($this->scriptDir)) {
            mkdir($this->scriptDir, 0755, true);
        }

        // Create the Python scripts
        $this->createLoginScript();
        $this->createSearchScript();

        // Check if the scripts already exist
        if (!file_exists($this->scriptDir . '/pin_details.py')) {
            // Run the simple scripts creator
            $simpleScriptsPath = $this->scriptDir . '/simple_scripts.php';
            if (file_exists($simpleScriptsPath)) {
                shell_exec('php ' . escapeshellarg($simpleScriptsPath));
            }
        }
    }

    /**
     * Get singleton instance
     *
     * @param string $email Pinterest account email
     * @param string $password Pinterest account password
     * @param string $username Pinterest username
     * @param string $credRoot Directory to store credentials
     * @param string $pythonPath Path to Python executable
     * @param string $chromeProfile Path to Chrome user profile directory
     * @return PinterestAPI Instance
     */
    public static function getInstance($email = null, $password = null, $username = null, $credRoot = null, $pythonPath = null, $chromeProfile = null) {
        if (self::$instance === null) {
            self::$instance = new self($email, $password, $username, $credRoot, $pythonPath, $chromeProfile);
        }
        return self::$instance;
    }

    /**
     * Create the Python login script
     */
    private function createLoginScript() {
        $scriptPath = $this->scriptDir . '/login.py';

        // Check if browser_cookie_pinterest.py exists
        $browserCookieScriptPath = $this->scriptDir . '/browser_cookie_pinterest.py';
        if (file_exists($browserCookieScriptPath)) {
            // Use the browser cookie implementation
            $scriptContent = <<<PYTHON
import json
import sys
import os
import subprocess

# Get credentials from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
chrome_profile = sys.argv[5] if len(sys.argv) > 5 else None

# Call the browser cookie Pinterest implementation
args = ["python", os.path.join(os.path.dirname(__file__), "browser_cookie_pinterest.py"), "login",
        "--email", email,
        "--password", password,
        "--username", username,
        "--cred_root", cred_root]
if chrome_profile:
    args.extend(["--chrome_profile", chrome_profile])

result = subprocess.run(args, capture_output=True, text=True)
print(result.stdout)
PYTHON;
        }
        // Check if custom_pinterest.py exists
        else if (file_exists($this->scriptDir . '/custom_pinterest.py')) {
            // Use the custom implementation instead
            $scriptContent = <<<PYTHON
import json
import sys
import os
import subprocess

# Get credentials from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
chrome_profile = sys.argv[5] if len(sys.argv) > 5 else None

# Call the custom Pinterest implementation
args = ["python", os.path.join(os.path.dirname(__file__), "custom_pinterest.py"), "login", email, password, username, cred_root]
if chrome_profile:
    args.append(chrome_profile)

result = subprocess.run(args, capture_output=True, text=True)
print(result.stdout)
PYTHON;
        } else {
            // Fallback to original implementation
            $scriptContent = <<<PYTHON
from py3pin.Pinterest import Pinterest
import json
import sys
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Get credentials from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
chrome_profile = sys.argv[5] if len(sys.argv) > 5 else None

# Configure Chrome options
chrome_options = Options()
if chrome_profile:
    chrome_options.add_argument(f"user-data-dir={chrome_profile}")

# Add additional options for stability
chrome_options.add_argument("--disable-extensions")
chrome_options.add_argument("--disable-gpu")
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")

# Create a custom browser function that uses our Chrome options
def custom_browser():
    try:
        # Try to use the ChromeDriverManager to get the correct driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        return driver
    except Exception as e:
        print(f"Error creating custom browser: {e}")
        # Fallback to default browser
        return None

# Create Pinterest instance with custom browser
pinterest = Pinterest(
    email=email,
    password=password,
    username=username,
    cred_root=cred_root
)

try:
    # Try to login
    pinterest.login()
    print(json.dumps({"success": True, "message": "Login successful"}))
except Exception as e:
    print(json.dumps({"success": False, "message": str(e)}))
PYTHON;
        }

        file_put_contents($scriptPath, $scriptContent);
    }

    /**
     * Create the Python search script
     */
    private function createSearchScript() {
        $scriptPath = $this->scriptDir . '/search.py';

        // Check if browser_cookie_pinterest.py exists
        $browserCookieScriptPath = $this->scriptDir . '/browser_cookie_pinterest.py';
        if (file_exists($browserCookieScriptPath)) {
            // Use the browser cookie implementation
            $scriptContent = <<<PYTHON
import json
import sys
import os
import subprocess

# Get search parameters from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
scope = sys.argv[5]
query = sys.argv[6]
limit = int(sys.argv[7]) if len(sys.argv) > 7 else 20
chrome_profile = sys.argv[8] if len(sys.argv) > 8 else None

# Call the browser cookie Pinterest implementation
args = ["python", os.path.join(os.path.dirname(__file__), "browser_cookie_pinterest.py"), "search",
        "--query", query,
        "--scope", scope,
        "--limit", str(limit)]
if chrome_profile:
    args.extend(["--chrome_profile", chrome_profile])

result = subprocess.run(args, capture_output=True, text=True)
print(result.stdout)
PYTHON;
        }
        // Check if custom_pinterest.py exists
        else if (file_exists($this->scriptDir . '/custom_pinterest.py')) {
            // Use the custom implementation instead
            $scriptContent = <<<PYTHON
import json
import sys
import os
import subprocess

# Get search parameters from command line arguments
email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
scope = sys.argv[5]
query = sys.argv[6]
limit = int(sys.argv[7]) if len(sys.argv) > 7 else 20
chrome_profile = sys.argv[8] if len(sys.argv) > 8 else None

# Call the custom Pinterest implementation
args = ["python", os.path.join(os.path.dirname(__file__), "custom_pinterest.py"), "search", query, scope, str(limit)]
if chrome_profile:
    args.append(chrome_profile)

result = subprocess.run(args, capture_output=True, text=True)
print(result.stdout)
PYTHON;
        } else {
            // Fallback to original implementation
            $scriptContent = <<<PYTHON
from py3pin.Pinterest import Pinterest
import json
import sys
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
PYTHON;
        }

        file_put_contents($scriptPath, $scriptContent);
    }
}
