<?php
/**
 * Freelance Management Routes
 *
 * This file contains all routes related to the freelance management system.
 */

// Freelance Dashboard
$router->get('/freelance', function() {
    $controller = new FreelanceController();
    $controller->index();
});

// Freelance Clients
$router->get('/freelance/clients', function() {
    $controller = new FreelanceController();
    $controller->clients();
});

$router->get('/freelance/clients/create', function() {
    $controller = new FreelanceClientController();
    $controller->create();
});

$router->post('/freelance/clients/store', function() {
    $controller = new FreelanceClientController();
    $controller->store();
});

$router->get('/freelance/clients/view/:id', function($id) {
    $controller = new FreelanceClientController();
    $controller->viewClient($id);
});

$router->get('/freelance/clients/edit/:id', function($id) {
    $controller = new FreelanceClientController();
    $controller->edit($id);
});

$router->post('/freelance/clients/update/:id', function($id) {
    $controller = new FreelanceClientController();
    $controller->update($id);
});

$router->get('/freelance/clients/delete/:id', function($id) {
    $controller = new FreelanceClientController();
    $controller->delete($id);
});

// Freelance Projects
$router->get('/freelance/projects', function() {
    $controller = new FreelanceController();
    $controller->projects();
});

$router->get('/freelance/projects/create', function() {
    $controller = new FreelanceProjectController();
    $controller->create();
});

$router->post('/freelance/projects/store', function() {
    $controller = new FreelanceProjectController();
    $controller->store();
});

$router->get('/freelance/projects/view/:id', function($id) {
    $controller = new FreelanceProjectController();
    $controller->viewProject($id);
});

$router->get('/freelance/projects/edit/:id', function($id) {
    $controller = new FreelanceProjectController();
    $controller->edit($id);
});

$router->post('/freelance/projects/update/:id', function($id) {
    $controller = new FreelanceProjectController();
    $controller->update($id);
});

$router->get('/freelance/projects/delete/:id', function($id) {
    $controller = new FreelanceProjectController();
    $controller->delete($id);
});

// Project Milestones
$router->get('/freelance/projects/:id/milestones/create', function($id) {
    $controller = new FreelanceProjectController();
    $controller->createMilestone($id);
});

$router->post('/freelance/projects/:id/milestones/store', function($id) {
    $controller = new FreelanceProjectController();
    $controller->storeMilestone($id);
});

$router->get('/freelance/milestones/edit/:id', function($id) {
    $controller = new FreelanceProjectController();
    $controller->editMilestone($id);
});

$router->post('/freelance/milestones/update/:id', function($id) {
    $controller = new FreelanceProjectController();
    $controller->updateMilestone($id);
});

$router->get('/freelance/milestones/delete/:id', function($id) {
    $controller = new FreelanceProjectController();
    $controller->deleteMilestone($id);
});

// Freelance Invoices
$router->get('/freelance/invoices', function() {
    $controller = new FreelanceController();
    $controller->invoices();
});

$router->get('/freelance/invoices/create', function() {
    $controller = new FreelanceInvoiceController();
    $controller->create();
});

$router->post('/freelance/invoices/store', function() {
    $controller = new FreelanceInvoiceController();
    $controller->store();
});

$router->get('/freelance/invoices/view/:id', function($id) {
    $controller = new FreelanceInvoiceController();
    $controller->viewInvoice($id);
});

$router->get('/freelance/invoices/edit/:id', function($id) {
    $controller = new FreelanceInvoiceController();
    $controller->edit($id);
});

$router->post('/freelance/invoices/update/:id', function($id) {
    $controller = new FreelanceInvoiceController();
    $controller->update($id);
});

$router->get('/freelance/invoices/delete/:id', function($id) {
    $controller = new FreelanceInvoiceController();
    $controller->delete($id);
});

$router->get('/freelance/invoices/print/:id', function($id) {
    $controller = new FreelanceInvoiceController();
    $controller->printInvoice($id);
});

$router->get('/freelance/invoices/send/:id', function($id) {
    $controller = new FreelanceInvoiceController();
    $controller->sendInvoice($id);
});

$router->get('/freelance/invoices/mark-paid/:id', function($id) {
    $controller = new FreelanceInvoiceController();
    $controller->markPaid($id);
});

// Freelance Payments
$router->get('/freelance/payments', function() {
    $controller = new FreelanceController();
    $controller->payments();
});

$router->get('/freelance/payments/create', function() {
    $controller = new FreelancePaymentController();
    $controller->create();
});

$router->post('/freelance/payments/store', function() {
    $controller = new FreelancePaymentController();
    $controller->store();
});

$router->get('/freelance/payments/view/:id', function($id) {
    $controller = new FreelancePaymentController();
    $controller->viewPayment($id);
});

$router->get('/freelance/payments/edit/:id', function($id) {
    $controller = new FreelancePaymentController();
    $controller->edit($id);
});

$router->post('/freelance/payments/update/:id', function($id) {
    $controller = new FreelancePaymentController();
    $controller->update($id);
});

$router->get('/freelance/payments/delete/:id', function($id) {
    $controller = new FreelancePaymentController();
    $controller->delete($id);
});

// Freelance Reports
$router->get('/freelance/reports', function() {
    $controller = new FreelanceController();
    $controller->reports();
});

$router->get('/freelance/reports/income', function() {
    $controller = new FreelanceReportController();
    $controller->income();
});

$router->get('/freelance/reports/clients', function() {
    $controller = new FreelanceReportController();
    $controller->clients();
});

$router->get('/freelance/reports/projects', function() {
    $controller = new FreelanceReportController();
    $controller->projects();
});

$router->get('/freelance/reports/export/:type', function($type) {
    $controller = new FreelanceReportController();
    $controller->export($type);
});
