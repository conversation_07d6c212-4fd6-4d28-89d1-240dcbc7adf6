<?php
/**
 * Checklist Routes
 *
 * This file contains all routes related to checklists and checklist templates.
 */

// Checklists
$router->get('/checklists', function() {
    $controller = new ChecklistController();
    $controller->index();
});

$router->get('/checklists/create', function() {
    $controller = new ChecklistController();
    $controller->create();
});

$router->post('/checklists/store', function() {
    $controller = new ChecklistController();
    $controller->store();
});

$router->get('/checklists/view/:id', function($id) {
    $controller = new ChecklistController();
    $controller->viewChecklist($id);
});

$router->get('/checklists/edit/:id', function($id) {
    $controller = new ChecklistController();
    $controller->edit($id);
});

$router->post('/checklists/update/:id', function($id) {
    $controller = new ChecklistController();
    $controller->update($id);
});

$router->get('/checklists/delete/:id', function($id) {
    $controller = new ChecklistController();
    $controller->delete($id);
});

$router->post('/checklists/update-item-status', function() {
    $controller = new ChecklistController();
    $controller->updateItemStatus();
});

$router->post('/checklists/add-item/:id', function($id) {
    $controller = new ChecklistController();
    $controller->addItem($id);
});

$router->get('/checklists/delete-item/:id', function($id) {
    $controller = new ChecklistController();
    $controller->deleteItem($id);
});

// Checklist Templates
$router->get('/checklists/templates', function() {
    $controller = new ChecklistController();
    $controller->templates();
});

$router->get('/checklists/view-template/:id', function($id) {
    $controller = new ChecklistController();
    $controller->viewTemplate($id);
});

$router->get('/checklists/create-template', function() {
    $controller = new ChecklistController();
    $controller->createTemplate();
});

$router->post('/checklists/store-template', function() {
    $controller = new ChecklistController();
    $controller->storeTemplate();
});

$router->get('/checklists/create-template-from-checklist/:id', function($id) {
    $controller = new ChecklistController();
    $controller->createTemplateFromChecklist($id);
});

$router->post('/checklists/store-template-from-checklist/:id', function($id) {
    $controller = new ChecklistController();
    $controller->storeTemplateFromChecklist($id);
});

$router->get('/checklists/delete-template/:id', function($id) {
    $controller = new ChecklistController();
    $controller->deleteTemplate($id);
});

// Project Checklists
$router->get('/projects/:projectId/checklists', function($projectId) {
    $controller = new ChecklistController();
    $controller->projectChecklists($projectId);
});

$router->get('/projects/:projectId/checklists/create', function($projectId) {
    $controller = new ChecklistController();
    $controller->createProjectChecklist($projectId);
});

$router->post('/projects/:projectId/checklists/store', function($projectId) {
    $controller = new ChecklistController();
    $controller->storeProjectChecklist($projectId);
});
