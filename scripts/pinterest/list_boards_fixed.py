#!/usr/bin/env python
"""
Pinterest Board Listing Script

This script lists all boards for a Pinterest user using the py3-pinterest library.
"""

import os
import sys
import json
from py3pin.Pinterest import Pinterest
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# Get arguments from command line
if len(sys.argv) < 5:
    print(json.dumps({
        "success": False,
        "message": "Missing required arguments. Usage: list_boards.py email password username cred_root [chrome_profile]"
    }))
    sys.exit(1)

email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]
cred_root = sys.argv[4]
chrome_profile = sys.argv[5] if len(sys.argv) > 5 else None

# Configure Chrome options if a profile is provided
chrome_options = Options()
if chrome_profile:
    # Use a specific Chrome profile if provided
    chrome_options.add_argument(f"--user-data-dir={chrome_profile}")
else:
    # Otherwise use a clean profile
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

# Create a custom browser function that uses our Chrome options
def custom_browser():
    try:
        # Try to use the ChromeDriverManager to get the correct driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        return driver
    except Exception as e:
        print(json.dumps({"success": False, "message": f"Error creating custom browser: {e}"}))
        # Fallback to default browser
        return None

# Create Pinterest instance - without browser_function parameter
pinterest = Pinterest(
    email=email,
    password=password,
    username=username,
    cred_root=cred_root
)

try:
    # Check if we need to login
    cred_file = os.path.join(cred_root, f"{username}.cookies")
    if not os.path.exists(cred_file) or os.path.getsize(cred_file) == 0:
        # Login to Pinterest
        pinterest.login()
    
    # Get all boards for the user
    boards = pinterest.boards(username=username)
    
    # Process the boards
    board_list = []
    for board in boards:
        board_data = {
            "id": board.get("id"),
            "name": board.get("name", ""),
            "description": board.get("description", ""),
            "url": board.get("url", ""),
            "pin_count": board.get("pin_count", 0),
            "privacy": board.get("privacy", "public"),
            "created_at": board.get("created_at", "")
        }
        board_list.append(board_data)
    
    print(json.dumps({
        "success": True,
        "boards": board_list,
        "message": f"Found {len(board_list)} boards"
    }))
except Exception as e:
    print(json.dumps({
        "success": False,
        "message": f"Error listing boards: {str(e)}"
    }))
