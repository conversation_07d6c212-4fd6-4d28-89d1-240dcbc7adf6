<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIToolsHub - Discover the Best AI Tools</title>
    <meta name="description" content="Explore our curated directory of 1,500+ AI tools to boost your productivity, creativity, and business growth.">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#10b981'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="bg-gray-50 text-gray-800 min-h-screen">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="index.html" class="text-3xl font-['Pacifico'] text-primary">AIToolsHub</a>
            </div>

            <!-- Search Bar -->
            <div class="hidden md:flex items-center flex-1 max-w-2xl mx-8">
                <div class="relative w-full">
                    <input type="text" placeholder="Search for AI tools..." data-search class="w-full py-2 pl-10 pr-4 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
                        <i class="ri-search-line"></i>
                    </div>
                </div>
            </div>

            <!-- Right Navigation -->
            <div class="flex items-center space-x-4">
                <button class="hidden md:flex items-center space-x-1 text-sm font-medium text-gray-600 hover:text-primary transition-colors">
                    <div class="w-5 h-5 flex items-center justify-center">
                        <i class="ri-add-line"></i>
                    </div>
                    <span>Submit Tool</span>
                </button>
                <button class="hidden md:flex items-center space-x-1 text-sm font-medium text-gray-600 hover:text-primary transition-colors">
                    <div class="w-5 h-5 flex items-center justify-center">
                        <i class="ri-user-line"></i>
                    </div>
                    <span>Sign In</span>
                </button>
                <button class="hidden md:block bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap">
                    Join Pro
                </button>

                <!-- Theme Toggle -->
                <div class="flex items-center">
                    <div class="relative inline-block w-10 mr-2 align-middle select-none">
                        <input type="checkbox" id="theme-toggle" class="toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer transition-transform duration-200 ease-in-out"/>
                        <label for="theme-toggle" class="toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer"></label>
                    </div>
                    <div class="w-5 h-5 flex items-center justify-center text-gray-600">
                        <i class="ri-moon-line"></i>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden w-10 h-10 flex items-center justify-center text-gray-600" data-mobile-menu>
                    <i class="ri-menu-line text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Search (Visible on small screens) -->
        <div class="md:hidden px-4 pb-3">
            <div class="relative w-full">
                <input type="text" placeholder="Search for AI tools..." data-search class="w-full py-2 pl-10 pr-4 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="md:hidden hidden bg-white border-t border-gray-200" data-mobile-menu-content>
            <div class="px-4 py-3 space-y-3">
                <a href="#" class="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors">
                    <i class="ri-add-line"></i>
                    <span>Submit Tool</span>
                </a>
                <a href="#" class="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors">
                    <i class="ri-user-line"></i>
                    <span>Sign In</span>
                </a>
                <a href="#" class="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors">
                    <i class="ri-vip-crown-line"></i>
                    <span>Join Pro</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative overflow-hidden">
        <div style="background-image: url('https://readdy.ai/api/search-image?query=futuristic%20technology%20background%20with%20AI%20visualization%2C%20abstract%20digital%20network%20connections%2C%20blue%20and%20purple%20tech%20elements%2C%20minimalist%20design%20with%20clean%20space%20on%20the%20left%20side%20for%20text%2C%20high%20quality%20professional&width=1600&height=600&seq=hero1&orientation=landscape');" class="w-full h-[500px] bg-cover bg-center">
            <div class="absolute inset-0 hero-gradient"></div>
            <div class="container mx-auto px-4 h-full flex items-center">
                <div class="w-full md:w-1/2 z-10">
                    <h1 class="text-4xl md:text-5xl font-bold mb-4 text-gray-900">Discover the Best AI Tools for Your Workflow</h1>
                    <p class="text-lg text-gray-700 mb-8">Explore our curated directory of 1,500+ AI tools to boost your productivity, creativity, and business growth.</p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-blue-600 transition-colors whitespace-nowrap">
                            Explore Tools
                        </button>
                        <button class="bg-white text-primary px-6 py-3 rounded-button font-medium border border-primary hover:bg-gray-50 transition-colors whitespace-nowrap">
                            Join Community
                        </button>
                    </div>
                    <div class="flex items-center mt-8 text-gray-600 text-sm">
                        <div class="flex items-center mr-6">
                            <div class="w-5 h-5 flex items-center justify-center mr-2">
                                <i class="ri-tools-line"></i>
                            </div>
                            <span>1,500+ Tools</span>
                        </div>
                        <div class="flex items-center mr-6">
                            <div class="w-5 h-5 flex items-center justify-center mr-2">
                                <i class="ri-user-line"></i>
                            </div>
                            <span>50K+ Users</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-5 h-5 flex items-center justify-center mr-2">
                                <i class="ri-star-line"></i>
                            </div>
                            <span>25K+ Reviews</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Category Filter Buttons -->
    <section class="bg-white py-6 border-b border-gray-200">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold">Popular Categories</h2>
                <a href="#" class="text-primary text-sm font-medium flex items-center">
                    View All
                    <div class="w-5 h-5 flex items-center justify-center ml-1">
                        <i class="ri-arrow-right-line"></i>
                    </div>
                </a>
            </div>
            <div class="flex flex-nowrap overflow-x-auto custom-scrollbar gap-3 pb-2">
                <button class="bg-primary text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-apps-line"></i>
                    </div>
                    All Tools
                </button>
                <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-image-line"></i>
                    </div>
                    Image Generation
                </button>
                <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-text"></i>
                    </div>
                    Text & Writing
                </button>
                <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-video-line"></i>
                    </div>
                    Video Creation
                </button>
                <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-customer-service-line"></i>
                    </div>
                    Chatbots
                </button>
                <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-code-line"></i>
                    </div>
                    Development
                </button>
                <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-voice-recognition-line"></i>
                    </div>
                    Audio & Voice
                </button>
                <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-line-chart-line"></i>
                    </div>
                    Business
                </button>
                <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-education-line"></i>
                    </div>
                    Education
                </button>
            </div>
        </div>
    </section>
    <!-- Featured Tools Section -->
    <section class="py-10 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold">Featured Tools</h2>
                <div class="flex items-center space-x-2">
                    <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
                        <i class="ri-arrow-left-s-line"></i>
                    </button>
                    <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
                        <i class="ri-arrow-right-s-line"></i>
                    </button>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Featured Tool Card 1 -->
                <div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
                    <div class="relative">
                        <img src="https://readdy.ai/api/search-image?query=AI%20image%20generation%20software%20interface%20with%20beautiful%20artwork%20being%20created%2C%20professional%20UI%20design%2C%20clean%20modern%20interface%2C%20showing%20advanced%20AI%20art%20creation%20tools&width=600&height=300&seq=feat1&orientation=landscape" alt="ImageAI Pro" class="w-full h-48 object-cover object-top">
                        <div class="absolute top-3 left-3 bg-yellow-400 text-xs font-bold px-2 py-1 rounded text-gray-800">SPONSORED</div>
                    </div>
                    <div class="p-5">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-lg font-bold">ImageAI Pro</h3>
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-4">Create stunning AI-generated images with advanced style controls and commercial usage rights.</p>
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="flex text-yellow-400">
                                    <i class="ri-star-fill"></i>
                                    <i class="ri-star-fill"></i>
                                    <i class="ri-star-fill"></i>
                                    <i class="ri-star-fill"></i>
                                    <i class="ri-star-half-fill"></i>
                                </div>
                                <span class="text-xs text-gray-500 ml-1">(4.5/5)</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-500">
                                <div class="w-4 h-4 flex items-center justify-center mr-1">
                                    <i class="ri-eye-line"></i>
                                </div>
                                <span>15.2K users</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors" data-bookmark>
                                    <i class="ri-bookmark-line"></i>
                                </button>
                                <button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors" data-share>
                                    <i class="ri-share-line"></i>
                                </button>
                            </div>
                            <a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap">Visit Tool</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-gray-900 text-white">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-3xl font-bold mb-4">Stay Updated with the Latest AI Tools</h2>
                <p class="text-gray-300 mb-8">Join 50,000+ AI enthusiasts and receive weekly updates on new tools, exclusive discounts, and industry insights.</p>
                <form class="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto" data-newsletter-form>
                    <input type="email" placeholder="Enter your email address" required class="flex-1 px-4 py-3 rounded-button border-none text-gray-800 focus:outline-none focus:ring-2 focus:ring-primary">
                    <button type="submit" class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-blue-600 transition-colors whitespace-nowrap">
                        Subscribe
                    </button>
                </form>
                <p class="text-gray-400 text-sm mt-4">We respect your privacy. Unsubscribe at any time.</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-gray-300">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
                <div class="lg:col-span-2">
                    <a href="index.html" class="text-3xl font-['Pacifico'] text-white mb-4 inline-block">AIToolsHub</a>
                    <p class="text-gray-400 mb-6">The largest directory of AI tools to help you discover the best AI-powered solutions for your personal and professional needs.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white hover:bg-primary transition-colors">
                            <i class="ri-twitter-x-line"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white hover:bg-primary transition-colors">
                            <i class="ri-linkedin-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white hover:bg-primary transition-colors">
                            <i class="ri-facebook-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white hover:bg-primary transition-colors">
                            <i class="ri-instagram-line"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h3 class="text-white font-bold text-lg mb-4">Categories</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Image Generation</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Text & Writing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Video Creation</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Audio & Voice</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Chatbots</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Development</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-white font-bold text-lg mb-4">Company</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Blog</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Press</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-white font-bold text-lg mb-4">Resources</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Submit a Tool</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Advertise</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Affiliate Program</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">API</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 AIToolsHub. All rights reserved.</p>
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="terms-of-service.html" class="text-gray-400 hover:text-white transition-colors text-sm">Terms of Service</a>
                    <a href="privacy-policy.html" class="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
                    <a href="cookie-policy.html" class="text-gray-400 hover:text-white transition-colors text-sm">Cookie Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Sitemap</a>
                </div>
                <div class="flex items-center mt-4 md:mt-0 space-x-4">
                    <i class="ri-visa-fill text-2xl"></i>
                    <i class="ri-mastercard-fill text-2xl"></i>
                    <i class="ri-paypal-fill text-2xl"></i>
                    <i class="ri-apple-fill text-2xl"></i>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/script.js"></script>
</body>
</html>
<div class="grid md:grid-cols-2 gap-6">
<div class="bg-gray-50 p-6 rounded-lg">
<h3 class="text-xl font-semibold mb-3">Subscription Terms</h3>
<ul class="list-disc pl-6 text-gray-600 space-y-2">
<li>Automatic renewal unless cancelled</li>
<li>Pricing subject to change</li>
<li>No refunds for partial months</li>
<li>Cancel anytime policy</li>
</ul>
</div>
<div class="bg-gray-50 p-6 rounded-lg">
<h3 class="text-xl font-semibold mb-3">Premium Features</h3>
<ul class="list-disc pl-6 text-gray-600 space-y-2">
<li>Ad-free experience</li>
<li>Early access to new tools</li>
<li>Exclusive discounts</li>
<li>Priority support</li>
</ul>
</div>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">6. Intellectual Property</h2>
<div class="bg-gray-50 p-6 rounded-lg">
<p class="text-gray-600 mb-4">All content and materials available on AIToolsHub, including but not limited to text, graphics, logos, button icons, images, audio clips, digital downloads, data compilations, and software, are the property of AIToolsHub or its content suppliers and protected by international copyright laws.</p>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">7. Limitation of Liability</h2>
<div class="bg-blue-50 p-6 rounded-lg border border-blue-100">
<p class="text-gray-600 mb-4">AIToolsHub is not liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.</p>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">8. Changes to Terms</h2>
<p class="text-gray-600">We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through our platform. Your continued use of our services after such modifications constitutes acceptance of the updated terms.</p>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">9. Contact Information</h2>
<div class="bg-gray-50 p-6 rounded-lg">
<p class="text-gray-600 mb-4">For questions about these Terms of Service, please contact us:</p>
<div class="space-y-2">
<div class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary">
<i class="ri-mail-line"></i>
</div>
<span><EMAIL></span>
</div>
<div class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary">
<i class="ri-customer-service-line"></i>
</div>
<span>1-800-AI-TOOLS</span>
</div>
<div class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary">
<i class="ri-map-pin-line"></i>
</div>
<span>123 AI Street, Tech City, TC 12345</span>
</div>
</div>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">2. Information We Collect</h2>
<div class="bg-gray-50 p-6 rounded-lg mb-4">
<h3 class="text-xl font-semibold mb-3">2.1 Personal Information</h3>
<ul class="list-disc pl-6 text-gray-600 space-y-2">
<li>Name and contact information</li>
<li>Email address</li>
<li>Account credentials</li>
<li>Payment information</li>
<li>Usage preferences</li>
</ul>
</div>
<div class="bg-gray-50 p-6 rounded-lg">
<h3 class="text-xl font-semibold mb-3">2.2 Automatically Collected Information</h3>
<ul class="list-disc pl-6 text-gray-600 space-y-2">
<li>IP address and device information</li>
<li>Browser type and settings</li>
<li>Usage data and analytics</li>
<li>Cookies and similar technologies</li>
</ul>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">3. How We Use Your Information</h2>
<div class="space-y-4 text-gray-600">
<p>We use the collected information for:</p>
<ul class="list-disc pl-6 space-y-2">
<li>Providing and improving our services</li>
<li>Processing your transactions</li>
<li>Sending you updates and newsletters</li>
<li>Personalizing your experience</li>
<li>Analyzing usage patterns</li>
<li>Preventing fraud and ensuring security</li>
</ul>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">4. Data Security</h2>
<div class="bg-blue-50 p-6 rounded-lg border border-blue-100">
<p class="text-gray-600 mb-4">We implement appropriate security measures to protect your personal information, including:</p>
<ul class="list-disc pl-6 text-gray-600 space-y-2">
<li>Encryption of sensitive data</li>
<li>Regular security assessments</li>
<li>Access controls and authentication</li>
<li>Secure data storage practices</li>
</ul>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">5. Your Rights</h2>
<div class="grid md:grid-cols-2 gap-6">
<div class="bg-gray-50 p-6 rounded-lg">
<h3 class="text-xl font-semibold mb-3">Access and Control</h3>
<ul class="list-disc pl-6 text-gray-600 space-y-2">
<li>Access your personal data</li>
<li>Correct inaccurate information</li>
<li>Request data deletion</li>
<li>Opt-out of communications</li>
</ul>
</div>
<div class="bg-gray-50 p-6 rounded-lg">
<h3 class="text-xl font-semibold mb-3">Data Portability</h3>
<ul class="list-disc pl-6 text-gray-600 space-y-2">
<li>Download your data</li>
<li>Transfer to another service</li>
<li>Request data format</li>
</ul>
</div>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">6. Cookies Policy</h2>
<div class="text-gray-600 space-y-4">
<p>We use cookies and similar tracking technologies to:</p>
<ul class="list-disc pl-6 space-y-2">
<li>Remember your preferences</li>
<li>Analyze site usage</li>
<li>Personalize content</li>
<li>Improve user experience</li>
</ul>
<p class="mt-4">You can control cookie settings through your browser preferences.</p>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">7. Third-Party Services</h2>
<div class="bg-gray-50 p-6 rounded-lg">
<p class="text-gray-600 mb-4">We may use third-party services that collect information about you. These services include:</p>
<ul class="list-disc pl-6 text-gray-600 space-y-2">
<li>Payment processors</li>
<li>Analytics providers</li>
<li>Email service providers</li>
<li>Advertising partners</li>
</ul>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">8. Changes to Privacy Policy</h2>
<p class="text-gray-600">We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last updated" date.</p>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold mb-4">9. Contact Us</h2>
<div class="bg-gray-50 p-6 rounded-lg">
<p class="text-gray-600 mb-4">If you have any questions about this privacy policy, please contact us:</p>
<div class="space-y-2">
<div class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary">
<i class="ri-mail-line"></i>
</div>
<span><EMAIL></span>
</div>
<div class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary">
<i class="ri-customer-service-line"></i>
</div>
<span>1-800-AI-TOOLS</span>
</div>
<div class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary">
<i class="ri-map-pin-line"></i>
</div>
<span>123 AI Street, Tech City, TC 12345</span>
</div>
</div>
</div>
</div>
<div class="border-t border-gray-200 pt-8 mt-12">
<div class="flex flex-col sm:flex-row justify-between items-center gap-4">
<p class="text-gray-500 text-sm">© 2025 AIToolsHub. All rights reserved.</p>
<div class="flex items-center space-x-4">
<a href="#" class="text-primary hover:text-blue-600 transition-colors text-sm">Privacy Policy</a>
<a href="#" class="text-primary hover:text-blue-600 transition-colors text-sm">Cookie Policy</a>
</div>
</div>
</div>
</div>
</div>
</section>
<!-- Header -->
<header class="sticky top-0 z-50 bg-white shadow-sm">
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<!-- Logo -->
<div class="flex items-center">
<a href="#" class="text-3xl font-['Pacifico'] text-primary">logo</a>
</div>
<!-- Search Bar -->
<div class="hidden md:flex items-center flex-1 max-w-2xl mx-8">
<div class="relative w-full">
<input type="text" placeholder="Search for AI tools..." class="w-full py-2 pl-10 pr-4 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
<div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-search-line"></i>
</div>
</div>
</div>
<!-- Right Navigation -->
<div class="flex items-center space-x-4">
<button class="hidden md:flex items-center space-x-1 text-sm font-medium text-gray-600 hover:text-primary transition-colors">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-add-line"></i>
</div>
<span>Submit Tool</span>
</button>
<button class="hidden md:flex items-center space-x-1 text-sm font-medium text-gray-600 hover:text-primary transition-colors">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-user-line"></i>
</div>
<span>Sign In</span>
</button>
<button class="hidden md:block bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap">
Join Pro
</button>
<!-- Theme Toggle -->
<div class="flex items-center">
<div class="relative inline-block w-10 mr-2 align-middle select-none">
<input type="checkbox" id="theme-toggle" class="toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer transition-transform duration-200 ease-in-out"/>
<label for="theme-toggle" class="toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer"></label>
</div>
<div class="w-5 h-5 flex items-center justify-center text-gray-600">
<i class="ri-moon-line"></i>
</div>
</div>
<!-- Mobile Menu Button -->
<button class="md:hidden w-10 h-10 flex items-center justify-center text-gray-600">
<i class="ri-menu-line text-xl"></i>
</button>
</div>
</div>
<!-- Mobile Search (Visible on small screens) -->
<div class="md:hidden px-4 pb-3">
<div class="relative w-full">
<input type="text" placeholder="Search for AI tools..." class="w-full py-2 pl-10 pr-4 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
<div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-search-line"></i>
</div>
</div>
</div>
</header>
<!-- Hero Section -->
<section class="relative overflow-hidden">
<div style="background-image: url('https://readdy.ai/api/search-image?query=futuristic%20technology%20background%20with%20AI%20visualization%2C%20abstract%20digital%20network%20connections%2C%20blue%20and%20purple%20tech%20elements%2C%20minimalist%20design%20with%20clean%20space%20on%20the%20left%20side%20for%20text%2C%20high%20quality%20professional&width=1600&height=600&seq=hero1&orientation=landscape');" class="w-full h-[500px] bg-cover bg-center">
<div class="absolute inset-0 hero-gradient"></div>
<div class="container mx-auto px-4 h-full flex items-center">
<div class="w-full md:w-1/2 z-10">
<h1 class="text-4xl md:text-5xl font-bold mb-4 text-gray-900">Discover the Best AI Tools for Your Workflow</h1>
<p class="text-lg text-gray-700 mb-8">Explore our curated directory of 1,500+ AI tools to boost your productivity, creativity, and business growth.</p>
<div class="flex flex-col sm:flex-row gap-4">
<button class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">
Explore Tools
</button>
<button class="bg-white text-primary px-6 py-3 rounded-button font-medium border border-primary hover:bg-gray-50 transition-colors whitespace-nowrap !rounded-button">
Join Community
</button>
</div>
<div class="flex items-center mt-8 text-gray-600 text-sm">
<div class="flex items-center mr-6">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-tools-line"></i>
</div>
<span>1,500+ Tools</span>
</div>
<div class="flex items-center mr-6">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-user-line"></i>
</div>
<span>50K+ Users</span>
</div>
<div class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-star-line"></i>
</div>
<span>25K+ Reviews</span>
</div>
</div>
</div>
</div>
</div>
</section>
<!-- Category Filter Buttons -->
<section class="bg-white py-6 border-b border-gray-200">
<div class="container mx-auto px-4">
<div class="flex items-center justify-between mb-4">
<h2 class="text-xl font-bold">Popular Categories</h2>
<a href="#" class="text-primary text-sm font-medium flex items-center">
View All
<div class="w-5 h-5 flex items-center justify-center ml-1">
<i class="ri-arrow-right-line"></i>
</div>
</a>
</div>
<div class="flex flex-nowrap overflow-x-auto custom-scrollbar gap-3 pb-2">
<button class="bg-primary text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-apps-line"></i>
</div>
All Tools
</button>
<button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-image-line"></i>
</div>
Image Generation
</button>
<button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-text"></i>
</div>
Text & Writing
</button>
<button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-video-line"></i>
</div>
Video Creation
</button>
<button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-customer-service-line"></i>
</div>
Chatbots
</button>
<button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-code-line"></i>
</div>
Development
</button>
<button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-voice-recognition-line"></i>
</div>
Audio & Voice
</button>
<button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-line-chart-line"></i>
</div>
Business
</button>
<button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200 hover:border-gray-300 flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-education-line"></i>
</div>
Education
</button>
</div>
</div>
</section>
<!-- Featured Tools Section -->
<section class="py-10 bg-white">
<div class="container mx-auto px-4">
<div class="flex items-center justify-between mb-6">
<h2 class="text-2xl font-bold">Featured Tools</h2>
<div class="flex items-center space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-arrow-left-s-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-arrow-right-s-line"></i>
</button>
</div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
<!-- Featured Tool Card 1 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20image%20generation%20software%20interface%20with%20beautiful%20artwork%20being%20created%2C%20professional%20UI%20design%2C%20clean%20modern%20interface%2C%20showing%20advanced%20AI%20art%20creation%20tools&width=600&height=300&seq=feat1&orientation=landscape" alt="ImageAI Pro" class="w-full h-48 object-cover object-top">
<div class="absolute top-3 left-3 bg-yellow-400 text-xs font-bold px-2 py-1 rounded text-gray-800">SPONSORED</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">ImageAI Pro</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">Create stunning AI-generated images with advanced style controls and commercial usage rights.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-half-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.5/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>15.2K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Featured Tool Card 2 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20writing%20assistant%20interface%20with%20text%20being%20generated%2C%20professional%20UI%20design%20with%20document%20editor%2C%20clean%20modern%20interface%20showing%20text%20suggestions%20and%20editing%20tools&width=600&height=300&seq=feat2&orientation=landscape" alt="WriterGPT" class="w-full h-48 object-cover object-top">
<div class="absolute top-3 left-3 bg-yellow-400 text-xs font-bold px-2 py-1 rounded text-gray-800">SPONSORED</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">WriterGPT</h3>
<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
</div>
<p class="text-gray-600 text-sm mb-4">AI-powered writing assistant that helps you create high-quality content for blogs, marketing, and more.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.9/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>23.7K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Featured Tool Card 3 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20video%20creation%20software%20interface%20showing%20video%20editing%20timeline%2C%20professional%20UI%20design%20with%20video%20preview%20panel%2C%20clean%20modern%20interface%20with%20video%20generation%20controls&width=600&height=300&seq=feat3&orientation=landscape" alt="VideoGenius AI" class="w-full h-48 object-cover object-top">
<div class="absolute top-3 left-3 bg-yellow-400 text-xs font-bold px-2 py-1 rounded text-gray-800">SPONSORED</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">VideoGenius AI</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">Turn text into professional videos in minutes with AI-powered video creation and editing tools.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-line"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.0/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>9.8K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
</div>
</div>
</section>
<!-- Premium Membership Banner -->
<section class="py-12 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
<div class="container mx-auto px-4">
<div class="flex flex-col md:flex-row items-center justify-between">
<div class="md:w-1/2 mb-8 md:mb-0">
<h2 class="text-3xl font-bold mb-4">Upgrade to AIToolsHub Pro</h2>
<p class="text-blue-100 mb-6">Get exclusive access to premium tools, early access to new AI releases, and remove all ads with our Pro membership.</p>
<ul class="mb-6 space-y-2">
<li class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-blue-300">
<i class="ri-check-line"></i>
</div>
<span>Unlimited access to 1,500+ premium AI tools</span>
</li>
<li class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-blue-300">
<i class="ri-check-line"></i>
</div>
<span>Exclusive discounts up to 60% on popular AI services</span>
</li>
<li class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-blue-300">
<i class="ri-check-line"></i>
</div>
<span>Ad-free browsing experience</span>
</li>
<li class="flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-blue-300">
<i class="ri-check-line"></i>
</div>
<span>Early access to new AI tool releases</span>
</li>
</ul>
<div class="flex space-x-4">
<button class="bg-white text-primary px-6 py-3 rounded-button font-medium hover:bg-gray-100 transition-colors whitespace-nowrap !rounded-button">
Join Pro Now
</button>
<button class="border border-white text-white px-6 py-3 rounded-button font-medium hover:bg-white/10 transition-colors whitespace-nowrap !rounded-button">
Learn More
</button>
</div>
</div>
<div class="md:w-5/12">
<img src="https://readdy.ai/api/search-image?query=professional%20business%20person%20using%20multiple%20AI%20tools%20on%20laptop%20and%20tablet%2C%20showing%20productivity%20and%20efficiency%2C%20clean%20modern%20workspace%20with%20blue%20accent%20lighting%2C%20high%20quality%20professional&width=600&height=400&seq=premium&orientation=landscape" alt="AIToolsHub Pro" class="rounded-lg shadow-xl">
</div>
</div>
</div>
</section>
<!-- Main Directory Section -->
<section class="py-10 bg-gray-50">
<div class="container mx-auto px-4">
<!-- Category Header -->
<div class="bg-white rounded-lg p-6 mb-8 shadow-sm">
<div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
<div>
<h1 class="text-2xl font-bold mb-2">Image Generation Tools</h1>
<p class="text-gray-600">Discover 200+ AI-powered tools for creating, editing and enhancing images</p>
</div>
<div class="flex items-center space-x-4">
<button class="flex items-center space-x-2 text-sm text-gray-600 hover:text-primary transition-colors">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-information-line"></i>
</div>
<span>Category Guide</span>
</button>
<button class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">
Submit Tool
</button>
</div>
</div>
</div>
<div class="flex flex-col lg:flex-row gap-8">
<!-- Filter Sidebar -->
<div class="lg:w-1/4">
<div class="bg-white p-5 rounded-lg shadow-sm sticky top-24">
<div class="flex items-center justify-between mb-4">
<h3 class="font-bold text-lg">Filters</h3>
<button class="text-primary text-sm font-medium">Reset All</button>
</div>
<!-- Price Range Filter -->
<div class="mb-6">
<h4 class="font-medium mb-3">Price</h4>
<div class="flex items-center space-x-3 mb-4">
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="price-free">
<label for="price-free" class="text-sm">Free</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="price-freemium">
<label for="price-freemium" class="text-sm">Freemium</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="price-paid">
<label for="price-paid" class="text-sm">Paid</label>
</label>
</div>
<div>
<label class="text-sm text-gray-600 mb-2 block">Price Range</label>
<input type="range" min="0" max="100" value="50" class="w-full">
<div class="flex justify-between text-xs text-gray-500 mt-1">
<span>$0</span>
<span>$100+</span>
</div>
</div>
</div>
<!-- Categories Filter -->
<div class="mb-6">
<h4 class="font-medium mb-3">Categories</h4>
<div class="space-y-2 max-h-48 overflow-y-auto custom-scrollbar pr-2">
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-image">
<label for="cat-image" class="text-sm">Image Generation</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-text">
<label for="cat-text" class="text-sm">Text & Writing</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-video">
<label for="cat-video" class="text-sm">Video Creation</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-audio">
<label for="cat-audio" class="text-sm">Audio & Voice</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-chatbots">
<label for="cat-chatbots" class="text-sm">Chatbots</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-dev">
<label for="cat-dev" class="text-sm">Development</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-business">
<label for="cat-business" class="text-sm">Business</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-education">
<label for="cat-education" class="text-sm">Education</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="checkbox" class="custom-checkbox" id="cat-productivity">
<label for="cat-productivity" class="text-sm">Productivity</label>
</label>
</div>
</div>
<!-- Rating Filter -->
<div class="mb-6">
<h4 class="font-medium mb-3">Minimum Rating</h4>
<div class="star-rating flex flex-row-reverse justify-end">
<input type="radio" id="star5" name="rating" value="5">
<label for="star5" title="5 stars"></label>
<input type="radio" id="star4" name="rating" value="4">
<label for="star4" title="4 stars"></label>
<input type="radio" id="star3" name="rating" value="3">
<label for="star3" title="3 stars"></label>
<input type="radio" id="star2" name="rating" value="2">
<label for="star2" title="2 stars"></label>
<input type="radio" id="star1" name="rating" value="1">
<label for="star1" title="1 star"></label>
</div>
</div>
<!-- Release Date Filter -->
<div class="mb-6">
<h4 class="font-medium mb-3">Release Date</h4>
<div class="space-y-2">
<label class="flex items-center cursor-pointer">
<input type="radio" name="release-date" class="custom-checkbox" id="release-all">
<label for="release-all" class="text-sm">All Time</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="radio" name="release-date" class="custom-checkbox" id="release-month">
<label for="release-month" class="text-sm">This Month</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="radio" name="release-date" class="custom-checkbox" id="release-week">
<label for="release-week" class="text-sm">This Week</label>
</label>
<label class="flex items-center cursor-pointer">
<input type="radio" name="release-date" class="custom-checkbox" id="release-today">
<label for="release-today" class="text-sm">Today</label>
</label>
</div>
</div>
<button class="w-full bg-primary text-white py-2 rounded-button font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">
Apply Filters
</button>
</div>
</div>
<!-- Main Content -->
<div class="lg:w-3/4">
<!-- Sorting and View Options -->
<div class="bg-white p-4 rounded-lg shadow-sm mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
<div class="flex items-center">
<span class="text-sm text-gray-600 mr-2">Sort by:</span>
<div class="relative">
<button class="bg-white border border-gray-300 text-gray-700 py-2 pl-4 pr-8 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent flex items-center">
<span>Trending</span>
<div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center pointer-events-none">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
</div>
</div>
<div class="flex items-center space-x-3">
<div class="flex items-center space-x-1">
<button class="w-8 h-8 flex items-center justify-center bg-primary text-white rounded">
<i class="ri-layout-grid-line"></i>
</button>
<button class="w-8 h-8 flex items-center justify-center bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors">
<i class="ri-list-check"></i>
</button>
</div>
<span class="text-sm text-gray-600">Showing 1-12 of 1,500+ tools</span>
</div>
</div>
<!-- Tools Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
<!-- Featured Tools Section -->
<div class="lg:col-span-3 mb-8">
<h2 class="text-xl font-bold mb-4">Featured Image Generation Tools</h2>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
<!-- Featured Tool: Midjourney -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=professional%20AI%20art%20generation%20interface%20with%20stunning%20digital%20artwork%20being%20created%2C%20sleek%20modern%20UI%20showing%20advanced%20artistic%20controls%20and%20style%20options&width=600&height=300&seq=feat_mid1&orientation=landscape" alt="Midjourney" class="w-full h-48 object-cover object-top">
<div class="absolute top-3 left-3 bg-yellow-400 text-xs font-bold px-2 py-1 rounded text-gray-800">FEATURED</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">Midjourney</h3>
<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
</div>
<p class="text-gray-600 text-sm mb-4">Create stunning AI-generated artwork with advanced style controls and commercial usage rights.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.9/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1.2M users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>

<!-- Featured Tool: DALL-E 3 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=professional%20AI%20image%20generation%20interface%20with%20photorealistic%20outputs%2C%20clean%20modern%20UI%20showing%20advanced%20image%20synthesis%20controls%20and%20realistic%20artwork&width=600&height=300&seq=feat_dalle&orientation=landscape" alt="DALL-E 3" class="w-full h-48 object-cover object-top">
<div class="absolute top-3 left-3 bg-yellow-400 text-xs font-bold px-2 py-1 rounded text-gray-800">FEATURED</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">DALL-E 3</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">OpenAI's latest image generation model with enhanced photorealistic capabilities and precise control.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-half-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.8/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>890K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>

<!-- Featured Tool: Stable Diffusion -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=professional%20open%20source%20AI%20image%20generation%20interface%20with%20technical%20controls%2C%20modern%20UI%20showing%20advanced%20image%20creation%20options%20and%20artistic%20results&width=600&height=300&seq=feat_sd&orientation=landscape" alt="Stable Diffusion" class="w-full h-48 object-cover object-top">
<div class="absolute top-3 left-3 bg-yellow-400 text-xs font-bold px-2 py-1 rounded text-gray-800">FEATURED</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">Stable Diffusion</h3>
<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Free</span>
</div>
<p class="text-gray-600 text-sm mb-4">Open-source AI image generation with extensive customization and active community support.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-line"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.2/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>750K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
</div>
</div>

<!-- Sub-categories -->
<div class="lg:col-span-3 mb-8">
<h2 class="text-xl font-bold mb-4">Browse by Style</h2>
<div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
<a href="#" class="block p-4 bg-white rounded-lg border border-gray-200 hover:border-primary transition-colors text-center">
<div class="w-12 h-12 mx-auto mb-2 flex items-center justify-center text-primary">
<i class="ri-artboard-line text-2xl"></i>
</div>
<span class="text-sm font-medium">Digital Art</span>
</a>
<a href="#" class="block p-4 bg-white rounded-lg border border-gray-200 hover:border-primary transition-colors text-center">
<div class="w-12 h-12 mx-auto mb-2 flex items-center justify-center text-primary">
<i class="ri-camera-line text-2xl"></i>
</div>
<span class="text-sm font-medium">Photorealistic</span>
</a>
<a href="#" class="block p-4 bg-white rounded-lg border border-gray-200 hover:border-primary transition-colors text-center">
<div class="w-12 h-12 mx-auto mb-2 flex items-center justify-center text-primary">
<i class="ri-paint-brush-line text-2xl"></i>
</div>
<span class="text-sm font-medium">Artistic</span>
</a>
<a href="#" class="block p-4 bg-white rounded-lg border border-gray-200 hover:border-primary transition-colors text-center">
<div class="w-12 h-12 mx-auto mb-2 flex items-center justify-center text-primary">
<i class="ri-character-recognition-line text-2xl"></i>
</div>
<span class="text-sm font-medium">Anime</span>
</a>
<a href="#" class="block p-4 bg-white rounded-lg border border-gray-200 hover:border-primary transition-colors text-center">
<div class="w-12 h-12 mx-auto mb-2 flex items-center justify-center text-primary">
<i class="ri-building-line text-2xl"></i>
</div>
<span class="text-sm font-medium">Architecture</span>
</a>
<a href="#" class="block p-4 bg-white rounded-lg border border-gray-200 hover:border-primary transition-colors text-center">
<div class="w-12 h-12 mx-auto mb-2 flex items-center justify-center text-primary">
<i class="ri-landscape-line text-2xl"></i>
</div>
<span class="text-sm font-medium">Landscapes</span>
</a>
</div>
</div>

<!-- All Tools -->
<div class="lg:col-span-3">
<h2 class="text-xl font-bold mb-4">All Image Generation Tools</h2>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=digital%20art%20interface%20showing%20creative%20AI%20image%20generation%2C%20professional%20stylized%20artwork%2C%20sleek%20modern%20UI%20controls%20for%20artistic%20creation&width=600&height=300&seq=mid1&orientation=landscape" alt="Midjourney" class="w-full h-40 object-cover object-top">
<div class="absolute top-3 right-3">
<span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">Popular</span>
</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">Midjourney</h3>
<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
</div>
<p class="text-gray-600 text-sm mb-4">Create stunning AI-generated artwork and illustrations with advanced style control and artistic features.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.9/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1.2M users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card: DALL-E 3 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20art%20generation%20platform%20with%20photorealistic%20outputs%2C%20clean%20interface%20showing%20image%20creation%20process%2C%20modern%20minimalist%20design&width=600&height=300&seq=dalle3&orientation=landscape" alt="DALL-E 3" class="w-full h-40 object-cover object-top">
<div class="absolute top-3 right-3">
<span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded">Hot</span>
</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">DALL-E 3</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">OpenAI's latest image generation model with enhanced photorealistic capabilities and precise prompt following.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-half-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.8/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>890K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card: Stable Diffusion -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=open%20source%20AI%20image%20generation%20interface%2C%20technical%20yet%20user-friendly%20UI%20showing%20advanced%20image%20synthesis%20options&width=600&height=300&seq=sd1&orientation=landscape" alt="Stable Diffusion" class="w-full h-40 object-cover object-top">
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">Stable Diffusion</h3>
<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Free</span>
</div>
<p class="text-gray-600 text-sm mb-4">Open-source AI image generation model with extensive customization options and active community.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-line"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.2/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>750K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Language Models & Writing -->
<!-- Tool Card: ChatGPT -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20chat%20interface%20with%20clean%20minimalist%20design%2C%20showing%20conversation%20bubbles%20and%20typing%20indicators%2C%20professional%20modern%20look&width=600&height=300&seq=cgpt&orientation=landscape" alt="ChatGPT" class="w-full h-40 object-cover object-top">
<div class="absolute top-3 right-3">
<span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">Most Popular</span>
</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">ChatGPT</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">Advanced conversational AI for writing, analysis, coding, and general assistance with GPT-4 capabilities.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.9/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>100M+ users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card: Claude -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=professional%20AI%20assistant%20interface%20with%20document%20analysis%20features%2C%20clean%20enterprise-grade%20design%20showing%20conversation%20and%20document%20processing&width=600&height=300&seq=claude&orientation=landscape" alt="Claude" class="w-full h-40 object-cover object-top">
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">Claude</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">Anthropic's AI assistant with strong analysis, writing, and coding capabilities plus document processing.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-half-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.7/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>5M+ users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card: Jasper -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=content%20writing%20platform%20interface%20showing%20document%20editor%20and%20AI%20writing%20suggestions%2C%20professional%20marketing%20focused%20design&width=600&height=300&seq=jasper&orientation=landscape" alt="Jasper" class="w-full h-40 object-cover object-top">
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">Jasper</h3>
<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
</div>
<p class="text-gray-600 text-sm mb-4">AI writing assistant specialized in marketing content with templates and brand voice customization.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-line"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.3/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>250K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20chatbot%20interface%20with%20conversation%20bubbles%2C%20professional%20UI%20design%2C%20clean%20modern%20interface%20showing%20chat%20with%20AI%20assistant&width=600&height=300&seq=tool1&orientation=landscape" alt="ChatMaster AI" class="w-full h-40 object-cover object-top">
<div class="absolute top-3 right-3">
<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">New</span>
</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">ChatMaster AI</h3>
<span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Free</span>
</div>
<p class="text-gray-600 text-sm mb-4">Advanced conversational AI chatbot with knowledge base integration and multilingual support.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-line"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.0/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>7.3K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card 2 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20code%20generation%20interface%20with%20syntax%20highlighting%2C%20professional%20UI%20design%2C%20clean%20modern%20interface%20showing%20code%20suggestions%20and%20programming%20environment&width=600&height=300&seq=tool2&orientation=landscape" alt="CodeCopilot" class="w-full h-40 object-cover object-top">
<div class="absolute top-3 right-3">
<span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">Popular</span>
</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">CodeCopilot</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">AI-powered coding assistant that helps developers write better code faster with smart suggestions.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-half-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.7/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>18.5K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card 3 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20voice%20synthesis%20interface%20with%20audio%20waveforms%2C%20professional%20UI%20design%2C%20clean%20modern%20interface%20showing%20voice%20cloning%20and%20audio%20generation%20tools&width=600&height=300&seq=tool3&orientation=landscape" alt="VoiceClone AI" class="w-full h-40 object-cover object-top">
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">VoiceClone AI</h3>
<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
</div>
<p class="text-gray-600 text-sm mb-4">Create realistic AI voices that sound like you or your brand with just a few minutes of audio.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-line"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.2/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>5.9K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card 4 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20data%20analysis%20dashboard%20with%20charts%20and%20graphs%2C%20professional%20UI%20design%2C%20clean%20modern%20interface%20showing%20business%20intelligence%20tools&width=600&height=300&seq=tool4&orientation=landscape" alt="DataMind AI" class="w-full h-40 object-cover object-top">
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">DataMind AI</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">AI-powered data analysis platform that turns complex data into actionable insights and visualizations.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-half-fill"></i>
<i class="ri-star-line"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(3.5/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>4.2K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card 5 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20presentation%20creator%20interface%20with%20slide%20templates%2C%20professional%20UI%20design%2C%20clean%20modern%20interface%20showing%20presentation%20design%20tools&width=600&height=300&seq=tool5&orientation=landscape" alt="SlideMagic AI" class="w-full h-40 object-cover object-top">
<div class="absolute top-3 right-3">
<span class="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded">Hot</span>
</div>
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">SlideMagic AI</h3>
<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
</div>
<p class="text-gray-600 text-sm mb-4">Create professional presentations in seconds with AI-powered design and content generation.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-half-fill"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.6/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>12.7K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
<!-- Tool Card 6 -->
<div class="tool-card bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300">
<div class="relative">
<img src="https://readdy.ai/api/search-image?query=AI%20email%20marketing%20assistant%20interface%20with%20email%20templates%2C%20professional%20UI%20design%2C%20clean%20modern%20interface%20showing%20email%20campaign%20creation%20tools&width=600&height=300&seq=tool6&orientation=landscape" alt="EmailGenius" class="w-full h-40 object-cover object-top">
</div>
<div class="p-5">
<div class="flex items-center justify-between mb-3">
<h3 class="text-lg font-bold">EmailGenius</h3>
<span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Freemium</span>
</div>
<p class="text-gray-600 text-sm mb-4">AI email marketing assistant that writes high-converting emails and optimizes campaigns for better results.</p>
<div class="flex items-center justify-between mb-4">
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-line"></i>
</div>
<span class="text-xs text-gray-500 ml-1">(4.1/5)</span>
</div>
<div class="flex items-center text-xs text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>8.3K users</span>
</div>
</div>
<div class="flex items-center justify-between">
<div class="flex space-x-2">
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
</div>
<a href="#" class="bg-primary text-white px-4 py-2 rounded-button text-sm font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">Visit Tool</a>
</div>
</div>
</div>
</div>
<!-- Pagination -->
<div class="flex justify-center mt-10">
<nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
<a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
<span class="sr-only">Previous</span>
<i class="ri-arrow-left-s-line"></i>
</a>
<a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary text-sm font-medium text-white">
1
</a>
<a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
2
</a>
<a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
3
</a>
<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
...
</span>
<a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
42
</a>
<a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
<span class="sr-only">Next</span>
<i class="ri-arrow-right-s-line"></i>
</a>
</nav>
</div>
</div>
</div>
</div>
</section>
<!-- Newsletter Section -->
<section class="py-16 bg-gray-900 text-white">
<div class="container mx-auto px-4">
<div class="max-w-3xl mx-auto text-center">
<h2 class="text-3xl font-bold mb-4">Stay Updated with the Latest AI Tools</h2>
<p class="text-gray-300 mb-8">Join 50,000+ AI enthusiasts and receive weekly updates on new tools, exclusive discounts, and industry insights.</p>
<div class="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
<input type="email" placeholder="Enter your email address" class="flex-1 px-4 py-3 rounded-button border-none text-gray-800 focus:outline-none focus:ring-2 focus:ring-primary">
<button class="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">
Subscribe
</button>
</div>
<p class="text-gray-400 text-sm mt-4">We respect your privacy. Unsubscribe at any time.</p>
</div>
</div>
</section>
<!-- Footer -->
<footer class="bg-gray-800 text-gray-300">
<div class="container mx-auto px-4 py-12">
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
<div class="lg:col-span-2">
<a href="#" class="text-3xl font-['Pacifico'] text-white mb-4 inline-block">logo</a>
<p class="text-gray-400 mb-6">The largest directory of AI tools to help you discover the best AI-powered solutions for your personal and professional needs.</p>
<div class="flex space-x-4">
<a href="#" class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white hover:bg-primary transition-colors">
<i class="ri-twitter-x-line"></i>
</a>
<a href="#" class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white hover:bg-primary transition-colors">
<i class="ri-linkedin-fill"></i>
</a>
<a href="#" class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white hover:bg-primary transition-colors">
<i class="ri-facebook-fill"></i>
</a>
<a href="#" class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center text-white hover:bg-primary transition-colors">
<i class="ri-instagram-line"></i>
</a>
</div>
</div>
<div>
<h3 class="text-white font-bold text-lg mb-4">Categories</h3>
<ul class="space-y-2">
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Image Generation</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Text & Writing</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Video Creation</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Audio & Voice</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Chatbots</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Development</a></li>
</ul>
</div>
<div>
<h3 class="text-white font-bold text-lg mb-4">Company</h3>
<ul class="space-y-2">
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Blog</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Careers</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Press</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
</ul>
</div>
<div>
<h3 class="text-white font-bold text-lg mb-4">Resources</h3>
<ul class="space-y-2">
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Submit a Tool</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Advertise</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Affiliate Program</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">API</a></li>
<li><a href="#" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
</ul>
</div>
</div>
<div class="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
<p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 AIToolsHub. All rights reserved.</p>
<div class="flex flex-wrap justify-center gap-4">
<a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Terms of Service</a>
<a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a>
<a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Cookie Policy</a>
<a href="#" class="text-gray-400 hover:text-white transition-colors text-sm">Sitemap</a>
</div>
<div class="flex items-center mt-4 md:mt-0 space-x-4">
<i class="ri-visa-fill text-2xl"></i>
<i class="ri-mastercard-fill text-2xl"></i>
<i class="ri-paypal-fill text-2xl"></i>
<i class="ri-apple-fill text-2xl"></i>
</div>
</div>
</div>
</footer>
<!-- Tool Preview Modal (Hidden by default) -->
<div id="tool-preview-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
<div class="absolute inset-0 bg-black bg-opacity-50"></div>
<div class="relative bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto custom-scrollbar">
<div class="sticky top-0 bg-white z-10 flex justify-between items-center p-4 border-b">
<h3 class="text-xl font-bold">Tool Details</h3>
<button id="close-modal" class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700">
<i class="ri-close-line text-xl"></i>
</button>
</div>
<div class="p-6">
<div class="flex flex-col md:flex-row gap-6">
<div class="md:w-2/5">
<img src="https://readdy.ai/api/search-image?query=AI%20writing%20assistant%20interface%20with%20text%20being%20generated%2C%20professional%20UI%20design%20with%20document%20editor%2C%20clean%20modern%20interface%20showing%20text%20suggestions%20and%20editing%20tools&width=600&height=400&seq=modal&orientation=landscape" alt="WriterGPT" class="w-full rounded-lg">
<div class="flex justify-between items-center mt-4">
<div class="flex space-x-2">
<button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-bookmark-line"></i>
</button>
<button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-share-line"></i>
</button>
<button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors">
<i class="ri-thumb-up-line"></i>
</button>
</div>
<div class="flex items-center">
<div class="flex text-yellow-400">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
</div>
<span class="text-sm text-gray-500 ml-1">(4.9/5)</span>
</div>
</div>
</div>
<div class="md:w-3/5">
<div class="flex items-center justify-between mb-4">
<h2 class="text-2xl font-bold">WriterGPT</h2>
<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
</div>
<p class="text-gray-600 mb-6">WriterGPT is an advanced AI writing assistant that helps content creators, marketers, and professionals create high-quality content in seconds. From blog posts and marketing copy to emails and social media content, WriterGPT can generate, edit, and enhance your writing with a human touch.</p>
<div class="mb-6">
<h3 class="font-bold text-lg mb-2">Key Features</h3>
<ul class="space-y-2">
<li class="flex items-start">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary mt-0.5">
<i class="ri-check-line"></i>
</div>
<span>Advanced content generation with customizable tone and style</span>
</li>
<li class="flex items-start">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary mt-0.5">
<i class="ri-check-line"></i>
</div>
<span>Grammar and style improvement suggestions</span>
</li>
<li class="flex items-start">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary mt-0.5">
<i class="ri-check-line"></i>
</div>
<span>SEO optimization for better search rankings</span>
</li>
<li class="flex items-start">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary mt-0.5">
<i class="ri-check-line"></i>
</div>
<span>Multilingual support for over 50 languages</span>
</li>
<li class="flex items-start">
<div class="w-5 h-5 flex items-center justify-center mr-2 text-primary mt-0.5">
<i class="ri-check-line"></i>
</div>
<span>Plagiarism-free content with originality check</span>
</li>
</ul>
</div>
<div class="mb-6">
<h3 class="font-bold text-lg mb-2">Pricing</h3>
<div class="space-y-3">
<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
<div>
<span class="font-medium">Starter</span>
<p class="text-sm text-gray-500">For occasional writers</p>
</div>
<span class="font-bold">$19/month</span>
</div>
<div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
<div>
<span class="font-medium">Professional</span>
<p class="text-sm text-gray-500">For content creators</p>
</div>
<span class="font-bold">$49/month</span>
</div>
<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
<div>
<span class="font-medium">Business</span>
<p class="text-sm text-gray-500">For teams and agencies</p>
</div>
<span class="font-bold">$99/month</span>
</div>
</div>
</div>
<a href="#" class="block w-full bg-primary text-white py-3 rounded-button text-center font-medium hover:bg-blue-600 transition-colors whitespace-nowrap !rounded-button">
Visit WriterGPT
</a>
</div>
</div>
<div class="mt-8 border-t pt-6">
<h3 class="font-bold text-lg mb-4">Similar Tools You Might Like</h3>
<div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
<div class="bg-gray-50 p-4 rounded-lg">
<h4 class="font-bold mb-1">ContentForge AI</h4>
<div class="flex text-yellow-400 text-sm mb-2">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-half-fill"></i>
<span class="text-xs text-gray-500 ml-1">(4.5)</span>
</div>
<p class="text-sm text-gray-600 mb-3">AI content creation platform with templates for various content types.</p>
<a href="#" class="text-primary text-sm font-medium hover:underline">View Tool</a>
</div>
<div class="bg-gray-50 p-4 rounded-lg">
<h4 class="font-bold mb-1">CopyGenius</h4>
<div class="flex text-yellow-400 text-sm mb-2">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-line"></i>
<span class="text-xs text-gray-500 ml-1">(4.0)</span>
</div>
<p class="text-sm text-gray-600 mb-3">Specialized in creating high-converting marketing and ad copy.</p>
<a href="#" class="text-primary text-sm font-medium hover:underline">View Tool</a>
</div>
<div class="bg-gray-50 p-4 rounded-lg">
<h4 class="font-bold mb-1">ProseAI</h4>
<div class="flex text-yellow-400 text-sm mb-2">
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<i class="ri-star-fill"></i>
<span class="text-xs text-gray-500 ml-1">(4.8)</span>
</div>
<p class="text-sm text-gray-600 mb-3">Creative writing assistant for authors, bloggers and storytellers.</p>
<a href="#" class="text-primary text-sm font-medium hover:underline">View Tool</a>
</div>
</div>
</div>
</div>
</div>
</div>
<script id="theme-toggle-script">
document.addEventListener('DOMContentLoaded', function() {
const themeToggle = document.getElementById('theme-toggle');
themeToggle.addEventListener('change', function() {
if (this.checked) {
// Dark mode
document.documentElement.classList.add('dark');
} else {
// Light mode
document.documentElement.classList.remove('dark');
}
});
});
</script>
<script id="modal-control-script">
document.addEventListener('DOMContentLoaded', function() {
const modal = document.getElementById('tool-preview-modal');
const closeModal = document.getElementById('close-modal');
const toolCards = document.querySelectorAll('.tool-card');
toolCards.forEach(card => {
card.addEventListener('click', function() {
modal.classList.remove('hidden');
document.body.style.overflow = 'hidden';
});
});
closeModal.addEventListener('click', function() {
modal.classList.add('hidden');
document.body.style.overflow = 'auto';
});
// Close modal when clicking outside
modal.addEventListener('click', function(e) {
if (e.target === modal) {
modal.classList.add('hidden');
document.body.style.overflow = 'auto';
}
});
});
</script>
<script id="range-slider-script">
document.addEventListener('DOMContentLoaded', function() {
const rangeInputs = document.querySelectorAll('input[type="range"]');
function handleInputChange(e) {
let target = e.target;
const min = target.min;
const max = target.max;
const val = target.value;
target.style.backgroundSize = (val - min) * 100 / (max - min) + '% 100%';
}
rangeInputs.forEach(input => {
input.addEventListener('input', handleInputChange);
// Set initial value
const min = input.min;
const max = input.max;
const val = input.value;
input.style.backgroundSize = (val - min) * 100 / (max - min) + '% 100%';
});
});
</script>
</body>
</html>