<?php
/**
 * Test script to simulate the note view functionality
 */

require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';

try {
    $db = Database::getInstance();
    
    // Test the Note model's find method
    $noteModel = new Note();
    
    echo "<h2>Testing Note Model</h2>\n";
    
    // Get a note using the model
    $noteId = 2; // Using the first note from our debug
    $note = $noteModel->find($noteId);
    
    if ($note) {
        echo "<strong>Note found via model:</strong><br>\n";
        echo "<strong>ID:</strong> " . $note['id'] . "<br>\n";
        echo "<strong>Title:</strong> " . htmlspecialchars($note['title']) . "<br>\n";
        echo "<strong>Content exists:</strong> " . (!empty($note['content']) ? 'YES' : 'NO') . "<br>\n";
        echo "<strong>Content length:</strong> " . strlen($note['content'] ?? '') . "<br>\n";
        echo "<strong>Content:</strong> <pre>" . htmlspecialchars($note['content'] ?? '') . "</pre>\n";
        echo "<strong>User ID:</strong> " . $note['user_id'] . "<br>\n";
        
        // Test the condition used in the view
        echo "<h3>View Condition Test</h3>\n";
        $contentExists = !empty($note['content']) && trim($note['content']) !== '';
        echo "<strong>Content condition (!empty && trim !== ''):</strong> " . ($contentExists ? 'TRUE' : 'FALSE') . "<br>\n";
        
        // Show all note fields
        echo "<h3>All Note Fields</h3>\n";
        echo "<pre>" . print_r($note, true) . "</pre>\n";
        
    } else {
        echo "Note not found via model!<br>\n";
    }
    
    // Test with different note IDs
    echo "<h2>Testing Other Notes</h2>\n";
    $allNotes = $db->fetchAll("SELECT id, title, LENGTH(content) as content_length FROM notes LIMIT 5");
    foreach ($allNotes as $noteInfo) {
        $testNote = $noteModel->find($noteInfo['id']);
        echo "<strong>Note {$noteInfo['id']}:</strong> {$noteInfo['title']} - ";
        echo "Content length: {$noteInfo['content_length']} - ";
        echo "Model content: " . (isset($testNote['content']) ? strlen($testNote['content']) : 'NULL') . "<br>\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
