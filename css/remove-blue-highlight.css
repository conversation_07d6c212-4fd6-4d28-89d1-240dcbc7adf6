/**
 * Remove Blue Highlight CSS
 *
 * This stylesheet removes the blue highlighting effect from task items
 * in the task list view without affecting filter buttons.
 */

/* Target only task items and rows for blue highlight removal */
.task-item.bg-primary-50,
.task-item.dark\:bg-primary-900,
.task-row.bg-primary-50,
.task-row.dark\:bg-primary-900 {
    background-color: transparent !important;
}

.task-item.border-l-4.border-primary-500,
.task-row.border-l-4.border-primary-500 {
    border-left: none !important;
    padding-left: initial !important;
}

/* Simple hover effect for task items */
.task-item:hover,
.task-row:hover {
    background-color: rgba(243, 244, 246, 0.2) !important;
}

.dark .task-item:hover,
.dark .task-row:hover {
    background-color: rgba(55, 65, 81, 0.2) !important;
}

/* Ensure transitions are smooth */
.task-item,
.task-row {
    transition: background-color 0.15s ease !important;
}

/* Preserve filter button styles */
.quick-filter-button.active {
    background-color: #eef2ff !important; /* Very light indigo background */
    color: #4f46e5 !important; /* Indigo text */
    border-color: #c7d2fe !important; /* Light indigo border */
    font-weight: 600 !important;
    border-left: 3px solid #4f46e5 !important;
    padding-left: calc(1rem - 3px) !important;
}

.dark .quick-filter-button.active {
    background-color: #312e81 !important; /* Dark indigo background */
    color: #a5b4fc !important; /* Light indigo text */
    border-color: #4f46e5 !important; /* Indigo border */
    border-left-color: #818cf8 !important;
}
