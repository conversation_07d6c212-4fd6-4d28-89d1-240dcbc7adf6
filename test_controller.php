<?php
/**
 * Test script to simulate the NoteController viewNote method
 */

// Start session
session_start();

require_once 'src/utils/Database.php';
require_once 'src/models/Note.php';
require_once 'src/utils/Session.php';

try {
    // Simulate a logged-in user (user_id = 2 based on our test data)
    $_SESSION['user'] = [
        'id' => 2,
        'name' => 'Test User',
        'email' => '<EMAIL>'
    ];
    
    echo "<h2>Testing NoteController viewNote Logic</h2>\n";
    
    $noteModel = new Note();
    $noteId = 2; // Test with note ID 2
    
    // Simulate the controller logic
    $user = Session::getUser();
    $userId = $user['id'];
    
    echo "<strong>User ID from session:</strong> " . $userId . "<br>\n";
    
    // Get note
    $note = $noteModel->find($noteId);
    
    echo "<strong>Note found:</strong> " . ($note ? 'YES' : 'NO') . "<br>\n";
    
    if ($note) {
        echo "<strong>Note user_id:</strong> " . $note['user_id'] . "<br>\n";
        echo "<strong>Session user_id:</strong> " . $userId . "<br>\n";
        echo "<strong>User owns note:</strong> " . ($note['user_id'] == $userId ? 'YES' : 'NO') . "<br>\n";
        
        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            echo "<strong>ERROR:</strong> Note not found or doesn't belong to user<br>\n";
        } else {
            echo "<strong>SUCCESS:</strong> Note verification passed<br>\n";
            
            // Test the view condition
            $contentExists = !empty($note['content']) && trim($note['content']) !== '';
            echo "<strong>Content condition result:</strong> " . ($contentExists ? 'TRUE' : 'FALSE') . "<br>\n";
            echo "<strong>Content:</strong> '" . htmlspecialchars($note['content']) . "'<br>\n";
            echo "<strong>Content length:</strong> " . strlen($note['content']) . "<br>\n";
            echo "<strong>Content trimmed:</strong> '" . htmlspecialchars(trim($note['content'])) . "'<br>\n";
            echo "<strong>Content trimmed length:</strong> " . strlen(trim($note['content'])) . "<br>\n";
        }
    }
    
    // Test with a few different notes
    echo "<h3>Testing Multiple Notes</h3>\n";
    $notes = $noteModel->getUserNotes($userId, [], 5, 0);
    foreach ($notes as $testNote) {
        $contentExists = !empty($testNote['content']) && trim($testNote['content']) !== '';
        echo "<strong>Note {$testNote['id']}:</strong> {$testNote['title']} - Content: " . ($contentExists ? 'YES' : 'NO') . "<br>\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
