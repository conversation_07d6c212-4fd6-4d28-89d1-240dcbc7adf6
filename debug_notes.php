<?php
/**
 * Debug script to check notes database structure and data
 */

require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h2>Database Connection Test</h2>\n";
    echo "Connected successfully!<br>\n";
    
    // Check notes table structure
    echo "<h2>Notes Table Structure</h2>\n";
    $structure = $db->fetchAll("DESCRIBE notes");
    echo "<table border='1'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    foreach ($structure as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Check if there are any notes
    echo "<h2>Notes Count</h2>\n";
    $count = $db->fetchOne("SELECT COUNT(*) as count FROM notes");
    echo "Total notes: " . $count['count'] . "<br>\n";
    
    // Get sample notes
    echo "<h2>Sample Notes</h2>\n";
    $notes = $db->fetchAll("SELECT id, title, content, user_id, created_at FROM notes LIMIT 5");
    if ($notes) {
        echo "<table border='1'>\n";
        echo "<tr><th>ID</th><th>Title</th><th>Content Length</th><th>Content Preview</th><th>User ID</th><th>Created</th></tr>\n";
        foreach ($notes as $note) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($note['id']) . "</td>";
            echo "<td>" . htmlspecialchars($note['title']) . "</td>";
            echo "<td>" . strlen($note['content'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars(substr($note['content'] ?? '', 0, 50)) . "...</td>";
            echo "<td>" . htmlspecialchars($note['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars($note['created_at']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "No notes found.<br>\n";
    }
    
    // Test specific note query (if we have notes)
    if ($count['count'] > 0) {
        echo "<h2>Test Specific Note Query</h2>\n";
        $firstNote = $db->fetchOne("SELECT * FROM notes ORDER BY id LIMIT 1");
        if ($firstNote) {
            echo "<strong>Note ID:</strong> " . $firstNote['id'] . "<br>\n";
            echo "<strong>Title:</strong> " . htmlspecialchars($firstNote['title']) . "<br>\n";
            echo "<strong>Content exists:</strong> " . (!empty($firstNote['content']) ? 'YES' : 'NO') . "<br>\n";
            echo "<strong>Content length:</strong> " . strlen($firstNote['content'] ?? '') . "<br>\n";
            echo "<strong>Content preview:</strong> " . htmlspecialchars(substr($firstNote['content'] ?? '', 0, 100)) . "<br>\n";
            echo "<strong>Raw content:</strong> <pre>" . htmlspecialchars($firstNote['content'] ?? '') . "</pre>\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
