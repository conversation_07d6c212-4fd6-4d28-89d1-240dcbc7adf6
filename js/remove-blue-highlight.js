/**
 * Remove Blue Highlight JS
 *
 * This script removes the blue highlighting effect from task items
 * in the task list view without interfering with filter buttons.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Remove Blue Highlight script loaded');

    // Remove blue highlight classes from task items only
    function removeBlueHighlight() {
        try {
            // Only target task items and rows, not filter buttons
            const taskItems = document.querySelectorAll('.task-item, .task-row');
            const maxItems = Math.min(taskItems.length, 50);

            for (let i = 0; i < maxItems; i++) {
                const item = taskItems[i];

                // Remove any existing blue highlight classes
                item.classList.remove('bg-primary-50', 'dark:bg-primary-900', 'border-l-4', 'border-primary-500');

                // Remove inline styles
                if (item.style.backgroundColor &&
                    (item.style.backgroundColor.includes('rgb(239, 246, 255)') ||
                     item.style.backgroundColor.includes('rgb(30, 58, 138)'))) {
                    item.style.backgroundColor = '';
                }

                if (item.style.borderLeft && item.style.borderLeft.includes('primary')) {
                    item.style.borderLeft = 'none';
                }

                if (item.style.paddingLeft) {
                    item.style.paddingLeft = '';
                }
            }
        } catch (error) {
            console.error('Error in removeBlueHighlight:', error);
        }
    }

    // Check if we're on the tasks page
    const isTasksPage = window.location.pathname.includes('/tasks');

    // Only run on pages that aren't the tasks filter page
    if (!isTasksPage || !window.location.search.includes('filter_processed=1')) {
        // Run immediately
        removeBlueHighlight();

        // Run again after a short delay to catch any elements that might be added dynamically
        setTimeout(removeBlueHighlight, 500);

        // Run again when the page is fully loaded
        window.addEventListener('load', removeBlueHighlight);

        // Run periodically but with a longer interval to avoid performance issues
        setInterval(removeBlueHighlight, 2000);
    } else {
        console.log('On tasks filter page - not applying blue highlight removal to avoid conflicts');
    }
});
