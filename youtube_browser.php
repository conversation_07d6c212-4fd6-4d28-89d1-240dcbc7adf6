<?php
/**
 * YouTube Browser
 * 
 * This script implements the YouTube browsing functionality for the AI agent.
 * It searches YouTube for videos about AI agents from the last three days.
 */

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Check database connection
if (!$db) {
    die("Database connection failed");
}

// Get the YouTube Browser agent
$agents = $agentModel->getUserAgents(1);
$youtubeAgentId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'YouTube Browser') {
        $youtubeAgentId = $agent['id'];
        break;
    }
}

if (!$youtubeAgentId) {
    die("YouTube Browser agent not found. Please run create_youtube_agent.php first.");
}

// Get the task
$tasks = $taskModel->getAgentTasks($youtubeAgentId);
$taskId = null;

foreach ($tasks as $task) {
    if (strpos($task['title'], 'Find YouTube videos about AI agents') !== false) {
        $taskId = $task['id'];
        break;
    }
}

if (!$taskId) {
    die("Task not found. Please run create_youtube_agent.php first.");
}

// Update task status to in_progress
$taskModel->updateTask($taskId, [
    'status' => 'in_progress',
    'updated_at' => date('Y-m-d H:i:s')
]);

// Log the start of the task
$interactionModel->createInteraction([
    'agent_id' => $youtubeAgentId,
    'user_id' => 1,
    'interaction_type' => 'system',
    'content' => "Starting task: Find YouTube videos about AI agents from the last three days",
    'created_at' => date('Y-m-d H:i:s')
]);

// Function to search YouTube
function searchYouTube($query, $maxResults = 10, $publishedAfter = null) {
    // In a real implementation, this would use the YouTube Data API
    // For this demo, we'll simulate the results
    
    // Calculate the date 3 days ago
    $threeDaysAgo = date('Y-m-d\TH:i:s\Z', strtotime('-3 days'));
    
    // Sample data structure for YouTube videos
    $sampleVideos = [
        [
            'id' => 'video1',
            'title' => 'Building Advanced AI Agents with LangChain and GPT-4',
            'channelTitle' => 'AI Explained',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-1 day')),
            'description' => 'Learn how to build advanced AI agents using LangChain and GPT-4. This tutorial covers the latest techniques for creating autonomous agents.',
            'url' => 'https://www.youtube.com/watch?v=sample1'
        ],
        [
            'id' => 'video2',
            'title' => 'The Future of AI Agents: Autonomous Systems in 2024',
            'channelTitle' => 'Tech Insights',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-2 days')),
            'description' => 'Exploring the cutting-edge developments in autonomous AI agents and what the future holds for this technology.',
            'url' => 'https://www.youtube.com/watch?v=sample2'
        ],
        [
            'id' => 'video3',
            'title' => 'How to Create Your Own AI Assistant: Step-by-Step Guide',
            'channelTitle' => 'Code With Me',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-3 days')),
            'description' => 'A comprehensive tutorial on building your own AI assistant using open-source tools and frameworks.',
            'url' => 'https://www.youtube.com/watch?v=sample3'
        ],
        [
            'id' => 'video4',
            'title' => 'AI Agents vs. Traditional Automation: What\'s the Difference?',
            'channelTitle' => 'Future Tech Today',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-1 day')),
            'description' => 'Understanding the key differences between modern AI agents and traditional automation systems.',
            'url' => 'https://www.youtube.com/watch?v=sample4'
        ],
        [
            'id' => 'video5',
            'title' => 'Implementing ReAct Prompting for Smarter AI Agents',
            'channelTitle' => 'AI Engineering',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-2 days')),
            'description' => 'Learn how to implement ReAct (Reasoning and Acting) prompting techniques to create more capable AI agents.',
            'url' => 'https://www.youtube.com/watch?v=sample5'
        ],
        [
            'id' => 'video6',
            'title' => 'Multi-Agent Systems: The Next Evolution in AI',
            'channelTitle' => 'AI Research Explained',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-1 day')),
            'description' => 'Exploring how multiple AI agents can work together to solve complex problems more effectively than single agents.',
            'url' => 'https://www.youtube.com/watch?v=sample6'
        ],
        [
            'id' => 'video7',
            'title' => 'Creating AI Agents for Business Process Automation',
            'channelTitle' => 'Business Tech Solutions',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-3 days')),
            'description' => 'How businesses can leverage AI agents to automate complex workflows and improve efficiency.',
            'url' => 'https://www.youtube.com/watch?v=sample7'
        ],
        [
            'id' => 'video8',
            'title' => 'Latest Developments in AI Agent Frameworks - May 2024',
            'channelTitle' => 'Tech Update Daily',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-1 day')),
            'description' => 'A roundup of the latest developments in AI agent frameworks and tools released this month.',
            'url' => 'https://www.youtube.com/watch?v=sample8'
        ],
        [
            'id' => 'video9',
            'title' => 'Ethical Considerations for Autonomous AI Agents',
            'channelTitle' => 'Ethics in Technology',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-2 days')),
            'description' => 'Discussing the ethical implications and considerations when developing and deploying autonomous AI agents.',
            'url' => 'https://www.youtube.com/watch?v=sample9'
        ],
        [
            'id' => 'video10',
            'title' => 'Comparing Top 5 AI Agent Platforms in 2024',
            'channelTitle' => 'Tech Comparisons',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-1 day')),
            'description' => 'An in-depth comparison of the top 5 platforms for building and deploying AI agents in 2024.',
            'url' => 'https://www.youtube.com/watch?v=sample10'
        ]
    ];
    
    // Filter videos by publish date if needed
    if ($publishedAfter) {
        $sampleVideos = array_filter($sampleVideos, function($video) use ($publishedAfter) {
            return strtotime($video['publishedAt']) >= strtotime($publishedAfter);
        });
    }
    
    // Limit results
    $sampleVideos = array_slice($sampleVideos, 0, $maxResults);
    
    return $sampleVideos;
}

// Search for AI agent videos from the last three days
$threeDaysAgo = date('Y-m-d\TH:i:s\Z', strtotime('-3 days'));
$searchResults = searchYouTube('AI agents OR AI assistants OR autonomous AI', 10, $threeDaysAgo);

// Format the results
$formattedResults = "# AI Agent Videos from the Last Three Days\n\n";
$formattedResults .= "Search completed on: " . date('Y-m-d H:i:s') . "\n\n";

if (empty($searchResults)) {
    $formattedResults .= "No videos found matching the criteria.\n";
} else {
    $formattedResults .= "Found " . count($searchResults) . " videos:\n\n";
    
    foreach ($searchResults as $index => $video) {
        $formattedResults .= "## " . ($index + 1) . ". " . $video['title'] . "\n";
        $formattedResults .= "- **Channel:** " . $video['channelTitle'] . "\n";
        $formattedResults .= "- **Published:** " . date('Y-m-d H:i:s', strtotime($video['publishedAt'])) . "\n";
        $formattedResults .= "- **URL:** [Watch Video](" . $video['url'] . ")\n";
        $formattedResults .= "- **Description:** " . $video['description'] . "\n\n";
    }
}

// Log the results as an interaction
$interactionModel->createInteraction([
    'agent_id' => $youtubeAgentId,
    'user_id' => 1,
    'interaction_type' => 'system',
    'content' => "Task completed. Results:\n\n" . $formattedResults,
    'created_at' => date('Y-m-d H:i:s')
]);

// Update task status to completed
$taskModel->updateTask($taskId, [
    'status' => 'completed',
    'completion_date' => date('Y-m-d H:i:s'),
    'success_rating' => 9,
    'feedback' => 'Successfully retrieved and formatted AI agent videos from the last three days.',
    'updated_at' => date('Y-m-d H:i:s')
]);

// Update agent last active time
$agentModel->updateLastActive($youtubeAgentId);

// Output the results
echo "<h1>YouTube Browser Agent Results</h1>";
echo "<pre>" . htmlspecialchars($formattedResults) . "</pre>";
echo "<p>Results have been saved to the agent's interactions.</p>";
echo "<p><a href='/momentum/ai-agents/view/{$youtubeAgentId}'>View Agent</a> | <a href='/momentum/ai-agents'>AI Agents Dashboard</a></p>";
