<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include the Database class
require_once BASE_PATH . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Read the schema file
$schema = file_get_contents(BASE_PATH . '/database/adhd_schema.sql');

// Split the schema into individual statements
$statements = explode(';', $schema);

// Execute each statement
echo "Importing ADHD schema...\n";
foreach ($statements as $statement) {
    $statement = trim($statement);
    if (!empty($statement)) {
        $result = $db->query($statement);
        if ($result) {
            echo ".";
        } else {
            echo "x";
        }
    }
}

echo "\nImport complete!\n";

// Check if tables exist
$tables = [
    'adhd_symptom_logs',
    'adhd_symptom_events',
    'consistency_trackers',
    'consistency_logs',
    'mindfulness_exercises',
    'user_mindfulness_logs',
    'thought_records',
    'cognitive_distortions',
    'productivity_strategies',
    'user_strategies'
];

echo "\nChecking database tables:\n";
foreach ($tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '{$table}'");
    $exists = $result && $result->rowCount() > 0;
    echo "- {$table}: " . ($exists ? "EXISTS" : "MISSING") . "\n";
}
